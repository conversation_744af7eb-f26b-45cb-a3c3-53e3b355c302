package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsubtitle;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.IconRichLabelModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.UnifiedShelfItemCommonSubTitleOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.strategy.ProductTagStrategy;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.strategy.ProductTagStrategyFactory;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSubTitleVP;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.IconRichLabelTypeEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.TextStyleEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class UnifiedShelfItemCommonSubTitleOptTest {

    @Mock
    private ActivityCxt context;
    @Mock
    private UnifiedShelfItemCommonSubTitleOpt.Config config;
    @Mock
    private UnifiedShelfItemSubTitleVP.Param param;
    @Mock
    private ProductM productM;
    @Mock
    private ProductTagStrategyFactory productTagStrategyFactory;

    @InjectMocks
    private UnifiedShelfItemCommonSubTitleOpt target;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(target, "productTagStrategyFactory", productTagStrategyFactory);
    }

    /**
     * 测试 compute 方法，当 config.isForceNull() 返回 true 时，应返回 null。
     */
    @Test
    public void testComputeWhenConfigIsForceNull() {
        // arrange
        when(config.isForceNull()).thenReturn(true);

        // act
        ItemSubTitleVO result = target.computeFromOpt(context, param, config);

        // assert
        assertNull(result);
    }

    /**
     * 测试 compute 方法，当 productTags 非空时，应返回非 null。
     */
    @Test
    public void testComputeWhenProductTagsIsNotEmpty() {
        // arrange
        when(config.isForceNull()).thenReturn(false);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getProductTags()).thenReturn(Arrays.asList("Tag1", "Tag2"));

        // act
        ItemSubTitleVO result = target.computeFromOpt(context, param, config);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.getTags().size());
    }

    /**
     * 测试安心标签启用但商品没有安心标签时的情况
     */
    @Test
    public void testComputeWhenAnXinEnabledButNoAnXinTag() {
        // arrange
        when(config.isForceNull()).thenReturn(false);
        when(config.isEnableAnXin()).thenReturn(true);
        when(config.getAnXinTagCodes()).thenReturn(Arrays.asList(1, 2, 3));
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr("attr_guaranteeTagCodes")).thenReturn(null);
        when(productM.getProductTags()).thenReturn(Arrays.asList("Tag1", "Tag2"));

        // act
        ItemSubTitleVO result = target.computeFromOpt(context, param, config);

        // assert
        Assert.assertNotNull(result);
        Assert.assertNull(result.getIconTag());
        Assert.assertEquals(2, result.getTags().size());
    }

    /**
     * 测试安心标签启用且商品有安心标签时的情况
     */
    @Test
    public void testComputeWhenAnXinEnabledAndHasAnXinTag() {
        // arrange
        when(config.isForceNull()).thenReturn(false);
        when(config.isEnableAnXin()).thenReturn(true);
        when(config.getAnXinTagCodes()).thenReturn(Arrays.asList(1, 2, 3));
        when(config.getAnXinTextList()).thenReturn(Arrays.asList("跑路必赔"));
        when(config.getAnXinTextType()).thenReturn(TextStyleEnum.TEXT_GRAY_RED.getType());
        when(config.getAnXinIconUrl()).thenReturn("https://example.com/icon.png");
        when(config.getAnXinIconHeight()).thenReturn(20);
        when(config.getAnXinIconAspectRatio()).thenReturn(1.5);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr("attr_guaranteeTagCodes")).thenReturn("[1]");
        when(productM.getProductTags()).thenReturn(Arrays.asList("Tag1", "Tag2"));

        // act
        ItemSubTitleVO result = target.computeFromOpt(context, param, config);

        // assert
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getTags());
        Assert.assertNotNull(result.getPreTags());
        Assert.assertEquals(1, result.getPreTags().size());
        Assert.assertEquals("安心练", result.getPreTags().get(0).getName());
        Assert.assertNotNull(result.getPreTags().get(0).getPrePic());
        Assert.assertEquals("https://example.com/icon.png", result.getPreTags().get(0).getPrePic().getPicUrl());
        Assert.assertTrue(result.getTags().stream()
                .anyMatch(tag -> "跑路必赔".equals(tag.getText()) && TextStyleEnum.TEXT_GRAY_RED.getType().equals(tag.getStyle())));
    }

    /**
     * 测试安心标签覆盖原副标题的情况
     */
    @Test
    public void testComputeWhenAnXinOverrideOriginalSubTitle() {
        // arrange
        when(config.isForceNull()).thenReturn(false);
        when(config.isEnableAnXin()).thenReturn(true);
        when(config.getAnXinTagCodes()).thenReturn(Arrays.asList(1, 2, 3));
        when(config.getAnXinTextList()).thenReturn(Arrays.asList("跑路必赔"));
        when(config.getAnXinTextType()).thenReturn(TextStyleEnum.TEXT_GRAY_RED.getType());
        when(config.isAnXinOverrideOriginalSubTitle()).thenReturn(true);
        when(config.getAnXinIconUrl()).thenReturn("https://example.com/icon.png");
        when(config.getAnXinIconHeight()).thenReturn(20);
        when(config.getAnXinIconAspectRatio()).thenReturn(1.5);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr("attr_guaranteeTagCodes")).thenReturn("[1]");
        when(productM.getProductTags()).thenReturn(Arrays.asList("Tag1", "Tag2"));

        // act
        ItemSubTitleVO result = target.computeFromOpt(context, param, config);

        // assert
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getTags());
        Assert.assertEquals(1, result.getTags().size());
        Assert.assertEquals("跑路必赔", result.getTags().get(0).getText());
        Assert.assertEquals(TextStyleEnum.TEXT_GRAY_RED.getType(), result.getTags().get(0).getStyle());
    }

    /**
     * 测试先用后付标签的情况
     */
    @Test
    public void testComputeWithAfterPayTag() {
        // arrange
        when(config.isForceNull()).thenReturn(false);
        when(config.isEnableAfterPay()).thenReturn(true);
        when(config.getPreAfterTag()).thenReturn("先用后付");
        when(config.getAfterPayExps()).thenReturn(Arrays.asList("exp1", "exp2"));
        when(param.getProductM()).thenReturn(productM);
        when(productM.getProductTags()).thenReturn(Arrays.asList("Tag1", "Tag2"));

        // Mock the ProductMAttrUtils.timeCardIsShowAfterPayTag method result
        // Since we can't directly mock static methods without additional libraries,
        // we'll need to test the integration with real implementation

        // act
        ItemSubTitleVO result = target.computeFromOpt(context, param, config);

        // assert
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getTags());
    }

    /**
     * 测试策略副标题的情况
     */
    @Test
    public void testComputeWithStrategy() {
        // arrange
        when(config.isForceNull()).thenReturn(false);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getProductTags()).thenReturn(Arrays.asList("Tag1", "Tag2"));

        // Mock strategy factory and strategy
        ProductTagStrategy mockStrategy = mock(ProductTagStrategy.class);
        when(productTagStrategyFactory.getProductTagStrategy(any())).thenReturn(mockStrategy);
        when(mockStrategy.build(any())).thenReturn(UnifiedShelfItemSubTitleVP.build4DefaultTags(Arrays.asList("StrategyTag1", "StrategyTag2")));

        // act
        ItemSubTitleVO result = target.computeFromOpt(context, param, config);

        // assert
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getTags());
    }

    /**
     * 测试静态副标题的情况
     */
    @Test
    public void testComputeWithStaticSubTitle() {
        // arrange
        when(config.isForceNull()).thenReturn(false);
        when(config.getStaticSubTitle()).thenReturn("静态副标题");
        when(param.getProductM()).thenReturn(productM);

        // act
        ItemSubTitleVO result = target.computeFromOpt(context, param, config);

        // assert
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getTags());
        Assert.assertEquals(1, result.getTags().size());
        Assert.assertEquals("静态副标题", result.getTags().get(0).getText());
    }

    /**
     * 测试构建安心图标
     */
    @Test
    public void testBuildAnXinIcon() {
        // arrange
        when(config.getAnXinIconUrl()).thenReturn("https://example.com/icon.png");
        when(config.getAnXinIconHeight()).thenReturn(20);
        when(config.getAnXinIconAspectRatio()).thenReturn(1.5);

        // act
        IconRichLabelModel result = ReflectionTestUtils.invokeMethod(target, "buildAnXinIcon", config);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(IconRichLabelTypeEnum.ICON.getType(), result.getType());
        Assert.assertNotNull(result.getIcon());
        Assert.assertEquals("https://example.com/icon.png", result.getIcon().getPicUrl());
        Assert.assertEquals(20, result.getIcon().getPicHeight());
        Assert.assertEquals(1.5, result.getIcon().getAspectRadio(), 0.001);
    }

    /**
     * 测试构建安心练前置标签
     */
    @Test
    public void testBuildAnXinPreTags() {
        // arrange
        when(config.getAnXinIconUrl()).thenReturn("https://example.com/anxin.png");
        when(config.getAnXinIconHeight()).thenReturn(16);
        when(config.getAnXinIconAspectRatio()).thenReturn(2.0);

        // act
        java.util.List<ShelfTagVO> result = ReflectionTestUtils.invokeMethod(target, "buildAnXinPreTags", config);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
        ShelfTagVO anXinTag = result.get(0);
        Assert.assertEquals("安心练", anXinTag.getName());
        Assert.assertNotNull(anXinTag.getPrePic());
        Assert.assertEquals("https://example.com/anxin.png", anXinTag.getPrePic().getPicUrl());
        Assert.assertEquals(16, anXinTag.getPrePic().getPicHeight());
        Assert.assertEquals(2.0, anXinTag.getPrePic().getAspectRadio(), 0.001);
    }

    /**
     * 测试构建先用后付标签
     */
    @Test
    public void testBuildPreAfterPayTag() {
        // arrange
        when(config.getPreAfterTag()).thenReturn("先用后付");

        // act
        StyleTextModel result = target.buildPreAfterPayTag(config);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals("先用后付", result.getText());
        Assert.assertEquals(TextStyleEnum.TEXT_GREEN.getType(), result.getStyle());
    }

    // Helper method to mock ProductTagStrategy
    private ProductTagStrategy mock(Class<ProductTagStrategy> clazz) {
        return org.mockito.Mockito.mock(clazz);
    }
}