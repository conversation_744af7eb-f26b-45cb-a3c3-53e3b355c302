package com.sankuai.common.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.dztheme.deal.res.standardservice.DealStandardServiceProjectDTO;
import com.sankuai.dztheme.deal.res.standardservice.DealStandardServiceProjectGroupDTO;
import com.sankuai.dztheme.deal.res.structured.DealDetailStructuredDTO;
import com.sankuai.dztheme.deal.res.structured.DealSkuAttrItemDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;

public class DealAttrUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static String getAttrFromDealDetailStructured(DealDetailStructuredDTO dealDetailStructuredDTO, String attrName) {
        if (dealDetailStructuredDTO == null || StringUtils.isEmpty(attrName)) {
            return null;
        }
        String result = null;
        if (CollectionUtils.isNotEmpty(dealDetailStructuredDTO.getMustGroups())) {
            result = dealDetailStructuredDTO.getMustGroups().stream()
                    .filter(group -> group != null && CollectionUtils.isNotEmpty(group.getSkuItems()))
                    .flatMap(group -> group.getSkuItems().stream())
                    .filter(skuItem -> skuItem != null && CollectionUtils.isNotEmpty(skuItem.getAttrItems()))
                    .flatMap(skuItem -> skuItem.getAttrItems().stream())
                    .filter(Objects::nonNull)
                    .filter(attr -> attrName.equals(attr.getAttrName()))
                    .map(DealSkuAttrItemDTO::getAttrValue)// 取值
                    .findFirst().orElse(null);
        }
        if (StringUtils.isNotBlank(result)) {
            return result;
        }
        if (CollectionUtils.isEmpty(dealDetailStructuredDTO.getOptionalGroups())) {
            return null;
        }
        return dealDetailStructuredDTO.getOptionalGroups().stream()
                .filter(group -> group != null && CollectionUtils.isNotEmpty(group.getSkuItems()))
                .flatMap(group -> group.getSkuItems().stream())
                .filter(skuItem -> skuItem != null && CollectionUtils.isNotEmpty(skuItem.getAttrItems()))
                .flatMap(skuItem -> skuItem.getAttrItems().stream())
                .filter(Objects::nonNull)
                .filter(attr -> attrName.equals(attr.getAttrName()))
                .map(DealSkuAttrItemDTO::getAttrValue)// 取值
                .findFirst().orElse(null);
    }

    public static boolean isListJson(String value) {
        if (StringUtils.isBlank(value)) {
            return false;
        }
        try {
            objectMapper.readValue(value, new TypeReference<List<String>>() {});
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static String getAttrFromStandardServiceProject(DealStandardServiceProjectDTO standardServiceProjectDTO, String attrName) {
        if (standardServiceProjectDTO == null) {
            return null;
        }
        String result = null;
        if (CollectionUtils.isNotEmpty(standardServiceProjectDTO.getMustGroups())) {
            result = getAttrFromStandardServiceProjectGroups(standardServiceProjectDTO.getMustGroups(), attrName);
        }
        if (StringUtils.isNotBlank(result)) {
            return result;
        }
        return getAttrFromStandardServiceProjectGroups(standardServiceProjectDTO.getOptionalGroups(), attrName);
    }

    private static String getAttrFromStandardServiceProjectGroups(List<DealStandardServiceProjectGroupDTO> groups, String attrName) {
        if (CollectionUtils.isEmpty(groups) || StringUtils.isEmpty(attrName)) {
            return null;
        }
        return groups.stream()
                .filter(group -> group != null && CollectionUtils.isNotEmpty(group.getServiceProjectItems()))
                .flatMap(group -> group.getServiceProjectItems().stream())
                .filter(skuItem -> skuItem != null && skuItem.getStandardAttribute() != null && CollectionUtils.isNotEmpty(skuItem.getStandardAttribute().getAttrs()))
                .flatMap(skuItem -> skuItem.getStandardAttribute().getAttrs().stream())
                .filter(Objects::nonNull)
                .filter(attr -> attrName.equals(attr.getAttrName()) && CollectionUtils.isNotEmpty(attr.getAttrValues())) // 匹配
                .flatMap(attr -> attr.getAttrValues().stream())
                .filter(value -> value != null && CollectionUtils.isNotEmpty(value.getSimpleValues())) // 取SimpleValues
                .flatMap(value -> value.getSimpleValues().stream())
                .filter(StringUtils::isNotBlank)// 取第一个有值的
                .findFirst().orElse(null);
    }
}
