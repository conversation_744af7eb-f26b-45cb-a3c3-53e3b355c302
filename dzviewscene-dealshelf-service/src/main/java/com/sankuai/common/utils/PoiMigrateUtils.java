package com.sankuai.common.utils;

import com.dianping.lion.client.Lion;
import lombok.Getter;

import java.util.Map;

public class PoiMigrateUtils {

    public static boolean needLongPoiProcess(String scene) {
        Map<String, Boolean> result = Lion.getMap("com.sankuai.dzviewscene.dealshelf", "poi.migrate.newPoiProcess.sceneSwitchMap", Boolean.class);
        // 当Map不存在的时候,走老流程
        if (result == null) {
            return false;
        }

        // 当Map存在的时候,默认走新流程,可以通过场景进行干预
        return result.getOrDefault(scene, true);
    }
}
