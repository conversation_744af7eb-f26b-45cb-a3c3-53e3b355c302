package com.sankuai.common.helper;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2023/8/31 16:30
 */
@Slf4j
public class LionConfigHelper {

    /**
     *
     * @param categoryId
     * @param key （团购价：hidePrice，门市价：marketPrice）
     * @return
     */
    public static Boolean hidePriceOrMarketPrice(int categoryId, String key){
        Map<String, List> hideMatchPair = Lion.getMap(LionKeys.APP_KEY_MAPI_DZTG_WEB,
                LionKeys.HIDE_OPTIONAL_PRICE_OR_MARKET_PRICE, List.class, Collections.<String, List>emptyMap());
        if (CollectionUtils.isNotEmpty(hideMatchPair) && StringUtils.isNotBlank(key) && CollectionUtils.isNotEmpty(hideMatchPair.get(key))){
            if (hideMatchPair.get(key).contains(categoryId)){
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 货架爆品包框图片展示开关
     * @return
     */
    public static Boolean getHotShelfPackagePicSwitch() {
        return Lion.getBoolean(LionKeys.APP_KEY, "com.sankuai.dzviewscene.dealshelf.hot.shelf.package.show", false);
    }
    /**
     * 货架爆品包框标签配置
     * @return
     */
    public static Map<String, DzPictureComponentVO> getHotShelfPackageTagsConfig() {
        return Lion.getMap(LionKeys.APP_KEY, "com.sankuai.dzviewscene.dealshelf.hot.shelf.tags.config", DzPictureComponentVO.class, Collections.emptyMap());
    }

    /**
     * 获取无效代码下线灰度比例
     * 
     * @return
     */
    public static int getCodeDownlineGrayPercent(String optName) {
        // 灰度比例0、10、30、60、100
        //默认灰度比例
        int percent = Lion.getInt(LionKeys.APP_KEY, LionKeys.GRAY_CODE_DOWNLINE_PERCENT, 0);
        //具体到opt的灰度配置
        Map<String, Integer> optPercentMap = Lion.getMap(LionKeys.APP_KEY, LionKeys.GRAY_CODE_DOWNLINE_PERCENT_OPT, Integer.class, Collections.emptyMap());
        if (Objects.isNull(optPercentMap)) {
            return percent;
        }
        Integer optPercent = optPercentMap.get(optName);
        return optPercent != null ? optPercent : percent;
    }

    /**
     * 是否开启无效代码下线
     * 
     * @return
     */
    public static boolean isOpenGrayCodeDownLine() {
        return Lion.getBoolean(LionKeys.APP_KEY, LionKeys.OPEN_GRAY_CODE_DOWNLINE, false);
    }

    public static boolean isGrayCodeDownLine(ActivityCxt context, String optName) {
        Cat.logEvent("grayCodeDownLine", optName);
        if (!isOpenGrayCodeDownLine()) {
            return false;
        }
        int grayPercent = getCodeDownlineGrayPercent(optName);
        long dpShopId = ParamsUtil.getLongSafely(context, ShelfActivityConstants.Params.dpPoiIdL);
        if (dpShopId % 100 <= grayPercent) {
            return true;
        }
        return false;
    }

    /**
     * 货架爆品包框图片展示开关
     * @return
     */
    public static Boolean isSigDegrade() {
        return Lion.getBoolean(LionKeys.APP_KEY, LionKeys.SIG_DEGRADE_SWITCH, false);
    }

    /**
     * 团详RCF缓存开关
     */
    public static Boolean hitDealDetailFlashCache(int dealId) {
        int ratio = Lion.getInt(LionKeys.APP_KEY, LionKeys.DEAL_DETAIL_FLASH_CACHE_SWITCH, 0);
        return dealId % 100 < ratio;
    }

    public static Map<String, Map> getSearchKeyScene() {
        return Lion.getMap(LionKeys.APP_KEY, "com.sankuai.dzviewscene.dealshelf.searchKeyScene", Map.class, Collections.emptyMap());
    }

    public static boolean timesDealExpressOptimizeSwitch() {
        return Lion.getBoolean(LionKeys.APP_KEY, LionKeys.TIMES_DEAL_OPTIMIZE_SWITCH, false);
    }

    public static Map<String, String> getSpringNoAvailable() {
        return Lion.getMap(LionKeys.APP_KEY, "com.sankuai.dzviewscene.dealshelf.spring.not.available", String.class, Collections.emptyMap());
    }

    // com.sankuai.dzu.tpbase.dztgdetailweb.product.credit.pay.switch
    public static boolean timesDealCreditPaySwitch() {
        return Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.product.credit.pay.switch", false);
    }

    /**
     * 门店id不一致日志开关
     * @return
     */
    public static boolean shopIdInconsistencySwitch() {
        return Lion.getBoolean(LionKeys.APP_KEY, "shopid.inconsistency.log.switch", false);
    }

    public static boolean longShopIdLogSwitch(long shopId) {
        int ratio = Lion.getInt(LionKeys.APP_KEY, "long.shopid.log.switch.ratio", 0);
        return shopId % 100 < ratio;
    }

    @Data
    public static class FilterSecondTabConfig {
        private boolean filterSwitch;
        private List<String> blackSceneCodeList;
        private int childrenMinShowNum;
        private List<Long> blackDpShopIdList;
    }
}
