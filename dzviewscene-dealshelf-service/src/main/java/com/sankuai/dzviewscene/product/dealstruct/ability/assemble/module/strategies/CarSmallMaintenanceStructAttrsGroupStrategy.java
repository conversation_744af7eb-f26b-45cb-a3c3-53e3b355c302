package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.DealDetailSkuGroupsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.*;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 小保养团详的"基础服务内容"模块展示
 *
 * <AUTHOR>
 * @date 2023/6/21
 */
@Component("carSmallMaintenanceStructAttrsGroupStrategy")
public class CarSmallMaintenanceStructAttrsGroupStrategy implements ModuleStrategy {
    private static final String OIL_ICON = "https://p0.meituan.net/travelcube/52b31381a5589131a8332be93fa26e232332.png"; // 机油的icon
    private static final String FILTER_ICON = "https://p0.meituan.net/travelcube/c0f30a75080ab21e2034015611773f782113.png"; // 机滤的icon
    private static final String TIME_ICON = "https://p1.meituan.net/travelcube/d53f59f61a1a1c9f37a28a5b280be9632255.png"; // 工时费的icon

    private static final List<String> THICKNESS_OPTIONS = Lists.newArrayList("0W-20","0W-30","0W-40","5W-20","5W-30","5W-40","10W-40","10W-60","15W-40","15W-50"); // 机油粘度的多选项
    private static final String MAN_HOUR_FEE_FORMAT = "工时费";
    private static final String PRICE_FORMAT = "%s元";
    private static final String COPIES = "1份";
    private static final String THICKNESS_FORMAT = "粘度 %s";
    private static final String LEVEL_FORMAT = "等级 %s";

    private static final String OIL_FILTER_SUFFIX = "机滤";
    private static final String ENGINE_OIL_BRAND = "engineOilBrand";
    private static final String TYPE = "type";
    private static final String ENGINE_OIL_TYPE = "engine_oil_type";
    private static final String ENGINE_OIL_CAPACITY = "engine_oil_capacity";
    private static final String OIL_MARKET_PRICE = "oilMarketPrice";
    private static final String THICKNESS = "thickness";
    private static final String LEVEL = "level";
    private static final String ENGINE_OIL_EFFECT = "engineOilEffect";
    private static final String ENGINE_OIL_FILTER_BRAND = "engineOilFilterBrand";
    private static final String OIL_FILTER_MARKET_PRICE = "oilFilterMarketPrice";
    private static final String ENGINE_OIL_FILTER_EFFECT = "engineOilFilterEffect";
    private static final String MAN_HOUR_FEE = "manHourFee";
    private static final String ORIGIN_THICKNESS_SEPARATOR = "、";
    private static final String NEW_THICKNESS_SEPARATOR = ",";
    private static final String COMMON_SEPARATOR = "-";


    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        List<AttrM> dealAttrs = getDealAttrs(dealDetailInfoModels); // 获取团单属性
        // 获取需要的依赖数据
        List<DealDetailSkuUniModel> dealDetailSkuUniModels = activityCxt.getSource(DealDetailSkuGroupsBuilder.CODE);
        SkuItemDto skuItemDto = getFirstValidSkuItemDto(CollectUtils.firstValue(dealDetailSkuUniModels));
        // 组装VOList
        List<DealSkuVO> dealSkuVOList = buildDealSkuVOList(skuItemDto, dealAttrs);
        // 组装为结果VO
        return buildDealDetailModuleVO(dealSkuVOList);
    }

    private List<AttrM> getDealAttrs(List<DealDetailInfoModel> dealDetailInfoModels) {
        DealDetailInfoModel dealDetailInfoModel = CollectUtils.firstValue(dealDetailInfoModels);
        if (dealDetailInfoModel == null) {
            return Lists.newArrayList();
        }
        return dealDetailInfoModel.getDealAttrs();
    }


    /**
     * 获取第一个有效的服务项目
     */
    private SkuItemDto getFirstValidSkuItemDto(DealDetailSkuUniModel dealDetail) {
        if (dealDetail == null) {
            return null;
        }
        DealDetailSkuGroupModel skuGroupModel = CollectUtils.firstValue(dealDetail.getMustGroups());
        if (skuGroupModel == null) {
            return null;
        }
        DealDetailSkuSetModel dealDetailSkuSetModel = CollectUtils.firstValue(skuGroupModel.getSkuSetModels());
        if (dealDetailSkuSetModel == null) {
            return null;
        }
        SkuItemModel skuItemModel = CollectUtils.firstValue(dealDetailSkuSetModel.getSkuItems());
        if (skuItemModel == null) {
            return null;
        }
        return skuItemModel.getSkuItemDto();
    }

    private List<DealSkuVO> buildDealSkuVOList(SkuItemDto skuItemDto, List<AttrM> dealAttrs) {
        if(skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return new ArrayList<>();
        }
        Map<String, SkuAttrItemDto> skuAttrItemDtoMap = skuItemDto.getAttrItems().stream().collect(Collectors.toMap(SkuAttrItemDto::getAttrName, Function.identity(), (existing, replacement) -> existing));
        // 1. build 机油部分
        DealSkuVO engineOil = buildEngineOil(skuAttrItemDtoMap, dealAttrs);
        // 2. build 机滤部分
        DealSkuVO engineOilFilter = buildEngineOilFilter(skuAttrItemDtoMap);
        // 3. build 工时费
        DealSkuVO manHourFee = buildManHourFee(skuAttrItemDtoMap);
        return Lists.newArrayList(engineOil, engineOilFilter, manHourFee);
    }

    /**
     * [icon] engineOilBrand + type '-' engine_oil_type '-' engine_oil_capacity '机油'      '1份'      oilMarketPrice '元'
     *      '粘度 ' thickness '｜等级 ' level
     *      engineOilEffect
     */
    private DealSkuVO buildEngineOil(Map<String, SkuAttrItemDto> skuAttrItemDtoMap, List<AttrM> dealAttrs) {
        // skuItemDto不为空
        // 拼接机油品牌信息
        String oilBrand = buildOilBrand(skuAttrItemDtoMap, dealAttrs);
        // 机油价格
        String oilMarketPrice = getStructuralAttr(skuAttrItemDtoMap, OIL_MARKET_PRICE);
        // 机油额外信息
        List<DealSkuItemVO> items = buildOilExtraInfo(skuAttrItemDtoMap);
        // 构造DealSkuVO
        return buildDealSkuVo(OIL_ICON, oilBrand, String.format(PRICE_FORMAT, oilMarketPrice), items);
    }

    private String buildOilBrand(Map<String, SkuAttrItemDto> skuAttrItemDtoMap, List<AttrM> dealAttrs) {
        String engineOilBrand = getStructuralAttr(skuAttrItemDtoMap, ENGINE_OIL_BRAND);
        String type = getStructuralAttr(skuAttrItemDtoMap, TYPE);
        String engineOilType = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, ENGINE_OIL_TYPE);
        String engineOilCapacity = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, ENGINE_OIL_CAPACITY);

        String brandType = engineOilBrand + type;
        return joinNotEmptyString(COMMON_SEPARATOR, brandType, engineOilType, engineOilCapacity);
    }

    private List<DealSkuItemVO> buildOilExtraInfo(Map<String, SkuAttrItemDto> skuAttrItemDtoMap) {
        String originThickness = getStructuralAttr(skuAttrItemDtoMap, THICKNESS);
        String level = getStructuralAttr(skuAttrItemDtoMap, LEVEL);
        String oilInfo = buildOilThicknessAndLevel(originThickness, level); // 副标题第一行
        String engineOilEffect = getStructuralAttr(skuAttrItemDtoMap, ENGINE_OIL_EFFECT); // 副标题第二行
        return buildItems(oilInfo, engineOilEffect);
    }

    private String buildOilThicknessAndLevel(String originThickness, String level) {
        String thickness = concatThickness(originThickness);
        String thicknessInfo = StringUtils.isEmpty(thickness) ? StringUtils.EMPTY : String.format(THICKNESS_FORMAT, thickness);
        String levelInfo = StringUtils.isEmpty(level) ? StringUtils.EMPTY : String.format(LEVEL_FORMAT, level);
        return joinNotEmptyString("｜", thicknessInfo, levelInfo);
    }

    /**
     * 将原始机油粘度进行分割，重新拼接
     * 拼接逻辑见 <a href="https://km.sankuai.com/collabpage/1716240978">小保养上单+团详优化</a>
     * @param originThickness 原始的机油粘度
     * @return 重新拼接的机油粘度
     */
    private String concatThickness(String originThickness) {
        if (StringUtils.isEmpty(originThickness)) {
            return StringUtils.EMPTY;
        }
        String[] thicknessElements = originThickness.split(ORIGIN_THICKNESS_SEPARATOR);
        List<String> elementsInOptions = new ArrayList<>();
        List<String> elementsNotInOptions = new ArrayList<>();
        for (String element : thicknessElements) {
            if (THICKNESS_OPTIONS.contains(element)) {
                elementsInOptions.add(element);
            } else {
                elementsNotInOptions.add(element);
            }
        }
        String thicknessInOptions = concatThicknessInOptions(elementsInOptions);
        String thicknessNotInOptions = StringUtils.join(elementsNotInOptions, NEW_THICKNESS_SEPARATOR);
        return joinNotEmptyString(NEW_THICKNESS_SEPARATOR, thicknessInOptions, thicknessNotInOptions);
    }

    /**
     * 将在多选项内的机油粘度item进行拼接
     * @param elementsInOptions 在多选项内的机油粘度
     * @return 拼接好的机油粘度字符串
     */
    private String concatThicknessInOptions(List<String> elementsInOptions) {
        if (CollectionUtils.isEmpty(elementsInOptions)) {
            return StringUtils.EMPTY;
        }
        return elementsInOptions.stream().sorted((ele1, ele2) -> {
            int[] range1 = Arrays.stream(ele1.split("W-")).mapToInt(Integer::parseInt).toArray();
            int[] range2 = Arrays.stream(ele2.split("W-")).mapToInt(Integer::parseInt).toArray();
            if (range1[0] != range2[0]) return range1[0] - range2[0];
            return range1[1] - range2[1];
        }).reduce((thicknessConcat, ele) -> {
            String[] thicknessRanges = thicknessConcat.split(NEW_THICKNESS_SEPARATOR);
            String firstThicknessRange = thicknessRanges[0];
            String[] split1 = firstThicknessRange.split(COMMON_SEPARATOR);
            String[] split2 = ele.split(COMMON_SEPARATOR);
            if (split1[0].equals(split2[0])) {
                String join = joinNotEmptyString("/", split1[1], split2[1]);
                thicknessRanges[0] = split1[0]+COMMON_SEPARATOR+join;
            } else {
                thicknessRanges[0] = joinNotEmptyString(NEW_THICKNESS_SEPARATOR, ele, firstThicknessRange);
            }
            return StringUtils.join(thicknessRanges, NEW_THICKNESS_SEPARATOR);
        }).orElse(StringUtils.EMPTY);
    }

    /**
     * [机滤]:     [icon] engineOilFilterBrand+'机滤'          '1份'       oilFilterMarketPrice+'元'
     *     engineOilFilterEffect
     */
    private DealSkuVO buildEngineOilFilter(Map<String, SkuAttrItemDto> skuAttrItemDtoMap) {
        String engineOilFilterBrand = getStructuralAttr(skuAttrItemDtoMap, ENGINE_OIL_FILTER_BRAND);
        String oilFilterMarketPrice = getStructuralAttr(skuAttrItemDtoMap, OIL_FILTER_MARKET_PRICE);
        String engineOilFilterEffect = getStructuralAttr(skuAttrItemDtoMap, ENGINE_OIL_FILTER_EFFECT);
        List<DealSkuItemVO> items = buildItems(engineOilFilterEffect);
        // 当engineOilFilterBrand不包含机滤字段时，则增加”机滤“后缀
        if (!engineOilFilterBrand.contains(OIL_FILTER_SUFFIX)) {
            engineOilFilterBrand += OIL_FILTER_SUFFIX;
        }
        return buildDealSkuVo(FILTER_ICON, engineOilFilterBrand,
                String.format(PRICE_FORMAT, oilFilterMarketPrice), items);
    }

    /**
     * [工时费]:   [icon] '工时费'     '1份'       manHourFee+'元'
     */
    private DealSkuVO buildManHourFee(Map<String, SkuAttrItemDto> skuAttrItemDtoMap) {
        String manHourFee = getStructuralAttr(skuAttrItemDtoMap, MAN_HOUR_FEE);
        return buildDealSkuVo(TIME_ICON, MAN_HOUR_FEE_FORMAT, String.format(PRICE_FORMAT, manHourFee), null);
    }

    private DealSkuVO buildDealSkuVo(String icon, String title, String price, List<DealSkuItemVO> items) {
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setIcon(icon);
        dealSkuVO.setTitle(title);
        dealSkuVO.setCopies(COPIES);
        dealSkuVO.setPrice(price);
        dealSkuVO.setItems(items);
        return dealSkuVO;
    }

    private List<DealSkuItemVO> buildItems(String... values) {
        if (values == null || values.length == 0) {
            return Lists.newArrayList();
        }
        return Arrays.stream(values).map(value -> {
            DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
            dealSkuItemVO.setName(value);
            return dealSkuItemVO;
        }).collect(Collectors.toList());
    }

    private DealDetailModuleVO buildDealDetailModuleVO(List<DealSkuVO> dealSkuVOList) {
        if (CollectionUtils.isEmpty(dealSkuVOList)) {
            return null;
        }
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        List<DealSkuGroupModuleVO> skuGroupsModel1 = Lists.newArrayList();
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(dealSkuVOList);
        dealSkuGroupModuleVO.setTitle(StringUtils.EMPTY);
        skuGroupsModel1.add(dealSkuGroupModuleVO);
        dealDetailModuleVO.setSkuGroupsModel1(skuGroupsModel1);
        return dealDetailModuleVO;
    }

    /**
     * 将多个字符串中非空的字符串连接起来
     * @param separator 连接符
     * @param strings 多个字符串
     * @return join后的字符串
     */
    private String joinNotEmptyString(String separator, String... strings) {
        Object[] stringsFiltered = Arrays.stream(strings).filter(StringUtils::isNotEmpty).toArray();
        return StringUtils.join(stringsFiltered, separator);
    }

    private String getStructuralAttr(Map<String, SkuAttrItemDto> skuAttrItemDtoMap, String attrName) {
        if (MapUtils.isEmpty(skuAttrItemDtoMap)) {
            return StringUtils.EMPTY;
        }
        SkuAttrItemDto skuAttrItemDto = skuAttrItemDtoMap.get(attrName);
        if (skuAttrItemDto == null) {
            return StringUtils.EMPTY;
        }
        return skuAttrItemDto.getAttrValue();
    }
}
