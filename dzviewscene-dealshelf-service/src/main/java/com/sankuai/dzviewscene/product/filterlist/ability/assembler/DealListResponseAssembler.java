package com.sankuai.dzviewscene.product.filterlist.ability.assembler;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.data.Converters;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityRequest;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.*;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.filter.DealFilterBuilder;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivity;
import com.sankuai.dzviewscene.product.filterlist.model.DealFilterListM;
import com.sankuai.dzviewscene.product.filterlist.utils.ContextParamBuildUtils;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterProductListVO;
import com.sankuai.dzviewscene.product.filterlist.vo.SearchBoxVO;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2022/5/1
 */
@Ability(code = DealListResponseAssembler.CODE,
        name = "VO-团单筛选列表生成能力",
        description = "团单筛选列表生成能力",
        activities = {DealFilterListActivity.CODE},
        dependency = {DealListModelAssembler.CODE, DealFilterBuilder.CODE, DealListBuilder.CODE}
)
public class DealListResponseAssembler extends PmfAbility<DzFilterProductListVO, DealListResponseAssembler.Request, DealListResponseAssembler.Config> {

    public static final String CODE = "DealListResponseAssembler";

    @Override
    public CompletableFuture<DzFilterProductListVO> build(ActivityCxt ctx, Request request, Config config) {
        DzFilterProductListVO filterListRes = new DzFilterProductListVO();
        filterListRes.setTitle(title(ctx, config));
        filterListRes.setSubtitle(config.getSubtitle());
        DealFilterListM dealFilterListM = ctx.getSource(DealListModelAssembler.CODE);
        if (dealFilterListM == null) {
            return CompletableFuture.completedFuture(filterListRes);
        }
        filterListRes.setFilters(ctx.getSource(DealFilterBuilder.CODE));
        filterListRes.setProducts(ctx.getSource(DealListBuilder.CODE));
        filterListRes.setAbTest(getABTest(dealFilterListM.getDouHus()));
        filterListRes.setShowType(this.getShowType(ctx, dealFilterListM.getDouHus(), config));
        filterListRes.setHasNext(listHasNext(dealFilterListM));
        filterListRes.setMoreJumpText(moreJumpText(ctx));
        filterListRes.setMoreJumpUrl(moreJumpUrl(ctx, request));
        filterListRes.setDefaultShowNum(getDefaultShowNum(ctx, config));
        filterListRes.setSearchBox(buildSearchBox(ctx, request));
        filterListRes.setExtraMap(getExtraMap(ctx));
        filterListRes.setTotalCount(dealFilterListM.getTotalCount());
        return CompletableFuture.completedFuture(filterListRes);
    }

    private int getShowType(ActivityCxt ctx, List<DouHuM> douHuList, Config config) {
        DealFilterListShowTypeVP<?> dealFilterListShowTypeVP = findVPoint(ctx, DealFilterListShowTypeVP.CODE);
        if (Objects.isNull(dealFilterListShowTypeVP) || Objects.isNull(dealFilterListShowTypeVP.getVPointOptionCode())) {
            return config.getShowType();
        }
        Integer showTypeResult = dealFilterListShowTypeVP.execute(ctx, DealFilterListShowTypeVP.Param.builder()
                .douHuList(douHuList).build());
        if (Objects.nonNull(showTypeResult)) {
            return showTypeResult;
        }
        return config.getShowType();
    }

    private SearchBoxVO buildSearchBox(ActivityCxt ctx, Request request) {
        SearchBoxVO searchBoxVO = new SearchBoxVO();
        SearchBoxJumpUrlVP<?> searchBoxJumpUrlVP = findVPoint(ctx, SearchBoxJumpUrlVP.CODE);
        searchBoxVO.setJumpUrl(searchBoxJumpUrlVP.execute(ctx, SearchBoxJumpUrlVP.Param.builder()
                .platform(request.getPlatform())
                .dpPoiId(request.getDpPoiId())
                .dpPoiIdL(PoiIdUtil.getDpPoiIdL(request))
                .mtPoiId(request.getMtPoiId())
                .mtPoiIdL(PoiIdUtil.getMtPoiIdL(request))
                .shopCategory(request.getCategory())
                .build()));
        return searchBoxVO;
    }

    private HashMap<String, Object> getExtraMap(ActivityCxt ctx) {
        int category = ParamsUtil.getIntSafely(ctx, "category");
        HashMap<String, Object> extraMap = new HashMap<>();
        extraMap.put("shop_category_id", category);
        return extraMap;
    }

    private String moreJumpUrl(ActivityCxt ctx, Request request) {
        DealFilterListMoreJumpUrlVP<?> dealFilterListMoreJumpUrlVP = findVPoint(ctx, DealFilterListMoreJumpUrlVP.CODE);
        return dealFilterListMoreJumpUrlVP.execute(ctx, DealFilterListMoreJumpUrlVP.Param.builder()
                .platform(request.getPlatform())
                .entityId(request.getEntityId())
                .userAgent(request.getUserAgent())
                .platformCityId(request.doGetPlatformCityId())
                .build());
    }

    private String moreJumpText(ActivityCxt ctx) {
        DealFilterListMoreTextVP<?> dealFilterListMoreTextVP = findVPoint(ctx, DealFilterListMoreTextVP.CODE);
        return dealFilterListMoreTextVP.execute(ctx, DealFilterListMoreTextVP.Param.builder().dealCount(ContextParamBuildUtils.getTotalProductNum(ctx)).build());
    }

    private int getDefaultShowNum(ActivityCxt ctx, Config config) {
        DealFilterListDefaultShowNumVP<?> dealFilterListDefaultShowNumVP = findVPoint(ctx, DealFilterListDefaultShowNumVP.CODE);
        Integer defaultShowNum = dealFilterListDefaultShowNumVP.execute(ctx, DealFilterListDefaultShowNumVP.Param.builder()
                .defaultShowNum(config.getDefaultShowNum())
                .build());
        return defaultShowNum == null ? config.getDefaultShowNum() : defaultShowNum;
    }

    private String title(ActivityCxt ctx, Config config) {
        try {
            DealFilterListTitleVP<?> dealFilterListTitleVP = findVPoint(ctx, DealFilterListTitleVP.CODE);
            String extTitle = dealFilterListTitleVP.execute(ctx, DealFilterListTitleVP.Param.builder()
                    .dealCount(ContextParamBuildUtils.getTotalProductNum(ctx))
                    .scriptKillTitle(ContextParamBuildUtils.getScriptTitle(ctx))
                    .build());
            if (extTitle == null) {
                return config.getTitle();
            }
            return extTitle;
        } catch (Exception e) {
            return config.getTitle();
        }
    }

    private String getABTest(List<DouHuM> douHuMList) {
        if (CollectionUtils.isEmpty(douHuMList)) {
            return null;
        }
        List<String> abTestList = Converters.newPropertyExtractorConverter("abtest").convert(douHuMList);
        return JsonCodec.encode(abTestList);
    }

    private boolean listHasNext(DealFilterListM dealFilterListM) {
        ProductGroupM productGroupM = dealFilterListM.getFirstProductGroup();
        return productGroupM == null ? false : productGroupM.isHasNext();
    }

    @AbilityCfg
    @Data
    public static class Config {
        /**
         * 展示类型
         * {@link ShelfActivityConstants.Style#showType}
         */
        private int showType;

        /**
         * 列表标题
         */
        private String title;

        /**
         * 副标题
         */
        private String subtitle;

        /**
         * 默认展示数量
         */
        private int defaultShowNum;

        /**
         * 默认展示数量
         */
        private String jumpUrlTemplate;
    }

    @AbilityRequest
    @Data
    public static class Request {

        private int dpPoiId;
        private long dpPoiIdL;

        private int mtPoiId;
        private long mtPoiIdL;

        private int platform;

        private int category;

        /**
         * {@link ShelfActivityConstants.Params#entityId}
         */
        private String entityId;

        /**
         * {@link ShelfActivityConstants.Params#userAgent}
         */
        private int userAgent;

        private int dpCityId;

        private int mtCityId;

        /**
         * @return 获取相应平台的 cityId
         */
        public int doGetPlatformCityId() {
            return PlatformUtil.isMT(this.platform) ? this.mtCityId : this.dpCityId;
        }
    }
}
