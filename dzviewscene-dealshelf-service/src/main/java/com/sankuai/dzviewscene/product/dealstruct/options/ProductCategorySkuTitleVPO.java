package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.cat.Cat;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.SkuTitleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/27 9:17 下午
 */
@VPointOption(name = "sku类别名称作为sku货标题默认变化点", description = "取sku类别名作为货标题",code = ProductCategorySkuTitleVPO.CODE, isDefault = false)
public class ProductCategorySkuTitleVPO extends SkuTitleVP<ProductCategorySkuTitleVPO.Config> {

    public static final String CODE = "ProductCategorySkuTitleVPO";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        SkuItemDto skuItemDto = param.getSkuItemDto();
        if (skuItemDto == null) {
            return null;
        }
        if (config != null && CollectionUtils.isNotEmpty(config.getSkuCategorysWithSkuName()) && config.getSkuCategorysWithSkuName().contains(skuItemDto.getProductCategory())) {
            return getSkuName(skuItemDto.getName(), config.getSkuNameFormat());
        }
        List<ProductSkuCategoryModel> productCategories = param.getProductCategories();
        return DealDetailUtils.getSkuCategoryBySkuCategoryId(skuItemDto.getProductCategory(), productCategories);
    }

    private String getSkuName(String skuName, String format) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.dealstruct.options.ProductCategorySkuTitleVPO.getSkuName(java.lang.String,java.lang.String)");
        if (StringUtils.isEmpty(skuName)) {
            return null;
        }
        if (StringUtils.isEmpty(format)) {
            return skuName;
        }
        return String.format(format, skuName);
    }

    @Data
    @VPointCfg
    public static class Config {
        //以下列表包含的sku类别均以sku名称作为sku标题进行展示
        private List<Long> skuCategorysWithSkuName;
        //以sku名称作为sku标题进行展示时的标题format
        private String skuNameFormat;
    }
}
