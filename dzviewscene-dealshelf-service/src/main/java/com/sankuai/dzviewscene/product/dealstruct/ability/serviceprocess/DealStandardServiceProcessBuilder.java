package com.sankuai.dzviewscene.product.dealstruct.ability.serviceprocess;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.IVPoint;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.servicefeatures.vcpoints.DealDetailStandardServiceModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.serviceprocess.vcpoints.DealStandardServiceProcessVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.ability.videoModule.DealDetailVideoModuleCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.videoModule.DealDetailVideoModuleParam;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.options.BeautyNailSkuAttrItemsVPO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.StandardServiceProcessVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.StandardServiceVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


@Ability(code = DealStandardServiceProcessBuilder.CODE,
        name = "团购标准服务流程构造能力",
        description = "团购标准服务流程构造能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE
        }
)
public class DealStandardServiceProcessBuilder extends PmfAbility<List<StandardServiceProcessVO>, DealStandardServiceProcessParam,
        DealStandardServiceProcessCfg> {

    public static final String CODE = "standardServiceProcessBuilder";

    @Override
    public CompletableFuture<List<StandardServiceProcessVO>> build(ActivityCxt activityCxt,
                                                                   DealStandardServiceProcessParam dealStandardServiceProcessParam, 
                                                                   DealStandardServiceProcessCfg dealStandardServiceProcessCfg) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        if (CollectionUtils.isEmpty(dealDetailInfoModels)) {
            return CompletableFuture.completedFuture(null);
        }
        DealDetailInfoModel dealDetailInfoModel = CollectUtils.firstValue(dealDetailInfoModels);
        List<AttrM> dealAttrs = dealDetailInfoModel.getDealAttrs();
        DealStandardServiceProcessVP<?> dealStandardServiceProcessVP = findVPoint(activityCxt, DealStandardServiceProcessVP.CODE);
        List<StandardServiceProcessVO> standardServiceProcessVOS = dealStandardServiceProcessVP.execute(activityCxt,
                DealStandardServiceProcessVP.Param
                        .builder()
                        .dealAttrs(dealAttrs)
                        .build());
        return CompletableFuture.completedFuture(standardServiceProcessVOS);
    }
}
