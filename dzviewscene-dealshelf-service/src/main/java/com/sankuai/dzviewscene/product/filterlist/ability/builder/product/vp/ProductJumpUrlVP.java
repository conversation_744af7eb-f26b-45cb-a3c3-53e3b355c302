package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;


@VPoint(name = "团单筛选列表跳链改写", description = "团单筛选列表跳链改写", code = ProductJumpUrlVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductJumpUrlVP<T> extends PmfVPoint<String, ProductJumpUrlVP.Param, T> {

    public static final String CODE = "ProductJumpUrlVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;

    }

}
