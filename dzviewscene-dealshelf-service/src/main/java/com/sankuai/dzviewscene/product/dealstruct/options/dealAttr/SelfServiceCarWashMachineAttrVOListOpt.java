package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@VPointOption(name = "爱车-洗车机DealAttrVO列表变化点", description = "爱车-洗车机DealAttrVO列表变化点",code = SelfServiceCarWashMachineAttrVOListOpt.CODE)
public class SelfServiceCarWashMachineAttrVOListOpt extends DealAttrVOListVP<SelfServiceCarWashMachineAttrVOListOpt.Config> {

    public static final String CODE = "SelfServiceCarWashMachineAttrVOListOpt";

    private static final String PACKAGE_DETAIL = "套餐内容";

    private static final String SERVICE_DETAIL = "服务内容";

    private static final String SERVICE_DETAIL_PIC = "操作流程";

    private static final String PACKAGE_CONTENT = "packageContent";

    private static final String SERVICE_PROCESS = "serviceProcess";

    private static final String AVAILABLE_TIME_TEXT = "availableTimeText";

    private static final String DEFAULT_SERVICE_PROCESS_ICON = "https://p1.meituan.net/travelcube/6bbf7a9c05ec992f7b3bbb1bbf75b8f71641.png";

    private static final Integer HOURS_PER_DAY = 24;

    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        if(config == null || param == null || CollectionUtils.isEmpty(param.getDealAttrs())) {
            return null;
        }
        List<SkuAttrItemDto> firstMustGroupFirstSkuAttrList = DealDetailUtils.getFirstMustGroupFirstSkuAttrList(param.getDealDetailDtoModel());
        if(CollectionUtils.isEmpty(firstMustGroupFirstSkuAttrList)){
            return null;
        }
        List<DealDetailStructAttrModuleGroupModel> dealDetailStructAttrModuleGroupModels = new ArrayList<>();
        DealDetailStructAttrModuleGroupModel packageContentDealDetailStructAttr = buildDealDetailStructAttrModuleGroupModel(firstMustGroupFirstSkuAttrList, config.getPackageDetailAttrModel(), PACKAGE_DETAIL);
        add2ListIfNotNull(dealDetailStructAttrModuleGroupModels, packageContentDealDetailStructAttr);
        DealDetailStructAttrModuleGroupModel serviceProcessDealDetailStructAttr = buildDealDetailStructAttrModuleGroupModel(firstMustGroupFirstSkuAttrList, config.getServiceProcessAttrModel(), SERVICE_DETAIL);
        add2ListIfNotNull(dealDetailStructAttrModuleGroupModels, serviceProcessDealDetailStructAttr);
        DealDetailStructAttrModuleGroupModel serviceProcessDealPicDetailStructAttr = buildDealDetailPicsAttrModuleGroupModel(firstMustGroupFirstSkuAttrList, config.getServiceProcessPicModel(), SERVICE_DETAIL_PIC, config.getProcessPicSwitch());
        add2ListIfNotNull(dealDetailStructAttrModuleGroupModels, serviceProcessDealPicDetailStructAttr);
        return dealDetailStructAttrModuleGroupModels;
    }



    private void add2ListIfNotNull(List<DealDetailStructAttrModuleGroupModel> list, DealDetailStructAttrModuleGroupModel element){
        if(element != null){
            list.add(element);
        }
    }

    private DealDetailStructAttrModuleGroupModel buildDealDetailPicsAttrModuleGroupModel(List<SkuAttrItemDto> skuAttrItems, List<AttrModel> attrModels, String groupName, boolean processPicSwitch) {
        if(CollectionUtils.isEmpty(attrModels) || !processPicSwitch){
            return null;
        }
        Map<String, String> processNameToUrlMap = attrModels
                .stream()
                .filter(attrModel -> !StringUtils.isEmpty(attrModel.getAttrName()) && !StringUtils.isEmpty(attrModel.getDisplayName()))
                .collect(Collectors.toMap(AttrModel::getAttrName, AttrModel::getDisplayName));

        List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOList = buildServiceProcessPicAttrModules(skuAttrItems, processNameToUrlMap);
        if (CollectionUtils.isEmpty(dealDetailStructAttrModuleVOList)) {
            return null;
        }
        DealDetailStructAttrModuleGroupModel dealDetailStructAttrModuleGroupModel = new DealDetailStructAttrModuleGroupModel();
        dealDetailStructAttrModuleGroupModel.setGroupName(groupName);
        dealDetailStructAttrModuleGroupModel.setDealDetailStructAttrModuleVOS(dealDetailStructAttrModuleVOList);
        return dealDetailStructAttrModuleGroupModel;
    }

    private List<DealDetailStructAttrModuleVO> buildServiceProcessPicAttrModules(List<SkuAttrItemDto> skuAttrItems, Map<String,String> processNameToUrlMap) {
        String serviceProcess = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "serviceProcess");
        if(StringUtils.isEmpty(serviceProcess)){
            return null;
        }
        List<ServiceProcessModel> serviceProcessModels = JsonCodec.decode(serviceProcess, new TypeReference<List<ServiceProcessModel>>() {
        });
        return serviceProcessModels
                .stream()
                .map(serviceProcessModel -> {
                    if(StringUtils.isEmpty(serviceProcessModel.getProcessName()) || StringUtils.isEmpty(serviceProcessModel.getProcessDescription())){
                        return null;
                    }
                    DealDetailStructAttrModuleVO dealDetailStructAttrModuleVO = new DealDetailStructAttrModuleVO();
                    dealDetailStructAttrModuleVO.setAttrName(serviceProcessModel.getProcessName());
                    dealDetailStructAttrModuleVO.setAttrValues(Lists.newArrayList(serviceProcessModel.getProcessName()));
                    if (!processNameToUrlMap.containsKey(serviceProcessModel.getProcessName())) {
                        dealDetailStructAttrModuleVO.setIcon(DEFAULT_SERVICE_PROCESS_ICON);
                        return dealDetailStructAttrModuleVO;
                    }
                    dealDetailStructAttrModuleVO.setIcon(processNameToUrlMap.get(serviceProcessModel.getProcessName()));
                    return dealDetailStructAttrModuleVO;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private DealDetailStructAttrModuleGroupModel buildDealDetailStructAttrModuleGroupModel(List<SkuAttrItemDto> skuAttrItems, List<AttrModel> attrModels, String groupName){
        if(CollectionUtils.isEmpty(attrModels)){
            return null;
        }
        List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOList = attrModels
                .stream()
                .flatMap(attrModel -> {
                    if (StringUtils.isEmpty(attrModel.getAttrName()) || StringUtils.isEmpty(attrModel.getDisplayName())) {
                        return null;
                    }
                    if(StringUtils.equals(SERVICE_PROCESS, attrModel.getAttrName())){
                        return buildServiceProcessAtrrModules(skuAttrItems, attrModel).stream();
                    }
                    return Lists.newArrayList(buildDealDetailStructAttrModuleVO(skuAttrItems, attrModel)).stream();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dealDetailStructAttrModuleVOList)) {
            return null;
        }
        DealDetailStructAttrModuleGroupModel dealDetailStructAttrModuleGroupModel = new DealDetailStructAttrModuleGroupModel();
        dealDetailStructAttrModuleGroupModel.setGroupName(groupName);
        dealDetailStructAttrModuleGroupModel.setDealDetailStructAttrModuleVOS(dealDetailStructAttrModuleVOList);
        return dealDetailStructAttrModuleGroupModel;
    }
    private List<DealDetailStructAttrModuleVO> buildServiceProcessAtrrModules(List<SkuAttrItemDto> skuAttrItems, AttrModel attrModel){
        String serviceProcess = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, attrModel.getAttrName());
        if(StringUtils.isEmpty(serviceProcess)){
            return Lists.newArrayList();
        }
        List<ServiceProcessModel> serviceProcessModels = JsonCodec.decode(serviceProcess, new TypeReference<List<ServiceProcessModel>>() {
        });
        return serviceProcessModels
                .stream()
                .map(serviceProcessModel -> {
                    if(StringUtils.isEmpty(serviceProcessModel.getProcessName()) || StringUtils.isEmpty(serviceProcessModel.getProcessDescription())){
                        return null;
                    }
                    DealDetailStructAttrModuleVO dealDetailStructAttrModuleVO = new DealDetailStructAttrModuleVO();
                    dealDetailStructAttrModuleVO.setAttrName(serviceProcessModel.getProcessName());
                    dealDetailStructAttrModuleVO.setAttrValues(Lists.newArrayList(serviceProcessModel.getProcessDescription()));
                    return dealDetailStructAttrModuleVO;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

    }
    private DealDetailStructAttrModuleVO buildDealDetailStructAttrModuleVO(List<SkuAttrItemDto> skuAttrItems, AttrModel attrModel){
        String skuAttrValue = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, attrModel.getAttrName());
        if(StringUtils.equals(PACKAGE_CONTENT, attrModel.getAttrName())){
            String homePkgName = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "homePkgName");
            skuAttrValue = buildPackageContentDisplayValue(homePkgName, skuAttrValue);
        }
        if(StringUtils.equals(AVAILABLE_TIME_TEXT, attrModel.getAttrName())){
            String startTime = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "start_time");
            String endTime = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "end_time");
            skuAttrValue = buildSuitableTimeDisplayValue(startTime, endTime, skuAttrValue);
        }
        // 处理单独展示的属性值
        if (StringUtils.isNotBlank(skuAttrValue) && StringUtils.isNotBlank(attrModel.getAttrValueDesc())) {
            skuAttrValue = String.format(attrModel.getAttrValueDesc(), skuAttrValue);
        }
        if(StringUtils.isEmpty(skuAttrValue)){
            return null;
        }
        DealDetailStructAttrModuleVO dealDetailStructAttrModule = new DealDetailStructAttrModuleVO();
        dealDetailStructAttrModule.setAttrName(attrModel.getDisplayName());
        dealDetailStructAttrModule.setAttrValues(Lists.newArrayList(skuAttrValue));
        return dealDetailStructAttrModule;
    }

    private String buildSuitableTimeDisplayValue(String startTime, String endTime, String availableTimeText) {
        if(StringUtils.isEmpty(availableTimeText)){
            return null;
        }
        if(StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)){
            return availableTimeText;
        }
        return  StringUtils.isEmpty(parseTime(endTime)) ? "" :startTime + "-" + parseTime(endTime);
    }
    private static String parseTime(String timeString) {
        String[] timeList = timeString.split(":");
        if (timeList.length != 2){
            return null;
        }
        int hours = Integer.parseInt(timeList[0]);
        String minutes = timeList[1];

        if (isNextDay(hours)){
            return getHours(hours) + ":" + minutes + "(次日)";
        }
        return timeString;
    }

    private static boolean isNextDay(Integer localTime) {
        if(localTime == null) {
            return false;
        }
        return localTime >= HOURS_PER_DAY;
    }

    private static String getHours(Integer localTime){
        if (localTime >= HOURS_PER_DAY) {
            localTime = localTime % HOURS_PER_DAY;
        }
        return String.format("%02d", localTime);
    }


    /**
     * @param homePkgName 套餐内容
     * @param packageContent 步骤
     * 解析步骤，拼接套餐内容和步骤
     */
    private String buildPackageContentDisplayValue(String homePkgName,String packageContent){
        if(StringUtils.isEmpty(packageContent)){
            return null;
        }
        List<PackageContentModel> packageContentModels = JsonCodec.decode(packageContent, new TypeReference<List<PackageContentModel>>() {
        });
        if(CollectionUtils.isEmpty(packageContentModels)){
            return null;
        }

        return packageContentModels
                .stream()
                .map(PackageContentModel::getHomePkgDescription)
                .collect(Collectors.joining("\n"));
    }

    @Data
    @VPointCfg
    public static class Config {
        //套餐内容配置
        private List<AttrModel> packageDetailAttrModel;
        //服务流程配置
        private List<AttrModel> serviceProcessAttrModel;
        //服务流程图配置
        private List<AttrModel> serviceProcessPicModel;
        //服务流程图开关
        private boolean processPicSwitch;
        public void setProcessPicSwitch(boolean processPicSwitch) {
            this.processPicSwitch = processPicSwitch;
        }
        public boolean getProcessPicSwitch() {
            return this.processPicSwitch;
        }
    }

    @Data
    static private class AttrModel {
        //展示标题
        private String displayName;
        //服务项目属性
        private String attrName;
        // 属性值描述
        private String attrValueDesc;
    }

    @Data
    static private class PackageContentModel{
        private String homePkgDescription;
    }

    @Data
    static private class ServiceProcessModel{
        private String processName;

        private String processDescription;
    }

}

