package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempromotag;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.RichLabelModel;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemPromoTagVP;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.RichLabelStyleEnum;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@VPointOption(name = "通用货架优惠标签字段",
        description = "通用货架优惠标签字段，在价格行展示",
        code = UnifiedCommonItemPromoTagOpt.CODE,
        isDefault = true)
public class UnifiedCommonItemPromoTagOpt extends UnifiedShelfItemPromoTagVP<UnifiedCommonItemPromoTagOpt.Config> {

    public static final String CODE = "UnifiedCommonItemPromoTagOpt";

    private static final BigDecimal TOP_LIMIT = new BigDecimal("9.9");

    private static final BigDecimal BOTTOM_LIMIT = new BigDecimal("0.1");

    @Override
    public List<RichLabelModel> compute(ActivityCxt context, Param param, Config config) {
        // 根据属性值过滤（不展示）折扣标签
        if (filterByProductAttr(context.getSource(ShelfDouHuFetcher.CODE), param.getProductM(), config)) {
            return Lists.newArrayList();
        }
        // 安心练优惠标签
        RichLabelModel anXinLianTag = buildAnXinLianTag(param, config);
        if (anXinLianTag != null) {
            return Lists.newArrayList(anXinLianTag);
        }
        //团购次卡
        if (param.getProductM().isTimesDeal()) {
            return buildDealTimesCardTag(param);
        }
        //普通团购
        //比价标签
        RichLabelModel priceComparisonTag = buildPriceComparisonTag(param, config);
        if (priceComparisonTag != null) {
            return Lists.newArrayList(priceComparisonTag);
        }
        //折扣标签
        RichLabelModel discountTag = buildDiscountTag(context, param, config);
        if (discountTag != null) {
            return Lists.newArrayList(discountTag);
        }
        return null;
    }

    private boolean filterByProductAttr(List<DouHuM> douHuMList, ProductM productM, Config config) {
        if (MapUtils.isEmpty(config.getFilterByAttr())
                || !enableFilterByProductAttr(douHuMList, config.getExpId2EnableConfigMap())) {
            return false;
        }
        for (String attrKey : config.getFilterByAttr().keySet()) {
            String attrValue = productM.getAttr(attrKey);
            List<String> filterValues = config.getFilterByAttr().getOrDefault(attrKey, Lists.newArrayList());
            if (attrValue != null && filterValues.contains(attrValue)) {
                return true;
            }
        }
        return false;
    }

    private boolean enableFilterByProductAttr(List<DouHuM> douHuMList, Map<String, EnableConfig> expId2EnableConfigMap) {
        if (MapUtils.isEmpty(expId2EnableConfigMap)) {
            return true;
        }
        return expId2EnableConfigMap.entrySet().stream()
                .filter(entry -> entry.getValue() != null && DouHuUtils.hitAnySk(douHuMList, entry.getKey()))
                .findFirst()
                .map(entry -> entry.getValue().isEnableFilterByAttr())
                .orElse(false);
    }

    private List<RichLabelModel> buildDealTimesCardTag(Param param) {
        String timesStr = param.getProductM().getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER);
        int times = NumberUtils.toInt(timesStr, 0);
        if (times <= 0) {
            return null;
        }
        String tagName = String.format("%s次¥%s", times, param.getSalePrice());
        return Lists.newArrayList(buildTag(tagName));
    }

    private RichLabelModel buildPriceComparisonTag(Param param, Config config){
        String priceComparisonTag = getPriceComparisonTag(param, config);
        if(StringUtils.isEmpty(priceComparisonTag)){
            return null;
        }
        RichLabelModel priceComparisonLabel = buildTag(priceComparisonTag);
        //埋点上报用
        param.getProductM().setAttr(PriceDisplayUtils.ITEM_PRICE_POWER_TAG, priceComparisonTag);
        return priceComparisonLabel;
    }

    private String getPriceComparisonTag(Param param, Config config) {
        //买贵必赔
        if (PriceDisplayUtils.hasPriceGuaranteeTag(param.getProductM())) {
            return "买贵必赔";
        }
        //价格力标签，未配置优先级使用通用优先级
        String priceTag = CollectionUtils.isNotEmpty(config.getPriceTagPriority()) ? PriceDisplayUtils.buildPriceTagByPriority(param.getProductM(), config.getPriceTagPriority())
                : PriceDisplayUtils.buildCommonPriceTag(param.getProductM());
        if (StringUtils.isNotEmpty(priceTag)) {
            return priceTag;
        }
        //价保
        String priceProtectionTag = PriceDisplayUtils.getPriceProtectionTag(param.getProductM());
        if (StringUtils.isNotEmpty(priceProtectionTag)) {
            return priceProtectionTag;
        }
        return null;
    }

    private RichLabelModel buildDiscountTag(ActivityCxt context, Param param, Config config) {
        BigDecimal salePrice = getSalePrice(param);
        BigDecimal marketOrBasicPrice = getMarketOrBasicPrice(context, param);
        if (salePrice == null || marketOrBasicPrice == null || Objects.equals(salePrice, BigDecimal.ZERO)
                || Objects.equals(marketOrBasicPrice, BigDecimal.ZERO)) {
            return null;
        }
        BigDecimal discountVal = getDiscountVal(salePrice, marketOrBasicPrice);
        if (discountVal.compareTo(getTopLimit(config)) > 0) {
            return null;
        }
        String discountStr = String.format("%s折", discountVal);
        RichLabelModel promoTag = buildTag(discountStr);
        appendAttrStr(promoTag, param.getProductM(), config);
        //埋点上报用
        param.getProductM().setAttr(PriceDisplayUtils.ITEM_DISCOUNT_TAG, String.join("|", promoTag.getMultiText()));
        return promoTag;
    }

    private RichLabelModel buildTag(String text) {
        RichLabelModel richLabelModel = new RichLabelModel();
        richLabelModel.setBackgroundColor(ColorUtils.colorFFF1EC);
        richLabelModel.setTextColor(ColorUtils.colorFF4B10);
        richLabelModel.setText(text);
        richLabelModel.setMultiText(Lists.newArrayList(text));
        return richLabelModel;
    }

    private BigDecimal getTopLimit(Config config) {
        if (config == null || BigDecimal.valueOf(config.getMaxDiscount()).compareTo(BigDecimal.ZERO) <= 0) {
            return TOP_LIMIT;
        }
        return BigDecimal.valueOf(config.getMaxDiscount());
    }

    private BigDecimal getSalePrice(Param param) {
        if (StringUtils.isEmpty(param.getSalePrice())) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(param.getSalePrice());
    }

    private BigDecimal getMarketOrBasicPrice(ActivityCxt context, Param param) {
        if (StringUtils.isEmpty(param.getProductM().getMarketPrice())) {
            return BigDecimal.ZERO;
        }
        // 国补市场价
        if (PriceUtils.isSupportDp4NationalSubsidy(ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.platform))
                && PriceUtils.hasNationalSubsidy(param.getProductM())) {
            return new BigDecimal(PriceUtils.getNationSubsidyMarketPriceTag(param.getProductM()));
        }
        return new BigDecimal(param.getProductM().getMarketPrice());
    }

    private BigDecimal getDiscountVal(BigDecimal salePrice, BigDecimal marketOrBasicPrice) {
        //向上取整
        BigDecimal discountVal = salePrice.divide(marketOrBasicPrice, 3, RoundingMode.DOWN)
                .multiply(BigDecimal.valueOf(10))
                .setScale(1, RoundingMode.UP);
        return discountVal.compareTo(BOTTOM_LIMIT) > 0 ? discountVal : BOTTOM_LIMIT;
    }

    private void appendAttrStr(RichLabelModel promoTag, ProductM productM, Config config) {
        if (CollectionUtils.isEmpty(config.getAppendAttrKeys())) {
            return;
        }
        for (String appendAttrKey : config.getAppendAttrKeys()) {
            String attrValue = productM.getAttr(appendAttrKey);
            if (StringUtils.isNotBlank(attrValue)) {
                promoTag.getMultiText().add(attrValue);
                return;
            }
        }
    }

    /**
     * 构建安心练优惠标签
     * @param param 参数
     * @param config 配置
     * @return 安心练优惠标签
     */
    private RichLabelModel buildAnXinLianTag(Param param, Config config) {
        ProductM productM = param.getProductM();

        // 检查是否有安心练标签
        String json = productM.getAttr("attr_guaranteeTagCodes");
        if (StringUtils.isBlank(json)) {
            return null;
        }
        List<Integer> tagCodes = JsonCodec.converseList(json, Integer.class);
        if (CollectionUtils.isEmpty(tagCodes)) {
            return null;
        }

        // 这里需要从配置中获取安心练标签ID，暂时硬编码一个示例
        List<Integer> anXinTagCodes = Lists.newArrayList(1, 2, 3); // 示例配置
        boolean hasAnXinTag = tagCodes.stream().anyMatch(anXinTagCodes::contains);
        if (!hasAnXinTag) {
            return null;
        }

        // 获取连续包月总期数
        String zongQiShu = productM.getAttr("attr_zongqishu");
        if (StringUtils.isBlank(zongQiShu)) {
            return null;
        }

        // 获取到手价
        String salePrice = param.getSalePrice();
        if (StringUtils.isBlank(salePrice)) {
            return null;
        }

        // 构建"Y期¥Z"格式的标签
        RichLabelModel richLabelModel = new RichLabelModel();
        richLabelModel.setText(zongQiShu + "期¥" + salePrice);
        richLabelModel.setTextColor("#FF6600"); // 橙色
        richLabelModel.setStyle(RichLabelStyleEnum.ROUND_CORNER.getType());

        return richLabelModel;
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 最大折扣
         */
        private double maxDiscount;

        /**
         * 追加的文案AttrKeys，可按优先级
         */
        private List<String> appendAttrKeys;

        /**
         * 指定价格力标签优先级
         */
        private List<Integer> priceTagPriority;

        /**
         * 有特定属性的商品，不会展示折扣标签
         */
        private Map<String, List<String>> filterByAttr = new HashMap<>();

        /**
         * 通过实验控制展示逻辑
         */
        private Map<String, EnableConfig> expId2EnableConfigMap;
    }

    @Data
    public static class EnableConfig {
        private boolean enableFilterByAttr = false;
    }

}
