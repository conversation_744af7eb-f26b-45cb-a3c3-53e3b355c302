package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;

public abstract class AbstractValueProcessor implements ValueProcessor {

    @Override
    public List<String> process(ValueConfig valueKey, Map<String, String> name2ValueMap,Object data) {
        if (validate(valueKey, name2ValueMap)) {
            return null;
        }
        String key = valueKey.getKey();
        String value = null;
        if (key.contains(".")){
            value = jsonProcess(key.split("\\."), name2ValueMap);
        } else {
            value = name2ValueMap.get(valueKey.getKey());
        }
        if (ObjectUtils.isEmpty(value) && !key.contains("|")) {
            return null;
        }
        List<String> values = null;
        if (key.contains("|")) {
            String[] split = key.split("\\|");
            values = new ArrayList<>();
            for (String s : split) {
                if (name2ValueMap.containsKey(s)) {
                    values.add(name2ValueMap.get(s));
                }
            }
        } else {
            values = valueKey.getIsList() ? convertListByStr(value) : Lists.newArrayList(value);
        }
        List<String> finalValues = new ArrayList<>();
        values.stream().filter(v -> !ObjectUtils.isEmpty(v))
                .forEach(v -> finalValues.addAll(convertDisplayValues(v, valueKey, name2ValueMap)));
        return finalValues;
    }

    public static String jsonProcess(String[] jsonKeys, Map<String, String> name2ValueMap) {
        if (jsonKeys.length < 2) {
            return null;
        }
        JSONObject obj = null;
        if (jsonKeys[0].contains("[0]")) {

            JSONArray arr = JSON.parseArray(name2ValueMap.get(jsonKeys[0].replace("[0]", "")));
            if (arr != null && arr.size() > 0) {
                obj = arr.getJSONObject(0);
            }
        } else if(isJsonArray(name2ValueMap.get(jsonKeys[0]))) {
           return processJsonArray(removeFirstElement(jsonKeys), name2ValueMap.get(jsonKeys[0]));
        } else {
            obj = JSON.parseObject(name2ValueMap.get(jsonKeys[0]));
        }
        if (obj == null || obj.isEmpty()) {
            return null;
        }
        for (int i = 1; i < jsonKeys.length - 1; i++) {
            String key = jsonKeys[i];
            obj = obj.getJSONObject(key);
            if (obj == null || obj.isEmpty()) {
                return null;
            }
        }
        return obj.getString(jsonKeys[jsonKeys.length - 1]);
    }

    public static String processJSONObject(String[] jsonKeys, String jsonStr){
        if (Objects.isNull(jsonKeys) || StringUtils.isEmpty(jsonStr)){
            return null;
        }
        if (NumberUtils.isCreatable(jsonStr)){
            return jsonStr;
        }
        JSONObject jsonObject = JSON.parseObject(jsonStr);
        String valueStr = JSON.toJSONString(jsonObject.get(jsonKeys[0]));
        if (jsonKeys.length == 1){
           return valueStr;
        }
        return parseValueStr(removeFirstElement(jsonKeys), valueStr);
    }
    public static String processJsonArray(String[] jsonKeys, String jsonStr){
        if (Objects.isNull(jsonKeys) || StringUtils.isEmpty(jsonStr)){
            return null;
        }
        JSONArray array = JSON.parseArray(jsonStr);
        // 对value（数值类型）做聚合
        BigDecimal value = new BigDecimal(0);
        for (int i = 0; i < array.size(); i++) {
            JSONObject arrayEle1 = (JSONObject) array.get(i);
            String key = jsonKeys[0];
            String valueStr = JSON.toJSONString(arrayEle1.get(key));
            if (jsonKeys.length == 1 && NumberUtils.isCreatable(valueStr)){
                value = value.add(new BigDecimal(valueStr));
            }
            valueStr = parseValueStr(removeFirstElement(jsonKeys), valueStr);
            value = NumberUtils.isCreatable(valueStr) ? value.add(new BigDecimal(valueStr)) : value;
        }
        return value.stripTrailingZeros().toPlainString();
    }

    public static String parseValueStr(String[] jsonKeys, String valueStr){
        if (isJsonArray(valueStr)){
            return processJsonArray(jsonKeys, valueStr);
        }else if (isValidJson(valueStr)){
            return processJSONObject(jsonKeys, valueStr);
        }
        return null;
    }
    public static String[] removeFirstElement(String[] original){
        if (Objects.isNull(original) || original.length <= 1){
            return null;
        }
        return Arrays.copyOfRange(original, 1, original.length);
    }

    public static boolean isJsonArray(String jsonString) {
        return StringUtils.isBlank(jsonString) ? false : JSONValidator.from(jsonString).validate() && jsonString.startsWith("[") && jsonString.endsWith("]");
    }

    public static boolean isValidJson(String jsonString) {
        return StringUtils.isBlank(jsonString) ? false : JSONValidator.from(jsonString).validate() && jsonString.startsWith("{") && jsonString.endsWith("}");
    }

    public boolean validate(ValueConfig valueKey, Map<String, String> name2ValueMap) {
        return ObjectUtils.isEmpty(valueKey) || ObjectUtils.isEmpty(name2ValueMap);
    }

    public List<String> convertListByStr(String value) {
        List<String> list = JsonCodec.decode(value, new TypeReference<List<String>>() {
        });
        if (ObjectUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list;
    }
}
