package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.cat.Cat;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.CommonAttrVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/12/12 5:29 下午
 */
@VPointOption(name = "配镜sku属性列表变化点", description = "配镜sku属性列表变化点，根据配置展示sku属性", code = GlassesSkuAttrListOpt.CODE)
public class GlassesSkuAttrListOpt extends SkuAttrListVP<GlassesSkuAttrListOpt.Config> {

    public static final String CODE = "GlassesSkuAttrListOpt";

    private static final Long OPTICAL_FRAME_MIRROR_JINGPIAN_CATEGORY_ID = 2104918L;
    private static final Long OPTICAL_FRAME_MIRROR_JINGKUANG_CATEGORY_ID = 2104919L;
    private static final Long OPTICAL_FRAME_MIRROR_PEIJIAN_CATEGORY_ID = 2104920L;
    private static final Long OPTICAL_FRAME_MIRROR_ZENGZHIFUWU_CATEGORY_ID = 2104921L;


    private static final Long DEFOCUS_MIRROR_JINGPIAN_CATEGORY_ID = 2104922L;
    private static final Long DEFOCUS_MIRROR_JINGKUANG_CATEGORY_ID = 2104923L;
    private static final Long DEFOCUS_MIRROR_PEIJIAN_CATEGORY_ID = 2104924L;
    private static final Long DEFOCUS_MIRROR_ZENGZHIFUWU_CATEGORY_ID = 2104925L;

    private static final List<String> SERVER_TYPE_LIST = Lists.newArrayList("光学框架镜", "离焦镜");


    @Override
    public List<DealSkuItemVO> compute(ActivityCxt context, Param param, Config config) {
        if (param == null) {
            return null;
        }
        return buildDealSkuItems(config, param.getSkuItemDto(), param.getDealAttrs());
    }

    private List<DealSkuItemVO> buildDealSkuItems(Config config, SkuItemDto skuItemDto, List<AttrM> dealAttrs) {
        if (Objects.isNull(skuItemDto)) {
            return null;
        }
        String serviceType = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "service_type");
        if (StringUtils.isNotEmpty(serviceType) && SERVER_TYPE_LIST.contains(serviceType)) {
            return buildSkuItem(skuItemDto, dealAttrs);
        }
        return Lists.newArrayList();
    }

    private List<DealSkuItemVO> buildSkuItem(SkuItemDto skuItemDto, List<AttrM> dealAttrs) {
        long productCategory = skuItemDto.getProductCategory();
        int copies = skuItemDto.getCopies();
        String marketPrice = skuItemDto.getMarketPrice().stripTrailingZeros().toPlainString();
        if (productCategory == OPTICAL_FRAME_MIRROR_JINGPIAN_CATEGORY_ID || productCategory == DEFOCUS_MIRROR_JINGPIAN_CATEGORY_ID) {
            //可用范围
            String useScope = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, "useScope");
            //可用价格段
            String availablePriceRange = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, "availablePriceRange");
            //品牌
            String brandname = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, "brandname");
            //折射率
            String refractivity = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, "refractivity");
            //镜面设计
            String mirrorDesign = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, "mirrorDesign");
            //是否进口
            String isimportStr = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, "isimportStr");
            //镜片功能
            String function = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, "function");
            //适用近视度数
            String applyMyopiaDegreeLeft = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, "applyMyopiaDegreeLeft");
            String applyMyopiaDegreeRight = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, "applyMyopiaDegreeRight");
            //适用散光度数
            String applyAstigmiaDegreeLeft = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, "applyAstigmiaDegreeLeft");
            String applyAstigmiaDegreeRight = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, "applyAstigmiaDegreeRight");
            //适用远视度数
            String applicableFarsightedPrescriptionLeft = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, "ApplicableFarsightedPrescriptionLeft");
            String rightAppliedDegreeOfFarsightedness = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, "RightAppliedDegreeOfFarsightedness");

            DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
            dealSkuItemVO.setName("镜片");
            dealSkuItemVO.setType(5);
            SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
            skuAttrAttrItemVO.setInfo(Lists.newArrayList(copies + "份", "￥" + marketPrice));
            if (StringUtils.isNotEmpty(useScope) && "指定镜片".equals(useScope)) {
                String importStr = org.apache.commons.lang.StringUtils.isEmpty(isimportStr) ? "" : "（" + ("是".equals(isimportStr) ? "进口" : "国产") + "）";
                skuAttrAttrItemVO.setName(brandname + refractivity + mirrorDesign + importStr);
            } else {
                skuAttrAttrItemVO.setName(availablePriceRange + "元内镜片任选");
            }
            List<CommonAttrVO> values = new ArrayList<>();
            if (StringUtils.isNotEmpty(function)) {
                CommonAttrVO functionCommonAttrVO = new CommonAttrVO();
                functionCommonAttrVO.setName("镜片功能");
                functionCommonAttrVO.setValue(function);
                values.add(functionCommonAttrVO);
            }
            if ((StringUtils.isNotEmpty(applyMyopiaDegreeLeft) && StringUtils.isNotEmpty(applyMyopiaDegreeRight)) || (StringUtils.isNotEmpty(applyAstigmiaDegreeLeft) && StringUtils.isNotEmpty(applyAstigmiaDegreeRight))) {
                CommonAttrVO commonAttrVO = new CommonAttrVO();
                commonAttrVO.setName("适用度数");
                String value = "";
                if (StringUtils.isNotEmpty(applyMyopiaDegreeLeft) && StringUtils.isNotEmpty(applyMyopiaDegreeRight)) {
                    value += "近视" + applyMyopiaDegreeLeft + "-" + applyMyopiaDegreeRight + "度";
                }
                if (StringUtils.isNotEmpty(applyAstigmiaDegreeLeft) && StringUtils.isNotEmpty(applyAstigmiaDegreeRight)) {
                    value += "，散光" + applyAstigmiaDegreeLeft + "-" + applyAstigmiaDegreeRight + "度";
                }
                if (StringUtils.isNotEmpty(applicableFarsightedPrescriptionLeft) && StringUtils.isNotEmpty(rightAppliedDegreeOfFarsightedness)) {
                    value += "，远视" + applicableFarsightedPrescriptionLeft + "-" + rightAppliedDegreeOfFarsightedness + "度";
                }
                if (value.startsWith("，")) {
                    value = value.substring(1);
                }
                commonAttrVO.setValue(value);
                values.add(commonAttrVO);
            }
            skuAttrAttrItemVO.setValues(values);
            dealSkuItemVO.setValueAttrs(Lists.newArrayList(skuAttrAttrItemVO));
            return Lists.newArrayList(dealSkuItemVO);
        }
        //镜框
        if (productCategory == DEFOCUS_MIRROR_JINGKUANG_CATEGORY_ID || productCategory == OPTICAL_FRAME_MIRROR_JINGKUANG_CATEGORY_ID) {
            //可用范围
            String useScope = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, "useScope");
            //可用价格段
            String availablePriceRange = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, "availablePriceRange");
            //品牌
            String brandname = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, "brandname");
            //材质
            String refractivity = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, "materialTexture");
            DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
            dealSkuItemVO.setName("镜框");
            dealSkuItemVO.setType(5);
            SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
            skuAttrAttrItemVO.setInfo(Lists.newArrayList(copies + "份", "￥" + marketPrice));
            if (StringUtils.isNotEmpty(useScope) && "指定镜框".equals(useScope)) {
                skuAttrAttrItemVO.setName(brandname + refractivity + "镜框");
            } else {
                skuAttrAttrItemVO.setName(availablePriceRange + "元内镜框任选");
            }
            dealSkuItemVO.setValueAttrs(Lists.newArrayList(skuAttrAttrItemVO));
            return Lists.newArrayList(dealSkuItemVO);
        }
        //配件
        if (productCategory == DEFOCUS_MIRROR_PEIJIAN_CATEGORY_ID || productCategory == OPTICAL_FRAME_MIRROR_PEIJIAN_CATEGORY_ID) {
            DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
            dealSkuItemVO.setName("配件");
            dealSkuItemVO.setType(5);
            String type = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, "type");
            SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
            skuAttrAttrItemVO.setInfo(Lists.newArrayList(copies + "份", "￥" + marketPrice));
            skuAttrAttrItemVO.setName(type);
            dealSkuItemVO.setValueAttrs(Lists.newArrayList(skuAttrAttrItemVO));
            return Lists.newArrayList(dealSkuItemVO);
        }
        //增值服务
        if (productCategory == DEFOCUS_MIRROR_ZENGZHIFUWU_CATEGORY_ID || productCategory == OPTICAL_FRAME_MIRROR_ZENGZHIFUWU_CATEGORY_ID) {
            DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
            dealSkuItemVO.setName("增值服务");
            dealSkuItemVO.setType(5);
            String type = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, "type");
            SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
            skuAttrAttrItemVO.setInfo(Lists.newArrayList(copies + "份", "￥" + marketPrice));
            skuAttrAttrItemVO.setName(type);
            dealSkuItemVO.setValueAttrs(Lists.newArrayList(skuAttrAttrItemVO));
            return Lists.newArrayList(dealSkuItemVO);
        }
        return Lists.newArrayList();
    }

    @Data
    @VPointCfg
    public static class Config {
        //sku属性构造模型列表
        private List<SkuAttrDisplayRuleCfg> skuAttrDisplayRules;
    }

    @Data
    private static class SkuAttrDisplayRuleCfg {
        //路由条件
        private RouteConditionCfg routeCondition;
        //sku属性规则配置
        private List<DisplayAttrRuleCfg> displayAttrRules;
    }

    @Data
    private static class DisplayAttrRuleCfg {
        //sku属性展示标题
        private String attrTitle;
        //属性key
        private String attrKey;
        //行业属性key
        private String dealAttrKey;
        //属性value与别名映射
        private Map<String, String> attrValueAliasMap;
        //类型 0-文案 1-流程类型(按照processDisplayRule展示)
        private int type = 0;
        //
        private ProjectDisplayRule projectDisplayRule;

        private Map<String, String> config;

        private String suffix;

        private String nextAttrKey;

        private String middleSymbol;

        private Long categoryId;
    }

    @Data
    private static class RouteConditionCfg {
        //满足所有属性key及属性值的映射，当该字段为空时不需要校验
        private Map<String, List<String>> satisfyAllAttrKeyValuesMap;
        //满足服务项目Sku所有属性key及属性值的映射，当该字段为空时不需要校验
        private Map<String, List<String>> satisfySkuAllAttrKeyValuesMap;
    }

    @Data
    private static class ProjectDisplayRule {
        private String titleTemplate = "共%count%项";
        //info数据的skuKey 支持多个
        private List<String> attrAttrInfoKey;
        private String attrAttrNameKey;
        //AttrAttr的commonValue的key
        private List<CommonKey> attrAttrCommonKey;
    }

    @Data
    private static class CommonKey {
        private String name;
        private String key;
    }

    @Data
    private static class ProjectExtConfig {
        private List<String> checkItemSortList;

        private Map<String, String> meanMap;
    }
}
