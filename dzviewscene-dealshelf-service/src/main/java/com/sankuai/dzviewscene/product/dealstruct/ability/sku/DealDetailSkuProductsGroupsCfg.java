package com.sankuai.dzviewscene.product.dealstruct.ability.sku;

import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * created by zhangzhiyuan04 in 2021/12/8
 */
@Deprecated
@Data
@AbilityCfg
public class DealDetailSkuProductsGroupsCfg {

    /**
     * 是否启用适配非结构化团单的构造
     * 取属性：{@link DealDetailFetcher#DETAIL_INFO}
     * {@example {"content":[{"data":{"schema":["名称","数量","价值"],"totalPrice":272.00,"salePrice":22.00,"groups":[{"isRequired":true,"repeatable":false,"values":[["200","1份","50.0元"],["222","1份","222.0元"]],"name":"","fullName":"","title":"","optionalCount":0}],"totalPriceDesc":"总价值"},"type":"table"}],"headline":"团购详情","version":1}}
     */
    private boolean enableAdaptNoStructDeal;

    private Map<String, SkuAttrGroupByItem> skuAttrGroups;

    @Data
    public static class SkuAttrGroupByItem {
        private List<SkuAttrGroup> groupBy;
    }

    @Data
    public static class SkuAttrGroup {
        private String itemName;
        private List<SkuAttrDTO> includeAttrName;
    }

    @Data
    public static class SkuAttrDTO {
        private String cnName;
        private String attrName;
        private String separator;
        private String desc;
    }
}
