package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.defaultShowNum;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListDefaultShowNumVP;
import lombok.Data;

/**
 * @author: wuwen<PERSON><PERSON>
 * @create: 2024-06-18
 * @description: 默认展示数量
 */
@VPointOption(name = "默认展示数量", description = "默认展示数量，可选择读取Assembler数据或Opt配置数据", code = DefaultShowNumOpt.CODE)
public class DefaultShowNumOpt extends DealFilterListDefaultShowNumVP<DefaultShowNumOpt.Config> {

    public static final String CODE = "DefaultShowNumOpt";

    @Override
    public Integer compute(ActivityCxt activityCxt, Param param, Config config) {
        if (!config.isUseConfigDefaultShowNum()) {
            return param.getDefaultShowNum();
        }
        return config.getDefaultShowNum();
    }

    @Data
    @VPointCfg
    public static class Config {
        private boolean useConfigDefaultShowNum = false;
        private int defaultShowNum;
    }
}
