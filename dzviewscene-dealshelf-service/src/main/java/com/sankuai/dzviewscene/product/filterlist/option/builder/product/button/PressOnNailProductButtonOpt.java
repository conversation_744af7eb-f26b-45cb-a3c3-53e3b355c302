package com.sankuai.dzviewscene.product.filterlist.option.builder.product.button;

import com.dianping.vc.enums.VCClientTypeEnum;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductButtonVP;
import com.sankuai.dzviewscene.product.shelf.utils.PressOnNailUtils;
import com.sankuai.dzviewscene.productshelf.vu.enums.ButtonTypeEnums;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/4/9 21:53
 */
@VPointOption(name = "穿戴甲",
        description = "穿戴甲",
        code = "PressOnNailProductButtonOpt")
public class PressOnNailProductButtonOpt extends ProductButtonVP<PressOnNailProductButtonOpt.Config> {

    private final static String MT_URL_FORMAT = "imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=gcsubmitordermrnmodules-unify&mrn_component=addtocartpage-popup&mrn_min_version=0.0.697&isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true&exitAnim=mrn_anim_exit_from_top&&overridePendingTransition=1&enterAnim=mrn_anim_enter_from_bottom&pagesource=poiShelf&autoadd=true&dealid=%s&shopid=%s&skuid=%s";

    private final static String DP_URL_FORMAT = "idianping://mrn?mrn_biz=gc&mrn_entry=gcsubmitordermrnmodules-unify&mrn_component=addtocartpage-popup&mrn_min_version=0.0.26&isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true&pagesource=poiShelf&dealid=%s&shopid=%s&skuid=%s";

    private final static String IMAGE_URL = "https://p1.meituan.net/travelcube/9ded5074e473b49a4f4acc80ac3afb44731.png";

    @Override
    public DzSimpleButtonVO compute(ActivityCxt context, Param param, Config config) {
        int ua = (int) Optional.ofNullable(context.getParam(ShelfActivityConstants.Params.userAgent)).orElse(0);
        if (CollectionUtils.isNotEmpty(param.getProductM().getSkuIdList()) && VCClientTypeEnum.MT_APP.getCode() == ua && PressOnNailUtils.checkExclusive(context)) {
            ProductM productM = param.getProductM();
            int dealId = productM.getProductId();
            String skuId = productM.getSkuIdList().get(0);
            long shopId;
            boolean isMt = VCClientTypeEnum.MT_APP.getCode() == ua;
            String url;
            if (isMt) {
                shopId = (long) Optional.ofNullable(context.getParam(ShelfActivityConstants.Params.mtPoiIdL)).orElse(0L);
                url = String.format(MT_URL_FORMAT, dealId, shopId, skuId);
            } else {
                shopId = (long) Optional.ofNullable(context.getParam(ShelfActivityConstants.Params.dpPoiIdL)).orElse(0L);
                url = String.format(DP_URL_FORMAT, dealId, shopId, skuId);
            }
            DzSimpleButtonVO button = new DzSimpleButtonVO();
            button.setType(ButtonTypeEnums.CART_BUTTON.getType());
            button.setAvailable(true);
            button.setShow(true);
            button.setJumpUrl(url);
            button.setImgUrl(IMAGE_URL);
            return button;
        }
        DzSimpleButtonVO button = new DzSimpleButtonVO();
        button.setJumpUrl(param.getProductM().getJumpUrl());
        button.setName("抢购");
        button.setType(ButtonTypeEnums.BUTTON.getType());
        button.setShow(true);
        button.setAvailable(true);
        return button;
    }

    @VPointCfg
    @Data
    public static class Config {
    }
}
