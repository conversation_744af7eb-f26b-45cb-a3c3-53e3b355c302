package com.sankuai.dzviewscene.product.dealstruct.options.skuList.edu;


import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@VPointOption(name = "教育规划类团单的赠品福利变化点", description = "教育规划类团单的赠品福利变化点", code = EduProgrammeDealGiftSkuListOpt.CODE)
public class EduProgrammeDealGiftSkuListOpt extends SkuListModuleVP<EduProgrammeDealGiftSkuListOpt.Config> {

    public static final String CODE = "eduProgrammeDealGiftSkuListOpt";

    public static final String ATTR_PROVIDE_SERVICE_TYPE = "provide_service_type";
    public static final String SKU_TITLE_FREE_GIFT = "免费资料";
    public static final String ATTR_MATERIAL_LIST = "material_list";
    public static final String ATTR_APPEND_FEN = "份";
    public static final String SKU_ITEM_NAME_FREE_GIFT_CONTENT = "资料内容";
    public static final int SKU_ITEM_TYPE_NORMAL_LIST = 1;
    public static final int SKU_ITEM_TYPE_ATTR_LIST = 2;
    public static final String ATTR_MATERIAL_TYPE = "material_type";
    public static final String SKU_ITEM_NAME_FREE_GIFT_TYPE = "资料类型";
    public static final String KEY_PROGRAMME = "报考规划";
    public static final String KEY_SELF_TEST = "水平测试";
    public static final String KEY_FREE_COURSE = "免费课程";
    public static final String ATTR_PLAN_CONTENT = "plan_content";
    public static final String SKU_ITEM_NAME_PROGRAMME_CONTENT = "规划内容";
    public static final String SKU_ITEM_NAME_PROGRAMME_TYPE = "规划方式";
    public static final String ATTR_EXAM_PLAN_METHOD = "exam_plan_method";
    public static final String ATTR_LEVEL_TEST_CONTENT = "level_test_content";
    public static final String ATTR_LEVEL_TEST_METHOD = "level_test_method";
    public static final String SKU_ITEM_NAME_SELF_TEST_CONTENT = "测试内容";
    public static final String SKU_ITEM_NAME_SELF_TEST_TYPE = "测试方式";
    public static final String SKU_TITLE_PROGRAMME = "报考规划";
    public static final String SKU_TITLE_SELF_TEST = "水平测试";
    public static final String SKU_TITLE_FREE_COURSE = "免费课程";
    public static final String SKU_ITEM_NAME_FREE_COURSE_TYPE = "授课方式";
    public static final String ATTR_COURSE_METHOD = "course_method";
    public static final String ATTR_COURSE_PLAN = "course_plan";
    public static final String SKU_ITEM_NAME_FREE_COURSE_CONTENT = "免费课程";
    public static final String COURSE_NUM_APPEND = "课时";

    @Override
    public List<DealDetailSkuListModuleGroupModel> compute(ActivityCxt activityCxt, Param param, Config config) {
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        if (dealDetailInfoModel == null) {
            return null;
        }
        List<DealSkuVO> skuVOList = getGiftSkuList(activityCxt, dealDetailInfoModel, config);
        if (CollectionUtils.isEmpty(skuVOList)) {
            return null;
        }
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(skuVOList);
        DealDetailSkuListModuleGroupModel dealDetailSkuListModuleGroupModel = new DealDetailSkuListModuleGroupModel();
        dealDetailSkuListModuleGroupModel.setDealSkuGroupModuleVOS(Lists.newArrayList(dealSkuGroupModuleVO));
        return Lists.newArrayList(dealDetailSkuListModuleGroupModel);
    }

    private List<DealSkuVO> getGiftSkuList(ActivityCxt activityCxt, DealDetailInfoModel dealDetailInfoModel, Config config) {
        String provideServiceJson = DealDetailUtils.getAttrSingleValueByAttrName(dealDetailInfoModel.getDealAttrs(), ATTR_PROVIDE_SERVICE_TYPE);
        if (StringUtils.isEmpty(provideServiceJson)) {
            return null;
        }
        List<DealSkuVO> result = Lists.newArrayList();
        Map<String, DealSkuVO> type2SkuVO = readProvideServiceSkuMap(activityCxt, dealDetailInfoModel, config);
        List<String> provideServices = JsonCodec.decode(provideServiceJson, new TypeReference<List<String>>() {});
        if (CollectionUtils.isEmpty(provideServices)) {
            return null;
        }
        for (String serviceTypeStr : provideServices) {
            DealSkuVO skuVO = type2SkuVO.get(serviceTypeStr);
            if (skuVO == null) {
                continue;
            }
            result.add(skuVO);
        }
        return result;
    }

    private Map<String, DealSkuVO> readProvideServiceSkuMap(ActivityCxt activityCxt, DealDetailInfoModel dealDetailInfoModel, Config config) {
        Map<String, DealSkuVO> result = Maps.newHashMap();
        // 免费资料
        result.put(SKU_TITLE_FREE_GIFT, buildFreeGiftSkuVO(dealDetailInfoModel));
        // 报考规划
        result.put(KEY_PROGRAMME, buildProgrammeSkuVO(dealDetailInfoModel));
        // 水平测试
        result.put(KEY_SELF_TEST, buildSelfTestSkuVO(dealDetailInfoModel));
        // 免费课程
        result.put(KEY_FREE_COURSE, buildFreeCourseSkuVO(dealDetailInfoModel));
        return result;
    }

    private DealSkuVO buildFreeCourseSkuVO(DealDetailInfoModel dealDetailInfoModel) {
        List<DealSkuItemVO> skuItemList = Lists.newArrayList();
        // 课程大纲
        skuItemList.add(buildFreeCourseContentSkuItem(dealDetailInfoModel));
        // 授课方式
        skuItemList.add(buildSingleContentSkuItem(dealDetailInfoModel, SKU_ITEM_NAME_FREE_COURSE_TYPE, ATTR_COURSE_METHOD));
        skuItemList = skuItemList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuItemList)) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(SKU_TITLE_FREE_COURSE);
        dealSkuVO.setItems(skuItemList);
        return dealSkuVO;
    }

    private DealSkuItemVO buildFreeCourseContentSkuItem(DealDetailInfoModel dealDetailInfoModel) {
        List<CoursePlan> coursePlanList = getCoursePlanList(dealDetailInfoModel.getDealAttrs());
        if (CollectionUtils.isEmpty(coursePlanList)) {
            return null;
        }
        List<SkuAttrAttrItemVO> valueAttrs = getFreeCourseContentSkuAttrs(coursePlanList);
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(SKU_ITEM_NAME_FREE_COURSE_CONTENT);
        dealSkuItemVO.setType(SKU_ITEM_TYPE_ATTR_LIST);
        dealSkuItemVO.setValueAttrs(valueAttrs);
        return dealSkuItemVO;
    }

    private List<SkuAttrAttrItemVO> getFreeCourseContentSkuAttrs(List<CoursePlan> coursePlanList) {
        List<SkuAttrAttrItemVO> valueAttrs = coursePlanList.stream()
                .filter(plan -> plan != null && StringUtils.isNotBlank(plan.getCourseModule()))
                .map(plan -> {
                    SkuAttrAttrItemVO attrItemVO = new SkuAttrAttrItemVO();
                    if (plan.getCourseTimeNum() != null) {
                        attrItemVO.setInfo(Lists.newArrayList(
                                plan.getCourseTimeNum().setScale(1, BigDecimal.ROUND_DOWN)
                                        .stripTrailingZeros().toPlainString() + COURSE_NUM_APPEND));
                    }
                    attrItemVO.setName(plan.getCourseModule());
                    return attrItemVO;
                }).collect(Collectors.toList());
        return valueAttrs;
    }

    private List<CoursePlan> getCoursePlanList(List<AttrM> dealAttrs) {
        String json = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, ATTR_COURSE_PLAN);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonCodec.decode(json, new TypeReference<List<CoursePlan>>() {});
    }

    private DealSkuVO buildSelfTestSkuVO(DealDetailInfoModel dealDetailInfoModel) {
        List<DealSkuItemVO> skuItemList = Lists.newArrayList();
        // 测试内容
        skuItemList.add(buildSingleContentSkuItem(dealDetailInfoModel, SKU_ITEM_NAME_SELF_TEST_CONTENT, ATTR_LEVEL_TEST_CONTENT));
        // 测试方式
        skuItemList.add(buildSingleContentSkuItem(dealDetailInfoModel, SKU_ITEM_NAME_SELF_TEST_TYPE, ATTR_LEVEL_TEST_METHOD));
        skuItemList = skuItemList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuItemList)) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(SKU_TITLE_SELF_TEST);
        dealSkuVO.setItems(skuItemList);
        return dealSkuVO;
    }

    private DealSkuVO buildProgrammeSkuVO(DealDetailInfoModel dealDetailInfoModel) {
        List<DealSkuItemVO> skuItemList = Lists.newArrayList();
        // 规划内容
        skuItemList.add(buildSingleContentSkuItem(dealDetailInfoModel, SKU_ITEM_NAME_PROGRAMME_CONTENT, ATTR_PLAN_CONTENT));
        // 规划方式
        skuItemList.add(buildSingleContentSkuItem(dealDetailInfoModel, SKU_ITEM_NAME_PROGRAMME_TYPE, ATTR_EXAM_PLAN_METHOD));
        skuItemList = skuItemList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuItemList)) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(SKU_TITLE_PROGRAMME);
        dealSkuVO.setItems(skuItemList);
        return dealSkuVO;
    }

    private DealSkuVO buildFreeGiftSkuVO(DealDetailInfoModel dealDetailInfoModel) {
        List<DealSkuItemVO> skuItemList = Lists.newArrayList();
        // 资料内容
        skuItemList.add(buildFreeGiftContentSkuItem(dealDetailInfoModel));
        // 资料类型
        skuItemList.add(buildSingleContentSkuItem(dealDetailInfoModel, SKU_ITEM_NAME_FREE_GIFT_TYPE, ATTR_MATERIAL_TYPE));
        skuItemList = skuItemList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuItemList)) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(SKU_TITLE_FREE_GIFT);
        dealSkuVO.setItems(skuItemList);
        return dealSkuVO;
    }

    private DealSkuItemVO buildSingleContentSkuItem(DealDetailInfoModel dealDetailInfoModel, String skuItemName, String attrKey) {
        String skuItemContent = DealDetailUtils.getAttrSingleValueByAttrName(dealDetailInfoModel.getDealAttrs(), attrKey);
        if (StringUtils.isBlank(skuItemContent)) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(skuItemName);
        dealSkuItemVO.setType(SKU_ITEM_TYPE_NORMAL_LIST);
        dealSkuItemVO.setValue(skuItemContent);
        return dealSkuItemVO;
    }

    private DealSkuItemVO buildFreeGiftContentSkuItem(DealDetailInfoModel dealDetailInfoModel) {
        String json = DealDetailUtils.getAttrSingleValueByAttrName(dealDetailInfoModel.getDealAttrs(), ATTR_MATERIAL_LIST);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        List<MaterialInfo> materialInfoList = JsonCodec.decode(json, new TypeReference<List<MaterialInfo>>() {});
        if (CollectionUtils.isEmpty(materialInfoList)) {
            return null;
        }
        List<SkuAttrAttrItemVO> valueAttrs = getFreeGiftContentSkuAttrs(materialInfoList);
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(SKU_ITEM_NAME_FREE_GIFT_CONTENT);
        dealSkuItemVO.setType(SKU_ITEM_TYPE_NORMAL_LIST);
        dealSkuItemVO.setValueAttrs(valueAttrs);
        return dealSkuItemVO;
    }

    @NotNull
    private List<SkuAttrAttrItemVO> getFreeGiftContentSkuAttrs(List<MaterialInfo> materialInfoList) {
        List<SkuAttrAttrItemVO> valueAttrs = materialInfoList.stream()
                .filter(materialInfo -> materialInfo != null && StringUtils.isNotBlank(materialInfo.getMaterialName()))
                .map(materialInfo -> {
                    SkuAttrAttrItemVO attrItemVO = new SkuAttrAttrItemVO();
                    if (materialInfo.getCopyNum() > 0) {
                        attrItemVO.setInfo(Lists.newArrayList(materialInfo.getCopyNum() + ATTR_APPEND_FEN));
                    }
                    attrItemVO.setName(materialInfo.getMaterialName());
                    return attrItemVO;
                }).collect(Collectors.toList());
        return valueAttrs;
    }

    @Data
    @VPointCfg
    public static class Config {
    }


    /**
     * 资料清单
     */
    @Data
    public static class MaterialInfo {

        /**
         * 资料类型
         */
        @JsonProperty("material_type")
        private String materialType;

        /**
         * 资料名称
         */
        @JsonProperty("material_name")
        private String materialName;

        /**
         * 份数
         */
        @JsonProperty("copy_num")
        private int copyNum;

    }

    @Data
    public static class CoursePlan {

        /**
         * 课程
         */
        @JsonProperty("course_module")
        private String courseModule;

        /**
         * 课时数
         */
        @JsonProperty("course_time_num")
        private BigDecimal courseTimeNum;

    }
}
