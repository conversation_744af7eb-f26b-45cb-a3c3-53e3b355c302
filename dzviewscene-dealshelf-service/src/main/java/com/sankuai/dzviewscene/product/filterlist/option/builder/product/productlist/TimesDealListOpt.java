package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

/**
 * @author: created by hang.yu on 2024/3/4 15:25
 */
@VPointOption(name = "多次卡筛选列表过滤与排序",
        description = "多次卡筛选列表过滤与排序",
        code = "TimesDealListOpt")
public class TimesDealListOpt extends AbstractTimesDealListOpt {

    @Override
    protected List<ProductM> getSimilarDealList(ActivityCxt context, ProductM currentProduct, List<ProductM> list) {
        return Lists.newArrayList();
    }

}
