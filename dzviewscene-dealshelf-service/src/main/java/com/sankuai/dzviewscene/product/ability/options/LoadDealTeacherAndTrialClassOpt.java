package com.sankuai.dzviewscene.product.ability.options;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Sets;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.dto.EduTechnicianVideoDTO;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.request.QueryEduTechnicianVideoForCRequest;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreAsyncHandlerVP;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 *  加载团单教师和试听课
 */
@VPointOption(name = "加载团单教师和试听课", description = "加载团单教师和试听课", code = LoadDealTeacherAndTrialClassOpt.CODE)
public class LoadDealTeacherAndTrialClassOpt extends PreAsyncHandlerVP<LoadDealTeacherAndTrialClassOpt.Config> {

    public static final String CODE = "LoadDealTeacherAndTrialClassOpt";

    public static final String ATTR_KEY_COURSE_TRIAL = "course_trial";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<Object> compute(ActivityCxt activityCxt, Param param, Config config) {
        Integer dealId = activityCxt.getParam(ProductDetailActivityConstants.Params.productId);
        if (dealId == null) {
            return CompletableFuture.completedFuture(null);
        }
        int platform = param.getPlatform();
        IdTypeEnum idTypeEnum = PlatformUtil.isMT(platform) ? IdTypeEnum.MT : IdTypeEnum.DP;
        long dealIdLong = NumberUtils.objToLong(dealId);
        QueryByDealGroupIdRequest request = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(dealIdLong), idTypeEnum)
                .attrsByKey(AttrSubjectEnum.DEAL_GROUP, Sets.newHashSet("course_trial"))
                .build();
        CompletableFuture<List<DealGroupDTO>> future = compositeAtomService.batchGetServiceProjectByDealIds(request);
        if (future == null) {
            return CompletableFuture.completedFuture(null);
        }
        return future.thenApply(dealGroupDTOS -> loadTeacherAndTrialClass(dealGroupDTOS));
    }

    private List<EduTechnicianVideoDTO> loadTeacherAndTrialClass(List<DealGroupDTO> dealGroupDTOS) {
        AttrDTO courseTrialAttr = findCourseTrialAttr(dealGroupDTOS);
        List<EduCourseTrial> teacherAndTrialClassList = readEduCourseTrialFromAttr(courseTrialAttr);
        if (CollectionUtils.isEmpty(teacherAndTrialClassList)) {
            return null;
        }
        List<Long> courseVideoIds = teacherAndTrialClassList.stream()
                .map(EduCourseTrial::getCourseVideoId)
                .collect(Collectors.toList());
        QueryEduTechnicianVideoForCRequest request = new QueryEduTechnicianVideoForCRequest();
        request.setIds(courseVideoIds);
        return compositeAtomService.batchQueryEduTechnicianVideoInfo(request).join();
    }

    private AttrDTO findCourseTrialAttr(List<DealGroupDTO> dealGroupDTOS) {
        if (CollectionUtils.isEmpty(dealGroupDTOS)) {
            return null;
        }
        DealGroupDTO dealGroupDTO = dealGroupDTOS.get(0);
        if (CollectionUtils.isEmpty(dealGroupDTO.getAttrs())) {
            return null;
        }
        return dealGroupDTO.getAttrs().stream()
                .filter(attr -> attr.getName().equals(ATTR_KEY_COURSE_TRIAL))
                .findFirst().orElse(null);
    }

    private List<EduCourseTrial> readEduCourseTrialFromAttr(AttrDTO courseTrialAttr) {
        if (courseTrialAttr == null || CollectionUtils.isEmpty(courseTrialAttr.getValue()) || StringUtils.isBlank(courseTrialAttr.getValue().get(0))) {
            return null;
        }
        return JsonCodec.decode(courseTrialAttr.getValue().get(0), new TypeReference<List<EduCourseTrial>>() {});
    }

    @Data
    public static class EduCourseTrial implements Serializable {
        /**
         * 老师ID
         **/
        private Integer teacher;

        /**
         * 试听课Id
         **/
        @JsonProperty("course_video")
        private Long courseVideoId;
    }


    @Data
    @VPointCfg
    public static class Config {
    }
}