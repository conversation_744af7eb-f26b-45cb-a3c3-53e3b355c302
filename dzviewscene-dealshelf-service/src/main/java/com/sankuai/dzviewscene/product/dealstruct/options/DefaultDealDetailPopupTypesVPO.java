package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.popup.vpoints.DealDetailPopupTypesVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.PopupTypeVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/30 9:30 下午
 */
@VPointOption(name = "团购详情补充描述type默认变化点", description = "团购详情补充描述type默认变化点，支持配置", code = DefaultDealDetailPopupTypesVPO.CODE, isDefault = true)
public class DefaultDealDetailPopupTypesVPO extends DealDetailPopupTypesVP<DefaultDealDetailPopupTypesVPO.Config> {

    public static final String CODE = "DefaultDealDetailDescTypeVPO";

    @Override
    public List<PopupTypeVO> compute(ActivityCxt context, Param param, Config config) {
        return config.getPopupTypeVOS();
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<PopupTypeVO> popupTypeVOS;
    }
}
