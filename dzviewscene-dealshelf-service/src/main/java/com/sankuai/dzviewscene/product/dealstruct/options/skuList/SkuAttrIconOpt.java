package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuIconVP;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

/**
 * @author: created by hang.yu on 2023/9/27 15:19
 */
@VPointOption(name = "取sku属性Icon变化点", description = "取sku属性Icon变化点", code = SkuAttrIconOpt.CODE)
public class SkuAttrIconOpt extends SkuIconVP<SkuAttrIconOpt.Config> {

    public static final String CODE = "SkuAttrIconOpt";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (param.getSkuItemDto() == null || CollectionUtils.isEmpty(param.getSkuItemDto().getAttrItems())) {
            return null;
        }
        return param.getSkuItemDto().getAttrItems().stream()
                .filter(attr -> StringUtils.equals(config.getIconKey(), attr.getAttrName()))
                .map(SkuAttrItemDto::getAttrValue).findFirst().orElse(null);
    }

    @Data
    @VPointCfg
    public static class Config {

        private String iconKey;

    }

}
