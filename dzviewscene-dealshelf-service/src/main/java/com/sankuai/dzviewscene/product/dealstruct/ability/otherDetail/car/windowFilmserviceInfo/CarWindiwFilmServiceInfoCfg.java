package com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.car.windowFilmserviceInfo;

import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/8 2:01 下午
 */
@AbilityCfg
@Data
public class CarWindiwFilmServiceInfoCfg {

    /**
     * 服务信息模块 标题：服务信息
     */
    private String carWindowFilmServiceInfoModuleTitle;

    /**
     * 服务信息模块 弹窗文案：查看全部服务
     */
    private String carWindowFilmServiceInfoModulePopupDoc;

    /**
     * 服务信息模块 弹窗标题：服务信息
     */
    private String carWindowFilmServiceInfoModulePopupTitle;

    /**
     * 服务信息模块展示的 属性name 到 属性title 配置map
     */
    private Map<String, String> attrName2AttrTitleMapInServiceInfo;

    /**
     * 弹窗页面展示的展示的 属性name 到 属性title 配置map
     */
    private Map<String, String> attrName2AttrTitleMapInPopup;
}
