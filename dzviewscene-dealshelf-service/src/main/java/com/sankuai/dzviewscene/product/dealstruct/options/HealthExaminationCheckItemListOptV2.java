package com.sankuai.dzviewscene.product.dealstruct.options;

import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.lion.client.Lion;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.healthExamination.checkItems.vpoints.HealthExaminationCheckItemListV2VP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination.HealthExaminationItemsGroupVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination.PrimaryExaminationItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination.SecondaryExaminationItemVO;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/14 16:20 下午
 */

@VPointOption(name = "体检团详新包检查项列表变化点V2", description = "体检团详新包检查项列表变化点V2",code = HealthExaminationCheckItemListOptV2.CODE, isDefault = false)
public class HealthExaminationCheckItemListOptV2 extends HealthExaminationCheckItemListV2VP<HealthExaminationCheckItemListOptV2.Config>{

    public static final String CODE = "HealthExaminationCheckItemListOptV2";

    private static final String CONFIG_APP_KEY = "com.sankuai.dzviewscene.productshelf";


    @Override
    public HealthExaminationItemsGroupVO compute(ActivityCxt activityCxt, Param param, Config config) {
        if (param.getDealDetailBasicInfo() == null) {
            return null;
        }
        DealDetailDtoModel dealDetailDtoModel = param.getDealDetailBasicInfo().getDealDetailDtoModel();
        if (dealDetailDtoModel == null || dealDetailDtoModel.getSkuUniStructuredDto() == null || CollectionUtils.isEmpty(dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups())) {
            return null;
        }

        List<MustSkuItemsGroupDto> mustGroups = dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups();
        List<SkuItemDto> skuItems = mustGroups.stream().map(e -> e.getSkuItems()).flatMap(e -> e.stream()).collect(Collectors.toList());

        //做个Mock方便测试
        String skuItemMock = Lion.getString("com.sankuai.dzviewscene.productshelf", "healthexamination.serviceitem.mock", "");
        if (StringUtils.isNotBlank(skuItemMock)) {
            skuItems = JSONObject.parseArray(skuItemMock, SkuItemDto.class);
        }

        List<PrimaryExaminationItemVO> examinationItemVOS = buildPrimaryExaminationItemVOS(config, skuItems);

        //构建整个的结构
        return buildHealthExaminationItemsGroupVO(examinationItemVOS, getAllMustTertiaryItemNum(examinationItemVOS, config.getAllMustTertiaryItemNumFormat()));
    }

    public List<PrimaryExaminationItemVO> buildPrimaryExaminationItemVOS(Config config, List<SkuItemDto> skuItems) {
        List<HealthExaminationItem> itemList = skuItems.stream().map(e -> parseSkuItem(e.getName(), e.getAttrItems()))
                .filter(e -> StringUtils.isNotBlank(e.getShopCategoryName())).collect(Collectors.toList());

        //根据商户分类去分组
        Map<String, List<HealthExaminationItem>> categoryIdMap = itemList.stream()
                .collect(Collectors.groupingBy(e -> e.getShopCategoryName(), LinkedHashMap::new, Collectors.toList()));

        ArrayListMultimap<String, SecondaryExaminationItemVO> result = ArrayListMultimap.create();
        ;

        Map<String, Long> categoryItemCountMap = Maps.newHashMap();

        categoryIdMap.forEach((shopCategory, items) -> {

            //平台一级分类
            List<String> platformFirstCategoryList = items.stream().map(e -> e.getCategory1()).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            //如果平台一级分类为空，或者平台一级分类不唯一的话
            if (CollectionUtils.isEmpty(platformFirstCategoryList) || platformFirstCategoryList.size() > 1) { //塞到其他里
                addMap(result, "其他", shopCategory, items);
            }  else { //如果平台一级分类唯一的话
                addMap(result, platformFirstCategoryList.get(0), shopCategory, items);
            }

            //检查项去重一下
            long itemsCount = items.stream().map(e-> e.getShopItemName()).filter(StringUtils::isNotBlank).distinct().count();
            categoryItemCountMap.put(shopCategory, itemsCount);
        });

        //构建一级分类
        List<PrimaryExaminationItemVO> examinationItemVOS = buildPrimaryExaminationItemVOS(result, categoryItemCountMap, config.getPrimaryItemsortedMap());
        return examinationItemVOS;
    }

    private List<PrimaryExaminationItemVO> buildPrimaryExaminationItemVOS(ArrayListMultimap<String, SecondaryExaminationItemVO> result, Map<String, Long> categoryItemCountMap,Map<String,Integer> primaryItemSortedMap) {

        return result.asMap().entrySet().stream().map(e -> {
            PrimaryExaminationItemVO itemVO = new PrimaryExaminationItemVO();
            //一级分类
            itemVO.setName(e.getKey());

            //一级分类下，所有二级分类的检查项总数
            int totalCount = e.getValue().stream().map(secondItem -> categoryItemCountMap.getOrDefault(secondItem.getName(), 0L))
                    .collect(Collectors.reducing(0L, Long::sum)).intValue();

            itemVO.setDesc(String.format("%d项", totalCount));
            itemVO.setSecondaryExaminationItemList(Lists.newArrayList(e.getValue()));
            return itemVO;
            //从小到大排序，取不到给99放在后面
        }).sorted(Comparator.comparingInt(e -> primaryItemSortedMap.getOrDefault(e.getName(), 99))).collect(Collectors.toList());
    }

    private void addMap(ArrayListMultimap<String, SecondaryExaminationItemVO> map, String firstPlatformCategory, String shopCategory, List<HealthExaminationItem> itemList) {

        SecondaryExaminationItemVO secondaryExaminationItemVO = new SecondaryExaminationItemVO();
        secondaryExaminationItemVO.setName(shopCategory);

        //分类下的检查项用顿号分隔
        String itemStr = itemList.stream().map(e -> e.getShopItemName()).distinct().filter(StringUtils::isNotBlank).collect(Collectors.joining("、"));
        secondaryExaminationItemVO.setTertiaryExaminations(itemStr);

        //检查意义的确定
        List<String> explainList = itemList.stream().map(e -> e.getExplain()).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        String explain = null;
        //如果检查意义不为空，并且检查意义唯一的话，就取该意义
        if (CollectionUtils.isNotEmpty(explainList) && explainList.size() == 1) {
            explain = explainList.get(0);
        }
        secondaryExaminationItemVO.setCheckSignificance(explain);

        //塞进去
        map.put(firstPlatformCategory, secondaryExaminationItemVO);
    }

    private HealthExaminationItem parseSkuItem(String shopItemName, List<SkuAttrItemDto> attrItems) {

        //为空的值给他过滤掉
        Map<String, String> attrValueMap = attrItems.stream().filter(e -> StringUtils.isNotBlank(e.getAttrValue()))
                .collect(Collectors.toMap(e -> e.getAttrName(), e -> e.getAttrValue(), (k1, k2) -> k1));

        return HealthExaminationItem.builder()
                .shopItemName(shopItemName)
                .shopCategoryName(attrValueMap.get("categoryName"))
                .explain(attrValueMap.get("explain"))
                .category1(attrValueMap.get("category1"))
                .category2(attrValueMap.get("category2"))
                .standardName(attrValueMap.get("standardName"))
                .build();

    }

    private HealthExaminationItemsGroupVO buildHealthExaminationItemsGroupVO(List<PrimaryExaminationItemVO> primaryExaminationItemVOS, String desc) {
        if (CollectionUtils.isEmpty(primaryExaminationItemVOS)) {
            return null;
        }
        HealthExaminationItemsGroupVO healthExaminationItemsGroupVO = new HealthExaminationItemsGroupVO();
        healthExaminationItemsGroupVO.setGroupName("包含项目");
        healthExaminationItemsGroupVO.setPrimaryExaminationItemList(primaryExaminationItemVOS);
        healthExaminationItemsGroupVO.setDesc(desc);
        return healthExaminationItemsGroupVO;
    }


    @Data
    @Builder
    public static class HealthExaminationItem {
        private String shopItemName; //商户输入的检查项目名称
        private String shopCategoryName; //商户输入的检查分类名称
        private String explain; //如果商户没有填写，则默认使用平台库的，如果平台库也没有则不传
        private String category1; //平台一级分类名称
        private String category2; //平台二级分类名称
        private String standardName; //平台检查项目名称
    }

    /**
     * 获取全部可享sku中三级检查项总数
     *@param
     *@return
     */
    private String getAllMustTertiaryItemNum(List<PrimaryExaminationItemVO> mustSkuCheckItemList, String format) {
        if (CollectionUtils.isEmpty(mustSkuCheckItemList)) {
            return null;
        }
        int sum = mustSkuCheckItemList.stream()
                .filter(item -> StringUtils.isNotEmpty(item.getDesc()))
                .map(item -> {
                    int num = NumberUtils.objToInt(item.getDesc().replaceAll("项", StringUtils.EMPTY));
                    return num == -1 ? 0 : num;
                }).reduce(Integer::sum).orElse(0);
        if (sum == 0) {
            return null;
        }
        return String.format(format, sum);
    }




    @Data
    @VPointCfg
    public static class Config {
        String allMustTertiaryItemNumFormat;
        Map<String, Integer> primaryItemsortedMap = Maps.newHashMap();
    }
}
