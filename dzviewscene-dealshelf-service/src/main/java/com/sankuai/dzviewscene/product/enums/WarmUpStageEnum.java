package com.sankuai.dzviewscene.product.enums;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Objects;

/**
 * 预热阶段枚举
 *
 * <AUTHOR>
 * @date 2023/4/3
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum WarmUpStageEnum {

    NOT_WARM_UP_AND_TIME_STOCK(0, "非预热&&非时段库存") {
        @Override
        boolean isMatch(WarmUpStageContext context) {
            return Objects.isNull(context.getWarmUpStartTime()) && !isTimeStockDeal(context.getDealTimeStock());
        }
    },
    ONLY_WARM_UP_PRESALE(1, "仅设置预热-预热阶段") {
        @Override
        boolean isMatch(WarmUpStageContext context) {
            if (Objects.isNull(context.getWarmUpStartTime())) {
                return false;
            }
            if (Objects.isNull(context.getProductM().getBeginDate())) {
                return false;
            }
            if (isTimeStockDeal(context.getDealTimeStock())) {
                return false;
            }
            //预热开始时间<=当前时间 <开售时间
            return System.currentTimeMillis() >= context.getWarmUpStartTime()
                    && System.currentTimeMillis() < context.getProductM().getBeginDate();
        }
    },
    ONLY_WARM_UP_SALE(2, "仅设置预热-售卖阶段") {
        @Override
        boolean isMatch(WarmUpStageContext context) {
            if (Objects.isNull(context.getWarmUpStartTime())) {
                return false;
            }
            if (Objects.isNull(context.getProductM().getBeginDate())) {
                return false;
            }
            if (isTimeStockDeal(context.getDealTimeStock())) {
                return false;
            }
            //开售时间<=当前时间
            return System.currentTimeMillis() >= context.getProductM().getBeginDate();
        }
    },

    ONLY_TIME_STOCK_PRESALE(3, "预热&时段库存-预热阶段") {
        @Override
        boolean isMatch(WarmUpStageContext context) {
            if (Objects.isNull(context.getWarmUpStartTime())) {
                return false;
            }
            if (Objects.isNull(context.getProductM().getBeginDate())) {
                return false;
            }
            if (!isTimeStockDeal(context.getDealTimeStock())) {
                return false;
            }
            //预热时间<=当前时间<=开售时间
            return System.currentTimeMillis() >= context.getWarmUpStartTime()
                    && System.currentTimeMillis() <= context.getProductM().getBeginDate();
        }
    },
    NOT_STARTED_TIME_STOCK(10, "未到释放库存时间点") {
        @Override
        boolean isMatch(WarmUpStageContext context) {
            return isMatchTimeStockStatus(context.getDealTimeStock(),
                    TimeStockStatusEnum.NOT_STARTED_DAILYTIMESTOCK_DEAL);
        }
    },
    IN_PROCESS_TIME_STOCK(11, "到达释放库存时间点，释放量有余量") {
        @Override
        boolean isMatch(WarmUpStageContext context) {
            return isMatchTimeStockStatus(context.getDealTimeStock(),
                    TimeStockStatusEnum.IN_PROCESS_DAILYTIMESTOCK_DEAL);
        }
    },
    CURRENT_TIME_END_HAVE_NEXT(12, "到达释放库存时间点，释放量有余量但释放时间结束，不过还有下一场且有库存") {
        @Override
        boolean isMatch(WarmUpStageContext context) {
            return isMatchTimeStockStatus(context.getDealTimeStock(),
                    TimeStockStatusEnum.CURRENT_TIME_END_DAILYTIMESTOCK_DEAL);
        }
    },
    CURRENT_STOCK_END_HAVE_NEXT(13, "经过释放库存时间点，释放量无余量但还有下一场且有库存") {
        @Override
        boolean isMatch(WarmUpStageContext context) {
            return isMatchTimeStockStatus(context.getDealTimeStock(),
                    TimeStockStatusEnum.CURRENT_STOCK_END_DAILYTIMESTOCK_DEAL);
        }
    },
    NO_NEXT_TIME_STOCK(15, "经过释放库存时间点，释放量无余量但没有下一场") {
        @Override
        boolean isMatch(WarmUpStageContext context) {
            return isMatchTimeStockStatus(context.getDealTimeStock(), TimeStockStatusEnum.NO_NEXT_DAILYTIMESTOCK_DEAL);
        }
    },
    NO_STOCK(16, "无总量库存") {
        @Override
        boolean isMatch(WarmUpStageContext context) {
            return isMatchTimeStockStatus(context.getDealTimeStock(), TimeStockStatusEnum.NO_STOCK_DEAL);
        }
    };

    private int code;
    private String description;

    abstract boolean isMatch(WarmUpStageContext context);

    /**
     * 预热开始时间属性名
     */
    public static final String ATTRIBUTE_KEY_WARM_UP_START_TIME = "warmUpStartTimeAttr";

    /**
     * 时段库存属性名
     */
    public static final String ATTRIBUTE_KEY_TIME_STOCK = "timeStockAttr";


    public static WarmUpStageResult getWarmUpStageResult(ProductM productM) {
        if (Objects.isNull(productM)) {
            return null;
        }
        WarmUpStageContext context = WarmUpStageContext.builder()
                .productM(productM)
                .warmUpStartTime(parseWarmUpStartTime(productM))
                .dealTimeStock(parseDealTimeStockModel(productM))
                .build();
        WarmUpStageResult result = WarmUpStageResult.builder()
                .dealTimeStock(context.getDealTimeStock())
                .warmUpStartTime(context.getWarmUpStartTime())
                .build();
        for (WarmUpStageEnum item : WarmUpStageEnum.values()) {
            if (item.isMatch(context)) {
                result.setStage(item);
                return result;
            }
        }
        return result;
    }

    public static boolean isDuringWarmUpPeriod(ProductM productM) {
        String warmUpStartTimeAttr = productM.getAttr(ATTRIBUTE_KEY_WARM_UP_START_TIME);
        if (StringUtils.isBlank(warmUpStartTimeAttr)) {
            return false;
        }
        // 售卖开始时间
        long sellStartTime = productM.getBeginDate();
        // 预热开始时间
        long warmUpStartTime = NumberUtils.toLong(warmUpStartTimeAttr);
        long currentTime = System.currentTimeMillis();
        //当前时间大于预热时间小于售卖开始时间
        if (currentTime >= warmUpStartTime && currentTime < sellStartTime) {
            return true;
        }
        return false;
    }

    private static boolean isTimeStockDeal(DealTimeStockModel data) {
        return Objects.nonNull(data) && Objects.nonNull(data.getTimeStockStatus())
                && data.getTimeStockStatus() != TimeStockStatusEnum.NOT_DAILYTIMESTOCK_DEAL.getCode();
    }

    private static DealTimeStockModel parseDealTimeStockModel(ProductM productM) {
        if (Objects.isNull(productM)) {
            return null;
        }
        String timeStockAttr = productM.getAttr(ATTRIBUTE_KEY_TIME_STOCK);
        if (StringUtils.isBlank(timeStockAttr)) {
            return null;
        }
        return JsonCodec.decode(timeStockAttr, DealTimeStockModel.class);
    }

    private static Long parseWarmUpStartTime(ProductM productM) {
        if (Objects.isNull(productM)) {
            return null;
        }
        String warmUpStartTimeAttr = productM.getAttr(ATTRIBUTE_KEY_WARM_UP_START_TIME);
        if (StringUtils.isBlank(warmUpStartTimeAttr)) {
            return null;
        }
        Long result = NumberUtils.toLong(warmUpStartTimeAttr);
        if (Objects.nonNull(result) && result > 0L) {
            return result;
        }
        return null;
    }

    private static boolean isMatchTimeStockStatus(DealTimeStockModel dealTimeStock, TimeStockStatusEnum status) {
        if (Objects.isNull(dealTimeStock) || Objects.isNull(status)) {
            return false;
        }
        if (!isTimeStockDeal(dealTimeStock)) {
            return false;
        }
        return Objects.nonNull(dealTimeStock.getTimeStockStatus()) && dealTimeStock.getTimeStockStatus() == status.getCode();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WarmUpStageContext {
        private ProductM productM;
        //预热开始时间
        private Long warmUpStartTime;
        //时段库存
        private DealTimeStockModel dealTimeStock;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WarmUpStageResult {

        private WarmUpStageEnum stage;

        //预热开始时间
        private Long warmUpStartTime;
        //时段库存
        private DealTimeStockModel dealTimeStock;

        public boolean isValidWarmUpStage() {
            return Objects.nonNull(stage) && stage != NOT_WARM_UP_AND_TIME_STOCK;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DealTimeStockModel {

        private Integer timeStockStatus;

        private DealTimeStockItemModel currentTimeStockItem;

        private DealTimeStockItemModel nextTimeStockItem;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DealTimeStockItemModel {

        private Long startTime;

        private Long endTime;

    }

    /**
     * 定时释放库存团单处在的状态
     */
    @Getter
    @AllArgsConstructor
    public enum TimeStockStatusEnum {
        NOT_DAILYTIMESTOCK_DEAL("非定时释放库存团单", 0),
        NOT_STARTED_DAILYTIMESTOCK_DEAL("未到释放库存时间点", 1),
        IN_PROCESS_DAILYTIMESTOCK_DEAL("到达释放库存时间点，释放量有余量", 2),
        CURRENT_TIME_END_DAILYTIMESTOCK_DEAL("到达释放库存时间点，释放量有余量但释放时间结束，不过还有下一场且有库存", 3),
        CURRENT_STOCK_END_DAILYTIMESTOCK_DEAL("经过释放库存时间点，释放量无余量但还有下一场且有库存", 4),
        NO_NEXT_DAILYTIMESTOCK_DEAL("经过释放库存时间点，释放量无余量但没有下一场", 5),
        NO_STOCK_DEAL("无总量库存", 6);

        private String description;
        private int code;
    }
}
