package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.DealAttrVOListModuleBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
@Component("DentistryInstrumentStructAttrStrategy")
public class DentistryInstrumentStructAttrStrategy implements ModuleStrategy {
    private final static String CONFIG_NAME = "仪器品牌备注";
    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt,
                                           DealDetailAssembleParam assambleParam,
                                           String config) {
        String configName = config == null ? CONFIG_NAME : config;
        String abilityCode = DealAttrVOListModuleBuilder.CODE;
        List<DealDetailStructAttrModuleGroupModel> modelList = activityCxt.getSource(abilityCode) == null
                ? Lists.newArrayList() : activityCxt.getSource(abilityCode);
        DealDetailStructAttrModuleGroupModel moduleGroupModel = modelList.stream().
                filter(model -> Objects.equals(configName,
                        model.getGroupName())).findFirst().orElse(null);
        if (moduleGroupModel == null || CollectionUtils.isEmpty(moduleGroupModel.getDealDetailStructAttrModuleVOS())) {
            return null;
        }
        DealDetailStructAttrModuleVO dealDetailStructAttrModuleVO = moduleGroupModel.getDealDetailStructAttrModuleVOS().get(0);
        List<String> attrValues = dealDetailStructAttrModuleVO.getAttrValues();
        if (CollectionUtils.isEmpty(attrValues)) {
            return null;
        }
        String memo = attrValues.get(0);
        if (StringUtils.isBlank(memo)) {
            return null;
        }
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        dealDetailModuleVO.setDescModel(memo);
        return dealDetailModuleVO;
    }
}
