package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bath.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 免费设施
 *
 * @author: created by hang.yu on 2023/10/7 17:03
 */
@Getter
@AllArgsConstructor
public enum FreefacilitiesEnum {

    BATHING_POOL("bathing_pool", "汤池", "https://p0.meituan.net/travelcube/3a6dc4c815290873b83d5e0bf7f69aab902.png"),
    SAUNA("sauna", "汗蒸", "https://p1.meituan.net/travelcube/40ea3961df9de704ef23df8570bc33361494.png"),
    LEISURE("leisure", "休闲玩乐设施", "https://p0.meituan.net/travelcube/142cae403c90a1de8592b67ac7ff67d51268.png"),
    ;
    /**
     * 免费设施code
     */
    private final String code;

    /**
     * 免费设施名称
     */
    private final String name;

    /**
     * 免费设施icon
     */
    private final String icon;

}
