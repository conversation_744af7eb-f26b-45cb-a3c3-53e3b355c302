package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.ImplantDealUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.MedicalDealAttrUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ImplantGroupConfig;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/27
 */
@VPointOption(name = "种植牙可配置的多SKU列表", description = "种植牙可配置的多SKU列表", code = ImplantMultiSkuListModelOpt.CODE)
public class ImplantMultiSkuListModelOpt extends SkuListModuleVP<ImplantMultiSkuListModelOpt.Config> {
    public static final String CODE = "ImplantMultiSkuListModelOpt";
    private static final String SERVICE_TYPE_KEY = "service_type";
    private static final String DENTAL_IMPLANT_THERAPY = "种植牙治疗";
    private static final String DENTAL_IMPLANT_DESIGN = "种植牙方案设计";

    @Override
    public List<DealDetailSkuListModuleGroupModel> compute(ActivityCxt activityCxt, Param param, ImplantMultiSkuListModelOpt.Config config) {
        Map<String, String> name2ValueMap = MedicalDealAttrUtils.convertName2ValueMap(param.getDealDetailInfoModel());
        if (validateRequest(name2ValueMap, config)) {
            return null;
        }

        String serviceType = name2ValueMap.get(SERVICE_TYPE_KEY);
        if (DENTAL_IMPLANT_THERAPY.equals(serviceType)) {
            return buildImplantTherapyMultiSkuListModel(param, name2ValueMap, config);
        } else if (DENTAL_IMPLANT_DESIGN.equals(serviceType)) {
            return buildImplantDesignMultiSkuListModel(name2ValueMap, config);
        }

        return null;
    }

    private List<DealDetailSkuListModuleGroupModel> buildImplantDesignMultiSkuListModel(Map<String, String> name2ValueMap, ImplantMultiSkuListModelOpt.Config config) {
        List<DealDetailSkuListModuleGroupModel> result = Lists.newArrayList();
        // 服务项目列表：服务内容
        List<DealSkuGroupModuleVO> serviceProjectGroupModuleVOs = ImplantDealUtils.buildDesignServiceProjectSkuGroupModuleVOs(name2ValueMap, config);
        DealDetailSkuListModuleGroupModel serviceProjectGroupModel = new DealDetailSkuListModuleGroupModel();
        serviceProjectGroupModel.setGroupName("服务项目列表");
        serviceProjectGroupModel.setDescModel(null);
        serviceProjectGroupModel.setDealSkuGroupModuleVOS(serviceProjectGroupModuleVOs);
        result.add(serviceProjectGroupModel);
        return result;
    }

    // 构建团购详情模块
    private List<DealDetailSkuListModuleGroupModel> buildImplantTherapyMultiSkuListModel(Param param, Map<String, String> name2ValueMap, ImplantMultiSkuListModelOpt.Config config) {
        List<DealDetailSkuListModuleGroupModel> result = Lists.newArrayList();
        // 服务项目列表：耗材+服务内容
        List<DealSkuGroupModuleVO> serviceProjectGroupModuleVOs = ImplantDealUtils.buildServiceProjectSkuGroupModuleVOs(name2ValueMap, config);
        if (CollectionUtils.isNotEmpty(serviceProjectGroupModuleVOs)) {
            DealDetailSkuListModuleGroupModel serviceProjectGroupModel = new DealDetailSkuListModuleGroupModel();
            serviceProjectGroupModel.setGroupName("服务项目列表");
            serviceProjectGroupModel.setDescModel(null);
            serviceProjectGroupModel.setDealSkuGroupModuleVOS(serviceProjectGroupModuleVOs);
            result.add(serviceProjectGroupModel);
        }

        // 付费加购
        List<DealSkuGroupModuleVO> additionalProjectGroupModuleVOs = ImplantDealUtils.buildAdditionalProject(param);
        if (CollectionUtils.isNotEmpty(additionalProjectGroupModuleVOs)) {
            DealDetailSkuListModuleGroupModel premiumPurchaseGroupModel = new DealDetailSkuListModuleGroupModel();
            premiumPurchaseGroupModel.setGroupName("付费加购");
            premiumPurchaseGroupModel.setGroupSubtitle("下单时可自行加购");
            premiumPurchaseGroupModel.setDealSkuGroupModuleVOS(additionalProjectGroupModuleVOs);
            result.add(premiumPurchaseGroupModel);
        }
        return result;
    }

    private boolean validateRequest(Map<String, String> name2ValueMap, ImplantMultiSkuListModelOpt.Config config) {
        return ObjectUtils.isEmpty(name2ValueMap)
                || ObjectUtils.isEmpty(name2ValueMap.get(SERVICE_TYPE_KEY))
                || ObjectUtils.isEmpty(config)
                || ObjectUtils.isEmpty(config.getGroupConfigs());
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<ImplantGroupConfig> groupConfigs;
    }
}
