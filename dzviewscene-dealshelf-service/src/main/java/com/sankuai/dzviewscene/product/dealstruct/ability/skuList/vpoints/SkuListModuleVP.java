package com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.DealDetailSkusModuleBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/3 8:10 下午
 */
@VPoint(name = "服务项目列表模块变化点", description = "服务项目列表模块变化点", code = SkuListModuleVP.CODE, ability = DealDetailSkusModuleBuilder.CODE)
public abstract class SkuListModuleVP<T> extends PmfVPoint<List<DealDetailSkuListModuleGroupModel>, SkuListModuleVP.Param, T> {

    public static final String CODE = "SkuListModuleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private DealDetailInfoModel dealDetailInfoModel;

        private List<DouhuResultModel> douhuResultModels;

    }
}
