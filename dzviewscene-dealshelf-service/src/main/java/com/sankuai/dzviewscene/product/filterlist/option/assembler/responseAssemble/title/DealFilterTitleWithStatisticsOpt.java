package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListTitleVP;
import lombok.Data;
import org.apache.commons.lang.StringUtils;


/**
 * <AUTHOR>
 * @date 2022/3/14
 */
@VPointOption(name = "团单筛选标题",
        description = "团单筛选标题，例如:'精选优惠(35)'",
        code = "DealFilterTitleWithStatisticsOpt")
public class DealFilterTitleWithStatisticsOpt extends DealFilterListTitleVP<DealFilterTitleWithStatisticsOpt.Config> {

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (config == null || StringUtils.isEmpty(config.getTitle())) {
            return "";
        }
        if (param.getDealCount() <= 0) {
            return config.getTitle();
        }
        if (config.hideCount != null && config.hideCount) {
            return config.getTitle();
        }
        //一般都是这个样式，暂时写死
        return config.getTitle() + String.format("(%d)", param.getDealCount());
    }

    @Data
    @VPointCfg
    public static class Config {
        private String title;
        private Boolean hideCount;
    }
}
