package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: wuweizhen
 * @Date: 2023/3/6 10:41
 * @Description:
 */
@VPointOption(name = "配镜DealAttrVO列表变化点"
        , description = "取镜时长和质保及售后服务DealAttrVO列表变化点"
        , code = GlassesAttrVOListOpt.CODE)
public class GlassesAttrVOListOpt extends DealAttrVOListVP<GlassesAttrVOListOpt.StepConfig> {

    public static final String CODE = "GlassesAttrVOListOpt";

    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt context, Param param, StepConfig config) {
        return buildAttr(context, param, config);
    }


    private List<DealDetailStructAttrModuleGroupModel> buildAttr(ActivityCxt context, Param param, StepConfig config) {
        if (config == null || CollectionUtils.isEmpty(config.configs) || param == null) {
            return Lists.newArrayList();
        }
        return config.configs.stream().map(modelConfig -> {
            String attrValue = DealDetailUtils.getAttrSingleValueByAttrName(param.getDealAttrs(), modelConfig.getAttrKey());
            if (StringUtils.isEmpty(attrValue)) {
                return null;
            }
            List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOS = buildAttrModules(attrValue, modelConfig);
            DealDetailStructAttrModuleGroupModel dealDetailStructAttrModuleGroupModel = new DealDetailStructAttrModuleGroupModel();
            dealDetailStructAttrModuleGroupModel.setGroupName(modelConfig.getTitle());
            dealDetailStructAttrModuleGroupModel.setDealDetailStructAttrModuleVOS(dealDetailStructAttrModuleVOS);
            return dealDetailStructAttrModuleGroupModel;
        }).filter(model -> Objects.nonNull(model)).collect(Collectors.toList());
    }

    private List<DealDetailStructAttrModuleVO> buildAttrModules(String attrValue, GlassesAttrVOListOpt.Config config) {
        List<DealDetailStructAttrModuleVO> attrModules = new ArrayList<>();
        DealDetailStructAttrModuleVO processStepAttrModule = new DealDetailStructAttrModuleVO();
        if (StringUtils.isNotEmpty(config.getAttrKey())) {
            processStepAttrModule.setAttrName(config.getAttrKey());
        }
        if (StringUtils.isNotEmpty(attrValue)) {
            if (config.getType() == 1) {
                List<String> attrs = JsonCodec.decode(attrValue, new TypeReference<List<String>>() {
                });
                processStepAttrModule.setAttrValues(attrs);
            } else {
                processStepAttrModule.setAttrValues(Lists.newArrayList(attrValue));
            }
        }
        attrModules.add(processStepAttrModule);
        return attrModules;
    }


    @Data
    @VPointCfg
    public static class StepConfig {
        private List<Config> configs;
    }

    @Data
    public static class Config {
        //标题
        private String title;
        //属性key
        private String attrKey;
        //类型，0字符串 1 list
        private Integer type;
    }
}