package com.sankuai.dzviewscene.product.dealstruct.model;

import com.sankuai.common.utils.DealAttrUtils;
import com.sankuai.dztheme.deal.res.standardservice.DealStandardServiceProjectDTO;
import com.sankuai.dztheme.deal.res.structured.DealDetailStructuredDTO;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.uniform.UniformStructContentModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.DealAdditionalProjectM;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/11/22 9:00 下午
 */
@Data
public class DealDetailInfoModel {

    /**
     * 团单ID
     */
    private int dealId;

    /*
     * 团购详情sku货结构化模型
     */
    private DealDetailDtoModel dealDetailDtoModel;

    /*
     * 团购详情sku货类目列表
     */
    private List<ProductSkuCategoryModel> productCategories;

    /*
     * 团购详情富文本Desc信息
     */
    private String desc;

    /*
     * 团单售价
     */
    private String salePrice;

    /*
     * 团单市场价
     */
    private String marketPrice;

    /*
     * 团单标题
     */
    private String dealTitle;

    /*
     * 团单属性信息
     */
    List<AttrM> dealAttrs;

    /*
     * 上单页面以组件形式上单的团单属性信息
     */
    List<UniformStructContentModel> dealModuleAttrs;

    /**
     * 标准化服务项目 for太极 序列化属性得到
     */
    private StandardServiceProjectDTO standardServiceProjectDTO;

    /**
     * 标准服务项目 新增团购主题文案得到
     */
    private DealStandardServiceProjectDTO dealStandardServiceProjectDTO;


    /**
     * 是否为太极团单
     */
    private boolean isUnifyProduct;

    /**
     * 团购交易类型
     */
    private Integer tradeType;

    /**
     * 加项列表
     */
    private List<DealAdditionalProjectM> additionalProjectList;

    /**
     * 结构化内容的服务项目
     */
    private DealDetailStructuredDTO dealDetailStructuredDTO;

    /**
     * 按照以下优先级取值
     * 团单自身的attr > 标准服务项目attr > 结构化服务项目attr
     * @param attrName
     * @return
     */
    public String getAttrValueFromAllAttrs(String attrName) {
        if (StringUtils.isBlank(attrName)) {
            return null;
        }
        String result = getAttrValueFromDealAttrs(attrName);
        // 团单自身的attr
        if (StringUtils.isNotBlank(result)) {
            return result;
        }
        result = DealAttrUtils.getAttrFromStandardServiceProject(dealStandardServiceProjectDTO,attrName);
        if (StringUtils.isNotBlank(result)) {
            return result;
        }
        // 结构化服务项目
        return DealAttrUtils.getAttrFromDealDetailStructured(dealDetailStructuredDTO,attrName);
    }

    /**
     * 根据属性名获取属性值
     *
     * @param attrName
     * @return
     */
    public String getAttrValueFromDealAttrs(String attrName) {
        if (CollectionUtils.isEmpty(this.getDealAttrs())) {
            return null;
        }
        AttrM attrM = this.getDealAttrs().stream()
                .filter(Objects::nonNull)
                .filter(attr -> StringUtils.isNotBlank(attr.getName()) && attr.getName().equals(attrName))
                .findFirst()
                .orElse(null);
        return attrM == null ? null : attrM.getValue();
    }

}
