package com.sankuai.dzviewscene.product.filterlist.acitivity;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.MultiGroupQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.MultiplexQueryFilterFetcher;

import java.util.List;
import java.util.Map;

/**
 * Created by wang<PERSON><PERSON> on 2022/05/07.
 */
@ActivityTemplate(activityCode = DealFilterListActivity.CODE, name = "团单筛选列表默认模板")
public class DealFilterListActivityTemplate implements IActivityTemplate {

    @Override
    public Class<? extends IActivityFlow> flow() {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivityTemplate.flow()");
        return null;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivityTemplate.extParams(java.util.Map)");

    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivityTemplate.extContexts(java.util.List)");
    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivityTemplate.extValidators(java.util.List)");

    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        // 1. 注册筛选能力实现
        abilities.put(QueryFetcher.ABILITY_PRODUCT_QUERY_CODE, MultiGroupQueryFetcher.class);
        // 2. 商品填充能力
        abilities.put(PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE, MultiGroupPaddingFetcher.class);
        // 3. 多路分组筛选查询能力
        abilities.put(FilterFetcher.ABILITY_FILTER_CODE, MultiplexQueryFilterFetcher.class);
    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
    }
}
