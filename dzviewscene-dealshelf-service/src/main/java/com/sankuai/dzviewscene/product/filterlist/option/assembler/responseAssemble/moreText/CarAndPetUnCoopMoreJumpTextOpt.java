package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.moreText;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.ability.model.CarAndPetUncoopShopShelfAttrM;
import com.sankuai.dzviewscene.product.ability.options.CarAndPetUnCoopCommonInfoOpt;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListMoreTextVP;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@VPointOption(name = "养车用车/宠物推荐列表频道跳转文案", description = "养车用车/宠物推荐列表频道跳转文案", code = CarAndPetUnCoopMoreJumpTextOpt.CODE)
public class CarAndPetUnCoopMoreJumpTextOpt extends DealFilterListMoreTextVP<CarAndPetUnCoopMoreJumpTextOpt.Config> {

    public static final String CODE = "carAndPetUnCoopMoreJumpText";

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Config config) {
        CarAndPetUncoopShopShelfAttrM carAndPetUncoopShopShelfAttrM = activityCxt
                .getParam(CarAndPetUnCoopCommonInfoOpt.CODE);
        if (Objects.nonNull(carAndPetUncoopShopShelfAttrM)
                && StringUtils.isNotBlank(carAndPetUncoopShopShelfAttrM.getJumpDesc())) {
            return carAndPetUncoopShopShelfAttrM.getJumpDesc();
        }
        return config.getDefaultText();
    }

    @Data
    @VPointCfg
    public static class Config {
        private String defaultText = "周边优惠团购";
    }
}
