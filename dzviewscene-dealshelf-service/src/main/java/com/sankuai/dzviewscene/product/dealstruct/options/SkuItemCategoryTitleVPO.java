package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints.SkuItemTitleVP;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/27 9:17 下午
 */
@VPointOption(name = "取sku货类别作为团购详情sku货标题变化点", description = "取sku货类别作为团购详情sku货标题变化点",code = SkuItemCategoryTitleVPO.CODE, isDefault = true)
public class SkuItemCategoryTitleVPO extends SkuItemTitleVP<SkuItemCategoryTitleVPO.Config> {

    public static final String CODE = "SkuItemCategoryTitleVPO";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        SkuItemDto skuItemDto = param.getSkuItemDto();
        if (skuItemDto == null) {
            return null;
        }
        List<ProductSkuCategoryModel> productCategories = param.getProductCategories();
        String skuCategory = getSkuCategory(skuItemDto, productCategories);
        if (CollectionUtils.isNotEmpty(config.skuCategoryShowingSkuName) && config.getSkuCategoryShowingSkuName().contains(skuCategory)) {
            return skuItemDto.getName();
        }
        return skuCategory;
    }

    private String getSkuCategory(SkuItemDto skuItemDto, List<ProductSkuCategoryModel> productCategories) {
        if (skuItemDto == null || CollectionUtils.isEmpty(productCategories)) {
            return null;
        }
        ProductSkuCategoryModel productSkuCategoryModel = productCategories.stream().filter(category -> category.getProductCategoryId() != null && skuItemDto.getProductCategory() == category.getProductCategoryId()).findFirst().orElse(null);
        if (productSkuCategoryModel == null) {
            return null;
        }
        return productSkuCategoryModel.getCnName();
    }

    @Data
    @VPointCfg
    public static class Config {
        //取sku名称作为sku标题展示的sku类
        private List<String> skuCategoryShowingSkuName;
    }
}
