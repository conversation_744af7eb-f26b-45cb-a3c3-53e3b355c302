package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.dp.arts.utils.recycler.Recycler;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.struct.DealDetailStructAttrListBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModel;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrsModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrGroupVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * created by zhangzhiyuan04 in 2021/12/14
 */
@Component("beautyNailStructAttrStrategy")
public class BeautyNailStructAttrStrategy implements ModuleStrategy {

    private static final String ATTR_GROUP_NAME = "项目附赠";

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        String abilityCode = DealDetailStructAttrListBuilder.CODE;
        List<DealDetailStructAttrModel> structAttrModels = activityCxt.getSource(abilityCode);
        if (CollectionUtils.isEmpty(structAttrModels)) {
            return null;
        }
        //拿到第一个团单（默认只有一个团单）
        DealDetailStructAttrModel dealDetailStructAttrModel = CollectUtils.firstValue(structAttrModels);
        if (dealDetailStructAttrModel == null) {
            return null;
        }
        List<StructAttrsModel> structAttrsModels = dealDetailStructAttrModel.getStructAttrsModels();
        if (CollectionUtils.isEmpty(structAttrModels)) {
            return null;
        }
        StructAttrsModel structAttrsModel = structAttrsModels.stream().filter(model -> ATTR_GROUP_NAME.equals(model.getName())).findFirst().orElse(null);
        if (structAttrsModel == null || CollectionUtils.isEmpty(structAttrsModel.getStructAttrModels())) {
            return null;
        }
        List<String> values = structAttrsModel.getStructAttrModels().stream().flatMap(item -> item.getAttrValues().stream()).collect(Collectors.toList());
        return buildDealDetailModuleVO(structAttrsModel.getName(), values);
    }

    private DealDetailModuleVO buildDealDetailModuleVO(String attrName, List<String> attrValues) {
        DealDetailStructAttrModuleVO dealDetailStructAttrModuleVO = new DealDetailStructAttrModuleVO();
        dealDetailStructAttrModuleVO.setAttrName(attrName);
        dealDetailStructAttrModuleVO.setAttrValues(attrValues);
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        dealDetailModuleVO.setDealStructAttrsModel1(Lists.newArrayList(dealDetailStructAttrModuleVO));
        return dealDetailModuleVO;
    }
}
