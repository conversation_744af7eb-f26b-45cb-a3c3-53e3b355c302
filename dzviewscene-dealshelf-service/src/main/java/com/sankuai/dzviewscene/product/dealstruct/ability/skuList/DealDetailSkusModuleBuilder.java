package com.sankuai.dzviewscene.product.dealstruct.ability.skuList;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.IVPoint;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.douhu.DealDetailDouhuFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.*;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.*;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * created by leimengdan in 2023/1/3
 */
@Ability(code = DealDetailSkusModuleBuilder.CODE,
        name = "服务项目模块构造能力",
        description = "团购详情模块服务项目模块构造能力，该能力只对外暴露一个变化点，统一在这个变化点中构造服务项目模块",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE,
                DealDetailDouhuFetcher.CODE
        }
)
public class DealDetailSkusModuleBuilder extends PmfAbility<List<DealDetailSkuListModuleGroupModel>, DealDetailSkuListsParam, DealDetailSkusModuleCfg> implements ModuleStrategy {

    public static final String CODE = "dealDetailSkusModuleBuilder";

    /**
     * 组装策略实现
     *
     * @param
     * @return
     */
    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        //获取构造好的多个sku列表组
        List<DealDetailSkuListModuleGroupModel> dealDetailSkuListModuleGroupModels = activityCxt.getSource(DealDetailSkusModuleBuilder.CODE);
        if (CollectionUtils.isEmpty(dealDetailSkuListModuleGroupModels)) {
            return null;
        }
        //按照config配置的组名获取对应的sku列表组
        DealDetailSkuListModuleGroupModel groupModel = dealDetailSkuListModuleGroupModels.stream().filter(group -> Objects.nonNull(group) && ObjectUtils.equals(group.getGroupName(), config)).findFirst().orElse(null);
        if (groupModel == null || (CollectionUtils.isEmpty(groupModel.getDealSkuGroupModuleVOS())
                && StringUtils.isBlank(groupModel.getDescModel()))) {
            return null;
        }
        //组装DealDetailModuleVO
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        dealDetailModuleVO.setSkuGroupsModel1(groupModel.getDealSkuGroupModuleVOS());
        dealDetailModuleVO.setSubTitle(groupModel.getGroupSubtitle());
        dealDetailModuleVO.setExtraExplain(groupModel.getExtraExplain());
        dealDetailModuleVO.setJumpUrl(groupModel.getJumpUrl());
        dealDetailModuleVO.setShowNum(groupModel.getShowNum());
        dealDetailModuleVO.setFoldStr(groupModel.getFoldStr());
        dealDetailModuleVO.setDotType(groupModel.getDotType());
        dealDetailModuleVO.setDescModel(groupModel.getDescModel());
        if (StringUtils.isNotBlank(groupModel.getGroupTitle())) {
            dealDetailModuleVO.setName(groupModel.getGroupTitle());
        }
        return dealDetailModuleVO;
    }

    /**
     * 构造能力实现
     *
     * @param
     * @return
     */
    @Override
    public CompletableFuture<List<DealDetailSkuListModuleGroupModel>> build(ActivityCxt activityCxt, DealDetailSkuListsParam dealDetailSkuListsParam, DealDetailSkusModuleCfg dealDetailSkusModuleCfg) {
        //获取团单所有信息数据
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        DealDetailInfoModel dealDetailInfoModel = CollectUtils.firstValue(dealDetailInfoModels);
        //有效性检验
        if (!isValidDealDetail(dealDetailInfoModel, dealDetailSkusModuleCfg)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        //获取变化点列表
        List<IVPoint> skuListModuleVPs = findVPoints(activityCxt, SkuListModuleVP.CODE);
        if (CollectionUtils.isEmpty(skuListModuleVPs)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        List<DouhuResultModel> douhuResultModels = activityCxt.getSource(DealDetailDouhuFetcher.CODE);
        //利用变化点列表构造sku列表组列表（每个变化点都可以构造出多个sku列表组）
        List<DealDetailSkuListModuleGroupModel> dealDetailSkuListModuleGroupModelList = new ArrayList<>();
        for (IVPoint ivPoint : skuListModuleVPs) {
            SkuListModuleVP<?> skuListModuleVP = (SkuListModuleVP) ivPoint;
            if (skuListModuleVP == null) {
                continue;
            }
            List<DealDetailSkuListModuleGroupModel> dealDetailSkuListModuleGroupModels = skuListModuleVP.execute(activityCxt, SkuListModuleVP.Param.builder().dealDetailInfoModel(dealDetailInfoModel)
                    .douhuResultModels(douhuResultModels).build());
            if (CollectionUtils.isEmpty(dealDetailSkuListModuleGroupModels)) {
                continue;
            }
            dealDetailSkuListModuleGroupModelList.addAll(dealDetailSkuListModuleGroupModels);
        }
        return CompletableFuture.completedFuture(dealDetailSkuListModuleGroupModelList);
    }

    private boolean isValidDealDetail(DealDetailInfoModel detailModel, DealDetailSkusModuleCfg dealDetailSkusModuleCfg) {
        if (dealDetailSkusModuleCfg != null && dealDetailSkusModuleCfg.isNonNeedDealDetail()) {
            return true;
        }
        if (detailModel == null || detailModel.getDealDetailDtoModel() == null || detailModel.getDealDetailDtoModel().getSkuUniStructuredDto() == null) {
            return false;
        }
        return true;
    }

}
