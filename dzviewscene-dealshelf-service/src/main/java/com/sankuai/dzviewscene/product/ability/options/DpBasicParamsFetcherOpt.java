package com.sankuai.dzviewscene.product.ability.options;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.nr.atom.cache.CacheCompositeAtomService;
import com.sankuai.dzviewscene.nr.atom.cache.CacheGrayUtils;
import com.sankuai.dzviewscene.nr.atom.cache.CacheMethodEnum;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreSyncHandlerVP;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.enums.ExtContextEnum;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.utils.CompletableFutureUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.ModelUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.enums.FieldsEnum;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2023/7/19 3:52 下午
 * ExtContextEnum可能有没补充的基础功能，详细可以搬运productshelf里面的ExtContextAbility。没时间一个个搬运了。
 */
@VPointOption(name = "点评侧基础参数取数fetcher", description = "如门店、城市等", code = DpBasicParamsFetcherOpt.CODE)
public class DpBasicParamsFetcherOpt extends PreSyncHandlerVP<DpBasicParamsFetcherOpt.Config> {

    public static final String CODE = "DpBasicParamsFetcherOpt";
    private static final String BACK_CAT_FIELD = "backMainCategoryPath";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Resource
    private CacheCompositeAtomService cacheCompositeAtomService;

    @Override
    public Map<String, Object> compute(ActivityCxt ctx, Param param, Config config) {
        List<Integer> needFields = param.getNeedFields();
        int platform = param.getPlatform();
        if (PlatformUtil.isMT(platform)) {
            return null;
        }
        long dpUserId = ParamsUtil.getLongSafely(ctx, PmfConstants.Params.dpUserId);
        long dpPoiIdL = ParamsUtil.getLongSafely(ctx, PmfConstants.Params.dpPoiIdL);
        int dpCityId = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.dpCityId);
        CompletableFuture<Long> mtPoiIdLCf = getMtPoiId(dpPoiIdL, ifNoNeed(needFields, ExtContextEnum.MT_SHOP_ID));
        CompletableFuture<Integer> mtCityIdCf = getMtCityId(dpCityId, dpPoiIdL, ifNoNeed(needFields, ExtContextEnum.MT_CITY_ID));
        CompletableFuture<ShopM> ctxShopCf = loadShop(dpPoiIdL, ctx, ifNoNeed(needFields, ExtContextEnum.SHOP_INFO));
        CompletableFuture<Integer> shopDpCityIdCf = ctxShopCf.thenApply(shopMObject -> loadDpCityId(shopMObject, ifNoNeed(needFields, ExtContextEnum.SHOP_DP_CITY_ID)));
        CompletableFuture<Integer> shopMtCityIdCf = ctxShopCf.thenCompose(ctxShop -> loadShopMtCityId(ctxShop, ctx, ifNoNeed(needFields, ExtContextEnum.SHOP_MT_CITY_ID)));
        CompletableFuture<Long> mtUserIdCf = covert2MtUserId(dpUserId, ifNoNeed(needFields, ExtContextEnum.MT_USER_ID));
        CompletableFuture<List<Integer>> shopBackCatsCf = loadShopBackCategory(dpPoiIdL, ctx, ifNoNeed(needFields, ExtContextEnum.SHOP_BACK_CATS));
        Map<String, CompletableFuture<Object>> resultMap = Maps.newHashMap();
        resultMap.put(PmfConstants.Params.dpPoiIdL, CompletableFuture.completedFuture(dpPoiIdL));
        resultMap.put(PmfConstants.Params.dpCityId, CompletableFuture.completedFuture(dpCityId));
        resultMap.put(PmfConstants.Params.mtPoiIdL, CompletableFutureUtil.covert2ObjCf(mtPoiIdLCf));
        resultMap.put(PmfConstants.Params.mtPoiId, mtPoiIdLCf.thenApply(NumberUtils::objToInt));
        resultMap.put(PmfConstants.Params.mtCityId, CompletableFutureUtil.covert2ObjCf(mtCityIdCf));
        resultMap.put(PmfConstants.Params.shopDpCityId, CompletableFutureUtil.covert2ObjCf(shopDpCityIdCf));
        resultMap.put(PmfConstants.Params.shopMtCityId, CompletableFutureUtil.covert2ObjCf(shopMtCityIdCf));
        resultMap.put(PmfConstants.Params.mtUserId, CompletableFutureUtil.covert2ObjCf(mtUserIdCf));
        resultMap.put(PmfConstants.Params.shopBackCatIds, CompletableFutureUtil.covert2ObjCf(shopBackCatsCf));
        resultMap.put(PmfConstants.Params.dpShopM, CompletableFutureUtil.covert2ObjCf(ctxShopCf));
        return CompletableFutureUtil.each(resultMap).join();
    }

    private CompletableFuture<Long> covert2MtUserId(long dpUserId, boolean ifNoNeed) {
        if (ifNoNeed) {
            return CompletableFuture.completedFuture(null);
        }
        return compositeAtomService.getUserInfoByDpUserId(dpUserId).thenApply(relation -> {
            if (relation == null) {
                return 0L;
            }
            return relation.getMtRealUserId();
        });
    }

    private boolean ifNoNeed(List<Integer> needFields, ExtContextEnum target) {
        return !needFields.contains(target.getType());
    }

    private CompletableFuture<Long> getMtPoiId(long dpPoiIdL, boolean ifNoNeed) {
        if (ifNoNeed) {
            return CompletableFuture.completedFuture(null);
        }
        boolean cacheSwitch = CacheGrayUtils.graySwitch(dpPoiIdL, VCPlatformEnum.DP.getType(), CacheMethodEnum.SHOP_ID_DP_2_MT.getCode());
        if (cacheSwitch) {
            return cacheCompositeAtomService.getMtByDpPoiIdL(dpPoiIdL).thenApply(id -> Optional.ofNullable(id).orElse(0L));
        }
        return compositeAtomService.getMtByDpPoiIdL(dpPoiIdL).thenApply(id -> Optional.ofNullable(id).orElse(0L));
    }

    private CompletableFuture<Integer> getMtCityId(int dpCityId, long dpShopId, boolean ifNoNeed) {
        if (ifNoNeed) {
            return CompletableFuture.completedFuture(null);
        }
        boolean cacheSwitch = CacheGrayUtils.graySwitch(dpShopId, VCPlatformEnum.DP.getType(), CacheMethodEnum.CITY_ID_DP_2_MT.getCode());
        if (cacheSwitch) {
            return cacheCompositeAtomService.getMtCityIdByDp(dpCityId).thenApply(id -> id == null ? 0 : id);
        }
        return compositeAtomService.getMtCityIdByDp(dpCityId).thenApply(id -> id == null ? 0 : id);
    }

    private Integer loadDpCityId(Object shopMObject, boolean ifNoNeed) {
        if (shopMObject == null || ifNoNeed) {
            return null;
        }
        ShopM shopM = (ShopM)shopMObject;
        return shopM.getCityId();
    }

    private CompletableFuture<Integer> loadShopMtCityId(ShopM ctxShop, ActivityCxt ctx, boolean ifNoNeed) {
        int shopMtCityId = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.shopMtCityId);
        if (shopMtCityId > 0) {
            return CompletableFuture.completedFuture(shopMtCityId);
        }
        if (ctxShop == null || ctxShop.getCityId() <= 0 || ifNoNeed) {
            return CompletableFuture.completedFuture(0);
        }
        return getMtCityId(ctxShop.getCityId(), ctxShop.getLongShopId(), false);
    }

    private CompletableFuture<ShopM> loadShop(long dpPoiId, ActivityCxt ctx, boolean ifNoNeed) {
        ShopM ctxShopM = ParamsUtil.getValue(ctx, PmfConstants.Ctx.ctxShop, null);
        if (ctxShopM != null || dpPoiId <= 0 || ifNoNeed) {
            return CompletableFuture.completedFuture(ctxShopM);
        }
        Cat.logEvent("RpcMonitor", "DpBasicParamsFetcherOpt.loadShop");
        return compositeAtomService.findShopsByDpShopIds(buildDpPoiRequest(dpPoiId)).thenApply(dpPoiList -> {
            if (CollectionUtils.isEmpty(dpPoiList)) {
                return null;
            }
            DpPoiDTO dpPoiDTO = dpPoiList.get(0);
            ShopM shopM = new ShopM();
            shopM.setLongShopId(Optional.ofNullable(dpPoiDTO.getShopId()).orElse(0L));
            shopM.setShopId(Optional.ofNullable(dpPoiDTO.getShopId()).orElse(0L).intValue());
            shopM.setShopUuid(Optional.ofNullable(dpPoiDTO.getUuid()).orElse(StringUtils.EMPTY));
            shopM.setShopName(Optional.ofNullable(dpPoiDTO.getShopName()).orElse(StringUtils.EMPTY));
            shopM.setShopType(Optional.ofNullable(dpPoiDTO.getShopType()).orElse(0));
            shopM.setCategory(Optional.ofNullable(dpPoiDTO.getMainCategoryId()).orElse(0));
            shopM.setBackCategory(ModelUtils.extractBackCat(dpPoiDTO));
            shopM.setLat(Optional.ofNullable(dpPoiDTO.getLat()).orElse(0d));
            shopM.setLng(Optional.ofNullable(dpPoiDTO.getLng()).orElse(0d));
            shopM.setCityId(Optional.ofNullable(dpPoiDTO.getCityId()).orElse(0));
            shopM.setStatus(Optional.ofNullable(dpPoiDTO.getPower()).orElse(0));
            return shopM;
        });
    }

    private CompletableFuture<List<Integer>> loadShopBackCategory(long dpPoiId, ActivityCxt ctx, boolean ifNoNeed) {
        if (dpPoiId <= 0 || ifNoNeed) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        ShopM ctxShopM = ParamsUtil.getValue(ctx, PmfConstants.Ctx.ctxShop, null);
        if (ctxShopM != null && CollectionUtils.isNotEmpty(ctxShopM.getBackCategory())) {
            return CompletableFuture.completedFuture(ctxShopM.getBackCategory());
        }
        return compositeAtomService.findShopsByDpShopIds(buildDpPoiRequest(dpPoiId)).thenApply(dpPoiList -> {
            if (CollectionUtils.isEmpty(dpPoiList)) {
                return Lists.newArrayList();
            }
            DpPoiDTO dpPoiDTO = dpPoiList.get(0);
            return ModelUtils.extractBackCat(dpPoiDTO);
        });
    }

    private DpPoiRequest buildDpPoiRequest(long dpPoiId) {
        DpPoiRequest dpPoiRequest = new DpPoiRequest();
        dpPoiRequest.setShopIds(Lists.newArrayList(dpPoiId));
        List<String> fields = Lists.newArrayList(FieldsEnum.SHOP_SERVER.getFields());
        fields.add(BACK_CAT_FIELD);
        dpPoiRequest.setFields(fields);
        return dpPoiRequest;
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
