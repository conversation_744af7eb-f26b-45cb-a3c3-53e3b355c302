package com.sankuai.dzviewscene.product.dealstruct.options;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.douhu.DealDetailDouhuFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.DealDetailSkuListAfterProcessingVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.*;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/16 9:43 上午
 */
@VPointOption(name = "宠物医院团购详情sku货列表后置处理默认变化点", description = "宠物医院团购详情sku货列表后置处理默认变化点",code = PetHospitalDealDetailSkuListAfterProcessingVPO.CODE, isDefault = false)
public class PetHospitalDealDetailSkuListAfterProcessingVPO extends DealDetailSkuListAfterProcessingVP<PetHospitalDealDetailSkuListAfterProcessingVPO.Config> {

    public static final String CODE = "PetHospitalDealDetailSkuListAfterProcessingVPO";

    private static final String VACCINE_SKU_CATEGORY = "疫苗接种";

    private static final String OTHER_VACCINE_SKU_CATEGORY = "其他疫苗";

    private static final String SKU_CATEGORY_ATTR_NAME = "货类别";

    private static final String VACCINE_TYPE_ATTR_NAME = "疫苗类型";

    private static final String VACCINE_BRAND_ATTR_NAME = "vaccineBrand";

    private static final String PRICE_UNIT = "元";

    private static final String CONTAIN_ITEM = "包含项目";


    private static final String OPTIONAL_ITEM = "可选项目";

    private static final String OTHER_ITEM = "其他项目";

    @Override
    public List<DealSkuVO> compute(ActivityCxt context, Param param, Config config) {
        //1.获取数据源
        List<DealSkuVO> dealSkuVOS = param.getDealSkuVOS();
        if (CollectionUtils.isEmpty(dealSkuVOS)) {
            return null;
        }
        List<DealSkuVO> resultDealSkuVOList = new ArrayList<>();
        //2.构造sku列表
        List<DealSkuVO> commonDealSkuVO = convertSkuCategory2SkuListMap2DealSkuVOList(dealSkuVOS, config, param.isMustGroup(), context);
        if (CollectionUtils.isNotEmpty(commonDealSkuVO)) {
            resultDealSkuVOList.addAll(commonDealSkuVO);
        }
        return resultDealSkuVOList;
    }

    private DealSkuVO buildDealSkuVO(String title, String copies, String price, List<DealSkuItemVO> dealSkuItemVOS, PopUpWindowVO popUpWindowVO) {
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(title);
        dealSkuVO.setCopies(copies);
        dealSkuVO.setPrice(price);
        dealSkuVO.setItems(dealSkuItemVOS);
        dealSkuVO.setPopup(popUpWindowVO);
        return dealSkuVO;
    }

    private String getVaccineType(List<DealSkuItemVO> items) {
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = items.stream().filter(item -> VACCINE_TYPE_ATTR_NAME.equals(item.getName())).findFirst().orElse(null);
        if (dealSkuItemVO == null) {
            return null;
        }
        return dealSkuItemVO.getValue();
    }

    public String getVaccineBrand(List<DealSkuItemVO> items) {
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = items.stream().filter(item -> VACCINE_BRAND_ATTR_NAME.equals(item.getName())).findFirst().orElse(null);
        if (dealSkuItemVO == null) {
            return null;
        }
        return dealSkuItemVO.getValue();
    }

    private DealSkuItemVO buildDealSkuItemVO(String name, String value, List<SkuAttrAttrItemVO> valueAttrs, int type) {
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setValue(value);
        dealSkuItemVO.setValueAttrs(valueAttrs);
        dealSkuItemVO.setType(type);
        return dealSkuItemVO;
    }

    private Map<String, List<DealSkuVO>> getCommonSkuCategory2SkuListMap(List<DealSkuVO> dealSkuVOS, Config config) {
        Map<String, List<DealSkuVO>> skuCategory2SkuListMap = new LinkedHashMap<>();
        for (DealSkuVO dealSkuVO : dealSkuVOS) {
            String skuCategory = getSkuCategory(dealSkuVO);
            //其他疫苗sku展示在疫苗接种分类下
            if (OTHER_VACCINE_SKU_CATEGORY.equals(skuCategory)) {
                skuCategory = VACCINE_SKU_CATEGORY;
            }
            //配置了分类，按照配置进行sku分类
            if(MapUtils.isNotEmpty(config.getSkuListName2SkuCategory())){
                skuCategory = getSkuCategoryFromConfig(config.getSkuListName2SkuCategory(), skuCategory);
            }
            //按照sku类别进行分类
            if (!skuCategory2SkuListMap.containsKey(skuCategory) || skuCategory2SkuListMap.get(skuCategory) == null) {
                List<DealSkuVO> dealSkuVOList = new ArrayList<>();
                dealSkuVOList.add(dealSkuVO);
                skuCategory2SkuListMap.put(skuCategory, dealSkuVOList);
                continue;
            }
            skuCategory2SkuListMap.get(skuCategory).add(dealSkuVO);
        }
        return skuCategory2SkuListMap;
    }

    private String getSkuCategoryFromConfig(Map<String, List<String>> skuListName2SkuCategory, String skuCategory){
        return skuListName2SkuCategory
                .entrySet()
                .stream()
                .filter(entry -> entry.getValue().contains(skuCategory))
                .findFirst()
                .map(Map.Entry::getKey)
                .orElse(OTHER_ITEM);
    }

    private String getSkuCategory(DealSkuVO dealSkuVO) {
        if (dealSkuVO == null || CollectionUtils.isEmpty(dealSkuVO.getItems())) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = dealSkuVO.getItems().stream().filter(item -> SKU_CATEGORY_ATTR_NAME.equals(item.getName())).findFirst().orElse(null);
        if (dealSkuItemVO == null) {
            return null;
        }
        return dealSkuItemVO.getValue();
    }

    private List<DealSkuVO> convertSkuCategory2SkuListMap2DealSkuVOList(List<DealSkuVO> dealSkuVOS, Config config, boolean isMustSkuGroup, ActivityCxt context) {
        Map<String, List<DealSkuVO>> skuCategory2SkuListMap = getCommonSkuCategory2SkuListMap(dealSkuVOS, config);
        if (MapUtils.isEmpty(skuCategory2SkuListMap)) {
            return null;
        }
        return skuCategory2SkuListMap.entrySet().stream().map(entry -> {
            String title = entry.getKey();
            List<DealSkuItemVO> dealSkuItemVOS = getDealSkuItemVOs(entry.getValue(), config, isMustSkuGroup, title, context);
            PopUpWindowVO popUpWindowVO = buildPopupWindowVO(context, config, title, entry.getValue());
            return buildDealSkuVO(title, null, null, dealSkuItemVOS, popUpWindowVO);
        }).filter(vo -> vo != null).collect(Collectors.toList());
    }

    private List<DealSkuItemVO> getDealSkuItemVOs(List<DealSkuVO> preDealSkuVOs, Config config, boolean isMustSkuGroup, String externalSkuCategory, ActivityCxt context) {
        if (CollectionUtils.isEmpty(preDealSkuVOs)) {
            return null;
        }
        if(config.getClassifySkuItemByCategory()){
            return classifySkuItemByCategory(preDealSkuVOs, config, context);
        }
        List<SkuAttrAttrItemVO> valueAttrs = buildSkuAttrAttrItemVO(preDealSkuVOs, config, externalSkuCategory, context);
        if (CollectionUtils.isEmpty(valueAttrs)) {
            return null;
        }
        String name = isMustSkuGroup ? CONTAIN_ITEM : OPTIONAL_ITEM;
        if(StringUtils.isNotEmpty(config.getSkuItemName())){
            //配置了skuItemName，取skuItemName
            name = config.getSkuItemName();
        }
        DealSkuItemVO dealSkuItemVO = buildDealSkuItemVO(name, null, valueAttrs, config.getSkuAttrShowType());
        return Lists.newArrayList(dealSkuItemVO);
    }

    private PopUpWindowVO buildPopupWindowVO(ActivityCxt context, Config config, String title, List<DealSkuVO> value) {
        if (MapUtils.isEmpty(config.getPopUpTileSubTitleMap())){
            return null;
        }
        String subTitle = config.getPopUpTileSubTitleMap().get(title);
        PopUpWindowVO popUpWindowVO = new PopUpWindowVO();
        List<CommonAttrVO> popInfos = buildPopupInfos(value);
        if (CollectionUtils.isEmpty(popInfos)){
            return null;
        }
        popUpWindowVO.setInfos(popInfos);
        popUpWindowVO.setContent(subTitle);
        popUpWindowVO.setTitle(title);
        popUpWindowVO.setIcon(config.getPopUpDefaultIcon());
        return popUpWindowVO;
    }

    private List<CommonAttrVO> buildPopupInfos(List<DealSkuVO> skuVOS) {
        List<CommonAttrVO> details = Lists.newArrayList();
        for (DealSkuVO skuVO : skuVOS){
            if (skuVO == null || CollectionUtils.isEmpty(skuVO.getItems())){
                continue;
            }
            Optional<DealSkuItemVO> descOpt = skuVO.getItems().stream().filter(entity -> entity.getName().equals("projectDesc")).findAny();
            if (descOpt.isPresent()){
                CommonAttrVO commonAttrVO = new CommonAttrVO();
                commonAttrVO.setValue(descOpt.get().getValue());
                commonAttrVO.setName(skuVO.getTitle());
                details.add(commonAttrVO);
            }
        }
        return details;
    }


    private List<DealSkuItemVO> classifySkuItemByCategory(List<DealSkuVO> preDealSkuVOs, Config config, ActivityCxt context){
        Map<String, List<DealSkuVO>> category2DealSkuList = new HashMap<>();
        for(DealSkuVO dealSkuVO : preDealSkuVOs){
            String skuCategory = getSkuCategory(dealSkuVO);
            //按照sku类别进行分类
            if (!category2DealSkuList.containsKey(skuCategory) || category2DealSkuList.get(skuCategory) == null) {
                List<DealSkuVO> dealSkuVOList = new ArrayList<>();
                dealSkuVOList.add(dealSkuVO);
                category2DealSkuList.put(skuCategory, dealSkuVOList);
                continue;
            }
            category2DealSkuList.get(skuCategory).add(dealSkuVO);
        }
        if(MapUtils.isEmpty(category2DealSkuList)){
            return new ArrayList<>();
        }
        return category2DealSkuList.entrySet()
                .stream()
                .map(entry -> {
                    List<DealSkuVO> dealSkus = entry.getValue();
                    List<SkuAttrAttrItemVO> skuAttrAttrItemVOList = buildSkuAttrAttrItemVO(dealSkus, config, entry.getKey(), context);
                    if(CollectionUtils.isEmpty(skuAttrAttrItemVOList)){
                        return null;
                    }
                    return buildDealSkuItemVO(entry.getKey(), null, skuAttrAttrItemVOList, config.getSkuAttrShowType());
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<SkuAttrAttrItemVO> buildSkuAttrAttrItemVO(List<DealSkuVO> dealSkus, Config config, String externalSkuCategory, ActivityCxt context){
        if(CollectionUtils.isEmpty(dealSkus)){
            return null;
        }
        return dealSkus.stream().flatMap(vo -> {
            if (vo == null) {
                return null;
            }
            if(splitSkuItem(vo, config)){
                return splitSkuItemIntoSkuAttrList(vo, config, context).stream();
            }
            SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
            skuAttrAttrItemVO.setName(getSkuAttrItemVOTitle(vo, vo.getTitle(), config, externalSkuCategory));
            if (StringUtils.isNotEmpty(vo.getCopies())) {
                skuAttrAttrItemVO.setInfo(getSkuAttrInfos(vo.getCopies(), vo.getPrice()));
            }
            String skuCategory = getSkuCategory(vo);
            List<CommonAttrVO> commonAttrVOS = getCommonAttrVOS(vo.getItems(), config, skuCategory, vo.getTitle(), context);
            skuAttrAttrItemVO.setValues(commonAttrVOS);
            return Lists.newArrayList(skuAttrAttrItemVO).stream();
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * @param dealSkuVO
     * @param config
     * 特殊逻辑：宠物体检标准团单，服务项目类别为传染病筛查的服务项目，需要根据【检测项】的服务项目属性勾选的数量，
     * 拆分为对应数量的服务项目进行展示，并将服务项目属性值【检测项】的值作为服务项目名称进行展示（B端已上线，短时间不会修改，需要我们兼容(Ｔ▽Ｔ)）
     */
    private List<SkuAttrAttrItemVO> splitSkuItemIntoSkuAttrList(DealSkuVO dealSkuVO, Config config, ActivityCxt context){
        String inspection = getSkuItemValueBySkuItemName(dealSkuVO.getItems(), "inspectionItems");
        List<String> inspectionItems = Lists.newArrayList(inspection.split("、"));
        return inspectionItems
                .stream()
                .map(inspectionItem -> {
                    SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
                    skuAttrAttrItemVO.setName(inspectionItem);
                    if (StringUtils.isNotEmpty(dealSkuVO.getCopies())) {
                        skuAttrAttrItemVO.setInfo(getSkuAttrInfos(dealSkuVO.getCopies(), dealSkuVO.getPrice()));
                    }
                    String skuCategory = getSkuCategory(dealSkuVO);
                    List<CommonAttrVO> commonAttrVOS = getCommonAttrVOS(dealSkuVO.getItems(), config, skuCategory, inspectionItem, context);
                    skuAttrAttrItemVO.setValues(commonAttrVOS);
                    return skuAttrAttrItemVO;
                })
                .collect(Collectors.toList());
    }
    private boolean splitSkuItem(DealSkuVO dealSkuVO, Config config){
        String skuCategory = getSkuCategory(dealSkuVO);
        String inspectionItems = getSkuItemValueBySkuItemName(dealSkuVO.getItems(), "inspectionItems");
        return config.getSplitDealSkuItem() && StringUtils.equals(skuCategory, "传染病筛查") && StringUtils.isNotEmpty(inspectionItems);
    }
    private List<String> getSkuAttrInfos(String copies, String price) {
        List<String> infos = new ArrayList<>();
        if (StringUtils.isNotEmpty(copies)) {
            infos.add(copies);
        }
        if (StringUtils.isNotEmpty(price)) {
            infos.add(price);
        }
        if (CollectionUtils.isEmpty(infos)) {
            return null;
        }
        return infos;
    }

    /**
     * 当货名称是配置列表中的货名称时，将检查项拼接在货名称后面，返回：sku的title（检查项属性值），其中检查项属名为inspectionItems
     * 配置了服务项目属性，返回服务项目属性
     * 其余情况返回sku的title；
     *@param
     *@return
     */
    private String getSkuAttrItemVOTitle(DealSkuVO dealSkuVO, String skuTitle, Config config, String skuCategory) {
        //如果有配置指定修改标题，则优先使用配置
        if (MapUtils.isNotEmpty(config.getSkuCategory2TitleCfg()) && config.getSkuCategory2TitleCfg().get(skuCategory) != null) {
            return getSkuTitleWithCfg(dealSkuVO, config.getSkuCategory2TitleCfg().get(skuCategory));
        }
        if(MapUtils.isNotEmpty(config.getSkuName2SkuAttrName()) && config.getSkuName2SkuAttrName().containsKey(skuTitle)){
            String skuItemValueBySkuItemName = getSkuItemValueBySkuItemName(dealSkuVO.getItems(), config.getSkuName2SkuAttrName().get(skuTitle));
            return StringUtils.isNotEmpty(skuItemValueBySkuItemName) ? skuItemValueBySkuItemName : skuTitle;
        }
        if (CollectionUtils.isEmpty(config.getSterilizationSkuNameList()) || !config.getSterilizationSkuNameList().contains(skuTitle)) {
            return skuTitle;
        }
        //检查项
        String examineItem = getSkuItemValueBySkuItemName(dealSkuVO.getItems(), "inspectionItems");
        if (StringUtils.isEmpty(examineItem)) {
            return skuTitle;
        }
        if(StringUtils.equals(skuTitle, "彩超")){
            return String.format("%s%s",examineItem, skuTitle);
        }
        return String.format("%s（%s）", skuTitle, examineItem);
    }

    public List<CommonAttrVO> getCommonAttrVOS(List<DealSkuItemVO> items, Config config, String skuCategory, String skuName, ActivityCxt context) {
        List<CommonAttrVO> result = new ArrayList<>();
        //sku属性对应固定CommonAttrVO列表
        List<CommonAttrVO> skuAttr2CommonList = getSkuAttr2CommonList(skuName, config.getSkuName2Desc());
        result.addAll(skuAttr2CommonList);
        //疫苗类型CommonAttrVO列表
        List<CommonAttrVO> vaccineCommonAttrVOs = getVaccineCommonAttrVOS(items, config.getVaccineSpecificationModels());
        result.addAll(vaccineCommonAttrVOs);
        //绝育类型CommonAttrVO列表
        List<CommonAttrVO> sterilizationDescCommonAttrVOs = getSterilizationDescCommonAttrVOs(config.getSterilizationSkuName2DescMap(), skuName, config, context);
        result.addAll(sterilizationDescCommonAttrVOs);
        //sku属性CommonAttrVO列表
        List<CommonAttrVO> skuAttrsCommonAttrVOs = getSkuAttrCommonAttrVOs(items, config.getPhysicalExamSkuCategory2AttrsMap(), skuCategory);
        result.addAll(skuAttrsCommonAttrVOs);
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        return result;
    }

    /**
     * 根据服务项目名称返回配置的描述文案
     */
    private List<CommonAttrVO> getSkuAttr2CommonList(String skuName, Map<String, List<DescModel>> skuAttrName2Desc){
        if(MapUtils.isEmpty(skuAttrName2Desc) || !skuAttrName2Desc.containsKey(skuName)){
            return new ArrayList<>();
        }
        return skuAttrName2Desc.get(skuName)
                .stream()
                .filter(Objects::nonNull)
                .map(descModel -> buildCommonAttrVO(descModel.getTitle(), descModel.getDesc()))
                .collect(Collectors.toList());
    }

    /**
     * 对于绝育标准团单，按照货名称获取对应的描述文案拼接在货属性列表中进行展示，展示格式为：作用：描述文案
     *
     * @param
     * @param config
     * @return
     */
    private List<CommonAttrVO> getSterilizationDescCommonAttrVOs(Map<String, String> skuName2DescMap, String skuName, Config config, ActivityCxt context) {
        return Lists.newArrayList();
    }

    /**
     * 获取sku属性CommonAttrVO列表
     *@param
     *@return
     */
    private List<CommonAttrVO> getSkuAttrCommonAttrVOs(List<DealSkuItemVO> items, Map<String, List<PhysicalExamSkuAttrModel>> physicalExamSkuCategory2AttrsMap, String skuCategory) {
        if (MapUtils.isEmpty(physicalExamSkuCategory2AttrsMap) || CollectionUtils.isEmpty(physicalExamSkuCategory2AttrsMap.get(skuCategory))) {
            return new ArrayList<>();
        }
        List<PhysicalExamSkuAttrModel> physicalExamSkuAttrModels = physicalExamSkuCategory2AttrsMap.get(skuCategory);
        return physicalExamSkuAttrModels.stream().map(model -> buildCommonAttrVO(model.getAttrTitle(), getSkuItemValueBySkuItemName(items, model.getAttrName()))).filter(vo -> vo != null).collect(Collectors.toList());
    }

    private String getSkuItemValueBySkuItemName(List<DealSkuItemVO> items, String name) {
        if (CollectionUtils.isEmpty(items) || StringUtils.isEmpty(name)) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = items.stream().filter(item -> name.equals(item.getName())).findFirst().orElse(null);
        if (dealSkuItemVO == null) {
            return null;
        }
        return dealSkuItemVO.getValue();
    }

    private CommonAttrVO buildCommonAttrVO(String name, String value) {
        if (StringUtils.isEmpty(name) || StringUtils.isEmpty(value)) {
            return null;
        }
        CommonAttrVO commonAttrVO =  new CommonAttrVO();
        commonAttrVO.setName(name);
        commonAttrVO.setValue(value);
        return commonAttrVO;
    }

    /**
     * 获取疫苗类型CommonAttrVO属性列表：根据vaccineType的值从配置上获取
     *@param
     *@return
     */
    private List<CommonAttrVO> getVaccineCommonAttrVOS(List<DealSkuItemVO> items, List<VaccineSpecificationModel> vaccineSpecificationModels) {
        String vaccineType = getVaccineType(items);
        String vaccineBrand = getVaccineBrand(items);
        if (CollectionUtils.isEmpty(vaccineSpecificationModels) || StringUtils.isEmpty(vaccineType)) {
            return new ArrayList<>();
        }
        VaccineSpecificationModel vaccineSpecificationModel = vaccineSpecificationModels.stream().filter(model -> {
            // 如果配置了疫苗品牌，根据类型和品牌进行匹配展示
            if (StringUtils.isNotBlank(vaccineBrand) && StringUtils.isNotBlank(model.getVaccineBrand())) {
                return vaccineType.equalsIgnoreCase(model.getVaccineType()) && vaccineBrand.equalsIgnoreCase(model.getVaccineBrand());
            }
            // 如果没有配置疫苗品牌，根据类型进行匹配展示(例如狂犬疫苗不需要品牌)
            return vaccineType.equalsIgnoreCase(model.getVaccineType());
        }).findFirst().orElse(null);
        if (vaccineSpecificationModel == null || MapUtils.isEmpty(vaccineSpecificationModel.getSpecificationContentMap())) {
            return new ArrayList<>();
        }
        return vaccineSpecificationModel.getSpecificationContentMap().entrySet().stream().map(entry -> buildCommonAttrVO(entry.getKey(), entry.getValue())).filter(vo -> vo != null).collect(Collectors.toList());
    }

    /**
     * @param dealSkuVO
     * @param config
     * @return
     */
    private String getSkuTitleWithCfg(DealSkuVO dealSkuVO, SkuTitleCfg config) {
        String defTitle = dealSkuVO.getTitle();
        if (StringUtils.isEmpty(config.getFormat()) || CollectionUtils.isEmpty(config.getAttrKeys())) {
            return defTitle;
        }
        List<String> values = config.getAttrKeys().stream()
                .map(key -> getSkuItemValueBySkuItemName(dealSkuVO.getItems(), key))
                .filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(values)) {
            return defTitle;
        }
        //拼接标题和属性
        return String.format(config.getFormat(), defTitle, StringUtils.join(values, config.getSeparator()));
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<VaccineSpecificationModel> vaccineSpecificationModels;
        //绝育标准团单中货名称对应展示的货描述信息
        private Map<String, String> sterilizationSkuName2DescMap;
        //绝育标准团单中将检查项属性拼接在货名称后面的货名称列表
        private List<String> sterilizationSkuNameList;
        private String skuAttrFormat;
        private int skuAttrShowType;
        private Map<String, List<PhysicalExamSkuAttrModel>> physicalExamSkuCategory2AttrsMap;
        private Map<String, List<String>> skuListName2SkuCategory;
        //是否按照货类别进行货分类
        private Boolean classifySkuItemByCategory = false;
        //服务项目分类与服务项目属性名称映射关系
        private Map<String, String> skuCategory2SkuAttrName;
        //体检标准团单货属性值对应展示的货描述信息
        private Map<String, List<DescModel>> skuName2Desc;
        //体检标准团单针对传染病筛查分类的服务项目，需要特殊处理（拆分服务项目）
        private Boolean splitDealSkuItem = false;
        //宠物sku属性名
        private String skuItemName;
        //skuName和展示服务项目属性的关系
        private Map<String, String> skuName2SkuAttrName;
        //skuTitle 的二次拼接规则
        private Map<String, SkuTitleCfg> skuCategory2TitleCfg;
        /**
         * 弹窗实验AB配置，实验完可拿掉
         */
        private List<String> douhuExpIds;;
        /**
         * 弹窗文案副标题映射
         */
        private Map<String,String> popUpTileSubTitleMap;
        /**
         * 弹窗icon
         */
        private String popUpDefaultIcon = "https://p0.meituan.net/ingee/d97872ac6f4f0a1396eede65dee27e9e1265.png";
    }

    @Data
    private static class SkuTitleCfg {
        /**
         * 拼接格式（固定 title 是第一个参数，第二个参数是 attrValue）
         */
        private String format;

        /**
         * 参与拼接的属性key
         */
        private List<String> attrKeys;

        /**
         * 连词符
         */
        private String separator;
    }


    @Data
    private static class DescModel {
        private String title;
        private String desc;
    }

    @Data
    private static class PhysicalExamSkuAttrModel {
        private String attrTitle;
        private String attrName;
    }

    @Data
    public static class VaccineSpecificationModel{
        private String vaccineType;
        private String vaccineBrand;
        private Map<String, String> specificationContentMap;
    }

}
