package com.sankuai.dzviewscene.product.dealstruct.ability.assemble;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleBuilderFactory;
import com.sankuai.dzviewscene.product.dealstruct.ability.desc.DealDetailDescBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.CarDailyMaintainSkuModuleBuilder;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealModuleDetailVO;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.product.shelf.utils.AntiCrawlerUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * @author: created by hang.yu on 2023/7/4 16:45
 */
@Ability(code = CarDailyMaintainDealAssembleAbility.CODE,
        name = "爱车-保养-日常养护团详模块组装能力",
        description = "爱车-保养-日常养护团详模块组装能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                CarDailyMaintainSkuModuleBuilder.CODE,
                DealDetailDescBuilder.CODE
        }
)
public class CarDailyMaintainDealAssembleAbility extends PmfAbility<DealModuleDetailVO, DealDetailAssembleParam, DealDetailAssembleCfg> {

    @Resource
    private ModuleBuilderFactory moduleBuilderFactory;

    public static final String CODE = "carDailyMaintainDealAssembleAbility";

    @Override
    public CompletableFuture<DealModuleDetailVO> build(ActivityCxt activityCxt, DealDetailAssembleParam assembleParam, DealDetailAssembleCfg assembleCfg) {
        // 获取模块详情
        List<DealDetailModuleVO> detailModuleVOList = moduleBuilderFactory.buildModuleListCustomName(activityCxt, assembleParam, assembleCfg.getModuleList());
        DealModuleDetailVO dealModuleDetailVO = buildDealModuleDetailVO(activityCxt, detailModuleVOList);
        return CompletableFuture.completedFuture(dealModuleDetailVO);
    }

    private DealModuleDetailVO buildDealModuleDetailVO(ActivityCxt activityCxt, List<DealDetailModuleVO> dealDetailModuleList) {
        DealModuleDetailVO dealModuleDetailVO = new DealModuleDetailVO();
        dealModuleDetailVO.setModuleList(dealDetailModuleList);
        dealModuleDetailVO.setSceneCode(activityCxt.getSceneCode());
        ActivityContext oldContext = ActivityCtxtUtils.toActivityContext(activityCxt);
        AntiCrawlerUtils.hideProductKeyInfo(dealModuleDetailVO, oldContext);
        return dealModuleDetailVO;
    }
}
