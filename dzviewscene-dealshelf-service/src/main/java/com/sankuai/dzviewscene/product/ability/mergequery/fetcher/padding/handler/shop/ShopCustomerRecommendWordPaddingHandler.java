package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.shop;


import com.dianping.lion.client.Lion;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ContextHandlerResult;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Component
public class ShopCustomerRecommendWordPaddingHandler implements ContextPaddingHandler {

    private static final String BEAUTY_TIME_CARD_GREY_DP_SHOP_ID = "com.sankuai.merchantcard.dzcard.supply.beauty.prepayCard.shelf.dpShopId";

    private static final String BEAUTY_TIME_CARD_GREY_MT_SHOP_ID = "com.sankuai.merchantcard.dzcard.supply.beauty.prepayCard.shelf.mtShopId";

    private static final String RECOMMEND_REASON = "reason";

    private static final String REASON_SPLIT = "#__#__#";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<ContextHandlerResult> padding(ActivityCxt ctx, ContextHandlerResult contextHandlerResult, Map<String, Object> params) {
        if(!checkShelfCondition(ctx)){
            return CompletableFuture.completedFuture(null);
        }
        RecommendParameters recommendParameters = buildRecommendRequest(ctx);
        CompletableFuture<RecommendResult<RecommendDTO>> recommendResultCompletableFuture = compositeAtomService.getRecommendResult(recommendParameters);
        return recommendResultCompletableFuture.thenApply(recommendResult -> {
            if (Objects.isNull(recommendResult) || CollectionUtils.isEmpty(recommendResult.getSortedResult())) {
                return contextHandlerResult;
            }
            List<String> ugcContents = parseRecommendDTO(recommendResult.getSortedResult().get(0));
            fillContextHandlerResult(ugcContents, contextHandlerResult);
            return contextHandlerResult;
        });
    }

    public void fillContextHandlerResult(List<String> customerRecommendWord, ContextHandlerResult contextHandlerResult) {
        if (CollectionUtils.isEmpty(customerRecommendWord)) {
            return;
        }
        contextHandlerResult.setShopCustomerRecommendWord(customerRecommendWord);
    }

    private boolean checkShelfCondition(ActivityCxt activityContext){
        int platform = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.platform);
        long shopId = PlatformUtil.isMT(platform) ? ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.mtPoiIdL) : ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.dpPoiIdL);
        //白名单控制
        List<Long> whiteShopIds = !PlatformUtil.isMT(platform) ? Lion.getList("com.sankuai.merchantcard.dzcard.supply", BEAUTY_TIME_CARD_GREY_DP_SHOP_ID, Long.class)
                : Lion.getList("com.sankuai.merchantcard.dzcard.supply", BEAUTY_TIME_CARD_GREY_MT_SHOP_ID, Long.class);
        if (CollectionUtils.isNotEmpty(whiteShopIds) && !whiteShopIds.contains(shopId)) {
            return false;
        }
        int shelfVersion = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.shelfVersion);
        if (shelfVersion <= 100) {
            return false;
        }
        return true;
    }

    private RecommendParameters buildRecommendRequest(ActivityCxt activityContext) {
        double lat = ParamsUtil.getDoubleSafely(activityContext, ShelfActivityConstants.Params.lat);
        double lng = ParamsUtil.getDoubleSafely(activityContext, ShelfActivityConstants.Params.lng);
        int platform = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.platform);
        long userId = PlatformUtil.isMT(platform) ? ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.mtUserId) : ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.dpUserId);
        long shopId = PlatformUtil.isMT(platform) ? ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.mtPoiIdL) : ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.dpPoiIdL);
        int cityId = PlatformUtil.isMT(platform) ? ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.mtCityId) : ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.dpCityId);
        String deviceId = ParamsUtil.getStringSafely(activityContext, ShelfActivityConstants.Params.deviceId);

        RecommendParameters recommendParameters = new RecommendParameters();
        recommendParameters.setBizId(475);
        recommendParameters.setPlatformEnum(PlatformUtil.isMT(platform) ? PlatformEnum.MT : PlatformEnum.DP);
        recommendParameters.setCityId(cityId);
        recommendParameters.setOriginUserId(String.valueOf(userId));
        if (PlatformUtil.isMT(platform)) {
            recommendParameters.setUuid(deviceId);
        } else {
            recommendParameters.setDpid(deviceId);
        }
        recommendParameters.setLat(lat);
        recommendParameters.setLng(lng);
        recommendParameters.setPageSize(1);
        recommendParameters.setBizParams(buildCardSaysBizParam(shopId));
        return recommendParameters;
    }

    private List<String> parseRecommendDTO(RecommendDTO recommendDTO) {
        if (Objects.isNull(recommendDTO)) {
            return Lists.newArrayList();
        }
        String reason = (String) recommendDTO.getBizData().get(RECOMMEND_REASON);
        if (StringUtils.isEmpty(reason)) {
            return Lists.newArrayList();
        }
        return Arrays.asList(reason.split(REASON_SPLIT));
    }

    private Map<String, Object> buildCardSaysBizParam(long shopId) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("shopIds", String.valueOf(shopId));
        bizParams.put("bizType", "002");
        bizParams.put("recReasonContentType", "001");
        bizParams.put("expType", "999");
        bizParams.put("flowFlag", "002");
        return bizParams;
    }
}
