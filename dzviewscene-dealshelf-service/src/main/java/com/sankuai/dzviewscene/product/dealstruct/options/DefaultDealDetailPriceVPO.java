package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.price.vpoints.DealDetailPriceVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailPriceModel;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/11/25 2:45 下午
 */
@VPointOption(name = "团购详情价格组件默认变化点", description = "团购详情价格组件默认变化点，支持配置", code = DefaultDealDetailPriceVPO.CODE, isDefault = true)
public class DefaultDealDetailPriceVPO extends DealDetailPriceVP<DefaultDealDetailPriceVPO.Config> {

    public static final String CODE = "DefaultDealDetailPriceVP";

    private static final String DEFAULT_SALE_PRICE_TITLE = "团购价";

    private static final String DEFAULT_SALE_PRICE_FORMAT = "%s元";

    private static final String DEFAULT_MARKET_PRICE_TITLE = "总价";

    private static final String DEFAULT_MARKET_PRICE_FORMAT = "%s元";

    @Override
    public DealDetailPriceModel compute(ActivityCxt context, Param param, Config config) {
        String salePrice = param.getSalePrice();
        String marketPrice = param.getMarketPrice();
        int dealId = param.getDealId();
        String salePriceFormat = StringUtils.isEmpty(config.getSalePriceFormat()) ? DEFAULT_SALE_PRICE_FORMAT : config.getSalePriceFormat();
        String salePriceTag = StringUtils.isEmpty(salePrice) ? null : String.format(salePriceFormat, salePrice);
        String salePriceTitle = StringUtils.isEmpty(config.getSalePriceTitle()) ? DEFAULT_SALE_PRICE_TITLE : config.getSalePriceTitle();
        String marketPriceFormat = StringUtils.isEmpty(config.getMarketPriceFormat()) ? DEFAULT_MARKET_PRICE_FORMAT : config.getMarketPriceFormat();
        String marketriceTag = StringUtils.isEmpty(marketPrice) ? null : String.format(marketPriceFormat, marketPrice);
        String marketPriceTitle = StringUtils.isEmpty(config.getMarketPriceTitle()) ? DEFAULT_MARKET_PRICE_TITLE : config.getMarketPriceTitle();
        return buildDealDetailPriceModuleVO(dealId, salePriceTag, salePriceTitle, marketriceTag, marketPriceTitle);
    }

    private DealDetailPriceModel buildDealDetailPriceModuleVO(int dealId, String salePrice, String salePriceTitle, String marketPrice, String marketPriceTitle) {
        if (StringUtils.isEmpty(salePrice) && StringUtils.isEmpty(marketPrice)) {
            return null;
        }
        DealDetailPriceModel dealDetailPriceModel = new DealDetailPriceModel();
        dealDetailPriceModel.setDealId(dealId);
        if (StringUtils.isNotEmpty(salePrice)) {
            dealDetailPriceModel.setSalePrice(salePrice);
            dealDetailPriceModel.setSalePriceTitle(salePriceTitle);
        }
        if (StringUtils.isNotEmpty(marketPrice)) {
            dealDetailPriceModel.setOriginalPrice(marketPrice);
            dealDetailPriceModel.setOriginalPriceTitle(marketPriceTitle);
        }
        return dealDetailPriceModel;
    }

    @Data
    @VPointCfg
    public static class Config {
        private String salePriceTitle;
        private String salePriceFormat;
        private String marketPriceTitle;
        private String marketPriceFormat;
    }
}
