package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query;

public abstract class QueryConstants {
    public interface Params {

        /**
         * 1.【一级参数, List<String>型, 必填】自定义组名
         */
        String groupNames = "groupNames";

        /**
         * 2. 【一级参数, Map<String, Object>型, 必填】指定每组商品召回参数
         */
        String groupParams = "groupParams";

        /**
         * 3. 【二级商品组参数, int型, 可选】分组内的id类型
         */
        String groupIdType = "groupIdType";

        /**
         * 4. 【二级商品组参数, int型, 可选】该组商品的商品类型
         */
        String productType = "productType";

        /**
         * 4. 【二级商品组参数, long型, 可选】该组商品的二级分类id,适用于需要二级分类召回的场景
         */
        String secondCategoryId = "secondCategoryId";


        String queryType = "queryType";

        /**
         * 6.【一级参数, Map<String, List<Integer>>型, 可选】每组商品对应的门店ID列表, 当召回多个门店
         */
        String groupName2ShopIds = "groupName2ShopIds";

        /**
         * 7. 【一级参数, Map<String, List<Integer>型, 可选】每组商品对应的商品ID列表
         */
        String groupName2ProductIds = "groupName2ProductIds";

        /**
         * 8. 分页页大小上限
         */
        String maxPageSize = "maxPageSize";

        /**
         * 9. 【二级参数, Long 型, 可选】该组商品的spu类型, 召回类型泛商品的时候使用
         */
        String spuType = "spuType";
        /**
         * 8. 【二级商品组参数, int型, 可选】召回商品起始索引, 用于分页
         */
        String start = "start";

        /**
         * 9. 【二级商品组参数, int型, 可选】单次召回商品数, 用于分页
         */
        String limit = "limit";

        /**
         * 10. 【二级商品组参数, int型, 可选】单次召回商品数, 用于分页
         */
        String sortItems = "sortItems";

        /**
         * 11. 【二级商品组参数, String型, 可选】召回商品的过滤属性
         */
        String attrQueryName = "attrQueryName";
        /**
         * 12. 【二级商品组参数, String型, 可选】召回商品的过滤值
         */
        String attrQueryValue = "attrQueryValue";

        /**
         * 13. 【二级商品组参数, 可选】范围过滤
         */
        String rangeItems = "rangeItems";

        /**
         * 14. 【二级商品组参数】商品标签ID 用于根据筛选条件搜索召回商品的场景
         */
        String tagId = "tagId";

        /**
         * 15.【筛选参数，可选】排除哪些商品
         */
        String excludeProductIds = "excludeProductIds";

        /**
         * 16. 【二级商品组参数，可选】标记商品组是否需要查询缓存，true-需要，false-不需要
         */
        String shopProductsCache = "shopProductsCache";

        /**
         * 17. KTV 召回 可选 是否需要优惠
         */
        String needPromoTags = "needPromoTags";

        /**
         * 18. 可选 不需要召回的商品组 不定义只要传商品ID则就走非召回逻辑 定义则为指定的商品组不走召回逻辑
         */
        String noQueryGroupNames = "noQueryGroupNames";

        /**
         * 19. 可选 到综推荐团单的bizType
         */
        String recommendBizType = "recommendBizType";

        /**
         * 20.商品组统计信息
         */
        String oneShopProductGroupCount = "oneShopProductGroupCount";

        /**
         * 21.到综推荐场景tab
         */
        String sceneTab = "sceneTab";

        /**
         * 22.到综推荐场景tab
         */
        String paramProductHandlerStatisticsFlag = "paramProductHandlerStatisticsFlag";

        /**
         * 23.泛商品搜索召回接口 商品是否在有效期类型 1为在有效期
         */
        String dateFilterType = "dateFilterType";

        /**
         * 24.推荐召回业务ID
         */
        String recommendBizId = "recommendBizId";

        /**
         * 商品id黑名单
         */
        String productIdBlackList = "productIdBlackList";
    }

    /**
     * 召回类型枚举
     */
    public enum QueryType {
        targetIdQuery(10000, "根据参数置指定id进行召回"),
        citySpuProductsQueryHandler(10001, "根据城市召回一口价标品"),
        poiSpuProductsQueryHandler(10002, "根据门店召回一口价标品"),
        dpCollectProductsQueryHandler(100003, "点评侧收藏召回"),

        poiSaleDealGroupQueryHandler(20000, "根据门店召回在线售卖团单"),
        poiTimesCardQueryHandler(20001, "根据门店召回在线次卡商品"),
        poiPackProductQueryHandler(20002, "根据门店召回在线打包商品");

        public int code;
        public String name;

        QueryType(int code, String name) {
            this.code = code;
            this.name = name;
        }
    }
}
