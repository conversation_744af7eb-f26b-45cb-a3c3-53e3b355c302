package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.AbstractBarSkuCreator;
import com.sankuai.dzviewscene.product.dealstruct.vo.PopUpWindowVO;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/9 4:50 下午
 */
public class DrinksExceptColaSkuCreator extends AbstractBarSkuCreator {

    @Override
    public boolean ideantify(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config) {
        return skuItemDto != null && !config.getColaSkuCateIds().contains(skuItemDto.getProductCategory()) && config.getDrinksSkuCateIds().contains(skuItemDto.getProductCategory());
    }

    @Override
    protected String buildIcon(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config, boolean isSameCategorySku) {
        if (skuItemDto == null) {
            return null;
        }
        return DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), HEAL_PIC_SKU_ATTR_NAME);
    }

    @Override
    protected Map<String, String> getSubtitlesAttrName2FormatMap(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config, boolean isHitDouhu) {
        if (isHitDouhu) {
            return config.getDrinksSubtitlesAttrName2FormatMap();
        }
        //老的，实验全量后删掉
        return new LinkedHashMap<String, String>() {{
            put("quantityAvailable", "%s");
            put("serviceLimit", "%s");
            put("taste", "%s");
            put("alcoholByVolume", "%s%%Vol");
            put("volume", "%smL");
        }};
    }

    @Override
    protected PopUpWindowVO buildPopUpWindowVO(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config, boolean isHitDouhu) {
        return buildPopUpWindowVOForDrinks(skuItemDto, config.getAlcoholByVolumeRange2DocMap(), config.getDrinksPopupAttrConfigModels());
    }

    
    @Override
    protected String extractCopies(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config) {
        //酒水数量, 可能会打空格
        String value =  DealDetailUtils.getAndTrimSkuAttrValue(skuItemDto.getAttrItems(), "quantityAvailable");
        //酒水单位
        String unit =  DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), "quantityUnit");
        unit = StringUtils.isEmpty(unit) ? "份" : unit;
        /**
         * 最外层的份数
         */
        int copies = skuItemDto.getCopies();
        /**
         * 最后展示=外*层
         */
        int number = copies * (StringUtils.isEmpty(value) ? 1 : NumberUtils.toInt(value, 1));
        return String.format("(%s%s)", number, unit);
    }
}
