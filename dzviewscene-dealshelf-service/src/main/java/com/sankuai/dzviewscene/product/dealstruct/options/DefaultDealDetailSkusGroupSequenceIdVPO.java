package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.DealDetailSkusGroupSequenceIdVP;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/26 5:42 下午
 */
@VPointOption(name = "团购详情sku货列表组顺序号默认变化点", description = "团购详情sku货列表组顺序号默认变化点",code = DefaultDealDetailSkusGroupSequenceIdVPO.CODE, isDefault = true)
public class DefaultDealDetailSkusGroupSequenceIdVPO extends DealDetailSkusGroupSequenceIdVP<DefaultDealDetailSkusGroupSequenceIdVPO.Config> {

    public static final String CODE = "DefaultDealDetailSkusGroupSequenceIdVPO";

    @Override
    public Integer compute(ActivityCxt context, DealDetailSkusGroupSequenceIdVP.Param param, DefaultDealDetailSkusGroupSequenceIdVPO.Config config) {
        return 0;
    }

    @Data
    @VPointCfg
    public static class Config {
    }

}
