package com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.DealAttrVOListModuleBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/20 4:49 下午
 */
@VPoint(name = "团购详情团单属性VO列表变化点", description = "团购详情团单属性VO列表变化点",code = DealAttrVOListVP.CODE, ability = DealAttrVOListModuleBuilder.CODE)
public abstract class DealAttrVOListVP<T> extends PmfVPoint<List<DealDetailStructAttrModuleGroupModel>, DealAttrVOListVP.Param, T> {

    public static final String CODE = "dealAttrVOListVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        //团单属性
        private List<AttrM> dealAttrs;
        //货信息
        private DealDetailDtoModel dealDetailDtoModel;
        //货类别
        private List<ProductSkuCategoryModel> productCategories;
        //团单信息
        private DealDetailInfoModel dealDetailInfoModel;
    }
}
