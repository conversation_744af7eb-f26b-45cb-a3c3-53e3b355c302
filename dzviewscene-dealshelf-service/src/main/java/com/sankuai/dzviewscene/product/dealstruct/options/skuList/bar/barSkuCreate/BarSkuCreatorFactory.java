package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create.ColaSkuCreator;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create.DrinksExceptColaSkuCreator;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create.EntranceTicketCreator;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create.MealsSkuCreator;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/9 3:49 下午
 */
public class BarSkuCreatorFactory {

    private static List<AbstractBarSkuCreator> barSkuBuilders = new ArrayList<>();

    static {
        barSkuBuilders.add(new ColaSkuCreator());
        barSkuBuilders.add(new DrinksExceptColaSkuCreator());
        barSkuBuilders.add(new EntranceTicketCreator());
        barSkuBuilders.add(new MealsSkuCreator());
    }

    public static AbstractBarSkuCreator creadSkuBuilder(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config) {
        for(AbstractBarSkuCreator barSkuListBuilder : barSkuBuilders) {
            if (barSkuListBuilder.ideantify(skuItemDto, config)) {
                return barSkuListBuilder;
            }
        }
        return null;
    }
}
