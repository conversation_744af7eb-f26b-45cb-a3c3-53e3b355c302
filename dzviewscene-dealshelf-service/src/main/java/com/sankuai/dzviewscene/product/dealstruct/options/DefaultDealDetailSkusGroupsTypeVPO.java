package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.cat.Cat;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.DealDetailSkusGroupsType;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/30 9:30 下午
 */
@VPointOption(name = "团购详情sku组列表type默认变化点", description = "团购详情sku组列表type默认变化点，支持配置", code = DefaultDealDetailSkusGroupsTypeVPO.CODE, isDefault = true)
public class DefaultDealDetailSkusGroupsTypeVPO extends DealDetailSkusGroupsType<DefaultDealDetailSkusGroupsTypeVPO.Config> {

    public static final String CODE = "DefaultDealDetailSkusGroupsTypeVPO";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.dealstruct.options.DefaultDealDetailSkusGroupsTypeVPO.compute(ActivityCxt,DealDetailSkusGroupsType$Param,DefaultDealDetailSkusGroupsTypeVPO$Config)");
        return config.getSkusGroupsType();
    }

    @Data
    @VPointCfg
    public static class Config {
        private String skusGroupsType;
    }
}
