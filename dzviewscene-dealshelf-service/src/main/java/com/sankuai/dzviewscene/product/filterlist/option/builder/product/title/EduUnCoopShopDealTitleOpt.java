package com.sankuai.dzviewscene.product.filterlist.option.builder.product.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.EduUnCoopShopProductQueryHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

@VPointOption(name = "教育非合作POI推荐列表商品名称展示",
        description = "教育非合作POI推荐列表商品名称展示",
        code = "LEUnCoopShopDealTitleOpt")
public class EduUnCoopShopDealTitleOpt extends ProductTitleVP<EduUnCoopShopDealTitleOpt.Config> {

    @Override
    public String compute(ActivityCxt context, Param param, EduUnCoopShopDealTitleOpt.Config config) {
        ProductM productM = param.getProductM();
        if (productM == null || StringUtils.isBlank(productM.getTitle())) {
            return null;
        }
        if (productM.getProductType() == ProductTypeEnum.GENERAL_SPU.getType()) {
            // 标品
            return getGeneralSpuTitle(productM);
        }
        // 团购
        return getDealTitle(productM);
    }

    private String getDealTitle(ProductM productM) {
        if (CollectionUtils.isEmpty(productM.getShopMs())) {
            return productM.getTitle();
        }
        String brandName = productM.getBrandName();
        if (StringUtils.isNotBlank(brandName)) {
            return String.format("%s | %s", brandName, productM.getTitle());
        }
        return productM.getTitle();
    }

    private String getGeneralSpuTitle(ProductM productM) {
        String subject = productM.getAttr(EduUnCoopShopProductQueryHandler.EXPLAIN_DOC);
        if (StringUtils.isNotBlank(subject)) {
            return String.format("%s【可选%s】", productM.getTitle(), subject);
        }
        return productM.getTitle();
    }

    @VPointCfg
    @Data
    public static class Config {

    }

}
