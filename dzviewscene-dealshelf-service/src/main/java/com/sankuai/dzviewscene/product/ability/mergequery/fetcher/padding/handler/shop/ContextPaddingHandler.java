package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.shop;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ContextHandlerResult;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2024/1/10 11:20
 */
public interface ContextPaddingHandler {
    /**
     * 填充门店信息
     *
     * @param ctx
     * @param contextHandlerResult
     * @param params
     * @return
     */
    CompletableFuture<ContextHandlerResult> padding(ActivityCxt ctx, ContextHandlerResult contextHandlerResult, Map<String, Object> params);
}
