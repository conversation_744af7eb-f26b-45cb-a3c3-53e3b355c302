package com.sankuai.dzviewscene.product.filterlist.option.builder.product.priceabovetags;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceAboveTagVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2024-06-17
 * @description:
 */
@VPointOption(name = "适用门店标签",
        description = "标品返回可用门店数量，团单返回最近可用门店信息",
        code = NearestShopPriceAboveTagsOpt.CODE)
public class NearestShopPriceAboveTagsOpt extends ProductPriceAboveTagVP<NearestShopPriceAboveTagsOpt.Config> {

    public static final String CODE = "NearestShopPriceAboveTagsOpt";

    public static final int HOT_SPU_PRODUCT_ID_TYPE = 10003;

    private static final List<String> EDU_UNCOOP_SCENE_CODE_LIST = Lists.newArrayList("edu_uncoolshop_deal_list",
            "edu_uncoolshop_unified_deal_list");

    private static final List<String> WED_PHOTO_UNCOOP_SCENE_CODE_LIST = Lists
            .newArrayList("wedphoto_uncoopshop_deal_spu_list", "wedphoto_uncoopshop_unified_deal_spu_list");

    private static final List<String> PHOTO_UNCOOP_SCENE_CODE_LIST = Lists.newArrayList("photo_uncoolshop_deal_list",
            "photo_uncoolshop_unified_deal_list");

    @Override
    public List<DzTagVO> compute(ActivityCxt activityCxt, Param param, Config config) {
        if (param.getProductM() == null) {
            return Lists.newArrayList();
        }
        ProductM productM = param.getProductM();
        return processDifferentBiz(productM, config, activityCxt);
    }

    private List<DzTagVO> processDifferentBiz(ProductM productM, Config config, ActivityCxt activityCxt) {
        String sceneCode = activityCxt.getSceneCode();
        if (StringUtils.isBlank(sceneCode)) {
            return Lists.newArrayList();
        }
        // 教育未合作场景
        if (EDU_UNCOOP_SCENE_CODE_LIST.contains(sceneCode)) {
            return processSpuAndDeal(productM, config);
        }
        // 婚摄未合作场景
        else if (WED_PHOTO_UNCOOP_SCENE_CODE_LIST.contains(sceneCode)) {
            // 处理爆品-可用门店数量
            if (isHotSpuProduct(productM)) {
                return buildApplyShopTags(productM, config);
            }
            // 处理泛商品-最近适用门店
            else if (productM.getProductType() == ProductTypeEnum.SPU.getType()) {
                return buildNearestShopTags(productM, config);
            }
        }
        // 处理摄影未合作场景下的标签展示
        else if (PHOTO_UNCOOP_SCENE_CODE_LIST.contains(sceneCode)) {
            return processSpuAndDeal(productM, config);
        }
        // 新场景若未接入将返回空
        return Lists.newArrayList();
    }

    /**
     * 处理标品-可用门店数量
     * 处理团单-最近适用门店
     * @param productM
     * @param config
     * @return
     */
    public List<DzTagVO> processSpuAndDeal(ProductM productM, Config config) {
        // 处理标品-可用门店数量
        if (productM.getProductType() == ProductTypeEnum.GENERAL_SPU.getType()) {
            return buildApplyShopTags(productM, config);
        }
        // 处理团单-最近适用门店
        else if (productM.getProductType() == ProductTypeEnum.DEAL.getType()) {
            return buildNearestShopTags(productM, config);
        }
        return Lists.newArrayList();
    }

    // 处理可用门店数量 + 最近门店距离
    private List<DzTagVO> buildApplyShopTags(ProductM productM, Config config) {
        DzTagVO shopNumTag = buildApplyShopNumTag(productM, config);
        DzTagVO nearestDistTag = buildDistTag(productM);
        return filterNonNullTags(shopNumTag, nearestDistTag);
    }

    // 处理最近适用门店 + 最近门店距离
    private List<DzTagVO> buildNearestShopTags(ProductM productM, Config config) {
        if (CollectionUtils.isEmpty(productM.getShopMs()) || productM.getShopMs().get(0) == null) {
            return Lists.newArrayList();
        }
        List<ShopM> shopMS = productM.getShopMs();
        ShopM nearestShop = shopMS.get(0);
        DzTagVO nearestShopTag = buildShopInfo(nearestShop.getShopName(), config);
        DzTagVO distTag = buildDistTag(nearestShop.getDistance());
        return filterNonNullTags(nearestShopTag, distTag);
    }

    private List<DzTagVO> filterNonNullTags(DzTagVO... tags) {
        return Lists.newArrayList(tags).stream().filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private DzTagVO buildApplyShopNumTag(ProductM productM, Config config) {
        // 团单适用门店数需要从attr指定属性中取
        int applyShopNum = 0;
        if (productM.getProductType() == ProductTypeEnum.DEAL.getType() && StringUtils.isNotEmpty(config.getDealApplyShopAttrKey())) {
            String applyShopNumStr = productM.getAttr(config.getDealApplyShopAttrKey());
            applyShopNum = getIntValFromStr(applyShopNumStr);
        }
        // 标品可以直接从productM中取
        else if (productM.getProductType() == ProductTypeEnum.GENERAL_SPU.getType()) {
            applyShopNum = productM.getShopNum();
        }
        String applyShopNumStr = processApplyShopNum(applyShopNum);
        if (StringUtils.isEmpty(applyShopNumStr)) {
            return null;
        }
        String tagText = String.format(config.getApplyShopNumTemplate(), applyShopNumStr);
        return createBaseTagWithValue(tagText);
    }

    private String processApplyShopNum(int applyShopNum) {
        if (applyShopNum <= 0) {
            return null;
        } else if (applyShopNum <= 49) {
            return String.valueOf(applyShopNum);
        } else if (applyShopNum <= 99) {
            return applyShopNum / 10 * 10 + "+";
        } else if (applyShopNum <= 999) {
            return applyShopNum / 100 * 100 + "+";
        } else {
            return applyShopNum / 1000 * 1000 + "+";
        }
    }

    private DzTagVO buildDistTag(ProductM productM) {
        String distStr = null;
        // 团单且最近商户信息不为空
        if (productM.getProductType() == ProductTypeEnum.DEAL.getType()
                && CollectionUtils.isNotEmpty(productM.getShopMs()) && productM.getShopMs().get(0) != null) {
            ShopM nearestShop = productM.getShopMs().get(0);
            distStr = nearestShop.getDistance();
        } else if (isHotSpuProduct(productM)) {
            // 爆品
            if (CollectionUtils.isNotEmpty(productM.getShopMs()) && productM.getShopMs().get(0) != null) {
                ShopM nearestShop = productM.getShopMs().get(0);
                distStr = nearestShop.getDistance();
            } else {
                distStr = productM.getNearestShopDesc();
            }
        } else if (isGeneralSpuNotHotProduct(productM)) {
            // 标品，非爆品
            distStr = productM.getNearestShopDesc();
        }
        return buildDistTag(distStr);
    }

    private DzTagVO buildDistTag(String dist) {
        if (StringUtils.isEmpty(dist)) {
            return null;
        }
        return createBaseTagWithValue(dist);
    }

    private DzTagVO buildShopInfo(String shopName, Config config) {
        if (StringUtils.isEmpty(shopName)) {
            return null;
        }
        DzTagVO dzTagVO = createBaseTagWithValue(shopName);
        if (StringUtils.isNotEmpty(config.getPreIconUrl())) {
            dzTagVO.setPrePic(buildPicComponent(config.getPreIconUrl(), config.getPrePicHeight(), config.getPreAspectRadio()));
        }
        return dzTagVO;
    }

    private DzTagVO createBaseTagWithValue(String name) {
        DzTagVO dzTagVO = new DzTagVO();
        dzTagVO.setText(name);
        return dzTagVO;
    }

    private DzPictureComponentVO buildPicComponent(String picUrl, int picHeight, double aspectRatio) {
        DzPictureComponentVO pic = new DzPictureComponentVO();
        pic.setPicUrl(picUrl);
        pic.setPicHeight(picHeight);
        pic.setAspectRadio(aspectRatio);
        return pic;
    }

    private boolean isHotSpuProduct(ProductM productM) {
        if (productM == null || productM.getProductType() <= 0) {
            return false;
        }
        int productType = productM.getProductType();
        int productIdType = productM.getId() != null ? productM.getId().getType() : 0;
        return productType == ProductTypeEnum.GENERAL_SPU.getType() && productIdType == HOT_SPU_PRODUCT_ID_TYPE;
    }

    private boolean isGeneralSpuNotHotProduct(ProductM productM) {
        if (productM == null || productM.getProductType() <= 0) {
            return false;
        }
        int productType = productM.getProductType();
        int productIdType = productM.getId() != null ? productM.getId().getType() : 0;
        return productType == ProductTypeEnum.GENERAL_SPU.getType() && productIdType != HOT_SPU_PRODUCT_ID_TYPE;
    }

    private int getIntValFromStr(String str) {
        if (!StringUtils.isNumeric(str) || StringUtils.isEmpty(str)) {
            return 0;
        }
        return Integer.parseInt(str);
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 适用门店数量模板
         */
        private String applyShopNumTemplate = "%s家门店通用";

        /**
         * 最近可用门店模板
         */
        private String nearestShopTemplate = "%s";

        /**
         * 前置图标链接
         */
        private String preIconUrl;

        /**
         * 前置图片宽高比
         */
        private double preAspectRadio;

        /**
         * 前置图片高
         */
        private int prePicHeight;

        /**
         * 团单适用门店数量属性key
         */
        private String dealApplyShopAttrKey;

    }
}
