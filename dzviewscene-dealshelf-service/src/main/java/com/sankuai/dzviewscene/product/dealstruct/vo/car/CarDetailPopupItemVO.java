package com.sankuai.dzviewscene.product.dealstruct.vo.car;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/12/6 5:45 下午
 */
@MobileDo(id = 0x61ca)
public class CarDetailPopupItemVO implements Serializable {
    /**
     * 弹窗详情
     */
    @MobileDo.MobileField(key = 0x4254)
    private CarPopupDetailVO popupDetail;

    /**
     * 弹窗名
     */
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;

    public CarPopupDetailVO getPopupDetail() {
        return popupDetail;
    }

    public void setPopupDetail(CarPopupDetailVO popupDetail) {
        this.popupDetail = popupDetail;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
