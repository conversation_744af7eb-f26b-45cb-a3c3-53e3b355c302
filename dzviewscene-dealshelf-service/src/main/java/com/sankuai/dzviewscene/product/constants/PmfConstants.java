package com.sankuai.dzviewscene.product.constants;

import com.sankuai.dzviewscene.shelf.business.utils.ParamUtil;

public interface PmfConstants {

    /**
     * 参数名定义列表
     */
    interface Params {

        /**
         * 平台
         */
        String platform = "platform";

        /**
         * 城市ID
         */
        String dpCityId = "dpCityId";

        /**
         * 点评城市
         */
        String mtCityId = "mtCityId";

        /**
         * 点评侧门店ID int类型的,不要使用不要使用不要使用!!!
         */
        String dpPoiId = "dpPoiId";

        /**
         * 美团侧门店ID int类型的,不要使用不要使用不要使用!!!
         */
        String mtPoiId = "mtPoiId";

        /**
         * 客户端类型
         */
        String clientType = "clientType";

        /**
         * 点评用户ID
         */
        String dpUserId = "dpUserId";

        /**
         * 美团用户实ID
         */
        String mtUserId = "mtUserId";

        /**
         * 美团用户虚ID，用于点评侧，美团侧为空
         */
        String mtVirtualUserId = "mtVirtualUserId";

        /**
         * 设备ID
         */
        String deviceId = "deviceId";

        /**
         * unionID
         */
        String unionId = "unionId";

        /**
         * 搜索关键词
         */
        String keyword = "keyword";

        /**
         * shopuuid
         */
        String shopUuid = "shopuuid";

        /**
         * 场景ID
         */
        String sceneCode = "sceneCode";

        /**
         * UA
         */
        String userAgent = "userAgent";

        /**
         * trace参数
         */
        String traceMark = "_activity_trace";

        /**
         * trace参数
         */
        String appVersion = "appVersion";

        /**
         * 经纬度
         */
        String lat = "lat";
        String lng = "lng";

        /**
         * 商品ID
         */
        String productId = "productId";

        /**
         * 商品ID
         */
        String productIdL = "longProductId";

        /**
         * SKU ID
         */
        String productItemId = "productItemId";

        /**
         * 选择日期
         */
        String selectDate = "selectDate";

        /**
         * 扩展字段
         */
        String extra = "extra";

        /**
         * 分享码
         */
        String shareCode = "shareCode";

        /**
         * 团单二级类目id
         */
        String dealCategoryId = "dealCategoryId";

        /**
         * 商品版本号
         */
        String productVersion = "productVersion";

        /**
         * 资源位
         */
        String spaceKey = "spaceKey";


        /**
         * 点评侧long类型门店ID
         */
        String dpPoiIdL = "dpPoiIdL";

        /**
         * 美团侧long类型门店ID
         */
        String mtPoiIdL = "mtPoiIdL";

        /**
         * 点评侧门店对应城市id
         */
        String shopDpCityId = "shopDpCityId";

        /**
         * 美团侧门店对应城市id
         */
        String shopMtCityId = "shopMtCityId";

        /**
         * 门店后台类目ID集合
         */
        String shopBackCatIds = "shopBackCatIds";

        /**
         * 点评门店信息
         */
        String dpShopM = "dpShopM";
        /**
         * 收藏类型
         */
        String collBizType = "collBizType";

        /**
         * 收藏id列表
         */
        String collBizIds = "collBizIds";

        /**
         * 点评实验id
         */
        String dpExpId = "dpExpId";

        /**
         * 美团实验id
         */
        String mtExpId = "dpExpId";

        /**
         * 点评实验展示的策略
         */
        String dpShowStrategy = "dpShowStrategy";

        /**
         * 美团实验展示的策略
         */
        String mtShowStrategy = "mtShowStrategy";

        /**
         * 请求来源参数字段, 用于区分是筛选接口还是商品列表接口
         */
        String channel = "channel";

        /**
         * 下挂商品Id 【新】
         * Ex：{"deal":"1,2,3","spu":"4,5,6"}
         * deal - 团单， spu - 泛商品
         * 解析工具如下：
         * {@link ParamUtil#getSummaryDealIds(java.lang.String,java.lang.String)}
         */
        String summaryProductIds = "summarypids";

        /**
         * tab锚定关键字
         */
        String searchKeyword = "searchkeyword";

        /**
         * 斗斛实验结果
         * value：List<DouHuM>
         */
        String douHus = "douHus";

        /**
         * 货架模块版本，由前端维护
         */
        String shelfVersion = "shelfversion";

        /**
         * 页面来源
         */
        String pageSource = "pageSource";
        /**
         * 页面大小
         */
        String pageSize = "pageSize";

        /**
         * 相似团购-团单
         */
        String entityId = "entityId";

        /**
         * 小程序标识
         */
        String mpSource = "mpSource";
    }

    interface Ctx {
        /**
         * 上下文中门店
         */
        String ctxShop = "ctxShop";
    }
}
