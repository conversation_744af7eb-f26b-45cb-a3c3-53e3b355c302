package com.sankuai.dzviewscene.product.factory;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.douhu.DealDetailDouhuFetcher;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailConstants;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * @author: created by hang.yu on 2023/10/16 14:51
 */
public abstract class AbstractDealCategoryStrategy implements DealCategoryStrategy {

    protected boolean hitAbForManySks(ActivityCxt activityCxt, DealDetailAssembleCfg assembleCfg) {
        List<DouhuResultModel> douhuResultModels = activityCxt.getSource(DealDetailDouhuFetcher.CODE);
        if (CollectionUtils.isEmpty(douhuResultModels)) {
            return false;
        }
        int platform = ParamsUtil.getIntSafely(activityCxt, DealDetailConstants.Params.platform);
        String expId = PlatformUtil.isMT(platform) ? assembleCfg.getMtExpId() : assembleCfg.getDpExpId();
        if (StringUtils.isBlank(expId)) {
            return false;
        }
        return douhuResultModels.stream()
                .filter(douhuResultModel -> StringUtils.equals(douhuResultModel.getExpId(), expId))
                .findFirst().map(douhuResultModel -> CollectionUtils.isNotEmpty(assembleCfg.getHitSks()) && assembleCfg.getHitSks().contains(douhuResultModel.getSk()))
                .orElse(false);
    }
}
