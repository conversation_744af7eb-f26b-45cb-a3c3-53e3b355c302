package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class DealAttrUtils {
    public static List<AttrM> extractFirstSkuAttrFromDealDetailDtoModel(DealDetailDtoModel dealDetailDtoModel) {
        if (dealDetailDtoModel == null || dealDetailDtoModel.getSkuUniStructuredDto() == null || CollectionUtils.isEmpty(dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups())) {
            return null;
        }
        List<MustSkuItemsGroupDto> mustSkuItemsGroupDtos = dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups();
        MustSkuItemsGroupDto mustSkuItemsGroupDto = CollectUtils.firstValue(mustSkuItemsGroupDtos);
        if (mustSkuItemsGroupDto == null || CollectionUtils.isEmpty(mustSkuItemsGroupDto.getSkuItems())) {
            return null;
        }
        SkuItemDto skuItemDto = CollectUtils.firstValue(mustSkuItemsGroupDto.getSkuItems());
        if (skuItemDto == null) {
            return null;
        }
        return skuItemDto.getAttrItems().stream().map(skuDto -> {
            if (skuDto == null) {
                return null;
            }
            AttrM attrM = new AttrM();
            attrM.setName(skuDto.getAttrName());
            attrM.setValue(skuDto.getAttrValue());
            return attrM;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static List<AttrM> getDealDetailInfoAttrsFromAttrs(List<AttrM> attrs) {
        if (CollectionUtils.isEmpty(attrs)) {
            return null;
        }
        AttrM dealDetailInfo = attrs.stream().filter(attr -> "detailInfo".equals(attr.getName())).findFirst().orElse(null);
        if (Objects.isNull(dealDetailInfo) || StringUtils.isBlank(dealDetailInfo.getValue())) {
            return null;
        }
        List<AttrM> dealDetailInfoAttrs = new ArrayList<>();
        try {
            Optional.ofNullable(JSON.parseObject(dealDetailInfo.getValue()))
                    .map(obj -> obj.getJSONArray("content"))
                    .filter(arr -> !arr.isEmpty())
                    .map(arr -> getDetailInfoContentByType(arr, "uniform-structure-table"))
                    .map(obj -> obj.getJSONObject("data"))
                    .map(obj -> obj.getJSONArray("groups"))
                    .filter(arr -> !arr.isEmpty())
                    .orElse(new JSONArray()).stream()
                    .map(JSONObject.class::cast)
                    .filter(obj -> obj.containsKey("units"))
                    .map(obj -> {
                        JSONArray units = obj.getJSONArray("units");
                        return units == null ? new JSONArray() : units;
                    })
                    .flatMap(Collection::stream)
                    .map(JSONObject.class::cast)
                    .forEach(obj -> {
                        if (obj.containsKey("attrValues")) {
                            JSONObject attrValues = obj.getJSONObject("attrValues");
                            for (String key : attrValues.keySet()) {
                                dealDetailInfoAttrs.add(new AttrM(key, attrValues.getString(key)));
                            }
                        }
                    });
        } catch (Exception e) {
            log.error("CommonPhotoDealAttrVOListOpt#getDealDetailInfoAttrsFromAttrs error:{}", JSON.toJSON(attrs), e);
            return null;
        }
        return dealDetailInfoAttrs;
    }

    private static JSONObject getDetailInfoContentByType(JSONArray array, String type) {
        Optional<JSONObject> opt = array.stream()
                .map(JSONObject.class::cast)
                .filter(obj -> type.equals(obj.getString("type")))
                .findFirst();
        return opt.orElse(null);
    }
}
