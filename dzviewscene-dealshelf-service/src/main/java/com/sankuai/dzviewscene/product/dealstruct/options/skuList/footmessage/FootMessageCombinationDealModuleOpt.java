package com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage;

import cn.hutool.core.convert.NumberChineseFormatter;
import com.dianping.cat.Cat;
import com.dianping.deal.struct.query.api.entity.dto.OptionalSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.DealSkuListModuleUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * @author: created by hang.yu on 2023/3/15 14:34
 */
@VPointOption(name = "足疗组合及多选一套餐团详sku列表组变化点", description = "足疗组合及多选一套餐团详sku列表组变化点", code = FootMessageCombinationDealModuleOpt.CODE)
public class FootMessageCombinationDealModuleOpt extends AbstractFootMessageModuleOpt<FootMessageCombinationDealModuleOpt.Config> {

    public static final String CODE = "FootMessageCombinationDealModuleOpt";

    @Override
    public List<DealDetailSkuListModuleGroupModel> compute(ActivityCxt context, Param param, FootMessageCombinationDealModuleOpt.Config config) {
        List<DealDetailSkuListModuleGroupModel> result = Lists.newArrayList();
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        // 过夜模块
        DealDetailSkuListModuleGroupModel overNightSkuListModuleGroupModel = DealSkuListModuleUtils.buildOverNightSkuListModule(dealDetailInfoModel.getDealAttrs());
        if (overNightSkuListModuleGroupModel != null) {
            result.add(overNightSkuListModuleGroupModel);
        }
        // 服务流程模块
        DealDetailSkuListModuleGroupModel dealDetailSkuListModuleGroupModel = buildServiceFlowSkuListModule(dealDetailInfoModel);
        if (dealDetailSkuListModuleGroupModel != null) {
            result.add(dealDetailSkuListModuleGroupModel);
        }
        context.addParam(QueryFetcher.Params.massageCombination, true);
        return result;
    }

    private DealDetailSkuListModuleGroupModel buildServiceFlowSkuListModule(DealDetailInfoModel dealDetailInfoModel) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.FootMessageCombinationDealModuleOpt.buildServiceFlowSkuListModule(DealDetailInfoModel)");
        List<DealSkuGroupModuleVO> list = buildDealSkuGroupModuleList(dealDetailInfoModel);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        DealDetailSkuListModuleGroupModel groupModel = new DealDetailSkuListModuleGroupModel();
        groupModel.setGroupName("服务流程模块");
        groupModel.setDealSkuGroupModuleVOS(list);
        return groupModel;
    }

    private List<DealSkuGroupModuleVO> buildDealSkuGroupModuleList(DealDetailInfoModel dealDetailInfoModel) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.FootMessageCombinationDealModuleOpt.buildDealSkuGroupModuleList(DealDetailInfoModel)");
        List<DealSkuGroupModuleVO> result = Lists.newArrayList();
        // 全部可享
        List<SkuItemDto> mustSkuItemList = DealSkuListModuleUtils.extractMustSkuItemList(dealDetailInfoModel);
        if (CollectionUtils.isNotEmpty(mustSkuItemList)) {
            result.add(buildAllAvailableGroup(dealDetailInfoModel, mustSkuItemList));
        }
        // 服务项目m选n
        List<OptionalSkuItemsGroupDto> optionalSkuItemList = DealSkuListModuleUtils.extractOptionalSkuItemsGroupList(dealDetailInfoModel);
        if (CollectionUtils.isNotEmpty(optionalSkuItemList)) {
            result.addAll(buildEitherOrGroupList(dealDetailInfoModel, optionalSkuItemList));
        }
        return result;
    }

    private DealSkuGroupModuleVO buildAllAvailableGroup(DealDetailInfoModel dealDetailInfoModel, List<SkuItemDto> mustSkuItemList) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.FootMessageCombinationDealModuleOpt.buildAllAvailableGroup(DealDetailInfoModel,List)");
        DealSkuGroupModuleVO allAvailableGroup = new DealSkuGroupModuleVO();
        allAvailableGroup.setTitle("以下服务项目全部可享");
        allAvailableGroup.setTitleStyle(0);
        allAvailableGroup.setDealSkuList(buildDealSkuList(mustSkuItemList, dealDetailInfoModel.getProductCategories()));
        return allAvailableGroup;
    }

    private List<DealSkuGroupModuleVO> buildEitherOrGroupList(DealDetailInfoModel dealDetailInfoModel, List<OptionalSkuItemsGroupDto> optionalSkuItemList) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.FootMessageCombinationDealModuleOpt.buildEitherOrGroupList(DealDetailInfoModel,List)");
        List<DealSkuGroupModuleVO> result = Lists.newArrayList();
        for (OptionalSkuItemsGroupDto optionalSkuItemsGroupDto : optionalSkuItemList) {
            List<SkuItemDto> skuItemList = optionalSkuItemsGroupDto.getSkuItems();
            if (CollectionUtils.isNotEmpty(skuItemList)) {
                DealSkuGroupModuleVO eitherOrGroup = new DealSkuGroupModuleVO();
                String title = String.format("以下服务项目%s选%s",
                        NumberChineseFormatter.format(skuItemList.size(), false),
                        NumberChineseFormatter.format(optionalSkuItemsGroupDto.getOptionalCount(), false));
                eitherOrGroup.setTitle(title);
                eitherOrGroup.setTitleStyle(0);
                eitherOrGroup.setDealSkuList(buildDealSkuList(skuItemList, dealDetailInfoModel.getProductCategories()));
                result.add(eitherOrGroup);
            }
        }
        return result;
    }

    private List<DealSkuVO> buildDealSkuList(List<SkuItemDto> skuItemList, List<ProductSkuCategoryModel> productCategories) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.FootMessageCombinationDealModuleOpt.buildDealSkuList(java.util.List,java.util.List)");
        List<DealSkuVO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(skuItemList)) {
            return result;
        }
        for (SkuItemDto skuItemDto : skuItemList) {
            DealSkuVO dealSkuVO = new DealSkuVO();
            dealSkuVO.setPrice(skuItemDto.getMarketPrice() == null ? null : skuItemDto.getMarketPrice().stripTrailingZeros().toPlainString() + "元");
            dealSkuVO.setTitle(getServiceFlowSkuName(skuItemDto.getAttrItems(), getCombinationServiceType(skuItemDto.getProductCategory(), productCategories)));
            dealSkuVO.setItems(getServiceItems(skuItemDto.getAttrItems(), skuItemDto.getProductCategory()));
            result.add(dealSkuVO);
        }
        return result;
    }

    @Data
    @VPointCfg
    public static class Config {
    }

}
