package com.sankuai.dzviewscene.product.dealstruct.ability.utils;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR> 2019-12-2
 **/
public class DataUtils {

    public static List<Long> toLongList(String source, String split) {
        List<String> strings = toList(source, split);
        return toLongList(strings);
    }

    public static List<Long> toLongList(List<String> stringList) {
        if (CollectionUtils.isEmpty(stringList)) {
            return Lists.newArrayList();
        }
        List<Long> res = Lists.newArrayList();
        for (String s : stringList) {
            try {
                res.add(Long.parseLong(s));
            } catch (Exception e) {
                Cat.logError("DataUtilsToLongListError",e);
            }
        }
        return res;
    }

    public static List<String> toList(String source, String split) {
        if (StringUtils.isEmpty(source)) {
            return Lists.newArrayList();
        }
        String[] splitArr = source.split(split);
        return toList(splitArr, true);
    }

    public static List<String> toList(String[] split, boolean trim) {
        if (split == null || split.length == 0) {
            return Lists.newArrayList();
        }
        List<String> list = Lists.newArrayList();
        for (String s : split) {
            if (trim) {
                if (s == null) {
                    list.add(null);
                } else {
                    list.add(s.trim());
                }
            } else {
                list.add(s);
            }
        }
        return list;
    }
}
