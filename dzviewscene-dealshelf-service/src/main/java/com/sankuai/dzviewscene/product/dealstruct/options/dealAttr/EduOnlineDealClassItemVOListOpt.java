package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.dianping.cat.Cat;
import com.dianping.pay.promo.common.utils.PlatformUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.dto.EduTechnicianVideoDTO;
import com.sankuai.dzviewscene.product.ability.options.LoadDealTeacherAndTrialClassOpt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailAttrDescVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.JumpUrlVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


@VPointOption(name = "在线教育团单的课程列表AttrVO列表变化点", description = "在线教育团单的课程列表AttrVO列表变化点",code = EduOnlineDealClassItemVOListOpt.CODE)
@Slf4j
public class EduOnlineDealClassItemVOListOpt extends DealAttrVOListVP<EduOnlineDealClassItemVOListOpt.Config> {

    public static final String CODE = "EduOnlineDealClassItemVOListOpt";

    public static final String ATTR_COURSE_METHOD = "course_method";
    public static final String COURSE_NUM_APPEND = "课时";
    public static final String ATTR_COURSE_PLAN = "course_plan";

    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        List<CoursePlan> coursePlanList = getCoursePlanList(param.getDealAttrs());
        List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOS = buildDealDetailStructAttrModuleVO(coursePlanList, param, config);
        if (CollectionUtils.isEmpty(dealDetailStructAttrModuleVOS) || CollectionUtils.isEmpty(coursePlanList)) {
            return null;
        }
        DealDetailStructAttrModuleGroupModel groupModule = new DealDetailStructAttrModuleGroupModel();
        groupModule.setGroupName(DealDetailUtils.getAttrSingleValueByAttrName(param.getDealAttrs(), ATTR_COURSE_METHOD));
        groupModule.setGroupSubtitle(getTotalClassHours(coursePlanList));
        groupModule.setDealDetailStructAttrModuleVOS(dealDetailStructAttrModuleVOS);
        groupModule.setJumpUrl(getOnlineClassJumpUrl(param.getDealDetailInfoModel(), config, context));
        return Lists.newArrayList(groupModule);
    }

    private List<DealDetailStructAttrModuleVO> buildDealDetailStructAttrModuleVO(List<CoursePlan> coursePlanList, Param param, Config config) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.EduOnlineDealClassItemVOListOpt.buildDealDetailStructAttrModuleVO(List,DealAttrVOListVP$Param,EduOnlineDealClassItemVOListOpt$Config)");
        if (CollectionUtils.isEmpty(coursePlanList)) {
            return null;
        }
        return coursePlanList.stream()
                .filter(plan -> plan != null && StringUtils.isNotBlank(plan.getCourseModule()))
                .map(plan -> {
                    DealDetailAttrDescVO descVO = new DealDetailAttrDescVO();
                    if (plan.getCourseTimeNum() != null) {
                        descVO.setTitle(plan.getCourseTimeNum().stripTrailingZeros().toPlainString() + COURSE_NUM_APPEND);
                    }
                    DealDetailStructAttrModuleVO attrModuleVO = new DealDetailStructAttrModuleVO();
                    attrModuleVO.setAttrName(plan.getCourseModule());
                    attrModuleVO.setDesc(Lists.newArrayList(descVO));
                    return attrModuleVO;
                }).collect(Collectors.toList());
    }

    private List<CoursePlan> getCoursePlanList(List<AttrM> dealAttrs) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.EduOnlineDealClassItemVOListOpt.getCoursePlanList(java.util.List)");
        String json = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, ATTR_COURSE_PLAN);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonCodec.decode(json, new TypeReference<List<CoursePlan>>() {});
    }

    private String getTotalClassHours(List<CoursePlan> coursePlanList) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.EduOnlineDealClassItemVOListOpt.getTotalClassHours(java.util.List)");
        BigDecimal result = new BigDecimal(0);
        for (CoursePlan coursePlan : coursePlanList) {
            if (coursePlan.getCourseTimeNum() == null) {
                continue;
            }
            result = result.add(coursePlan.getCourseTimeNum());
        }
        if (result.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        return result.setScale(1, BigDecimal.ROUND_DOWN).stripTrailingZeros().toPlainString() + COURSE_NUM_APPEND;
    }


    private JumpUrlVO getOnlineClassJumpUrl(DealDetailInfoModel dealDetailInfoModel, Config config, ActivityCxt context) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.EduOnlineDealClassItemVOListOpt.getOnlineClassJumpUrl(DealDetailInfoModel,EduOnlineDealClassItemVOListOpt$Config,ActivityCxt)");
        boolean hasOnlineClass = isHasOnlineClass(context);
        if (!hasOnlineClass || PlatformUtil.isAnyXcx(context.getParam(ProductDetailActivityConstants.Params.mpSource))) {
            return null;
        }
        JumpUrlVO jumpUrlVO = new JumpUrlVO();
        jumpUrlVO.setIcon(config.getTrialClassIcon());
        jumpUrlVO.setName(config.getTrialClassJumpName());
        jumpUrlVO.setUrl(getTrialClassUrl(dealDetailInfoModel, config, context));
        return jumpUrlVO;
    }

    private boolean isHasOnlineClass(ActivityCxt context) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.EduOnlineDealClassItemVOListOpt.isHasOnlineClass(com.sankuai.athena.viewscene.framework.ActivityCxt)");
        List<EduTechnicianVideoDTO> teacherAndVideoList = context.getParam(LoadDealTeacherAndTrialClassOpt.CODE);
        if (CollectionUtils.isEmpty(teacherAndVideoList)) {
            return false;
        }
        return teacherAndVideoList.stream()
                .filter(video -> video != null && video.getStatus() == 0)
                .count() > 0;
    }

    private String getTrialClassUrl(DealDetailInfoModel dealDetailInfoModel, Config config, ActivityCxt context) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.EduOnlineDealClassItemVOListOpt.getTrialClassUrl(DealDetailInfoModel,EduOnlineDealClassItemVOListOpt$Config,ActivityCxt)");
        int platform = context.getParam(ProductDetailActivityConstants.Params.platform);
        Long dpShopId = context.getParam(ProductDetailActivityConstants.Params.dpPoiIdL);
        Long mtShopId = context.getParam(ProductDetailActivityConstants.Params.mtPoiIdL);
        String shopUUID = context.getParam(ProductDetailActivityConstants.Params.shopUuid);
        if (PlatformUtil.isMT(platform)) {
            return String.format(config.getMtTrialClassJumpUrlTemplate(), dealDetailInfoModel.getDealId(), mtShopId);
        }
        return String.format(config.getDpTrialClassJumpUrlTemplate(), dealDetailInfoModel.getDealId(), dpShopId, shopUUID);
    }

    @Data
    @VPointCfg
    public static class Config {

        /**
         * 试听课的跳转名称
         */
        private String trialClassJumpName = "免费试听";

        /**
         * 试听课的跳转链接
         */
        private String dpTrialClassJumpUrlTemplate = "dianping://mrn?mrn_biz=gcbu&mrn_entry=mrn-le-edutrialcourse&mrn_component=mrn-le-edutrialcourse&dealid=%s&shopid=%s&shopuuid=%s";
        private String mtTrialClassJumpUrlTemplate = "imeituan://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=mrn-le-edutrialcourse&mrn_component=mrn-le-edutrialcourse&dealid=%s&shopid=%s";

        /**
         * 试听课的跳转icon
         */
        private String trialClassIcon = "https://p0.meituan.net/travelcube/e25d4203979fbedfee7f09f5b53071281377.png";

    }


    @Data
    public static class CoursePlan {

        /**
         * 课程
         */
        @JsonProperty("course_module")
        private String courseModule;

        /**
         * 课时数
         */
        @JsonProperty("course_time_num")
        private BigDecimal courseTimeNum;

    }

}
