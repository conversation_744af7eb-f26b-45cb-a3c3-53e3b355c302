package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints.SkuItemAttrVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.SkuAttrModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.CommonAttrVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/27 10:16 下午
 */
@VPointOption(name = "足疗团购详情货attr列表变化点", description = "足疗团购详情货attr列表变化点", code = FootSkuItemAttrItemsVPO.CODE, isDefault = false)
public class FootSkuItemAttrItemsVPO extends SkuItemAttrVP<FootSkuItemAttrItemsVPO.Config> {

    public static final String CODE = "FootSkuItemAttrItemsVPO";

    private static final String PROJECT_PROCESS_ARRAY_SKU_ATTR_NAME = "projectProcessArray";

    private static final String WHOLE_BODY_TAEGET_LOCATION1 = "全身";

    @Override
    public List<SkuAttrModel> compute(ActivityCxt context, Param param, Config config) {
        SkuItemDto skuItemDto = param.getSkuItemDto();
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return null;
        }
        String projectProcessArrayJsonStr = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), PROJECT_PROCESS_ARRAY_SKU_ATTR_NAME);
        List<List<FootServiceProcessItem>> projectProcessArray = JsonCodec.decode(projectProcessArrayJsonStr, new TypeReference<List<List<FootServiceProcessItem>>>() {} );
        if (CollectionUtils.isEmpty(projectProcessArray)) {
            return null;
        }
        return convertProjectProcessArray2SkuAttrModelList(projectProcessArray, config);
    }

    private List<SkuAttrModel> convertProjectProcessArray2SkuAttrModelList(List<List<FootServiceProcessItem>> projectProcessArray, Config config) {
        if (CollectionUtils.isEmpty(projectProcessArray) || config == null) {
            return null;
        }
        List<SkuAttrAttrItemVO> skuAttrAttrItemVOS = projectProcessArray.stream().map(processItems -> convertFootServiceProcessItems2SkuAttrAttrItemVO(processItems, config)).filter(process -> process != null).collect(Collectors.toList());
        SkuAttrModel skuAttrModel = buildSkuAttrModel(config.getSkuProjectProcessAttrsGroupName(), skuAttrAttrItemVOS);
        if (skuAttrModel == null) {
            return null;
        }
        return Lists.newArrayList(skuAttrModel);

    }

    private SkuAttrModel buildSkuAttrModel(String name, List<SkuAttrAttrItemVO> skuAttrAttrItemVOS) {
        if (CollectionUtils.isEmpty(skuAttrAttrItemVOS)) {
            return null;
        }
        SkuAttrModel skuAttrModel = new SkuAttrModel();
        skuAttrModel.setName(name);
        skuAttrModel.setValueAttrsExtValue(JsonCodec.encodeWithUTF8(skuAttrAttrItemVOS));
        return skuAttrModel;
    }

    private SkuAttrAttrItemVO convertFootServiceProcessItems2SkuAttrAttrItemVO(List<FootServiceProcessItem> processItems, Config config) {
        if (CollectionUtils.isEmpty(processItems)) {
            return null;
        }
        FootServiceProcessItem majorProcess = processItems.stream().filter(process -> process.isMajor()).findFirst().orElse(null);
        if (majorProcess == null) {
            return null;
        }
        SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
        skuAttrAttrItemVO.setName(getMajorProcessName(majorProcess));
        skuAttrAttrItemVO.setInfo(getMajorProcessInfo(majorProcess, config));
        skuAttrAttrItemVO.setValues(getMinorProcess(processItems, config));
        return skuAttrAttrItemVO;
    }

    private List<CommonAttrVO> getMinorProcess(List<FootServiceProcessItem> processItems, Config config) {
        if (CollectionUtils.isEmpty(processItems)) {
            return null;
        }
        List<FootServiceProcessItem> minorProcessItems = processItems.stream().filter(process -> !process.isMajor()).collect(Collectors.toList());
        return convertMinorProcess2CommonAttrVOs(minorProcessItems, config);
    }

    private List<String> getMajorProcessInfo(FootServiceProcessItem majorProcess, Config config) {
        if (majorProcess == null || StringUtils.isEmpty(majorProcess.getServiceDuration())) {
            return null;
        }
        String duration = String.format(config.getMajorProcessTimeFormat(), majorProcess.getServiceDuration());
        if (StringUtils.isEmpty(duration)) {
            return null;
        }
        return Lists.newArrayList(duration);
    }

    private String getMajorProcessName(FootServiceProcessItem majorProcess) {
        if (majorProcess == null) {
            return null;
        }
        if (StringUtils.isEmpty(majorProcess.getTargetLocation2()) || StringUtils.isEmpty(majorProcess.getServiceContent2())) {
            return majorProcess.getTargetLocation();
        }
        if (StringUtils.isNotEmpty(majorProcess.getTargetLocation1()) && majorProcess.getTargetLocation1().equals(WHOLE_BODY_TAEGET_LOCATION1)) {
            return majorProcess.getTargetLocation1() + majorProcess.getServiceContent2();
        }
        return majorProcess.getTargetLocation2() + majorProcess.getServiceContent2();
    }

    private List<CommonAttrVO> convertMinorProcess2CommonAttrVOs(List<FootServiceProcessItem> processItems, Config config) {
        if (CollectionUtils.isEmpty(processItems)) {
            return null;
        }
        return processItems.stream().map(item -> convertFootServiceProcessItem2CommonAttrVO(item, config)).filter(item -> item != null).collect(Collectors.toList());
    }

    private CommonAttrVO convertFootServiceProcessItem2CommonAttrVO(FootServiceProcessItem item, Config config) {
        if (item == null) {
            return null;
        }
        if (StringUtils.isEmpty(item.getServiceContent2()) || StringUtils.isEmpty(item.getTargetLocation2())) {
            return null;
        }
        CommonAttrVO commonAttrVO = new CommonAttrVO();
        commonAttrVO.setName(config.getMinorProcessName());
        commonAttrVO.setValue(item.getTargetLocation2() + item.getServiceContent2());
        return commonAttrVO;
    }


    @Data
    @VPointCfg
    public static class Config {
        private String skuProjectProcessAttrsGroupName;
        private String majorProcessTimeFormat;
        private String minorProcessName;
    }

    @Data
    private static class FootServiceProcessItem {
        private boolean major;
        private String serviceContent;
        private String serviceContent1;
        private String serviceContent2;
        private String targetLocation;
        private String targetLocation1;
        private String targetLocation2;
        private String serviceDuration;
    }
}
