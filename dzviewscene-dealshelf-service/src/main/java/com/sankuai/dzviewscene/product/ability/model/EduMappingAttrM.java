package com.sankuai.dzviewscene.product.ability.model;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;

import java.util.List;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2024-06-13
 * @description: 教育映射匹配属性
 */
@Data
public class EduMappingAttrM {
    /**
     * 第一优先级标品科目
     */
    private String firstSubject;
    /**
     * 第一优先级可解释性文案
     */
    private String firstExplainDoc;
    /**
     * 第二优先级标品科目
     */
    private String secondSubject;
    /**
     * 第二优先级可解释性文案
     */
    private String secondExplainDoc;
    /**
     * 第一优先级商品标签ID
     */
    private String firstProductLabel;
    /**
     * 第二优先级商品标签ID
     */
    private String secondProductLabel;
    /**
     * 标品列表
     */
    private List<ProductM> standardProductList;

}
