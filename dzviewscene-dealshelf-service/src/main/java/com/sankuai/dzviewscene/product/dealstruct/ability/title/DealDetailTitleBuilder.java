package com.sankuai.dzviewscene.product.dealstruct.ability.title;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.title.vpoints.DealDetailTitleVP;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailTitleModel;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/24 7:00 下午
 */
@Ability(code = DealDetailTitleBuilder.CODE,
        name = "团购详情模块标题组件构造能力",
        description = "团购详情模块标题组件构造能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE
        }
)
public class DealDetailTitleBuilder extends PmfAbility<List<DealDetailTitleModel>, DealDetailTitleParam, DealDetailTitleCfg> {

    public static final String CODE = "dealDetailTitleBuilder";


    @Override
    public CompletableFuture<List<DealDetailTitleModel>> build(ActivityCxt activityCxt, DealDetailTitleParam titleParam, DealDetailTitleCfg titleCfg) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        if (CollectionUtils.isEmpty(dealDetailInfoModels)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        List<DealDetailTitleModel> dealDetailTitleModels = dealDetailInfoModels.stream().map(detailModel -> {
            if(detailModel == null) {
                return null;
            }
            String pretitle = detailModel.getDealTitle();
            DealDetailTitleVP<?> dealDetailTitleVP = findVPoint(activityCxt, DealDetailTitleVP.CODE);
            String showTitle = dealDetailTitleVP.execute(activityCxt, DealDetailTitleVP.Param.builder().title(pretitle).build());
            DealDetailTitleModel dealDetailTitleModel = new DealDetailTitleModel();
            dealDetailTitleModel.setTitle(showTitle);
            dealDetailTitleModel.setDealId(detailModel.getDealId());
            return dealDetailTitleModel;
        }).collect(Collectors.toList());
        return CompletableFuture.completedFuture(dealDetailTitleModels);
    }

}
