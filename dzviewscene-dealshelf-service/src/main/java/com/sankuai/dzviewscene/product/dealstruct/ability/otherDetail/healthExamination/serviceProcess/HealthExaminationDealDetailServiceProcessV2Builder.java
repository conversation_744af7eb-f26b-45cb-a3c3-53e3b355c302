package com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.healthExamination.serviceProcess;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.DealAttrVOListModuleBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.CommonAttrsVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealModuleDetailVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination.*;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.uniform.UniformStructContentModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Ability(code = HealthExaminationDealDetailServiceProcessV2Builder.CODE,
        name = "体检团购详情模块服务流程构造能力V2",
        description = "体检团购详情模块服务流程构造能力V2",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE,
                DealAttrVOListModuleBuilder.CODE
        }
)
public class HealthExaminationDealDetailServiceProcessV2Builder extends PmfAbility<DealModuleDetailVO, HealthExaminationDealDetailServiceProcessV2Param, HealthExaminationDealDetailServiceProcessV2Cfg> {

    public static final String CODE = "healthExaminationDealDetailServiceProcessBuilderV2";

    @Override
    public CompletableFuture<DealModuleDetailVO> build(ActivityCxt ctx, HealthExaminationDealDetailServiceProcessV2Param healthExaminationDealDetailServiceProcessV2Param, HealthExaminationDealDetailServiceProcessV2Cfg healthExaminationDealDetailServiceProcessV2Cfg) {
        //1.获取构造服务流程所需的团单基本信息
        DealDetailInfoModel dealDetailBasicInfo = getDealDetailBasicInfo(ctx);
        if (dealDetailBasicInfo == null || CollectionUtils.isEmpty(dealDetailBasicInfo.getDealAttrs())) {
            return null;
        }
        //1 获取原始服务流程数据
        List<ServiceProcessItem> serviceProcessItems = getServiceProcessData(dealDetailBasicInfo.getDealAttrs());
        //2.构造DealModuleDetailVO
        return CompletableFuture.completedFuture(getDealModuleDetailVO(serviceProcessItems, dealDetailBasicInfo.getDealAttrs()));
    }

    private DealModuleDetailVO getDealModuleDetailVO(List<ServiceProcessItem> serviceProcessItems, List<AttrM> dealAttrs) {
        //1.构造流程信息
        ProcessModuleListVO serviceProcess = getServiceProcess(serviceProcessItems, dealAttrs);
        //2.构造体检须知
        CommonAttrsVO examinationNotice = buildExaminationNotice(dealAttrs);
        if (serviceProcess == null && examinationNotice == null) {
            return null;
        }
        DealModuleDetailVO dealModuleDetailVO = new DealModuleDetailVO();
        HealthExaminationDealDetailVO healthExaminationDealDetailVO = new HealthExaminationDealDetailVO();
        healthExaminationDealDetailVO.setServiceProcess(serviceProcess);
        healthExaminationDealDetailVO.setExaminationNotice(examinationNotice);
        dealModuleDetailVO.setHealthExaminationDealDetailVO(healthExaminationDealDetailVO);
        return dealModuleDetailVO;
    }

    private CommonAttrsVO buildExaminationNotice(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        List<Map<String, String>> list = DealDetailUtils.getAttrVal(dealAttrs, "info_before_check", new TypeReference<List<Map<String, String>>>() {
        });
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<String> noticeList = new ArrayList<>();
        list.stream().filter(Objects::nonNull).forEach(map -> {
            String stepDesc = map.get("stepDesc");
            if (StringUtils.isNotBlank(stepDesc)) {
                noticeList.add(stepDesc);
            }
        });
        if (CollectionUtils.isEmpty(noticeList)) {
            return null;
        }
        CommonAttrsVO commonAttrsVO = new CommonAttrsVO();
        commonAttrsVO.setName("体检须知");
        commonAttrsVO.setValues(noticeList);
        return commonAttrsVO;
    }

    private ProcessModuleListVO getServiceProcess(List<ServiceProcessItem> serviceProcessItems, List<AttrM> dealAttrs) {
        List<ProcessModuleVO> allProcessModuleVOS = new ArrayList<>();
        addServiceProcess(allProcessModuleVOS, serviceProcessItems, dealAttrs);

        //添加发票信息
        addInvoiceInfo(dealAttrs, allProcessModuleVOS);
        if (CollectionUtils.isEmpty(allProcessModuleVOS)) {
            return null;
        }
        ProcessModuleListVO processModuleListVO = new ProcessModuleListVO();
        processModuleListVO.setTitle("服务流程");
        processModuleListVO.setProcessModuleList(allProcessModuleVOS);
        return processModuleListVO;
    }

    private void addInvoiceInfo(List<AttrM> dealAttrs, List<ProcessModuleVO> result) {
        if (result == null) {
            return;
        }
        List<ProcessModuleItemVO> processes = getProcessModuleItemVOs(dealAttrs);
        if (CollectionUtils.isEmpty(processes)) {
            return;
        }
        ProcessModuleVO processModuleVO = new ProcessModuleVO();
        processModuleVO.setProcessName("获取发票");
        processModuleVO.setProcesses(processes);
        result.add(processModuleVO);
    }

    private void addServiceProcess(List<ProcessModuleVO> result, List<ServiceProcessItem> serviceProcessItems, List<AttrM> dealAttrs) {
        if (result == null || CollectionUtils.isEmpty(serviceProcessItems)) {
            return;
        }
        List<ProcessModuleVO> processModuleVOS = serviceProcessItems.stream()
                .map(item -> buildProcessModuleVO(item, dealAttrs))
                .filter(process -> process != null)
                .collect(Collectors.toList());
        result.addAll(processModuleVOS);
    }

    private ProcessModuleVO buildProcessModuleVO(ServiceProcessItem serviceProcessItem, List<AttrM> dealAttrs) {
        if (serviceProcessItem == null) {
            return null;
        }
        ProcessModuleVO processModuleVO = new ProcessModuleVO();
        //流程步骤名
        processModuleVO.setProcessName(serviceProcessItem.getStepName());
        //流程步骤，由一个或多个流程步骤子模块组成
        List<ProcessModuleItemVO> processModuleItemVOS = getProcessModuleItemVOs(serviceProcessItem, dealAttrs);
        processModuleVO.setProcesses(processModuleItemVOS);
        return processModuleVO;
    }

    private List<ProcessModuleItemVO> getProcessModuleItemVOs(ServiceProcessItem serviceProcessItem, List<AttrM> dealAttrs) {
        List<ProcessModuleItemVO> processModuleItemVOS = new ArrayList<>();
        //添加流程内容ProcessModuleItemVO
        addProcessModuleItem(processModuleItemVOS, serviceProcessItem.getOtherInfo(), null, null);
        //如果流程名称是"预约门店及时间"，则再添加预约时间和预约方式ProcessModuleItemVO
        if ("预约门店及时间".equals(serviceProcessItem.getStepName())) {
            addReserveTimeAndMethod(processModuleItemVOS, dealAttrs);
        }
        //如果流程名称是"查询报告"，则再添加出报告时间和报告获取方式ProcessModuleItemVO
        if ("查询报告".equals(serviceProcessItem.getStepName())) {
            addReportTimeAndAbtainMethod(processModuleItemVOS, dealAttrs);
        }
        if (CollectionUtils.isEmpty(processModuleItemVOS)) {
            return null;
        }
        return processModuleItemVOS;
    }

    private List<ProcessModuleItemVO> getProcessModuleItemVOs(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        List<ProcessModuleItemVO> processModuleItemVOS = new ArrayList<>();
        //添加开发票咨询电话
        List<String> invoiceConsultTels = DealDetailUtils.getAttrValueByAttrName(dealAttrs, "bill-tel");
        addProcessModuleItem(processModuleItemVOS, null, null, buildNumProcessSubitemVO("咨询电话", invoiceConsultTels));
        //添加发票开具方式
        String invoiceObtainMethod = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "bill-way");
        addProcessModuleItem(processModuleItemVOS, null, buildProcessSubitemVO("开具方式", invoiceObtainMethod), null);
        return processModuleItemVOS;
    }

    private void addReportTimeAndAbtainMethod(List<ProcessModuleItemVO> processModuleItemVOS, List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs) || processModuleItemVOS == null) {
            return;
        }
        //出报告时间
        Integer reportTimeNum = DealDetailUtils.getAttrVal(dealAttrs, "physical_examination_get_result_time", new TypeReference<Integer>() {
        });
        String reportTime = getReportTime(reportTimeNum);
        if (StringUtils.isNotBlank(reportTime)) {
            addProcessModuleItem(processModuleItemVOS, null, buildProcessSubitemVO("出报告时间", reportTime), null);
        }
        //报告获取方式
        processModuleItemVOS.addAll(getReportProcessModuleItemVOs(dealAttrs));
    }

    private String getReportTime(Integer reportTimeNum) {
        if (Objects.isNull(reportTimeNum)) {
            return null;
        }
        switch (reportTimeNum) {
            case 0:
                return "当日";
            case 1:
                return "1个工作日";
            case 2:
                return "2个工作日";
            case 3:
                return "3个工作日";
            case 4:
                return "4个工作日";
            case 5:
                return "5个工作日";
            case 6:
                return "6个工作日";
            case 7:
                return "7个工作日及以上";
            default:
                return null;
        }
    }

    private List<ProcessModuleItemVO> getReportProcessModuleItemVOs(List<AttrM> dealAttrs) {
        List<String> reportObtainMethodTypes = DealDetailUtils.getAttrVal(dealAttrs, "physical_examination_report_type", new TypeReference<List<String>>() {
        });
        if (CollectionUtils.isEmpty(reportObtainMethodTypes)) {
            return new ArrayList<>();
        }
        return reportObtainMethodTypes.stream()
                .map(type -> {
                    List<String> reportObtainMethodList = "电子报告".equals(type) ? DealDetailUtils.getAttrVal(dealAttrs, "electronic_reporting_mode", new TypeReference<List<String>>() {
                    }) : DealDetailUtils.getAttrVal(dealAttrs, "paper_report_mode", new TypeReference<List<String>>() {
                    });
                    if (CollectionUtils.isNotEmpty(reportObtainMethodList)) {
                        String reportObtainMethod = reportObtainMethodList.stream().collect(Collectors.joining(","));
                        return buildProcessModuleItemVO(null, buildProcessSubitemVO(type, reportObtainMethod), null);
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private void addReserveTimeAndMethod(List<ProcessModuleItemVO> processModuleItemVOS, List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs) || processModuleItemVOS == null) {
            return;
        }
        //判断是否需要预约
        boolean needReserve = "是".equals(DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "reservation_is_needed_or_not"));
        if (!needReserve) {
            return;
        }
        //预约时间
        String reserveTime = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "reservation_detail");
        //删除商品平台带过来的多余换行符
        reserveTime = reserveTime == null ? reserveTime : reserveTime.replaceAll("\n", "");
        addProcessModuleItem(processModuleItemVOS, null, buildProcessSubitemVO("预约时间", reserveTime), null);
        //预约电话
        String reserveTelNumStr = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "reservation_number");
        if (StringUtils.isEmpty(reserveTelNumStr)) {
            return;
        }
        addProcessModuleItem(processModuleItemVOS, null, null, buildNumProcessSubitemVO("预约电话", Lists.newArrayList(reserveTelNumStr.split(","))));
    }

    private ProcessSubitemVO buildProcessSubitemVO(String itemName, String itemValue) {
        if (StringUtils.isEmpty(itemName) || StringUtils.isEmpty(itemValue)) {
            return null;
        }
        ProcessSubitemVO processSubitemVO = new ProcessSubitemVO();
        processSubitemVO.setItemName(itemName);
        processSubitemVO.setItemValue(itemValue);
        return processSubitemVO;
    }

    private NumProcessSubitemVO buildNumProcessSubitemVO(String itemName, List<String> nums) {
        if (StringUtils.isEmpty(itemName) || CollectionUtils.isEmpty(nums)) {
            return null;
        }
        NumProcessSubitemVO numProcessSubitemVO = new NumProcessSubitemVO();
        numProcessSubitemVO.setItemName(itemName);
        numProcessSubitemVO.setNums(nums);
        return numProcessSubitemVO;
    }

    private void addProcessModuleItem(List<ProcessModuleItemVO> processModuleItemVOS, String processContent, ProcessSubitemVO processItem, NumProcessSubitemVO numProcessItem) {
        if (processModuleItemVOS == null) {
            return;
        }
        ProcessModuleItemVO processModuleItemVO = buildProcessModuleItemVO(processContent, processItem, numProcessItem);
        if (processModuleItemVO == null) {
            return;
        }
        processModuleItemVOS.add(processModuleItemVO);
    }

    private ProcessModuleItemVO buildProcessModuleItemVO(String processContent, ProcessSubitemVO processItem, NumProcessSubitemVO numProcessItem) {
        if (StringUtils.isEmpty(processContent) && processItem == null && numProcessItem == null) {
            return null;
        }
        ProcessModuleItemVO processModuleItemVO = new ProcessModuleItemVO();
        processModuleItemVO.setProcessContent(processContent);
        processModuleItemVO.setProcessItem(processItem);
        processModuleItemVO.setNumProcessItem(numProcessItem);
        return processModuleItemVO;
    }

    private List<ServiceProcessItem> getServiceProcessData(List<AttrM> dealAttrs) {
        List<Map<String, String>> serviceStep = DealDetailUtils.getAttrVal(dealAttrs, "servicestep", new TypeReference<List<Map<String, String>>>() {
        });
        if (CollectionUtils.isEmpty(serviceStep)) {
            return null;
        }
        List<ServiceProcessItem> stepList = serviceStep.stream().filter(Objects::nonNull).map(map -> {
            String stepName = map.get("stepName");
            String stepDesc = map.get("stepDesc");
            if (StringUtils.isBlank(stepName)) {
                return null;
            }
            ServiceProcessItem item = new ServiceProcessItem();
            item.setStepName(stepName);
            item.setOtherInfo(stepDesc);
            return item;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return stepList;
    }

    private DealDetailInfoModel getDealDetailBasicInfo(ActivityCxt activityCxt) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        return CollectUtils.firstValue(dealDetailInfoModels);
    }

    @Data
    private static class ServiceProcess {
        private List<ServiceProcessItem> groups;
    }

    @Data
    private static class ServiceProcessItem {
        private String otherInfo;
        private String stepName;
    }
}
