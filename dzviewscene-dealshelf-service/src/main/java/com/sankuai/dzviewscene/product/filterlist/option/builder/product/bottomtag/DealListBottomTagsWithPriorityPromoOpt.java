package com.sankuai.dzviewscene.product.filterlist.option.builder.product.bottomtag;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductBottomTagsVP;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itembottomtags.ItemBottomTagsWithPromoOpt;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/4/12 20:42
 */
@VPointOption(name = "团单底部次卡拼团标签,带有优惠字样，按优先级只取一个",
        description = "团单底部次卡拼团标签,带有优惠字样，按优先级只取一个",
        code = "DealListBottomTagsWithPriorityPromoOpt")
public class DealListBottomTagsWithPriorityPromoOpt extends ProductBottomTagsVP<Void> {

    /**
     * 注入opt,避免编写重复代码
     */
    @Autowired
    private ItemBottomTagsWithPromoOpt itemBottomTagsWithPromoOpt;

    @Override
    public List<RichLabelVO> compute(ActivityCxt context, Param param, Void unused) {
        ProductM productM = param.getProductM();
        // 取真实售价
        String salePrice = param.getSalePrice();
        // 优惠信息
        int platform = Optional.ofNullable((Integer) context.getParam(ShelfActivityConstants.Params.platform)).orElse(1);
        RichLabelVO discount = itemBottomTagsWithPromoOpt.buildDiscountByNum(productM, platform);
        if (Objects.nonNull(discount)) {
            return Lists.newArrayList(discount);
        }
        // 次卡
        List<RichLabelVO> timeCardLabel = buildRichLabel(productM.getCardPrice(), salePrice, "");
        if (CollectionUtils.isNotEmpty(timeCardLabel)) {
            return Lists.newArrayList(timeCardLabel.get(0));
        }
        // 拼团
        List<RichLabelVO> pinTuanCardLabel = buildRichLabel(productM.getPinPrice(), salePrice, "");
        if (CollectionUtils.isNotEmpty(pinTuanCardLabel)) {
            return Lists.newArrayList(pinTuanCardLabel.get(0));
        }
        return null;
    }
}
