package com.sankuai.dzviewscene.product.dealstruct.options;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.struct.vpoints.DealDetailStructAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrItemModel;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrsModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/27 9:17 下午
 */
@VPointOption(name = "丽人-美妆团购详情结构化属性卡片认变化点", description = "丽人-美妆团购详情结构化属性卡片认变化点",code = BeautyCosmeticsDealDetailStructAttrListVPO.CODE, isDefault = true)
public class BeautyCosmeticsDealDetailStructAttrListVPO extends DealDetailStructAttrListVP<BeautyCosmeticsDealDetailStructAttrListVPO.Config> {

    public static final String CODE = "BeautyCosmeticsDealDetailStructAttrListVPO";

    private static final String SUITABLE_SKIN_QUALITY_ATTR_NAME = "suitable_skin_quality";

    private static final String SUITABLE_PART_ATTR_NAME = "suitable_body_parts";

    private static final String SERVICE_EFFECT_ATTR_NAME = "service_effect";

    private static final String SUITABLE_ALL_SKIN_QUALITY_ATTR_VALUE = "适用所有肤质";

    public static final String ALL_SKIN_QUALITY = "所有肤质";

    public static final String SUITABLE_PART_SKIN_QUALITY_ATTR_NAME = "suitable_part_skin_quality";

    private static final String SEPERATOR = "、";

    private static final String SUITABLE_RANGE_ATTR_NAME = "适用范围";

    @Override
    public List<StructAttrsModel> compute(ActivityCxt context, Param param, Config config) {
        List<AttrM> dealAttrs = param.getDealAttrs();
        Map<String, String> attrName2ShowNameMap = config.getAttrName2ShowNameMap();
        if (CollectionUtils.isEmpty(dealAttrs) || MapUtils.isEmpty(attrName2ShowNameMap)) {
            return null;
        }
        List<StructAttrItemModel> structAttrModels = attrName2ShowNameMap.entrySet().stream().map(entry -> {
            List<String> values = DealDetailUtils.getAttrValueByAttrName(dealAttrs, entry.getKey());
            if (SERVICE_EFFECT_ATTR_NAME.equals(entry.getKey())) {
                return buildStructAttrModel(entry.getValue(), values);
            }
            return buildSuitableRangeStructAttrItemModel(entry.getValue(), dealAttrs);
        }).filter(model -> model != null).collect(Collectors.toList());
        StructAttrsModel structAttrsModel = buildStructAttrsModel(config.getGroupName(), structAttrModels);
        if (structAttrsModel == null) {
            return null;
        }
        return Lists.newArrayList(structAttrsModel);
     }

    /**
     * 构造适用范围StructAttrItemModel：由 适用肤质 和 适用部位 两个团单属性组成
     *@param
     *@return
     */
    private StructAttrItemModel buildSuitableRangeStructAttrItemModel(String attrName, List<AttrM> dealAttrs) {
        List<String> attrValues = new ArrayList<>();
        String suitableSkinQuality = getSuitableSkinQualityAttrValue(dealAttrs);
        if (StringUtils.isNotEmpty(suitableSkinQuality)) {
            attrValues.add(suitableSkinQuality);
        }
        String suitablePart = getSuitablePart(dealAttrs);
        if (StringUtils.isNotEmpty(suitablePart)) {
            attrValues.add(suitablePart);
        }
        return buildStructAttrModel(attrName, attrValues);
    }

    /**
     * 获取团单适用部位
     *@param
     *@return
     */
    private String getSuitablePart(List<AttrM> dealAttrs) {
        List<String> suitablePart = DealDetailUtils.getAttrValueByAttrName(dealAttrs, SUITABLE_PART_ATTR_NAME);
        if (CollectionUtils.isEmpty(suitablePart)) {
            return null;
        }
        return StringUtils.join(suitablePart, SEPERATOR);
    }

    /**
     * 获取团单适用肤质：当行业属性【适用肤质】的值为“适用所有肤质”时，展示文案“所有肤质”；当行业属性【适用肤质】的值为“适用部分肤质”时，展示【适用部分肤质】这个行业属性下的选项值，存在多个值时以“、”隔开，例如“干性/混干肌肤、油痘肌”
     *@param
     *@return
     */
    private String getSuitableSkinQualityAttrValue(List<AttrM> dealAttrs) {
        String suitableSkinQuality = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, SUITABLE_SKIN_QUALITY_ATTR_NAME);
        if (SUITABLE_ALL_SKIN_QUALITY_ATTR_VALUE.equals(suitableSkinQuality)) {
            return ALL_SKIN_QUALITY;
        }
        List<String> suitablePartSkinQuality = DealDetailUtils.getAttrValueByAttrName(dealAttrs, SUITABLE_PART_SKIN_QUALITY_ATTR_NAME);
        if (CollectionUtils.isEmpty(suitablePartSkinQuality)) {
            return null;
        }
        return StringUtils.join(suitablePartSkinQuality, SEPERATOR);
    }

    private StructAttrsModel buildStructAttrsModel(String name, List<StructAttrItemModel> structAttrModels) {
        if (CollectionUtils.isEmpty(structAttrModels)) {
            return null;
        }
        StructAttrsModel structAttrsModel = new StructAttrsModel();
        structAttrsModel.setName(name);
        structAttrsModel.setStructAttrModels(structAttrModels);
        return structAttrsModel;
    }

    private StructAttrItemModel buildStructAttrModel(String name, List<String> values) {
        if (StringUtils.isEmpty(name) || CollectionUtils.isEmpty(values)) {
            return null;
        }
        StructAttrItemModel structAttrModel = new StructAttrItemModel();
        structAttrModel.setAttrName(name);
        structAttrModel.setAttrValues(values);
        return structAttrModel;
    }

    @Data
    @VPointCfg
    public static class Config {
        private Map<String, String> attrName2ShowNameMap;
        private String groupName;
    }
}
