package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.product.filterlist.vo.DzProductVO;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemButtonVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/5/1
 */
@VPoint(name = "填充商品图片相关数据", description = "包括比例、图片、角标，不返回数据直接填充", code = ProductPicPaddingVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductPicPaddingVP<T> extends PmfVPoint<Void, ProductPicPaddingVP.Param, T> {
    public static final String CODE = "ProductPicPaddingVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private DzProductVO dzProductVO;

        private ProductM productM;
    }
}
