package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.AbstractBarSkuCreator;
import com.sankuai.dzviewscene.product.dealstruct.vo.PopUpWindowVO;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/18 7:22 下午
 */
public class EntranceTicketCreator extends AbstractBarSkuCreator {

    @Override
    public boolean ideantify(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config) {
        return skuItemDto != null && skuItemDto.getProductCategory() == 4050L;
    }

    @Override
    protected String buildIcon(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config, boolean isSameCategorySku) {
        return null;
    }

    @Override
    protected Map<String, String> getSubtitlesAttrName2FormatMap(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config, boolean isHitDouhu) {
        //当服务项目内是否含酒水的字段为“是”时展示副标题，副标题展示如下{含N杯酒水}
        String containWine = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), "containWine");
        if (!isHitDouhu || "否".equals(containWine)) {
            return null;
        }
        return new LinkedHashMap<String, String>(){{
           put("quantity", "含%s酒水");
        }};
    }

    @Override
    protected PopUpWindowVO buildPopUpWindowVO(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config, boolean isHitDouhu) {
        return null;
    }

    @Override
    protected String extractCopies(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config) {
        int copies = skuItemDto.getCopies();
        return String.format("(%s份)", copies);
    }
}
