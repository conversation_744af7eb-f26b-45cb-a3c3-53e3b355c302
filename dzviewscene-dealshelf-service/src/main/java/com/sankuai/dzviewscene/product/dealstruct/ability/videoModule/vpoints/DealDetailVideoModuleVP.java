package com.sankuai.dzviewscene.product.dealstruct.ability.videoModule.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.videoModule.DealDetailVideoModuleBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.VideoModuleVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/4 8:56 下午
 */
@VPoint(name = "团购详情模块视频组件变化点", description = "团购详情模块视频组件变化点",code = DealDetailVideoModuleVP.CODE, ability = DealDetailVideoModuleBuilder.CODE)
public abstract class DealDetailVideoModuleVP<T> extends PmfVPoint<VideoModuleVO, DealDetailVideoModuleVP.Param, T> {

    public static final String CODE = "DealDetailVideoModuleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private List<AttrM> dealAttrs;
        private DealDetailDtoModel dealDetailDtoModel;
        private List<ProductSkuCategoryModel> productCategories;
        private List<DouhuResultModel> douhuResultModels;
    }
}
