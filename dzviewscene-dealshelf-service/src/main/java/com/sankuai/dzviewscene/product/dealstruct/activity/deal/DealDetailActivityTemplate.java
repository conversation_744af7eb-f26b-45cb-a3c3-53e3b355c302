package com.sankuai.dzviewscene.product.dealstruct.activity.deal;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.HairPermDyeDealDetailBuilderExt;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.detail.ability.builder.PlatformProductDetailBuilder;
import com.sankuai.dzviewscene.shelf.platform.detail.ability.builder.ProductDetailBuilder;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/22 8:39 下午
 */
@ActivityTemplate(activityCode = DealDetailActivity.CODE, name = "团购详情活动默认模板")
public class DealDetailActivityTemplate implements IActivityTemplate {

    @Override
    public Class<? extends IActivityFlow> flow() {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivityTemplate.flow()");
        return null;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivityTemplate.extParams(java.util.Map)");
    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivityTemplate.extContexts(java.util.List)");

    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivityTemplate.extValidators(java.util.List)");

    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivityTemplate.extAbilities(java.util.Map)");
        // 1. 配置填充能力
        abilities.put(PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE, MultiGroupPaddingFetcher.class);
        // 2. 配置VO转换能力
        abilities.put(ProductDetailBuilder.ABILITY_PRODUCT_DETAIL_CODE, PlatformProductDetailBuilder.class);
    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        extPoints.put(HairPermDyeDealDetailBuilderExt.EXT_POINT_PRODUCT_DETAIL_BUILDER_CODE, HairPermDyeDealDetailBuilderExt.class);
    }
}
