package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.cat.Cat;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.SkuAttrItemsVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/27 10:16 下午
 */
@VPointOption(name = "团购详情货attr列表默认变化点", description = "团购详情货attr列表默认变化点，支持配置", code = DefaultSkuAttrItemsVPO.CODE, isDefault = true)
public class DefaultSkuAttrItemsVPO extends SkuAttrItemsVP<DefaultSkuAttrItemsVPO.Config> {

    public static final String CODE = "DefaultSkuAttrItemsVPO";

    @Override
    public List<DealSkuItemVO> compute(ActivityCxt context, Param param, Config config) {
        SkuItemDto skuItemDto = param.getSkuItemDto();
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return null;
        }
        List<SkuAttrItemDto> skuAttrItemDtos = skuItemDto.getAttrItems();
        return skuAttrItemDtos.stream().map(attr -> {
            if (attr == null) {
                return null;
            }
            return buildDealSkuItemVO(attr.getChnName(), attr.getAttrValue());
        }).filter(dto -> dto != null).collect(Collectors.toList());
    }

    private DealSkuItemVO buildDealSkuItemVO(String name, String value) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.dealstruct.options.DefaultSkuAttrItemsVPO.buildDealSkuItemVO(java.lang.String,java.lang.String)");
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setType(0);
        dealSkuItemVO.setValue(value);
        return dealSkuItemVO;
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
