package com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.DealDetailSkuListsBuilder;
import lombok.Builder;
import lombok.Data;

@VPoint(name = "团购详情服务项目Icon变化点", description = "团购详情服务项目Icon变化点", code = SkuIconVP.CODE, ability = DealDetailSkuListsBuilder.CODE)
public abstract class SkuIconVP<T> extends PmfVPoint<String, SkuIconVP.Param, T> {

    public static final String CODE = "SkuIconVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private SkuItemDto skuItemDto;
    }
}
