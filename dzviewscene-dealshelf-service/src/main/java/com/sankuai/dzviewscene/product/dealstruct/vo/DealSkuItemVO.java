package com.sankuai.dzviewscene.product.dealstruct.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/25 4:21 下午
 */
@MobileDo(id = 0xb32395d)
public class DealSkuItemVO implements Serializable {

    /**
     * icon
     */
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    /**
     *
     */
    @MobileDo.MobileField(key = 0x2c7d)
    private DealSkuItemConfig config;

    /**
     * sku属性类型，为0时属性默认取name和value，为1时属性默认取name和picValue，为2时参照美容团购详情模块【服务步骤部分】
     */
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    /**
     * 二级属性值
     */
    @MobileDo.MobileField(key = 0x5995)
    private List<SkuAttrAttrItemVO> valueAttrs;

    /**
     * sku图片列表属性值
     */
    @MobileDo.MobileField(key = 0xedd3)
    private List<PicItemVO> picValues;

    /**
     * sku属性值
     */
    @MobileDo.MobileField(key = 0x97dd)
    private String value;

    /**
     * sku属性名
     */
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;

    @MobileDo.MobileField(key = 0xaf10)
    private String rightText;


    private ExtraExplainVO extraExplain;

    private String originalPrice;

    /**
     * 关闭标题
     */
    @MobileDo.MobileField(key = 0xcb3f)
    private String closeTitle;

    /**
     * 打开的标题
     */
    @MobileDo.MobileField(key = 0x31a5)
    private String openTitle;

    /**
     * 展示折叠信息
     */
    @MobileDo.MobileField(key = 0x9253)
    private boolean showAll;

    public DealSkuItemConfig getConfig() {
        return config;
    }

    public void setConfig(DealSkuItemConfig config) {
        this.config = config;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public List<SkuAttrAttrItemVO> getValueAttrs() {
        return valueAttrs;
    }

    public void setValueAttrs(List<SkuAttrAttrItemVO> valueAttrs) {
        this.valueAttrs = valueAttrs;
    }

    public List<PicItemVO> getPicValues() {
        return picValues;
    }

    public void setPicValues(List<PicItemVO> picValues) {
        this.picValues = picValues;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(String originalPrice) {
        this.originalPrice = originalPrice;
    }

    public ExtraExplainVO getExtraExplain() {
        return extraExplain;
    }

    public void setExtraExplain(ExtraExplainVO extraExplain) {
        this.extraExplain = extraExplain;
    }

    public String getRightText() {
        return rightText;
    }

    public void setRightText(String rightText) {
        this.rightText = rightText;
    }

    public boolean isShowAll() {
        return showAll;
    }

    public void setShowAll(boolean showAll) {
        this.showAll = showAll;
    }

    public String isOpenTitle() {
        return openTitle;
    }

    public void setOpenTitle(String openTitle) {
        this.openTitle = openTitle;
    }

    public String getCloseTitle() {
        return closeTitle;
    }

    public void setCloseTitle(String closeTitle) {
        this.closeTitle = closeTitle;
    }
}

