package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.dianping.lion.common.util.JsonUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.spuproduct.enums.SpuAttrEnum;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.shelf.utils.LogControl;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@VPointOption(name = "LE留资行业cross类目品推荐的团购列表组装",
        description = "LE留资行业cross类目品推荐的团购列表组装",
        code = "LECrossShopDealListOpt")
@Slf4j
public class LECrossShopDealListOpt extends ProductListVP<LECrossShopDealListOpt.Config> {

    @Resource
    private CompositeAtomService compositeAtomService;

    private static final int DEFAULT_PAGE_SIZE = 10;
    public static final double ZERO = 0.001;

    @Override
    public List<ProductM> compute(ActivityCxt context, Param param, Config config) {
        List<ProductM> productMS = param.getProductMS();
        if (CollectionUtils.isEmpty(productMS)) {
            return productMS;
        }
        //将商品还原回group模式
        Map<String, List<ProductM>> groupName2Products = productMS.stream()
                .filter(productM -> StringUtils.isNotBlank(productM.getAttr(DealListBuilder.PRODUCT_ATTR_GROUP_NAME)))
                .collect(Collectors.groupingBy(productM -> productM.getAttr(DealListBuilder.PRODUCT_ATTR_GROUP_NAME)));
        if (MapUtils.isEmpty(groupName2Products)) {
            return productMS;
        }
        List<ProductM> result = new ArrayList<>();
        // 取第一组商品：海马推荐商品，直接加入
        result.addAll(MapUtils.getObject(groupName2Products, config.getFirstShowGroupName(), Lists.newArrayList()));
        // 取第二组商品：海马配置标品，并且增加距离过滤
        add2ResultWhenNotExist(result, filterAndBuildSecondGroup(context, groupName2Products.get(config.getSecondShowGroupName()), config));
        // 取第三组商品：推荐侧召回数据，直接加入
        add2ResultWhenNotExist(result, getGroupProducts(groupName2Products.get(config.getThirdShowGroupName()), context, config));
        // 截取
        int pageSize = getPageSize(context, config);
        addTopTitleAndCardKey(context, result, config);
        if (context.getSceneCode().equals("edu_crosscat_deal_list")) {
            ShopM shopM = context.getParam(ShelfActivityConstants.Ctx.ctxShop);
            Map<String, Object> map = new HashMap<String, Object>() {{ put("_shelf_debug", true); }};
            LogControl.logFuc(map, () -> log.info(XMDLogFormat.build().putTag("scene", "edu_crosscat_deal_list").putTag("method", "DealListBuilder").message(String.format("edu_crosscat_deal_list.LECrossShopDealListOpt: dpCityId:%s, lng:%s, lat:%s, shopId:%s, platfrom:%s, dpUserId:%s, mtUserId:%s, productGroup:%s", context.getParam(PmfConstants.Params.dpCityId), context.getParam(PmfConstants.Params.lng), context.getParam(PmfConstants.Params.lat), shopM.getLongShopId(), context.getParam(PmfConstants.Params.platform), context.getParam(PmfConstants.Params.dpUserId), context.getParam(PmfConstants.Params.mtUserId), JsonUtils.toJson(result)))));
        }
        return result.stream().limit(pageSize).collect(Collectors.toList());
    }

    private List<ProductM> filterAndBuildSecondGroup(ActivityCxt context, List<ProductM> productMS, Config config) {
        if (productMS == null) {
            return new ArrayList<>();
        }
        Integer distanceLimit = getSpuDistanceLimit(context, config);
        if (distanceLimit == null || distanceLimit < 0) {
            return productMS;
        }
        List<ProductM> result = productMS.stream()
                .filter(productM -> {
                    // 避免标品召回超时，如果跳链为null则当作召回失败
                    if (productM == null || productM.getJumpUrl() == null ||productM.getJumpUrl().isEmpty()) {
                        Map<String, Object> map = new HashMap<String, Object>() {{ put("_shelf_debug", true); }};
                        LogControl.logFuc(map, () ->log.info(XMDLogFormat.build().putTag("scene", "edu_crosscat_deal_list").putTag("method", "DealListBuilder").message(String.format("edu_crosscat_deal_list.filterAndBuildGroup: dpCityId:%s, lng:%s, lat:%s, platfrom:%s, dpUserId:%s, mtUserId:%s", context.getParam(PmfConstants.Params.dpCityId), context.getParam(PmfConstants.Params.lng), context.getParam(PmfConstants.Params.lat), context.getParam(PmfConstants.Params.platform), context.getParam(PmfConstants.Params.dpUserId), context.getParam(PmfConstants.Params.mtUserId)))));
                        return false;
                    }
                    return getDistanceNum(productM) <= distanceLimit;
                })
                .collect(Collectors.toList());

        // 其他场景直接返回商品
        return result;
    }

    /**
     * 获取距离限制，如果没有配置，则表示没有距离限制
     * @param context
     * @param config
     * @return
     */
    private Integer getSpuDistanceLimit(ActivityCxt context, Config config) {
        return config.spuDistanceLimit;
    }

    private double getDistanceNum(ProductM productM) {
        Double distance = productM.getNearestShopDistance();
        return (distance == null || distance <0) ? 0.0 : distance;
    }

    private List<ProductM> getGroupProducts(List<ProductM> productMS, ActivityCxt context, Config config) {
        if (productMS == null) {
            return Lists.newArrayList();
        }
        // 其他场景直接返回商品
        return productMS;
    }

    private void add2ResultWhenNotExist(List<ProductM> result, List<ProductM> needAddProducts) {
        if (CollectionUtils.isEmpty(needAddProducts)) {
            return;
        }
        needAddProducts.stream()
                .filter(productM -> isProductMNotExist(result, productM))
                .forEach(result::add);
    }

    private boolean isProductMNotExist(List<ProductM> result, ProductM productM) {
        return result.stream()
                .noneMatch(existingProduct -> existingProduct.getProductId() == productM.getProductId()
                        && existingProduct.getProductType() == productM.getProductType());
    }


    private int getPageSize(ActivityCxt context, Config config) {
        int pageSize = 0;
        // 从config配置中取，再取不到则从入参中取，再走兜底
        pageSize = config.getLimit() != null ? config.getLimit() : ParamsUtil.getIntSafely(context, PmfConstants.Params.pageSize);
        if (pageSize < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        }
        return pageSize;
    }

    private void addTopTitleAndCardKey(ActivityCxt context, List<ProductM> productMS, Config config) {
        if (productMS == null) {
            return;
        }
        String topAreaTitle = null;
        int cardIdx = 1;
        String specialCardKey = config.getProductAttrKeyForSpecialCard();
        topAreaTitle = config.getRecommendTopTitle();

        // 为标品添加标题，并为相邻同一组商品添加specialCardKey
        for (int i = 0; i < productMS.size(); i++) {
            ProductM product = productMS.get(i);
            // 为特点商品添加堆头标题
            if (validateAddTopAreaTitle(context, product)) {
                addAttr(product, config.getProductAttrKeyForTopGroupTitle(), topAreaTitle);
            }
            // 为商品添加specialCardKey，用于前端标识同一组相邻商品
            addAttr(product, config.getProductAttrKeyForSpecialCard(), specialCardKey + cardIdx);
            if (i + 1 < productMS.size() && product.getProductType() != productMS.get(i + 1).getProductType()) {
                cardIdx++;
            }
        }
    }

    private boolean validateAddTopAreaTitle(ActivityCxt context, ProductM product) {
        if (product == null) {
            return false;
        }
        // 如果是标品则需要添加堆头标题
        return product.getProductType() == ProductTypeEnum.GENERAL_SPU.getType();
    }
    private void addAttr(ProductM productM, String attrKey, String attrValue) {
        if (productM == null || StringUtils.isAnyBlank(attrKey, attrValue)) {
            return;
        }
        productM.setAttr(attrKey, attrValue);
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 第一组商品的groupName，该组商品会展示在列表的顶部，并且附带特殊样式
         */
        private String firstShowGroupName = "置顶";

        /**
         * 第二组商品的groupName
         */
        private String secondShowGroupName = "广告";

        /**
         * 第三组商品的groupName
         */
        private String thirdShowGroupName = "推荐";

        /**
         * 置顶商品组的标题key
         */
        private String productAttrKeyForTopGroupTitle = "topAreaTitle";

        /**
         * 置顶商品组的副标题key
         */
        private String productAttrKeyForTopGroupSubTitle = "topAreaSubTitle";

        /**
         * 特殊卡商品组的key
         */
        private String productAttrKeyForSpecialCard = "specialCard";

        /**
         * 商品总个数，MaxShowNum
         */
        private Integer limit;

        /**
         * 留资过滤天数
         */
        private Integer leadsFilterDays;

        /**
         * 标品召回范围
         */
        private Integer spuDistanceLimit = 10000;

        /**
         * 商品顶部主标题
         */
        private String recommendTopTitle = "好课随心学";
    }
}
