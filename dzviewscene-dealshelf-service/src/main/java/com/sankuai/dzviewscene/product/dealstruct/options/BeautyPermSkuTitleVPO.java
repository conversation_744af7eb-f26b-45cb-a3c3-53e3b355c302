package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.SkuTitleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/27 9:17 下午
 */
@VPointOption(name = "丽人-烫染团购详情sku货标题默认变化点", description = "丽人-烫染团购详情sku货标题默认变化点",code = BeautyPermSkuTitleVPO.CODE, isDefault = false)
public class BeautyPermSkuTitleVPO extends SkuTitleVP<BeautyPermSkuTitleVPO.Config> {

    public static final String CODE = "BeautyPermSkuTitleVPO";

    private static final String OTHER_SKU_CATEGORY = "自填";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        String skuTitle = param.getSkuTitle();
        SkuItemDto skuItemDto = param.getSkuItemDto();
        if (skuItemDto == null) {
            return skuTitle;
        }
        List<ProductSkuCategoryModel> productCategories = param.getProductCategories();
        String skuCategory = DealDetailUtils.getSkuCategoryBySkuCategoryId(skuItemDto.getProductCategory(), productCategories);
        if (StringUtils.isEmpty(skuCategory) || skuCategory.equals(OTHER_SKU_CATEGORY)) {
            return skuTitle;
        }
        return skuCategory;
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
