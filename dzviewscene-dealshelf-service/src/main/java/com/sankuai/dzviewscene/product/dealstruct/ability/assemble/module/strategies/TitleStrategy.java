package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.struct.DealDetailStructAttrListBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.title.DealDetailTitleBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailTitleModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * created by zhangzhiyuan04 in 2021/12/14
 */
@Component("title")
public class TitleStrategy implements ModuleStrategy {

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        String abilityCode = DealDetailTitleBuilder.CODE;
        List<DealDetailTitleModel> detailTitleModels = activityCxt.getSource(abilityCode);
        if (CollectionUtils.isEmpty(detailTitleModels)) {
            return null;
        }
        DealDetailTitleModel title = CollectUtils.firstValue(detailTitleModels);
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        dealDetailModuleVO.setTitleModel(title.getTitle());
        return dealDetailModuleVO;
    }
}
