package com.sankuai.dzviewscene.product.filterlist.option.builder.product.jumpurl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductJumpUrlVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

@VPointOption(name = "穿戴甲跳转链接改写",
        description = "穿戴甲跳转链接改写",
        code = "PressOnNailProductJumpUrlOpt")
public class PressOnNailProductJumpUrlOpt extends ProductJumpUrlVP<PressOnNailProductJumpUrlOpt.Config> {
    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        ProductM productM = param.getProductM();
        if(productM != null && StringUtils.isNotBlank(productM.getJumpUrl())){
            if(CollectionUtils.isNotEmpty(productM.getSkuIdList())){
                return productM.getJumpUrl() + "&skuinitindex=" + productM.getSkuIdList().get(0);
            }else{
                return productM.getJumpUrl();
            }
        }
        return "";
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
