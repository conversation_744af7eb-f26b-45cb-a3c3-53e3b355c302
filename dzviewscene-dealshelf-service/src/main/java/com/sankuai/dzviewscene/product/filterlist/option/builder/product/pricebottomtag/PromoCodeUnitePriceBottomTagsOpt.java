package com.sankuai.dzviewscene.product.filterlist.option.builder.product.pricebottomtag;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceBottomTagsVP;
import com.sankuai.dzviewscene.product.utils.MerchantMemberPromoUtils;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * @description :
 * @date : 2025/4/22
 */
@VPointOption(name = "优惠码货架统一优惠感知",
        description = "优惠码货架统一优惠感知标签",
        code = "PromoCodeUnitePriceBottomTagsOpt")
public class PromoCodeUnitePriceBottomTagsOpt extends ProductPriceBottomTagsVP<PromoCodeUnitePriceBottomTagsOpt.Config> {

    @Override
    public List<DzTagVO> compute(ActivityCxt context, Param param, Config config) {
        if (param.getProductM() == null || config == null || config.getTagStyleConfig() == null) {
            return null;
        }
        // 团单多次卡优惠
        if (param.getProductM().isTimesDeal() && CollectionUtils.isNotEmpty(param.getDzPromoVOS())) {
            return buildDzTagVOs(param.getDzPromoVOS().get(0).getName(), config);
        }
        // 会员价优惠
        ProductPromoPriceM hasPromoPrice = PriceUtils.getUserHasPromoPrice(param.getProductM(), param.getCardM());
        if (MerchantMemberPromoUtils.hasMerchantMemberPromoByPromoDetailList(Lists.newArrayList(hasPromoPrice))) {
            return buildDzTagVOs("会员价", config);
        }
        // 神券价优惠
        if (DzPromoUtils.promoCombinationWithMagicalMemberCoupon(hasPromoPrice)){
            return buildDzTagVOs("神券价", config);
        }
        // 普通折扣
        if (CollectionUtils.isNotEmpty(param.getDzPromoVOS())) {
            return buildDzTagVOs(param.getDzPromoVOS().get(0).getName(), config);
        }
        return null;
    }


    private List<DzTagVO> buildDzTagVOs(String name, Config config) {
        DzTagVO dzTagVO = new DzTagVO(config.getTagStyleConfig().getTextColor(),
                config.getTagStyleConfig().isHasBorder(), config.getTagStyleConfig().getBackground(),
                name);
        return Lists.newArrayList(dzTagVO);
    }


    @VPointCfg
    @Data
    public static class Config {

        /**
         * 标签样式
         */
        private TagStyleConfig tagStyleConfig;
    }

    @Data
    public static class TagStyleConfig {
        private String textColor;
        private String background;
        private boolean hasBorder;
    }
}
