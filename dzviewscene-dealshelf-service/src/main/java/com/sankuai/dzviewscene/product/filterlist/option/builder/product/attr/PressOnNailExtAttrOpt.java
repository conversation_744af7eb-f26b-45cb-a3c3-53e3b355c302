package com.sankuai.dzviewscene.product.filterlist.option.builder.product.attr;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductExtAttrVP;
import com.sankuai.dzviewscene.product.shelf.utils.PressOnNailUtils;

import java.util.HashMap;
import java.util.Map;

@VPointOption(name = "穿戴甲商品埋点",
        description = "穿戴甲商品埋点",
        code = "PressOnNailExtAttrOpt")
public class PressOnNailExtAttrOpt extends ProductExtAttrVP<Void> {
    @Override
    public String compute(ActivityCxt context, Param param, Void unused) {
        Map<String, Object> oceanMap = new HashMap<>();
        oceanMap.put("customize_array", Lists.newArrayList(PressOnNailUtils.buildItemOceanMap(context, param.getProductM())));
        return JsonCodec.encode(oceanMap);
    }
}
