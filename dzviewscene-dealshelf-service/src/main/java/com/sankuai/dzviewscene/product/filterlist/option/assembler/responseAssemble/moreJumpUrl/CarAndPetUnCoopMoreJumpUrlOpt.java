package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.moreJumpUrl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.ability.model.CarAndPetUncoopShopShelfAttrM;
import com.sankuai.dzviewscene.product.ability.options.CarAndPetUnCoopCommonInfoOpt;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListMoreJumpUrlVP;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@VPointOption(name = "养车用车/宠物推荐列表频道跳转url", description = "养车用车/宠物推荐列表频道跳转url", code = CarAndPetUnCoopMoreJumpUrlOpt.CODE)
public class CarAndPetUnCoopMoreJumpUrlOpt extends DealFilterListMoreJumpUrlVP<CarAndPetUnCoopMoreJumpUrlOpt.Config> {
    public static final String CODE = "carAndPetUnCoopMoreJumpUrl";

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Config config) {
        CarAndPetUncoopShopShelfAttrM carAndPetUncoopShopShelfAttrM = activityCxt
                .getParam(CarAndPetUnCoopCommonInfoOpt.CODE);
        if (Objects.nonNull(carAndPetUncoopShopShelfAttrM)
                && StringUtils.isNotBlank(carAndPetUncoopShopShelfAttrM.getJumpUrl())) {
            return carAndPetUncoopShopShelfAttrM.getJumpUrl();
        }
        return StringUtils.EMPTY;

    }

    @VPointCfg
    @Data
    public static class Config {

    }
}
