package com.sankuai.dzviewscene.product.dealstruct.model;

import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.ExtraExplainVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.JumpUrlVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/3 7:52 下午
 */
@Data
public class DealDetailSkuListModuleGroupModel {

    /**
     * 服务项目列表组名
     */
    private String groupName;

    /**
     * 服务项目列表副标题
     */
    private String groupSubtitle;

    /**
     * 服务项目列表
     */
    private List<DealSkuGroupModuleVO> dealSkuGroupModuleVOS;

    /**
     * 标题-问号-弹窗
     */
    private ExtraExplainVO extraExplain;

    /**
     * 跳转
     */
    private JumpUrlVO jumpUrl;

    /**
     * 列表形式，默认0，1代表原点，2代表数字
     */
    private int dotType;

    /**
     * 最小显示几个，超过后会折叠
     */
    private int showNum;

    /**
     * 折叠文案的前缀
     * 查看更多9件
     */
    private String foldStr;

    /**
     * 服务项目列表组Title
     */
    private String groupTitle;

    /**
     * 描述信息
     */
    private String descModel;

}
