package com.sankuai.dzviewscene.product.filterlist.ability.builder.product;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityRequest;
import com.sankuai.athena.viewscene.framework.pmf.core.IVPoint;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.DealListModelAssembler;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.*;
import com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivity;
import com.sankuai.dzviewscene.product.filterlist.model.DealFilterListM;
import com.sankuai.dzviewscene.product.filterlist.vo.*;
import com.sankuai.dzviewscene.product.filterlist.vo.DzPromoVO;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.card.CardFetcher;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.userexposure.UserCreditPayFetcher;
import com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.utils.CardPromoUtils;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import com.sankuai.dzviewscene.shelf.framework.monitor.FloorItemsMonitor;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.shelf.utils.ShelfErrorUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/1
 */
@Slf4j
@Ability(code = DealListBuilder.CODE,
        name = "VO-团单列表构造",
        description = "团单列表构造。构造 List<DzProductVO>",
        activities = {DealFilterListActivity.CODE},
        dependency = {DealListModelAssembler.CODE, CardFetcher.CODE, UserCreditPayFetcher.CODE}
)
public class DealListBuilder extends PmfAbility<List<DzProductVO>, DealListBuilder.Request, DealListBuilder.Config> {
    public static final String CODE = "DealListBuilder";

    /**
     * 团单的group属性
     */
    public static final String PRODUCT_ATTR_GROUP_NAME = "from_group";

    @Resource
    private FloorItemsMonitor floorItemsMonitor;

    @Override
    public CompletableFuture<List<DzProductVO>> build(ActivityCxt ctx, Request request, Config config) {
        DealFilterListM dealFilterListM = ctx.getSource(DealListModelAssembler.CODE);
        if (dealFilterListM == null) {
            return CompletableFuture.completedFuture(new ArrayList<>());
        }
        ProductGroupM productGroup = getAssembledProducts(config, dealFilterListM.getProductGroupMs());
        if (productGroup == null || CollectionUtils.isEmpty(productGroup.getProducts())) {
            return CompletableFuture.completedFuture(new ArrayList<>());
        }
        // 多次卡召回标识
        ctx.addParam(TimesDealUtil.TIMES_DEAL_QUERY_FLAG, buildTimesDealQueryFlag(productGroup.getProducts()));
        return CompletableFuture.completedFuture(buildProductList(productGroup, ctx, request, config));
    }

    private ProductGroupM getAssembledProducts(Config config, Map<String, ProductGroupM> productGroupMs) {
        if (MapUtils.isEmpty(productGroupMs)) {
            return null;
        }
        if (!config.isUseMultiGroup()) {
            // 兼容老逻辑
            return productGroupMs.values().stream().filter(Objects::nonNull).findFirst().orElse(null);
        }
        ActivityM activity = productGroupMs.values().stream()
                .filter(productGroupM -> productGroupM != null && productGroupM.getActivity() != null)
                .map(ProductGroupM::getActivity)
                .findFirst().orElse(null);
        int total = productGroupMs.values().stream()
                .filter(productGroupM -> productGroupM != null && productGroupM.getTotal() > 0)
                .mapToInt(ProductGroupM::getTotal)
                .sum();
        boolean hasNext = productGroupMs.values().stream()
                .filter(productGroupM -> productGroupM != null)
                .anyMatch(ProductGroupM::isHasNext);
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(assembleProducts(productGroupMs));
        productGroupM.setPreLoadProducts(assemblePreLoadProductMap(productGroupMs));
        productGroupM.setActivity(activity);
        productGroupM.setStatistics(assembleStatistics(productGroupMs));
        productGroupM.setTotal(total);
        productGroupM.setHasNext(hasNext);
        return productGroupM;
    }

    private List<ProductM> assembleProducts(Map<String, ProductGroupM> productGroupMs) {
        List<ProductM> result = new ArrayList<>();
        for (String groupName : productGroupMs.keySet()) {
            ProductGroupM productGroupM = productGroupMs.get(groupName);
            if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())) {
                continue;
            }
            List<ProductM> products = productGroupM.getProducts();
            // 增加group标识
            products.forEach(productM -> productM.setAttr(PRODUCT_ATTR_GROUP_NAME, groupName));
            result.addAll(products);
        }
        return result;
    }

    private Map<String, Object> assembleStatistics(Map<String, ProductGroupM> productGroupMs) {
        Map<String, Object> resultValue = new HashMap<>();
        for (ProductGroupM productGroupM : productGroupMs.values()) {
            if (productGroupM == null || MapUtils.isEmpty(productGroupM.getStatistics())) {
                continue;
            }
            productGroupM.getStatistics().forEach((key, value) -> {
                if (resultValue.containsKey(key)) {
                    return;
                }
                resultValue.put(key, value);
            });
        }
        return resultValue;
    }

    private Map<Integer, List<ProductM>> assemblePreLoadProductMap(Map<String, ProductGroupM> productGroupMs) {
        Map<Integer, List<ProductM>> result = new HashMap<>();
        for (String groupName : productGroupMs.keySet()) {
            ProductGroupM productGroupM = productGroupMs.get(groupName);
            if (productGroupM == null || MapUtils.isEmpty(productGroupM.getPreLoadProducts())) {
                continue;
            }
            productGroupM.getPreLoadProducts().forEach((key, value) -> {
                if (CollectionUtils.isEmpty(value)) {
                    return;
                }
                // 增加group标识
                value.forEach(productM -> productM.setAttr(PRODUCT_ATTR_GROUP_NAME, groupName));
                if (result.containsKey(key)) {
                    result.get(key).addAll(value);
                } else {
                    result.put(key, value);
                }
            });
        }
        return result;
    }

    public boolean buildTimesDealQueryFlag(List<ProductM> products) {
        return products.stream().anyMatch(ProductM::isTimesDealQueryFlag);
    }

    private List<DzProductVO> buildProductList(ProductGroupM productGroup, ActivityCxt activityCxt, Request abilityReq, Config config) {
        // 获取到手价，方便后续的排序、取值
        PriceDisplayUtils.batchSetSalePrice(activityCxt, productGroup.getProducts());
        //商品列表后置处理变化点
        List<ProductM> productsPostDealVP = buildProductsByVP(productGroup.getProducts(), activityCxt, config);

        // 获取次数最少的团购次卡,并打标记,方便后面构建团购次卡标题中的营销标签
        TimesDealUtil.setPricePromoTagFlag(productsPostDealVP, activityCxt, config.getCreditPayExps());

        return productsPostDealVP.stream()
                .filter(Objects::nonNull)
                .map(productM -> buildDzProductVO(productM, activityCxt, abilityReq, config))
                .collect(Collectors.toList());
    }

    private List<ProductM> buildProductsByVP(List<ProductM> products, ActivityCxt ctx, Config config) {
        try {
            ProductListVP<List<ProductM>> vPoint = findVPoint(ctx, ProductListVP.CODE);
            List<ProductM> execute = vPoint.execute(ctx, ProductListVP
                    .Param
                    .builder()
                    .productMS(products)
                    .douHuMS(ctx.getSource(ShelfDouHuFetcher.CODE))
                    .build());
            return CollectionUtils.isEmpty(execute) ? Lists.newArrayList() : execute;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(ctx, e);
            return products;
        }
    }

    private DzProductVO buildDzProductVO(ProductM productM, ActivityCxt activityCxt, Request abilityReq, Config config) {
        DzProductVO dzProductVO = new DzProductVO();
        try {
            CardM cardM = activityCxt.getSource(CardFetcher.CODE);
            dzProductVO.setProductId(productM.getProductId());
            dzProductVO.setProductIdL(productM.getProductId());
            //paddingPic方法中活动角标填充后期废弃，activityTags改为当前类中buildActivityTag方法实现
            paddingPic(activityCxt, productM, dzProductVO);
            String salePrice = itemComponentSalePrice(activityCxt, productM, cardM);
            dzProductVO.setSalePrice(salePrice);
            dzProductVO.setRichTitle(itemRichTitle(activityCxt, productM));
            dzProductVO.setTitle(itemTitle(activityCxt, productM));
            dzProductVO.setExpressOptimize(productM.isExpressOptimize());
            dzProductVO.setSalePriceDesc(itemComponentSalePriceDesc(activityCxt, productM));
            dzProductVO.setMarketPrice(itemComponentMarketPrice(activityCxt, productM));
            String jumpUrl = buildJumpUrl(activityCxt, productM);
            if(StringUtils.isNotBlank(jumpUrl)){
                productM.setJumpUrl(jumpUrl);
            }
            dzProductVO.setDetailJumpUrl(productM.getJumpUrl());
            dzProductVO.setButtonJumpUrl(productM.getOrderUrl());
            dzProductVO.setSale(itemComponentSale(activityCxt, productM));
            dzProductVO.setPurchase(productM.getPurchase());
            dzProductVO.setPurchaseRich(itemComponentPurchase(activityCxt, productM));
            dzProductVO.setItemBottomTagList(itemBottomTagList(activityCxt, productM, salePrice));
            dzProductVO.setProductTags(itemComponentProductTags(activityCxt, productM));
            dzProductVO.setProductRichTags(itemComponentProductRichTags(activityCxt, productM));
            List<DzPromoVO> dzPromoVOS = itemComponentPromo(activityCxt, productM, salePrice);
            // 多次卡不展示promoVOList
            dzProductVO.setPromoVOList(productM.isTimesDeal() ? null : dzPromoVOS);
            dzProductVO.setPinPrice(buildDzFixPrice(productM.getPinPrice()));
            dzProductVO.setCardPrice(buildDzFixPrice(productM.getCardPrice()));
            dzProductVO.setExtAttrsStr(buildExtAttrsStr(activityCxt, productM));
            dzProductVO.setActivityTags(buildActivityTag(activityCxt, productM, abilityReq, dzProductVO));
            dzProductVO.setShop(buildShopVO(productM.getShopMs()));
            dzProductVO.setPriceAboveTags(buildPriceAboveTags(activityCxt, productM));
            List<DzTagVO> dzTagVOS = buildPriceBottomPromoTag(activityCxt, productM, abilityReq, cardM, salePrice);
            // 多次卡标签合并
            dzProductVO.setPriceBottomPromoTagList(productM.isTimesDeal() ? buildPriceBottomPromoTagForTilesDeal(dzPromoVOS, dzTagVOS) : dzTagVOS);
            dzProductVO.setActivityRemainSeconds(fillActivityRemainSeconds(productM));
            dzProductVO.setBtn(buildBtn(activityCxt, productM));
            dzProductVO.setWarmUp(buildWarmUp(activityCxt, productM));
            dzProductVO.setActivityEndTime(buildActivityEndTime(activityCxt, productM));
            // 单位价格
            dzProductVO.setUnitPrice(buildProductUnitPrice(activityCxt, productM, dzProductVO));
            dzProductVO.setSalePricePrefixDesc(buildProductPricePrefixDesc(activityCxt,productM,dzProductVO));
            dzProductVO.setPicActivityTags(buildPicActivityTag(activityCxt, productM, abilityReq));
            dzProductVO.setPriceBottomTags(buildPriceBottomTags(activityCxt, productM, cardM, dzPromoVOS));
            // 神会员水位监控
            floorItemsMonitor.doMonitor(activityCxt, dzProductVO);
        } catch (Exception ex) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, ex);
        }
        return dzProductVO;
    }

    private List<DzTagVO> buildPriceBottomTags(ActivityCxt activityCxt, ProductM productM, CardM cardM, List<DzPromoVO> dzPromoVOS) {
        try {
            ProductPriceBottomTagsVP<?> varyPoint = findVPoint(activityCxt, ProductPriceBottomTagsVP.CODE);
            if (Objects.isNull(varyPoint) || Objects.isNull(varyPoint.getVPointOptionCode())) {
                return null;
            }
            return Optional.ofNullable(varyPoint)
                    .map(opt -> opt.execute(activityCxt,
                            ProductPriceBottomTagsVP.Param.builder()
                                    .productM(productM)
                                    .dzPromoVOS(dzPromoVOS)
                                    .cardM(cardM)
                                    .build()))
                    .orElse(null);
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
        }
        return null;
    }

    private List<DzActivityTagVO> buildPicActivityTag(ActivityCxt activityCxt, ProductM productM, Request abilityReq) {
        try {
            ProductPicActivityTagVP<?> varyPoint = findVPoint(activityCxt, ProductPicActivityTagVP.CODE);
            if (Objects.isNull(varyPoint) || Objects.isNull(varyPoint.getVPointOptionCode())) {
                return null;
            }
            return Optional.ofNullable(varyPoint)
                    .map(opt -> opt.execute(activityCxt,
                            ProductPicActivityTagVP.Param.builder()
                                    .productM(productM)
                                    .platform(abilityReq.getPlatform())
                                    .build()))
                    .orElse(null);
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
        }
        return null;
    }

    private String buildProductPricePrefixDesc(ActivityCxt activityCxt, ProductM productM, DzProductVO dzProductVO) {
        try {
            ProductSalePricePrefixDescVP<?> vp = findVPoint(activityCxt, ProductSalePricePrefixDescVP.CODE);
            return vp.execute(activityCxt, ProductSalePricePrefixDescVP.Param.builder()
                    .productM(productM)
                    .dzProductVO(dzProductVO)
                    .build());
        } catch (Exception e) {
            log.error("salePricePrefixDesc error, productM:{}", productM, e);
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return StringUtils.EMPTY;
        }
    }

    private List<DzTagVO> buildPriceAboveTags(ActivityCxt activityCxt, ProductM productM) {
        try {
            ProductPriceAboveTagVP<?> itemActivityEndTimeVP = findVPoint(activityCxt, ProductPriceAboveTagVP.CODE);
            return itemActivityEndTimeVP.execute(activityCxt, ProductPriceAboveTagVP.Param.builder()
                    .productM(productM)
                    .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return null;
        }
    }

    private String buildProductUnitPrice(ActivityCxt cxt, ProductM productM, DzProductVO dzProductVO) {
        try {
            ProductUnitPriceVP<?> vp = findVPoint(cxt, ProductUnitPriceVP.CODE);
            return vp.execute(cxt, ProductUnitPriceVP.Param.builder()
                            .productM(productM)
                            .dzProductVO(dzProductVO)
                            .build());
        } catch (Exception e) {
            log.error("buildProductUnitPrice error, productM:{}", productM, e);
            ShelfErrorUtils.addBuilderCatError(cxt, productM, e);
            return StringUtils.EMPTY;
        }
    }

    private String buildJumpUrl(ActivityCxt activityCxt, ProductM productM) {
        try {
            ProductJumpUrlVP<?> productJumpUrlVP = findVPoint(activityCxt, ProductJumpUrlVP.CODE);
            return productJumpUrlVP.execute(activityCxt, ProductJumpUrlVP.Param.builder()
                    .productM(productM)
                    .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return productM.getJumpUrl();
        }
    }

    private CountdownLabelVO buildActivityEndTime(ActivityCxt activityCxt, ProductM productM) {
        try {
            ProductActivityEndTimeVP<?> itemActivityEndTimeVP = findVPoint(activityCxt, ProductActivityEndTimeVP.CODE);
            return itemActivityEndTimeVP.execute(activityCxt, ProductActivityEndTimeVP.Param.builder()
                    .productM(productM)
                    .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return null;
        }
    }

    /**
     * 补充 价格描述标签
     *
     * @param activityCxt
     * @param productM
     * @return
     */
    private String itemComponentSalePriceDesc(ActivityCxt activityCxt, ProductM productM) {
        try {
            ProductSalePriceDescVP<?> productSalePriceDescVP = findVPoint(activityCxt, ProductSalePriceDescVP.CODE);
            return productSalePriceDescVP.execute(activityCxt, ProductSalePriceDescVP.Param.builder()
                    .productM(productM)
                    .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 补充 底部富文本标签
     *
     * @param activityCxt
     * @param productM
     * @param salePrice
     * @return
     */
    private List<RichLabelVO> itemBottomTagList(ActivityCxt activityCxt, ProductM productM, String salePrice) {
        try {
            ProductBottomTagsVP<?> productBottomTagsVP = findVPoint(activityCxt, ProductBottomTagsVP.CODE);
            return productBottomTagsVP.execute(activityCxt, ProductBottomTagsVP.Param.builder()
                    .productM(productM)
                    .salePrice(salePrice)
                    .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return new ArrayList<>();
        }
    }

    /**
     * 补充价格下方标签
     *
     * @param productM
     * @param abilityReq
     * @return
     */
    @SuppressWarnings("unchecked")
    private List<DzTagVO> buildPriceBottomPromoTag(ActivityCxt activityCxt, ProductM productM, Request abilityReq, CardM cardM, String salePrice) {
        try {
            List<IVPoint> vPoints = findVPoints(activityCxt, ProductPriceBottomTagVP.CODE);
            if (CollectionUtils.isEmpty(vPoints)) {
                return null;
            }
            List<DzTagVO> result = Lists.newArrayList();
            vPoints.forEach(vPoint -> {
                Object vPointResult = vPoint.execute(activityCxt,
                        ProductPriceBottomTagVP.Param.builder()
                                .productM(productM)
                                .cardM(cardM)
                                .salePrice(salePrice)
                                .platform(abilityReq.getPlatform())
                                .build());
                if (vPointResult == null) {
                    return;
                }
                result.addAll((List<DzTagVO>) vPointResult);
            });
            return result;
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    /**
     * 多次卡价格下方标签
     */
    private List<DzTagVO> buildPriceBottomPromoTagForTilesDeal(List<DzPromoVO> promoVOList, List<DzTagVO> priceBottomPromoTagList) {
        if (CollectionUtils.isEmpty(promoVOList) && CollectionUtils.isEmpty(priceBottomPromoTagList)) {
            return null;
        }
        if (!CollectionUtils.isEmpty(priceBottomPromoTagList)) {
            priceBottomPromoTagList.forEach(priceBottomPromoTag -> priceBottomPromoTag.setBorderRadius(3));
        }
        if (CollectionUtils.isEmpty(promoVOList)) {
            return priceBottomPromoTagList;
        }
        List<DzTagVO> result = Lists.newArrayList();
        promoVOList.forEach(promoVO -> {
            DzTagVO dzTagVO = new DzTagVO();
            BeanUtils.copyProperties(promoVO, dzTagVO);
            dzTagVO.setBorderColor(ColorUtils.colorFF4B10);
            dzTagVO.setTextColor(ColorUtils.colorFF4B10);
            dzTagVO.setHasBorder(true);
            dzTagVO.setBorderRadius(3);
            result.add(dzTagVO);
        });
        result.addAll(priceBottomPromoTagList);
        return result;
    }

    /**
     * 补充按钮
     *
     * @param productM
     * @return
     */
    private DzSimpleButtonVO buildBtn(ActivityCxt activityCxt, ProductM productM) {
        try {
            ProductButtonVP<?> productButtonVP = findVPoint(activityCxt, ProductButtonVP.CODE);
            return productButtonVP.execute(activityCxt,
                    ProductButtonVP.Param.builder()
                            .productM(productM)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    /**
     * 预热秒杀倒计时（距结束xxx、xxx后开抢、xxx后下一场），当前版本在抢购按钮下方
     *
     * @param activityCxt
     * @param productM
     * @return
     */
    private WarmUpVO buildWarmUp(ActivityCxt activityCxt, ProductM productM) {
        try {
            ProductWarmUpVP<?> productWarmUpVP = findVPoint(activityCxt, ProductWarmUpVP.CODE);
            return productWarmUpVP.execute(activityCxt,
                    ProductWarmUpVP.Param.builder()
                            .productM(productM)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
            return null;
        }
    }

    /**
     * 补充商品倒计时信息
     *
     * @param productM
     * @return
     */
    private long fillActivityRemainSeconds(ProductM productM) {
        CountdownLabelVO countdownLabelVO = RainbowSecKillUtils.getSecKillCountdownLabelFromRainbow(productM);
        if (Objects.isNull(countdownLabelVO)) {
            return 0L;
        }
        return countdownLabelVO.getTimestamp();
    }

    /**
     * 商品扩展属性信息
     *
     * @param activityCxt
     * @param productM
     * @return
     */
    private String buildExtAttrsStr(ActivityCxt activityCxt, ProductM productM) {
        try {
            ProductExtAttrVP<?> productExtAttrVP = findVPoint(activityCxt, ProductExtAttrVP.CODE);
            return productExtAttrVP.execute(activityCxt,
                    ProductExtAttrVP.Param.builder()
                            .productM(productM)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * @param productM
     * @return productTags, 通常是副标题
     */
    private List<String> itemComponentProductTags(ActivityCxt activityCxt, ProductM productM) {
        try {
            ProductTagsVP<?> productTagsVP = findVPoint(activityCxt, ProductTagsVP.CODE);
            return productTagsVP.execute(activityCxt,
                    ProductTagsVP.Param.builder()
                            .productM(productM)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    /**
     * 商品富文本标签
     * @param activityCxt
     * @param productM
     * @return
     */
    private List<IconRichLabelVO> itemComponentProductRichTags(ActivityCxt activityCxt, ProductM productM) {
        try {
            ProductRichTagsVP<?> productRichTagsVP = findVPoint(activityCxt, ProductRichTagsVP.CODE);
            return productRichTagsVP.execute(activityCxt,
                    ProductRichTagsVP.Param.builder()
                            .productM(productM)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    /**
     * @param productM
     * @return 广义的购买信息，不局限于用户X小时前购买，可包括预售时间信息（预售时间也可以通过增加新字段返回，参照团购货架）
     */
    private RichLabelVO itemComponentPurchase(ActivityCxt activityCxt, ProductM productM) {
        try {
            ProductPurchaseVP<?> productPurchaseVP = findVPoint(activityCxt, ProductPurchaseVP.CODE);
            return productPurchaseVP.execute(activityCxt,
                    ProductPurchaseVP.Param.builder()
                            .productM(productM)
                            .build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    /**
     * 填充活动标签信息
     *
     * @param activityCxt
     * @param productM
     * @param abilityReq
     * @param dzProductVO
     * @return
     */
    private List<DzActivityTagVO> buildActivityTag(ActivityCxt activityCxt, ProductM productM, Request abilityReq, DzProductVO dzProductVO) {
        try {
            if (CollectionUtils.isNotEmpty(dzProductVO.getActivityTags())) {
                return dzProductVO.getActivityTags();
            }
            ProductActivityTagsVP<?> varyPoint = findVPoint(activityCxt, ProductActivityTagsVP.CODE);
            if (Objects.isNull(varyPoint) || Objects.isNull(varyPoint.getVPointOptionCode())) {
                return null;
            }
            return Optional.ofNullable(varyPoint)
                    .map(opt -> opt.execute(activityCxt,
                            ProductActivityTagsVP.Param.builder()
                                    .productM(productM)
                                    .platform(abilityReq.getPlatform())
                                    .filterId(abilityReq.getSelectedFilterId())
                                    .build()))
                    .orElse(null);
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
        }
        return null;
    }

    private ShopVO buildShopVO(List<ShopM> shopMs) {
        if (CollectionUtils.isEmpty(shopMs)) {
            return null;
        }
        ShopM shopM = shopMs.get(0);
        ShopVO shopVO = new ShopVO();
        shopVO.setShopId(shopM.getLongShopId());
        shopVO.setShopName(shopM.getShopName());
        shopVO.setBranchName(shopM.getBranchName());
        shopVO.setRegionName(shopM.getMainRegionName());
        shopVO.setDistance(shopM.getDistance());
        shopVO.setScore(shopM.getScore());
        shopVO.setScoreTag(shopM.getScoreTag());
        return shopVO;
    }

    /**
     * 填充图片信息【pic、picScale、activityTags】
     */
    private void paddingPic(ActivityCxt activityCxt, ProductM productM, DzProductVO productVO) {
        try {
            ProductPicPaddingVP<?> varyPoint = findVPoint(activityCxt, ProductPicPaddingVP.CODE);
            varyPoint.execute(activityCxt,
                    ProductPicPaddingVP.Param.builder().productM(productM).dzProductVO(productVO).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
        }
    }

    private String itemComponentSalePrice(ActivityCxt activityCxt, ProductM productM, CardM cardM) {
        try {
            ProductSalePriceVP<?> productSalePriceVP = findVPoint(activityCxt, ProductSalePriceVP.CODE);
            String price = productSalePriceVP.execute(activityCxt, ProductSalePriceVP.Param.builder().productM(productM).cardM(cardM).build());
            if (FloorsBuilderExtAdapter.EMPTY_VALUE.equals(price)) {
                return StringUtils.EMPTY;
            }
            if (StringUtils.isNotBlank(price)) {
                return price;
            }
            return itemComponentDefaultSalePrice(activityCxt, productM, cardM);
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return StringUtils.EMPTY;
        }
    }

    private String itemRichTitle(ActivityCxt activityCxt, ProductM productM) {
        try {
            ProductRichTitleVP<?> productRichTitleVP = findVPoint(activityCxt, ProductRichTitleVP.CODE);
            if (productRichTitleVP == null) {
                return StringUtils.EMPTY;
            }
            return productRichTitleVP.execute(activityCxt,
                    ProductRichTitleVP.Param.builder().productM(productM).build());
        } catch (Exception e) {
            log.error("itemRichTitle error, productM:{}", productM, e);
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
        }
        return StringUtils.EMPTY;
    }

    private String itemTitle(ActivityCxt activityCxt, ProductM productM) {
        try {
            ProductTitleVP<?> productTitleVP = findVPoint(activityCxt, ProductTitleVP.CODE);
            if (productTitleVP == null) {
                return productM.getTitle();
            }
            return productTitleVP.execute(activityCxt,
                    ProductTitleVP.Param.builder().productM(productM).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
        }
        return productM.getTitle();
    }

    /**
     * 商品售价默认逻辑
     * 0、取卡价（当前没有）
     * 1、取玩美季价
     * 2、取立减价
     * 3、取默认售价
     *
     * @param activityCxt
     * @param productM
     * @param cardM
     * @return
     */
    private String itemComponentDefaultSalePrice(ActivityCxt activityCxt, ProductM productM, CardM cardM) {
        try {
            //卡
            ProductPromoPriceM cardPromo = CardPromoUtils.getFirstUserHoldCardPromo(productM.getPromoPrices(), cardM);
            if (cardPromo != null && StringUtils.isNotEmpty(cardPromo.getPromoPriceTag())) {
                //有卡优惠 且 卡优惠没有倒挂，才展示卡价
                if (!CardPromoUtils.isCardDaoGuaPromo(productM.getPromoPrices())) {
                    return cardPromo.getPromoPriceTag();
                }
            }
            //立减
            ProductPromoPriceM productPromoPriceM = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
            if (productPromoPriceM != null && StringUtils.isNotEmpty(productPromoPriceM.getPromoPriceTag())) {
                return productPromoPriceM.getPromoPriceTag();
            }
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
        }
        //团购
        return productM.getBasePriceTag();
    }

    private String itemComponentMarketPrice(ActivityCxt activityCxt, ProductM productM) {
        try {
            ProductMarketPriceVP<?> itemMarketPriceVP = findVPoint(activityCxt, ProductMarketPriceVP.CODE);
            String price = itemMarketPriceVP.execute(activityCxt, ProductMarketPriceVP.Param.builder().productM(productM).build());
            if (FloorsBuilderExtAdapter.EMPTY_VALUE.equals(price)) {
                return StringUtils.EMPTY;
            }
            if (StringUtils.isNotBlank(price)) {
                return price;
            }
            return productM.getMarketPrice();
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 销量标签
     *
     * @param activityCxt
     * @param productM
     * @return
     */
    private String itemComponentSale(ActivityCxt activityCxt, ProductM productM) {
        if (productM.getSale() == null || StringUtils.isBlank(productM.getSale().getSaleTag())) {
            return StringUtils.EMPTY;
        }
        // 走VP实现
        try {
            ProductSaleVP<?> productSaleVP = findVPoint(activityCxt, ProductSaleVP.CODE);
            // 兜底没有勾选opt的情况
            if (productSaleVP == null || productSaleVP.getVPointOptionCode() == null) {
                return productM.getSale().getSaleTag();
            }
            return productSaleVP.execute(activityCxt, ProductSaleVP.Param.builder().productM(productM).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
        }
        // 兜底走老逻辑
        return productM.getSale().getSaleTag();
    }

    private List<DzPromoVO> itemComponentPromo(ActivityCxt activityCxt, ProductM productM, String salePrice) {
        try {
            ProductPromosVP<?> vPoint = findVPoint(activityCxt, ProductPromosVP.CODE);
            List<DzPromoVO> result = vPoint.execute(activityCxt,
                    ProductPromosVP.Param.builder()
                            .productM(productM)
                            .salePrice(salePrice)
                            .build());
            return formatPromoList(result);
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }

    private List<DzPromoVO> formatPromoList(List<DzPromoVO> promoList) {
        if (CollectionUtils.isEmpty(promoList)) {
            return null;
        }
        promoList = promoList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(promoList)) {
            return null;
        }
        return promoList;
    }

    private DzFixPriceVO buildDzFixPrice(ProductPriceM productPriceM) {
        if (productPriceM == null) {
            return null;
        }
        DzFixPriceVO dzFixPriceVO = new DzFixPriceVO();
        dzFixPriceVO.setPriceDesc(productPriceM.getPriceDesc());
        dzFixPriceVO.setPrice(productPriceM.getPriceTag());
        return dzFixPriceVO;
    }

    @AbilityCfg
    @Data
    public static class Config {
        /**
         * 是否使用多组商品进行聚合
         */
        private boolean useMultiGroup = false;

        private List<String> creditPayExps = Lists.newArrayList();
    }

    @AbilityRequest
    @Data
    public static class Request {
        /**
         * {@link ShelfActivityConstants.Params#platform}
         */
        private int platform;

        /**
         * {@link ShelfActivityConstants.Params#selectedFilterId}
         */
        private int selectedFilterId;
    }
}
