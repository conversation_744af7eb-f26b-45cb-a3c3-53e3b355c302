package com.sankuai.dzviewscene.product.dealstruct.options.skuList.edu;


import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.dto.EduTechnicianVideoDTO;
import com.sankuai.dzviewscene.product.ability.options.LoadDealTeacherAndTrialClassOpt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.EduSkuUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.*;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@VPointOption(name = "在线教育团单的课程列表变化点，带开课时间", description = "在线教育团单的课程列表变化点，带开课时间", code = EduOnlineDealClassItemSkuListOpt.CODE)
public class EduOnlineDealClassItemSkuListOpt extends SkuListModuleVP<EduOnlineDealClassItemSkuListOpt.Config> {

    public static final String CODE = "eduOnlineDealClassItemSkuListOpt";

    public static final String ATTR_COURSE_METHOD = "course_method";
    public static final String COURSE_NUM_APPEND = "课时";
    public static final String ATTR_COURSE_PLAN = "course_plan";
    public static final int SKU_ITEM_TYPE_ATTR_LIST = 2;
    public static final String CLASS_ITEM_TITLE = "课程内容";
    public static final int VIDEO_STATUS_PASS = 0;


    @Override
    public List<DealDetailSkuListModuleGroupModel> compute(ActivityCxt activityCxt, Param param, Config config) {
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        if (dealDetailInfoModel == null) {
            return null;
        }
        List<DealSkuVO> dealSkuList = buildSkuVO(activityCxt, dealDetailInfoModel, config);
        if (CollectionUtils.isEmpty(dealSkuList)) {
            return null;
        }
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(dealSkuList);
        DealDetailSkuListModuleGroupModel dealDetailSkuListModuleGroupModel = new DealDetailSkuListModuleGroupModel();
        dealDetailSkuListModuleGroupModel.setDealSkuGroupModuleVOS(Lists.newArrayList(dealSkuGroupModuleVO));
        return Lists.newArrayList(dealDetailSkuListModuleGroupModel);
    }

    /**
     * 构建SkuVo
     *
     * @param context
     * @param dealDetailInfoModel
     * @param config
     * @return
     */
    private List<DealSkuVO> buildSkuVO(ActivityCxt context, DealDetailInfoModel dealDetailInfoModel, Config config) {
        List<CoursePlan> coursePlanList = getCoursePlanList(dealDetailInfoModel.getDealAttrs());
        List<DealSkuItemVO> skuItemList = buildSkuItemList(dealDetailInfoModel, config, coursePlanList);
        if (CollectionUtils.isEmpty(skuItemList)) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(DealDetailUtils.getAttrSingleValueByAttrName(dealDetailInfoModel.getDealAttrs(), ATTR_COURSE_METHOD));
        dealSkuVO.setSubTitle(getTotalClassHours(coursePlanList));
        dealSkuVO.setJumpUrl(getOnlineClassJumpUrl(dealDetailInfoModel, config, context));
        dealSkuVO.setItems(skuItemList);
        return Lists.newArrayList(dealSkuVO);
    }

    /**
     * 构建SkuItem列表
     *
     * @param dealDetailInfoModel
     * @param config
     * @param coursePlanList
     * @return
     */
    private List<DealSkuItemVO> buildSkuItemList(DealDetailInfoModel dealDetailInfoModel, Config config, List<CoursePlan> coursePlanList) {
        if (CollectionUtils.isEmpty(coursePlanList)) {
            return null;
        }
        List<DealSkuItemVO> skuItemList = Lists.newArrayList();
        List<AttrM> dealAttrs = dealDetailInfoModel.getDealAttrs();
        //目标证书
        skuItemList.add(EduSkuUtils.buildTargetCertificate(dealAttrs));
        //学习科目
        skuItemList.add(EduSkuUtils.buildSubject(dealAttrs));
        //课程效果
        skuItemList.add(EduSkuUtils.buildCourseEffect(dealAttrs));
        //开班时间
        skuItemList.add(EduSkuUtils.buildOpenClassTimeSkuItem(dealDetailInfoModel));
        //课程安排
        skuItemList.add(EduSkuUtils.buildClassSchedule(dealAttrs));
        //课程内容
        skuItemList.add(buildCourseListItem(coursePlanList));
        return skuItemList.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }


    private DealSkuItemVO buildCourseListItem(List<CoursePlan> coursePlanList) {
        if (CollectionUtils.isEmpty(coursePlanList)) {
            return null;
        }
        List<SkuAttrAttrItemVO> valueAttrs = coursePlanList.stream()
                .filter(plan -> plan != null && StringUtils.isNotBlank(plan.getCourseModule()))
                .map(plan -> {
                    SkuAttrAttrItemVO attrItemVO = new SkuAttrAttrItemVO();
                    if (plan.getCourseTimeNum() != null) {
                        attrItemVO.setInfo(Lists.newArrayList(
                                plan.getCourseTimeNum().setScale(1, BigDecimal.ROUND_DOWN)
                                        .stripTrailingZeros().toPlainString() + COURSE_NUM_APPEND));
                    }
                    attrItemVO.setName(plan.getCourseModule());
                    return attrItemVO;
                }).collect(Collectors.toList());
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(CLASS_ITEM_TITLE);
        dealSkuItemVO.setType(SKU_ITEM_TYPE_ATTR_LIST);
        dealSkuItemVO.setValueAttrs(valueAttrs);
        return dealSkuItemVO;
    }


    private JumpUrlVO getOnlineClassJumpUrl(DealDetailInfoModel dealDetailInfoModel, Config config, ActivityCxt context) {
        boolean hasOnlineClass = isHasOnlineClass(context);
        if (!hasOnlineClass || PlatformUtil.isAnyXcx(context.getParam(ProductDetailActivityConstants.Params.mpSource))) {
            return null;
        }
        JumpUrlVO jumpUrlVO = new JumpUrlVO();
        jumpUrlVO.setIcon(config.getTrialClassIcon());
        jumpUrlVO.setName(config.getTrialClassJumpName());
        jumpUrlVO.setUrl(getTrialClassUrl(dealDetailInfoModel, config, context));
        return jumpUrlVO;
    }

    private boolean isHasOnlineClass(ActivityCxt context) {
        List<EduTechnicianVideoDTO> teacherAndVideoList = context.getParam(LoadDealTeacherAndTrialClassOpt.CODE);
        if (CollectionUtils.isEmpty(teacherAndVideoList)) {
            return false;
        }
        return teacherAndVideoList.stream()
                .filter(video -> video.getId() > 0L && video != null && video.getStatus() == VIDEO_STATUS_PASS)
                .count() > 0;
    }

    private String getTrialClassUrl(DealDetailInfoModel dealDetailInfoModel, Config config, ActivityCxt context) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.dealstruct.options.skuList.edu.EduOnlineDealClassItemSkuListOpt.getTrialClassUrl(DealDetailInfoModel,EduOnlineDealClassItemSkuListOpt$Config,ActivityCxt)");
        int platform = context.getParam(ProductDetailActivityConstants.Params.platform);
        Long dpShopId = context.getParam(ProductDetailActivityConstants.Params.dpPoiIdL);
        Long mtShopId = context.getParam(ProductDetailActivityConstants.Params.mtPoiIdL);
        String shopUUID = context.getParam(ProductDetailActivityConstants.Params.shopUuid);
        if (PlatformUtil.isMT(platform)) {
            return String.format(config.getMtTrialClassJumpUrlTemplate(), dealDetailInfoModel.getDealId(), mtShopId);
        }
        return String.format(config.getDpTrialClassJumpUrlTemplate(), dealDetailInfoModel.getDealId(), dpShopId, shopUUID);
    }

    private List<CoursePlan> getCoursePlanList(List<AttrM> dealAttrs) {
        String json = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, ATTR_COURSE_PLAN);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonCodec.decode(json, new TypeReference<List<CoursePlan>>() {
        });
    }

    private String getTotalClassHours(List<CoursePlan> coursePlanList) {
        if (CollectionUtils.isEmpty(coursePlanList)) {
            return null;
        }
        BigDecimal result = new BigDecimal(0);
        for (CoursePlan coursePlan : coursePlanList) {
            if (coursePlan.getCourseTimeNum() == null) {
                continue;
            }
            result = result.add(coursePlan.getCourseTimeNum());
        }
        if (result.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        return result.setScale(1, BigDecimal.ROUND_DOWN).stripTrailingZeros().toPlainString() + COURSE_NUM_APPEND;
    }

    @Data
    @VPointCfg
    public static class Config {

        /**
         * 试听课的跳转名称
         */
        private String trialClassJumpName = "免费试听";

        /**
         * 试听课的跳转链接
         */
        private String dpTrialClassJumpUrlTemplate = "dianping://mrn?mrn_biz=gcbu&mrn_entry=mrn-le-edutrialcourse&mrn_component=mrn-le-edutrialcourse&dealid=%s&shopid=%s&shopuuid=%s";
        private String mtTrialClassJumpUrlTemplate = "imeituan://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=mrn-le-edutrialcourse&mrn_component=mrn-le-edutrialcourse&dealid=%s&shopid=%s";

        /**
         * 试听课的跳转icon
         */
        private String trialClassIcon = "https://p0.meituan.net/travelcube/e25d4203979fbedfee7f09f5b53071281377.png";

    }


    @Data
    public static class CoursePlan {

        /**
         * 课程
         */
        @JsonProperty("course_module")
        private String courseModule;

        /**
         * 课时数
         */
        @JsonProperty("course_time_num")
        private BigDecimal courseTimeNum;

    }
}
