package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bath.enums.FreeBathingSuppliesEnum;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bath.enums.FreefacilitiesEnum;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * @author: created by hang.yu on 2023/10/7 17:08
 */
public class BathServiceFacilityUtils {

    private static final String SPERATOR = ",";

    /**
     * 服务设施icon
     */
    private static final String SERVICE_FACILITY_ICON = "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png";

    public static List<DealSkuVO> parseServiceFacility(List<AttrM> dealAttrs) {
        List<DealSkuVO> result = Lists.newArrayList();
        // 免费设施
        for (FreefacilitiesEnum freefacilitiesEnum : FreefacilitiesEnum.values()) {
            String toolValue = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, freefacilitiesEnum.getCode());
            if (StringUtils.isNotBlank(toolValue)) {
                DealSkuVO dealSkuVO = new DealSkuVO();
                dealSkuVO.setTitle(freefacilitiesEnum.getName());
                dealSkuVO.setIcon(freefacilitiesEnum.getIcon());
                List<DealSkuItemVO> items = Lists.newArrayList();
                for (String s : toolValue.split(SPERATOR)) {
                    items.add(buildToolItem(s));
                }
                dealSkuVO.setItems(items);
                result.add(dealSkuVO);
            }
        }
        // 免费洗浴用品
        StringBuilder freeBathingSupplies = new StringBuilder();
        for (FreeBathingSuppliesEnum freeBathingSuppliesEnum : FreeBathingSuppliesEnum.values()) {
            String toolValue = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, freeBathingSuppliesEnum.getCode());
            if (StringUtils.isNotBlank(toolValue)) {
                freeBathingSupplies.append(toolValue).append(SPERATOR);
            }
        }
        String freeBathingSuppliesStr = freeBathingSupplies.toString();
        if (StringUtils.isNotBlank(freeBathingSuppliesStr)) {
            DealSkuVO dealSkuVO = new DealSkuVO();
            dealSkuVO.setTitle(FreeBathingSuppliesEnum.BATHING_SUPPLIES.getName());
            dealSkuVO.setIcon(FreeBathingSuppliesEnum.BATHING_SUPPLIES.getIcon());
            List<DealSkuItemVO> items = Lists.newArrayList();
            for (String s : freeBathingSuppliesStr.substring(0, freeBathingSuppliesStr.length() - 1).split(SPERATOR)) {
                items.add(buildToolItem(s));
            }
            dealSkuVO.setItems(items);
            result.add(dealSkuVO);
        }
        return result;
    }

    private static DealSkuItemVO buildToolItem(String toolValue) {
        DealSkuItemVO tool = new DealSkuItemVO();
        tool.setName(toolValue);
        tool.setIcon(SERVICE_FACILITY_ICON);
        return tool;
    }
}
