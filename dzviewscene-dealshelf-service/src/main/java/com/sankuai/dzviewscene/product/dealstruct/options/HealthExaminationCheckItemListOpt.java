package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.OptionalSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.healthExamination.checkItems.vpoints.HealthExaminationCheckItemListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination.HealthExaminationItemsGroupVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination.PrimaryExaminationItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination.SecondaryExaminationItemVO;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/14 16:20 下午
 */

@VPointOption(name = "体检团详新包检查项列表变化点", description = "体检团详新包检查项列表变化点",code = HealthExaminationCheckItemListOpt.CODE, isDefault = false)
public class HealthExaminationCheckItemListOpt extends HealthExaminationCheckItemListVP<HealthExaminationCheckItemListOpt.Config>{

    public static final String CODE = "HealthExaminationCheckItemListOpt";

    private static final String SELF_CUSTOMIZED_CHECK_ITEM = "自定义名称检查项";

    @Override
    public HealthExaminationItemsGroupVO compute(ActivityCxt activityCxt, Param param, Config config) {
        if (param.getDealDetailBasicInfo() == null) {
            return null;
        }
        List<PrimaryExaminationItemVO> primaryExaminationItemVOS = new ArrayList<>();
        //1.添加全部可享检查项列表
        List<PrimaryExaminationItemVO> mustSkuCheckItemList = buildPrimaryCheckItemListByMustSkusGroupList(param.getDealDetailBasicInfo().getDealDetailDtoModel(), param.getDealDetailBasicInfo().getProductCategories(), config);
        addIfNotNull(primaryExaminationItemVOS, mustSkuCheckItemList);
        //2.添加部分可选检查项列表
        List<PrimaryExaminationItemVO> optionalSkuCheckItemList = buildCheckItemListByOptionalSkusGroupList(param.getDealDetailBasicInfo().getDealDetailDtoModel(), config.getSecondaryCheckItem2SignificanceMap());
        addIfNotNull(primaryExaminationItemVOS, optionalSkuCheckItemList);
        //3.组装结果
        return buildHealthExaminationItemsGroupVO(primaryExaminationItemVOS, getAllMustTertiaryItemNum(mustSkuCheckItemList, config.getAllMustTertiaryItemNumFormat()));
    }

    private HealthExaminationItemsGroupVO buildHealthExaminationItemsGroupVO(List<PrimaryExaminationItemVO> primaryExaminationItemVOS, String desc) {
        if (CollectionUtils.isEmpty(primaryExaminationItemVOS)) {
            return null;
        }
        HealthExaminationItemsGroupVO healthExaminationItemsGroupVO = new HealthExaminationItemsGroupVO();
        healthExaminationItemsGroupVO.setGroupName("包含项目");
        healthExaminationItemsGroupVO.setPrimaryExaminationItemList(primaryExaminationItemVOS);
        healthExaminationItemsGroupVO.setDesc(desc);
        return healthExaminationItemsGroupVO;
    }

    /**
     * 获取全部可享sku中三级检查项总数
     *@param
     *@return
     */
    private String getAllMustTertiaryItemNum(List<PrimaryExaminationItemVO> mustSkuCheckItemList, String format) {
        if (CollectionUtils.isEmpty(mustSkuCheckItemList)) {
            return null;
        }
        int sum = mustSkuCheckItemList.stream()
                .filter(item -> StringUtils.isNotEmpty(item.getDesc()))
                .map(item -> {
                    int num = NumberUtils.objToInt(item.getDesc().replaceAll("项", StringUtils.EMPTY));
                    return num == -1 ? 0 : num;
                }).reduce(Integer::sum).orElse(0);
        if (sum == 0) {
            return null;
        }
        return String.format(format, sum);
    }

    private void addIfNotNull(List<PrimaryExaminationItemVO> allItemList, List<PrimaryExaminationItemVO> primaryExaminationItemVOS) {
        if (allItemList == null || CollectionUtils.isEmpty(primaryExaminationItemVOS)) {
            return;
        }
        allItemList.addAll(primaryExaminationItemVOS);
    }

    /**
     * 利用可选服务项目组构造检查项单元列表
     * 每组可选服务项目组作为一个检查项单元，一级检查项的位置展示文案#可选项目#，二级检查项的部分展示服务项目名称，三级检查项部分将sku的所有三级检查项（即服务项目所有属性值）放在一行展示
     *@param
     *@return
     */
    private List<PrimaryExaminationItemVO> buildCheckItemListByOptionalSkusGroupList(DealDetailDtoModel dealDetailDtoModel, Map<String, String> secondaryCheckItem2SignificanceMap) {
        if (dealDetailDtoModel == null || dealDetailDtoModel.getSkuUniStructuredDto() == null || CollectionUtils.isEmpty(dealDetailDtoModel.getSkuUniStructuredDto().getOptionalGroups())) {
            return new ArrayList<>();
        }
        return dealDetailDtoModel.getSkuUniStructuredDto().getOptionalGroups().stream().map(optionalGroup -> buildPrimaryCheckItemByOptionalSkuGroup(optionalGroup, secondaryCheckItem2SignificanceMap)).filter(vo -> vo != null).collect(Collectors.toList());
    }

    private PrimaryExaminationItemVO buildPrimaryCheckItemByOptionalSkuGroup(OptionalSkuItemsGroupDto optionalGroup, Map<String, String> secondaryCheckItem2SignificanceMap) {
        if (optionalGroup == null || CollectionUtils.isEmpty(optionalGroup.getSkuItems())) {
            return null;
        }
        List<SecondaryExaminationItemVO> secondaryExaminationItemVOS = optionalGroup.getSkuItems().stream().map(sku -> buildSecondaryCheckItemByOptionalSku(sku, secondaryCheckItem2SignificanceMap)).filter(vo -> vo != null).collect(Collectors.toList());
        return buildPrimaryExaminationItemVO("可选项目", String.format("以下%s选%s", optionalGroup.getSkuItems().size(), optionalGroup.getOptionalCount()), secondaryExaminationItemVOS);
    }

    private PrimaryExaminationItemVO buildPrimaryExaminationItemVO(String name, String desc, List<SecondaryExaminationItemVO> secondaryExaminationItemVOS) {
        PrimaryExaminationItemVO primaryExaminationItemVO = new PrimaryExaminationItemVO();
        primaryExaminationItemVO.setName(name);
        primaryExaminationItemVO.setDesc(desc);
        primaryExaminationItemVO.setSecondaryExaminationItemList(secondaryExaminationItemVOS);
        return primaryExaminationItemVO;
    }

    private SecondaryExaminationItemVO buildSecondaryCheckItemByOptionalSku(SkuItemDto sku, Map<String, String> secondaryCheckItem2SignificanceMap) {
        if (sku == null) {
            return null;
        }
        SecondaryExaminationItemVO secondaryExaminationItemVO = new SecondaryExaminationItemVO();
        secondaryExaminationItemVO.setName(sku.getName());
        secondaryExaminationItemVO.setCheckSignificance(MapUtils.isEmpty(secondaryCheckItem2SignificanceMap) ? null : secondaryCheckItem2SignificanceMap.get(sku.getName()));
        secondaryExaminationItemVO.setTertiaryExaminations(buildTertiaryCheckItemStrByOptionalSkuAttrs(sku.getAttrItems()));
        return secondaryExaminationItemVO;
    }

    private String buildTertiaryCheckItemStrByOptionalSkuAttrs(List<SkuAttrItemDto> skuAttrItems) {
        if (CollectionUtils.isEmpty(skuAttrItems)) {
            return null;
        }
        List<String> tertiaryCheckItems = skuAttrItems.stream().map(skuAttr -> skuAttr.getChnName()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tertiaryCheckItems)) {
            return null;
        }
        return StringUtils.join(tertiaryCheckItems, "、");
    }

    private List<PrimaryExaminationItemVO> buildPrimaryCheckItemListByMustSkusGroupList(DealDetailDtoModel dealDetailDtoModel, List<ProductSkuCategoryModel> productCategories, Config config) {
        List<SkuItemDto> skuList = extractMustSkuList(dealDetailDtoModel);
        if (CollectionUtils.isEmpty(skuList)) {
            return new ArrayList<>();
        }
        Map<String, List<SkuItemDto>> skuCategory2SkuListMap = groupSkuCtegory2SkuListMap(skuList, productCategories, config.getPrimaryCheckItemName2PriorityMap());
        if (MapUtils.isEmpty(skuCategory2SkuListMap)) {
            return new ArrayList<>();
        }
        return skuCategory2SkuListMap.entrySet().stream()
                .map(entry -> buildMustSkuPrimaryExaminationItemVOList(entry.getKey(), entry.getValue(), config.getSecondaryCheckItem2SignificanceMap(), config.getSecondaryCheckItemName2PriorityMap())).filter(item -> item != null).collect(Collectors.toList());
    }

    /**
     * 利用必选服务项目组构造检查项单元列表
     * 必选服务项目列表中，按照服务项目类别对总列表进行分类，每类服务项目作为一个检查项单元：一级检查项展示服务项目类别名，二级检查项展示该类服务项目列表中被勾选的sku属性名，三级检查项展示该类服务项目列表中以二级检查项作为属性名的所有属性值的拼接
     *@param
     *@return
     */
    private PrimaryExaminationItemVO buildMustSkuPrimaryExaminationItemVOList(String skuCategory, List<SkuItemDto> skuList, Map<String, String> secondaryCheckItem2SignificanceMap, Map<String, Integer> secondaryCheckItemName2PriorityMap) {
        Map<String, List<String>> mustSkuSecondaryCheckItem2TertiaryCheckItemsMap = getMustSkuSecondaryCheckItem2TertiaryCheckItemsMap(skuList, secondaryCheckItemName2PriorityMap);
        if (MapUtils.isEmpty(mustSkuSecondaryCheckItem2TertiaryCheckItemsMap)) {
            return null;
        }
        List<SecondaryExaminationItemVO> mustSkuSecondaryExaminationItemVOs = mustSkuSecondaryCheckItem2TertiaryCheckItemsMap.entrySet().stream()
                .map(entry -> {
                    SecondaryExaminationItemVO secondaryExaminationItemVO = new SecondaryExaminationItemVO();
                    if (entry.getKey() != null && !entry.getKey().contains(SELF_CUSTOMIZED_CHECK_ITEM)) {
                        secondaryExaminationItemVO.setName(entry.getKey());
                    }
                    secondaryExaminationItemVO.setTertiaryExaminations(StringUtils.join(entry.getValue(), "、"));
                    secondaryExaminationItemVO.setCheckSignificance(secondaryCheckItem2SignificanceMap.get(entry.getKey()));
                    return secondaryExaminationItemVO;
                }).collect(Collectors.toList());
        return buildPrimaryExaminationItemVO(skuCategory, String.format("%s项", mustSkuSecondaryCheckItem2TertiaryCheckItemsMap.values().stream().flatMap(items -> items.stream()).collect(Collectors.toList()).size()), mustSkuSecondaryExaminationItemVOs);
    }

    private LinkedHashMap<String, List<String>> getMustSkuSecondaryCheckItem2TertiaryCheckItemsMap(List<SkuItemDto> skuList, Map<String, Integer> secondaryCheckItemName2PriorityMap) {
        LinkedHashMap<String, List<String>> skuSecondaryCheckItem2TertiaryCheckItemsMap = new LinkedHashMap<>();
        addSecondaryCheckItem2TertiaryCheckItemsMapFromSkuAttrs(skuList, skuSecondaryCheckItem2TertiaryCheckItemsMap);
        addSecondaryCheckItem2TertiaryCheckItemsMapFromSkuName(skuList, skuSecondaryCheckItem2TertiaryCheckItemsMap);
        //按照配置排序
        return skuSecondaryCheckItem2TertiaryCheckItemsMap.entrySet().stream()
                .sorted((o1, o2) -> {
                    int priority1 = secondaryCheckItemName2PriorityMap.get(o1.getKey()) == null ? Integer.MAX_VALUE : secondaryCheckItemName2PriorityMap.get(o1.getKey());
                    int priority2 = secondaryCheckItemName2PriorityMap.get(o2.getKey()) == null ? Integer.MAX_VALUE : secondaryCheckItemName2PriorityMap.get(o2.getKey());
                    return priority1 - priority2;
                }).collect(LinkedHashMap::new, (map, entry) -> map.put(entry.getKey(), entry.getValue()), LinkedHashMap::putAll);
    }

    private void addSecondaryCheckItem2TertiaryCheckItemsMapFromSkuName(List<SkuItemDto> skuList, LinkedHashMap<String, List<String>> result) {
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }
        Map<String, List<String>> secondaryCheckItem2TertiaryCheckItemsMapFromSkuName = skuList.stream()
                .filter(sku -> sku != null && CollectionUtils.isEmpty(sku.getAttrItems()))
                .collect(LinkedHashMap::new, (map, sku) -> map.put(SELF_CUSTOMIZED_CHECK_ITEM + skuList.indexOf(sku), Lists.newArrayList(sku.getName())), LinkedHashMap::putAll);
        result.putAll(secondaryCheckItem2TertiaryCheckItemsMapFromSkuName);
    }

    private void addSecondaryCheckItem2TertiaryCheckItemsMapFromSkuAttrs(List<SkuItemDto> skuList, LinkedHashMap<String, List<String>> result) {
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }
        //将sku列表中所有sku的属性按照属性名进行分类，返回map的key为属性名，value为属性值列表
        Map<String, List<String>> skuSecondaryCheckItem2TertiaryCheckItemsMap = skuList.stream()
                .filter(sku -> CollectionUtils.isNotEmpty(sku.getAttrItems()))
                .flatMap(sku -> sku.getAttrItems().stream())
                .filter(skuAttr -> skuAttr != null)
                .collect(HashMap::new, (map, skuAttr) -> {
                    if (StringUtils.isEmpty(skuAttr.getChnName()) || StringUtils.isEmpty(skuAttr.getAttrValue())) {
                        return;
                    }
                    List<String> tertiaryCheckItems = Lists.newArrayList(skuAttr.getAttrValue().split("、"));
                    if (!map.containsKey(skuAttr.getChnName()) || CollectionUtils.isEmpty(map.get(skuAttr.getChnName()))) {
                        map.put(skuAttr.getChnName(), tertiaryCheckItems);
                        return;
                    }
                    List<String> allTertiaryCheckItems = map.get(skuAttr.getChnName());
                    allTertiaryCheckItems.addAll(tertiaryCheckItems);
                }, HashMap::putAll);
        result.putAll(skuSecondaryCheckItem2TertiaryCheckItemsMap);
    }

    /**
     * 将sku列表按照sku所属类别进行分组
     *@param skuItemDtoList 需要进行分组的sku列表
     *@param productCategories 该团单下所有sku类别的全集，用来获取该sku的类别名
     *@return sku类别名到所属该类别的sku列表对应的map
     */
    private LinkedHashMap<String, List<SkuItemDto>> groupSkuCtegory2SkuListMap(List<SkuItemDto> skuItemDtoList, List<ProductSkuCategoryModel> productCategories, Map<String, Integer> primaryCheckItemName2PriorityMap) {
        if (CollectionUtils.isEmpty(skuItemDtoList)) {
            return new LinkedHashMap<>();
        }
        return skuItemDtoList
                .stream()
                .sorted((o1, o2) -> {
                    int priority1 = primaryCheckItemName2PriorityMap.get(getSkuCategoryNameById(o1.getProductCategory(), productCategories)) == null ? Integer.MAX_VALUE : primaryCheckItemName2PriorityMap.get(getSkuCategoryNameById(o1.getProductCategory(), productCategories));
                    int priority2 = primaryCheckItemName2PriorityMap.get(getSkuCategoryNameById(o2.getProductCategory(), productCategories)) == null ? Integer.MAX_VALUE : primaryCheckItemName2PriorityMap.get(getSkuCategoryNameById(o2.getProductCategory(), productCategories));
                    return priority1 - priority2;
                })
                .collect(LinkedHashMap::new, (map, sku) -> {
                    String skuCategoryName = getSkuCategoryNameById(sku.getProductCategory(), productCategories);
                    //如果map中不含该sku类别的key或者该sku类别的key对应的value值为空列表，则新建一个含有该sku的列表作为该类别名key对应的value值放入map
                    if (!map.containsKey(skuCategoryName) || CollectionUtils.isEmpty(map.get(skuCategoryName))) {
                        map.put(skuCategoryName, Lists.newArrayList(sku));
                        return;
                    }
                    List<SkuItemDto> skuList = map.get(skuCategoryName);
                    //将该sku放入map中该sku类别key对应的sku列表中
                    skuList.add(sku);
                }, LinkedHashMap::putAll);
    }

    /**
     * 根据sku类别id获取sku类别名
     *@param productCategories 该团单下sku类别全集
     *@return
     */
    private String getSkuCategoryNameById(long categoryId, List<ProductSkuCategoryModel> productCategories) {
        if (CollectionUtils.isEmpty(productCategories)) {
            return null;
        }
        ProductSkuCategoryModel categoryModel = productCategories.stream().filter(category -> category.getProductCategoryId() != null && category.getProductCategoryId() == categoryId).findFirst().orElse(null);
        return categoryModel == null ? null : categoryModel.getCnName();
    }

    private List<SkuItemDto> extractMustSkuList(DealDetailDtoModel dealDetailDtoModel) {
        if (dealDetailDtoModel == null || dealDetailDtoModel.getSkuUniStructuredDto() == null || CollectionUtils.isEmpty(dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups())) {
            return new ArrayList<>();
        }
        return dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups().stream().flatMap(skuGroup -> skuGroup.getSkuItems().stream()).collect(Collectors.toList());
    }

    @Data
    @VPointCfg
    public static class Config {
        //二级检查项目 - 二级检查项检查意义 Map
        private Map<String, String> secondaryCheckItem2SignificanceMap;
        //一级检查项目优先级配置
        private Map<String, Integer> primaryCheckItemName2PriorityMap;
        //二级检查项目优先级配置
        private Map<String, Integer> secondaryCheckItemName2PriorityMap;
        //全部可享三级检查项总数format
        private String allMustTertiaryItemNumFormat;
    }
}
