package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.DealDetailSkuListAfterProcessingVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.DealDetailSkusGroupSequenceIdVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/16 9:43 上午
 */
@VPointOption(name = "团购详情sku货列表后置处理默认变化点", description = "团购详情sku货列表后置处理默认变化点",code = DefaultDealDetailSkuListAfterProcessingVPO.CODE, isDefault = true)
public class DefaultDealDetailSkuListAfterProcessingVPO extends DealDetailSkuListAfterProcessingVP<DefaultDealDetailSkuListAfterProcessingVPO.Config> {

    public static final String CODE = "DefaultDealDetailSkusGroupSequenceIdVPO";

    @Override
    public List<DealSkuVO> compute(ActivityCxt context, DealDetailSkuListAfterProcessingVP.Param param, DefaultDealDetailSkuListAfterProcessingVPO.Config config) {
        return param.getDealSkuVOS();
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
