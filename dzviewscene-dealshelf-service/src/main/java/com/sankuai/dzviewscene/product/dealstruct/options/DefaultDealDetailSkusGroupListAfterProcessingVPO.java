package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.DealDetailSkusGroupListAfterProcessingVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/16 9:43 上午
 */
@VPointOption(name = "团购详情sku货列表组列表后置处理默认变化点", description = "团购详情sku货列表组列表后置处理默认变化点",code = DefaultDealDetailSkusGroupListAfterProcessingVPO.CODE, isDefault = true)
public class DefaultDealDetailSkusGroupListAfterProcessingVPO extends DealDetailSkusGroupListAfterProcessingVP<DefaultDealDetailSkusGroupListAfterProcessingVPO.Config> {

    public static final String CODE = "DefaultDealDetailSkusGroupSequenceIdVPO";

    @Override
    public List<DealSkuGroupModuleVO> compute(ActivityCxt context, Param param, DefaultDealDetailSkusGroupListAfterProcessingVPO.Config config) {
        return param.getSkuGroups();
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
