package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuCopiesVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/9/8 5:29 下午
 */
@VPointOption(name = "sku份数默认变化点", description = "sku份数默认变化点", code = DefaultSkuCopiesOpt.CODE, isDefault = true)
public class DefaultSkuCopiesOpt extends SkuCopiesVP<DefaultSkuCopiesOpt.Config> {

    public static final String CODE = "DefaultSkuCopiesOpt";

    private static final String DEFAULT_FORMAT = "%s份";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (config != null && config.getDisplayCopiesRule() != null && param != null) {
            return getCopiesByDisplayCopiesRule(config.getDisplayCopiesRule(), param);
        }
        String format = StringUtils.isEmpty(config.getFormat()) ? DEFAULT_FORMAT : config.getFormat();
        return String.format(format, param.getCopies());
    }

    private String getCopiesByDisplayCopiesRule(DisplayCopiesRuleCfg displayCopiesRule, Param param) {
        boolean hitDisplayCopiesRule = hitDisplayCopiesRule(param.getSkuItemDto(), param.getDealAttrs(), displayCopiesRule.getRouteCondition());
        if (hitDisplayCopiesRule) {
            return displayCopiesRule.satisfyRouteConditionCopies;
        }
        return displayCopiesRule.noSatisfyRouteConditionCopies;
    }

    private boolean hitDisplayCopiesRule(SkuItemDto skuItemDto, List<AttrM> dealAttrs, RouteConditionCfg routeCondition) {
        if (routeCondition == null) {
            return false;
        }
        boolean satisfyAllAttrKeyValue = ProductMAttrUtils.satisfyAllAttrKeyValue(dealAttrs, routeCondition.getSatisfyAllAttrKeyValuesMap());
        boolean satisfyAllSkuAttrKeyValue = ProductMAttrUtils.satisfyAllSkuAttrKeyValue(skuItemDto, routeCondition.getSatisfySkuAllAttrKeyValuesMap());
        return satisfyAllAttrKeyValue && satisfyAllSkuAttrKeyValue;
    }

    @Data
    @VPointCfg
    public static class Config {
        private String format;
        private DisplayCopiesRuleCfg displayCopiesRule;
    }

    @Data
    private static class DisplayCopiesRuleCfg {
        //路由条件
        private RouteConditionCfg routeCondition;
        //满足路由条件展示的份数
        private String satisfyRouteConditionCopies;
        //不满足路由条件展示的份数
        private String noSatisfyRouteConditionCopies;
    }

    @Data
    private static class RouteConditionCfg {
        //满足所有属性key及属性值的映射
        private Map<String, List<String>> satisfyAllAttrKeyValuesMap;
        //满足服务项目Sku所有属性key及属性值的映射
        private Map<String, List<String>> satisfySkuAllAttrKeyValuesMap;
    }
}
