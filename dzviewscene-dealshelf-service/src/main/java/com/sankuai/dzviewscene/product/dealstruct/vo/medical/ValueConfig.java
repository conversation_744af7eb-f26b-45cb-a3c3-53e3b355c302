package com.sankuai.dzviewscene.product.dealstruct.vo.medical;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Data
public class ValueConfig implements Serializable {
    private Integer processType;
    private String key;
    private Boolean isList = false;

    private Map<String, String> mapping = new HashMap<>();
    private String regex;
    private Map<String,ValueConfig> linkedMap;

    private String format;

    //如果是价格处理为 ¥xx.xx，最多保留两位小数，如果没有不展示小数，一位就只展示一位小数 xx-xxx元，展示为¥xx-¥xx
    private Boolean priceProcess;
}
