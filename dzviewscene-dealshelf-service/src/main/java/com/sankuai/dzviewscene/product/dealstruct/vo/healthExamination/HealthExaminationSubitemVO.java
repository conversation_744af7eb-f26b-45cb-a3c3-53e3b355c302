package com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/10/18 11:04 上午
 */
@MobileDo(id = 0xe26f)
public class HealthExaminationSubitemVO implements Serializable {
    /**
     * 检查项名称
     */
    @MobileDo.MobileField(key = 0xee12)
    private String itemName;

    /**
     * 检查意义
     */
    @MobileDo.MobileField(key = 0x8dff)
    private String itemSignificance;

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemSignificance() {
        return itemSignificance;
    }

    public void setItemSignificance(String itemSignificance) {
        this.itemSignificance = itemSignificance;
    }
}
