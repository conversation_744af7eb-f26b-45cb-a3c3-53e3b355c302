package com.sankuai.dzviewscene.product.ability.extCtx.vp;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.ability.extCtx.PreHandlerContextAbility;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/19 2:30 下午
 */
@VPoint(name = "串行预处理变化点", description = "预处理变化点", code = PreSyncHandlerVP.CODE, ability = PreHandlerContextAbility.CODE)
public abstract class PreSyncHandlerVP<T> extends PmfVPoint<Map<String, Object>, PreSyncHandlerVP.Param, T> {

    public static final String CODE = "PreSyncHandlerVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        /**
         * 上下文信息
         */
        private ActivityCxt ctx;

        private List<Integer> needFields;

        private int platform;
    }
}