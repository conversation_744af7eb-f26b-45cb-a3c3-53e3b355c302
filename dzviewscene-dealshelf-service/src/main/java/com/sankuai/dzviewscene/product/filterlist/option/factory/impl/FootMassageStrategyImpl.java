package com.sankuai.dzviewscene.product.filterlist.option.factory.impl;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.filterlist.option.factory.AbstractMassageStrategy;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: created by hang.yu on 2024/4/9 14:37
 */
@Component
public class FootMassageStrategyImpl extends AbstractMassageStrategy {

    @Override
    public String getFilterListTitle(SkuItemDto skuItemDto, String serviceType) {
        String serviceDuration = getServiceDuration(skuItemDto);
        return String.format("%s%s", serviceDuration, serviceType);
    }

    @Override
    public List<Long> getProductCategorys() {
        return Lists.newArrayList(ProductCategoryEnum.FOOT_MASSAGE.getProductCategoryId(), ProductCategoryEnum.SCRAPING.getProductCategoryId());
    }

}
