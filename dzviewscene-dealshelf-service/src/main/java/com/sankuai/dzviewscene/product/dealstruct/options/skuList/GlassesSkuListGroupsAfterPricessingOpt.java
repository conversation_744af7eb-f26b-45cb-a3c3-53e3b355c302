package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListGroupsAfterProcessingVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/9/8 5:29 下午
 */
@VPointOption(name = "配镜所有sku列表组后置处理默认变化点", description = "配镜所有sku列表组后置处理默认变化点", code = GlassesSkuListGroupsAfterPricessingOpt.CODE, isDefault = false)
public class GlassesSkuListGroupsAfterPricessingOpt extends SkuListGroupsAfterProcessingVP<GlassesSkuListGroupsAfterPricessingOpt.Config> {

    public static final String CODE = "GlassesSkuListGroupsAfterPricessingOpt";

    private static final String JINGPIAN = "镜片";
    private static final String JINGKUANG = "镜框";
    private static final String PEIJIAN = "配件";
    private static final String ZENGZHIFUWU = "增值服务";


    @Override
    public List<DealSkuGroupModuleVO> compute(ActivityCxt context, Param param, Config config) {
        List<DealSkuGroupModuleVO> skuGroupModuleVOList = param.getSkuGroups();
        if (CollectionUtils.isEmpty(skuGroupModuleVOList)) {
            return skuGroupModuleVOList;
        }
        Map<String, List<DealSkuItemVO>> map = new HashMap<>();

        skuGroupModuleVOList.stream().filter(skuGroupModuleVO -> CollectionUtils.isNotEmpty(skuGroupModuleVO.getDealSkuList())).forEach(skuGroupModuleVO -> {
            skuGroupModuleVO.getDealSkuList().stream().filter(dealSkuVO -> CollectionUtils.isNotEmpty(dealSkuVO.getItems())).forEach(dealSkuVO -> {
                List<DealSkuItemVO> dealSkuItemVOS = dealSkuVO.getItems();
                for (DealSkuItemVO dealSkuItemVO : dealSkuItemVOS) {
                    if (JINGPIAN.equals(dealSkuItemVO.getName())) {
                        if (map.containsKey(JINGPIAN)) {
                            dealSkuItemVO.setName("");
                            map.get(JINGPIAN).add(dealSkuItemVO);

                        } else {
                            dealSkuItemVO.setName(JINGPIAN + skuGroupModuleVO.getTitle());
                            map.put(JINGPIAN, Lists.newArrayList(dealSkuItemVO));
                        }
                    }
                    if (JINGKUANG.equals(dealSkuItemVO.getName())) {
                        if (map.containsKey(JINGKUANG)) {
                            dealSkuItemVO.setName("");
                            map.get(JINGKUANG).add(dealSkuItemVO);
                        } else {
                            dealSkuItemVO.setName(JINGKUANG + skuGroupModuleVO.getTitle());
                            map.put(JINGKUANG, Lists.newArrayList(dealSkuItemVO));
                        }
                    }
                    if (PEIJIAN.equals(dealSkuItemVO.getName())) {
                        if (map.containsKey(PEIJIAN)) {
                            dealSkuItemVO.setName("");
                            map.get(PEIJIAN).add(dealSkuItemVO);

                        } else {
                            dealSkuItemVO.setName(PEIJIAN + skuGroupModuleVO.getTitle());
                            map.put(PEIJIAN, Lists.newArrayList(dealSkuItemVO));
                        }
                    }
                    if (ZENGZHIFUWU.equals(dealSkuItemVO.getName())) {
                        if (map.containsKey(ZENGZHIFUWU)) {
                            dealSkuItemVO.setName("");
                            map.get(ZENGZHIFUWU).add(dealSkuItemVO);
                        } else {
                            dealSkuItemVO.setName(ZENGZHIFUWU + skuGroupModuleVO.getTitle());
                            map.put(ZENGZHIFUWU, Lists.newArrayList(dealSkuItemVO));
                        }
                    }
                }

            });
        });
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(DealDetailUtils.getAttrSingleValueByAttrName(param.getDealAttrs(),"product_name"));
        List<DealSkuItemVO> items = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(map.get(JINGPIAN))){
            items.addAll(map.get(JINGPIAN));
        }
        if(CollectionUtils.isNotEmpty(map.get(JINGKUANG))){
            items.addAll(map.get(JINGKUANG));
        }
        if(CollectionUtils.isNotEmpty(map.get(PEIJIAN))){
            items.addAll(map.get(PEIJIAN));
        }
        if(CollectionUtils.isNotEmpty(map.get(ZENGZHIFUWU))){
            items.addAll(map.get(ZENGZHIFUWU));
        }
        dealSkuVO.setItems(items);
        dealSkuGroupModuleVO.setDealSkuList(Lists.newArrayList(dealSkuVO));
        return Lists.newArrayList(dealSkuGroupModuleVO);
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
