package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.docProcessing.DocBuilderUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.ExhibitsItemModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.PicItemVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/7 9:36 AM
 */
@VPointOption(name = "sku属性列表默认变化点", description = "sku属性列表默认变化点，展示配置的sku属性",code = BeautyEyelashSkuAttrListOpt.CODE, isDefault = false)
public class BeautyEyelashSkuAttrListOpt extends SkuAttrListVP<BeautyEyelashSkuAttrListOpt.Config> {

    public static final String CODE = "BeautyEyelashSkuAttrListOpt";

    private static String SKU_CATEGORY_ID = "skuCategoryId";

    private static String SKU_CATEGORY_NAME = "skuCategoryName";

    private static final String DEAL_RELATED_EXHIBITS_ATTR_NAME = "dealRelatedCaseAttr";

    private static final String REFERENCE_TYPE = "参考款式";


    @Override
    public List<DealSkuItemVO> compute(ActivityCxt context, Param param, Config config) {
        if (CollectionUtils.isEmpty(config.getSkuAttrModels())) {
            return null;
        }
        //团单属性
        List<SkuAttrItemDto> attrItems = param.getSkuItemDto() == null ? null : param.getSkuItemDto().getAttrItems();
        //sku类别
        long productCategory = param.getDealAttrs() == null ? 0 : param.getSkuItemDto().getProductCategory();
        return config.getSkuAttrModels().stream().map(model -> getDealSkuItemVO(context, model, productCategory, param.getProductCategories(), param.getDealAttrs(), attrItems)).filter(vo -> vo != null).collect(Collectors.toList());
    }

    private DealSkuItemVO getDealSkuItemVO(ActivityCxt context, SkuAttrModel skuAttrModel, long productCategory, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs, List<SkuAttrItemDto> skuAttrs) {
        if (skuAttrModel == null || CollectionUtils.isEmpty(skuAttrModel.getSkuAttrBuildModelList())) {
            return null;
        }
        //1.获取展示的属性值文案
        String skuAttrValue = getSkuAttrValue(context, skuAttrModel.getSkuAttrBuildModelList(), productCategory, productCategories, dealAttrs, skuAttrs);
        //2.获取展示的属性标题
        String skuAttrTitle = skuAttrModel.getSkuAttrTitle();
        //3.构造属性
        return buildDealSkuItemVO(skuAttrTitle, skuAttrValue, skuAttrModel.isIgnoreEmptyName());
    }

    private String getSkuAttrValue(ActivityCxt context, List<SkuAttrBuildModel> skuAttrBuildModelList, long productCategory, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs, List<SkuAttrItemDto> skuAttrs) {
        if (CollectionUtils.isEmpty(skuAttrBuildModelList)) {
            return null;
        }
        //1. resourceDataMap用于存放构造sku属性值文案所需的所有数据
        Map<String, Object> resourceDataMap = new HashMap<>();
        //1.1 sku服务项目属性
        Map<String, Object> skuAttrMap = getSkuAttrName2AttrValueMap(skuAttrs);
        resourceDataMap.putAll(skuAttrMap);
        //1.2 sku类别
        Map<String, Object> skuCategoryMap = getSkuCategory(productCategory, productCategories);
        resourceDataMap.putAll(skuCategoryMap);
        //1.3 团单属性
        Map<String, Object> dealAttrMap = getDealAttrName2AttrValueMap(dealAttrs);
        resourceDataMap.putAll(dealAttrMap);
        //2. 构造最终展示的sku属性值文案
        for (SkuAttrBuildModel buildModel : skuAttrBuildModelList) {
            //2.1 通过配置的属性值展示校验语句判断该属性值是否需要展示
            if (DocBuilderUtils.verify(buildModel.getSkuAttrVerifyExpression(), resourceDataMap, context)) {
                //2.2 通过配置的属性展示构造语句构造属性值文案
                Object value = DocBuilderUtils.build(buildModel.getSkuAttrBuildExpression(), resourceDataMap, context);
                return value == null ? null : value.toString();
            }
        }
        return null;
    }

    private Map<String, Object> getSkuCategory(long productCategory, List<ProductSkuCategoryModel> productCategories) {
        if (CollectionUtils.isEmpty(productCategories)) {
            return new HashMap<>();
        }
        ProductSkuCategoryModel skuCategory = productCategories.stream().filter(category -> category.getProductCategoryId() != null && category.getProductCategoryId() == productCategory).findFirst().orElse(null);
        if (skuCategory == null) {
            return new HashMap<>();
        }
        Map<String, Object> skuCategoryMap = new HashMap<>();
        //sku类别id
        skuCategoryMap.put(SKU_CATEGORY_ID, productCategory);
        //sku类别中文名
        skuCategoryMap.put(SKU_CATEGORY_NAME, skuCategory.getCnName());
        return skuCategoryMap;
    }

    private Map<String, Object> getSkuAttrName2AttrValueMap(List<SkuAttrItemDto> skuAttrs) {
        if (CollectionUtils.isEmpty(skuAttrs)) {
            return new HashMap<>();
        }
        return skuAttrs.stream().collect(HashMap::new, (map, skuAttr) -> map.put(skuAttr.getAttrName(), skuAttr.getAttrValue()), HashMap::putAll);
    }

    private Map<String, Object> getDealAttrName2AttrValueMap(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return new HashMap<>();
        }
        return dealAttrs.stream().collect(HashMap::new, (map, dealAttr) -> map.put(dealAttr.getName(), dealAttr.getValue()), HashMap::putAll);
    }

    public DealSkuItemVO buildDealSkuItemVO(String name, String value, boolean ignoreEmptyName) {
        if ((StringUtils.isEmpty(name) && !ignoreEmptyName) || StringUtils.isEmpty(value)) {
            return null;
        }
        if ( REFERENCE_TYPE.equals(name)) {
            // 获取参考款式图片属性
            return buildDealSkuItemVOReference(name, 3, value);
        }
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setValue(value);
        return dealSkuItemVO;
    }

    private DealSkuItemVO buildDealSkuItemVOReference(String name, int type, String exhibitsStr){
        List<PicItemVO> picValues = getPicItemVOS(exhibitsStr);
        if (CollectionUtils.isEmpty(picValues)){
            return null;
        }
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setType(type);
        dealSkuItemVO.setPicValues(picValues);
        return dealSkuItemVO;
    }
    private List<PicItemVO> getPicItemVOS(String exhibitsStr) {
        if (org.apache.commons.lang.StringUtils.isEmpty(exhibitsStr)) {
            return null;
        }
        List<ExhibitsItemModel> exhibitsItemModels = JsonCodec.converseList(exhibitsStr, ExhibitsItemModel.class);
        if (CollectionUtils.isEmpty(exhibitsItemModels)) {
            return null;
        }
        List<PicItemVO> picValues = exhibitsItemModels.stream().map(model -> buildPicItemVO(model)).filter(model -> model != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(picValues)) {
            return null;
        }
        return picValues;
    }

    private PicItemVO buildPicItemVO(ExhibitsItemModel model) {
        if (model == null) {
            return null;
        }
        PicItemVO picItemVO = new PicItemVO();
        picItemVO.setTitle(model.getName());
        picItemVO.setUrl(model.getPic());
        return picItemVO;
    }

    @Data
    @VPointCfg
    public static class Config {
        //sku属性构造模型列表
        private List<SkuAttrModel> skuAttrModels;

    }

    @Data
    private static class SkuAttrModel {
        //sku属性展示标题
        private String skuAttrTitle;
        //sku属性构造配置
        private List<SkuAttrBuildModel> skuAttrBuildModelList;
        // 忽略空的属性名
        private boolean ignoreEmptyName;

    }

    @Data
    private static class SkuAttrBuildModel {
        //用于判该情况下的sku属性值是否应该展示的校验表达式
        private String skuAttrVerifyExpression;
        //用于构造sku属性值最终展示结构的构造表达式
        private String skuAttrBuildExpression;
    }
}
