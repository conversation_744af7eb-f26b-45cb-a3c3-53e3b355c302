package com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.DealDetailSkuProductsGroupsBuilder;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/27 10:43 下午
 */
@Deprecated
@VPoint(name = "团购详情sku货顺序变化点", description = "团购详情sku货顺序变化点", code = SkuSequenceVP.CODE, ability = DealDetailSkuProductsGroupsBuilder.CODE)
public abstract class SkuSequenceVP<T> extends PmfVPoint<Integer, SkuSequenceVP.Param, T> {

    public static final String CODE = "SkuSequenceVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private SkuItemDto skuItemDto;
        private List<ProductSkuCategoryModel> productCategories;
    }
}
