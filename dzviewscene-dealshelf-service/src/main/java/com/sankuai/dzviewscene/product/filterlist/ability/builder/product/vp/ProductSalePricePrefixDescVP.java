package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.product.filterlist.vo.DzProductVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

@VPoint(name = "价格前缀描述", description = "价格前缀描述能力点", code = ProductSalePricePrefixDescVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductSalePricePrefixDescVP<T> extends PmfVPoint<String, ProductSalePricePrefixDescVP.Param, T> {

    public static final String CODE = "ProductSalePricePrefixDescVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;
        private DzProductVO dzProductVO;
    }

}
