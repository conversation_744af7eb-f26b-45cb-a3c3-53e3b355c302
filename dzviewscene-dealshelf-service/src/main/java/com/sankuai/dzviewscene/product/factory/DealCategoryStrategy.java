package com.sankuai.dzviewscene.product.factory;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleItem;

import java.util.List;

/**
 * @author: created by hang.yu on 2023/10/16 11:23
 */
public interface DealCategoryStrategy {

    List<ModuleItem> getModuleList(ActivityCxt activityCxt, DealDetailAssembleCfg assembleCfg);

}
