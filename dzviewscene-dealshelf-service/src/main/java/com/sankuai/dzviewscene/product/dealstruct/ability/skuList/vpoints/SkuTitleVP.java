package com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.DealDetailSkuListsBuilder;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/27 9:15 下午
 */
@VPoint(name = "团购详情sku标题变化点", description = "团购详情sku货标题变化点", code = SkuTitleVP.CODE, ability = DealDetailSkuListsBuilder.CODE)
public abstract class SkuTitleVP<T> extends PmfVPoint<String, SkuTitleVP.Param, T> {

    public static final String CODE = "SkuTitleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private String skuTitle;
        private SkuItemDto skuItemDto;
        private List<AttrM> dealAttrs;
        private List<ProductSkuCategoryModel> productCategories;
    }
}
