package com.sankuai.dzviewscene.product.ability.options;

import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.haima.entity.haima.HaimaConfig;
import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.mobile.base.datatypes.utils.VersionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreSyncHandlerVP;
import com.sankuai.dzviewscene.product.ability.model.CarAndPetUncoopShopShelfAttrM;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.utils.HaimaQueryUtils;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.CompletableFutureUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@VPointOption(
        name = "养车用车与宠物非合作POI推荐列表通用信息", description = "养车用车与宠物非合作POI推荐列表通用信息", code = CarAndPetUnCoopCommonInfoOpt.CODE
)
public class CarAndPetUnCoopCommonInfoOpt extends PreSyncHandlerVP<CarAndPetUnCoopCommonInfoOpt.Config> {

    public static final String CODE = "CarAndPetUnCoopCommonInfoOpt";
    private static final String DZGM_VIEW_DEAL_INCREASE_SCENE_KEY = "dzgm_view_deal_increase";
    private static final String COMMON = "common";
    
    @ConfigValue(key = "com.sankuai.backendforapp.dzgmservice.deal.group.no.search.words.category", defaultValue = "[]")
    private List<Integer> noSearchWordsCategories;

    @ConfigValue(key = "com.sankuai.backendforapp.dzgmservice.deal.group.search.words.category", defaultValue = "[]")
    private List<Integer> searchWordsCategories;

    @ConfigValue(key = "com.sankuai.backendforapp.dzgmservice.deal.group.search.words",defaultValue = "[]")
    private List<String> searchWordsConfig;
    
    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public Map<String, Object> compute(ActivityCxt context, Param param, Config config) {
        HaimaRequest request = buildHaimaRequest(context, DZGM_VIEW_DEAL_INCREASE_SCENE_KEY);
        CompletableFuture<HaimaResponse> responseFuture = compositeAtomService.getHaiMaResponse(request);
        CompletableFuture<List<CarAndPetUncoopShopShelfAttrM>> shopShelfAttrMCf = responseFuture
                .thenApply(response -> convert2ShelfAttrM(response));
        CompletableFuture<CarAndPetUncoopShopShelfAttrM> selectedAttrM = shopShelfAttrMCf
                .thenApply(reponse -> selectShopShelfAttrM(reponse, context));
        Map<String, CompletableFuture<Object>> resultMap = Maps.newHashMap();
        resultMap.put(CarAndPetUnCoopCommonInfoOpt.CODE, CompletableFutureUtil.covert2ObjCf(selectedAttrM));
        return CompletableFutureUtil.each(resultMap).join();
    }

    private CarAndPetUncoopShopShelfAttrM selectShopShelfAttrM(List<CarAndPetUncoopShopShelfAttrM> shelfAttrMList, ActivityCxt context) {
        List<Integer> backCatIds = context.getParam(PmfConstants.Params.shopBackCatIds);
        if (CollectionUtils.isEmpty(shelfAttrMList) || CollectionUtils.isEmpty(backCatIds)) {
            return null;
        }

        String viewType = chooseViewType(shelfAttrMList);
        List<CarAndPetUncoopShopShelfAttrM> filterByViewTypeAndVersion = shelfAttrMList.stream()
                .filter(Objects::nonNull)
                .filter(item -> StringUtils.isNotEmpty(item.getViewType()))
                .filter(item -> item.getViewType().equals(viewType))
                .filter(item -> isValidVersion(context, item))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterByViewTypeAndVersion)) {
            return null;
        }

        return backCatIds.stream()
                .filter(Objects::nonNull)
                .map(String::valueOf)
                .flatMap(catId -> filterByViewTypeAndVersion.stream()
                        .filter(item -> hasMatchingCategory(item, catId)))
                .findFirst()
                .orElse(null);
    }

    private boolean hasMatchingCategory(CarAndPetUncoopShopShelfAttrM item, String targetCatId) {
        if (StringUtils.isEmpty(item.getBackCategoryId())) {
            return false;
        }
        return Arrays.asList(item.getBackCategoryId().split(",")).contains(targetCatId);
    }

    private boolean isValidVersion(ActivityCxt context, CarAndPetUncoopShopShelfAttrM item) {
        String version = ParamsUtil.getStringSafely(context.getParameters(), ShelfActivityConstants.Params.appVersion);
        if (StringUtils.isBlank(version)) {
            return true;
        }
        if (StringUtils.isNotBlank(item.getDzgmStartVersion())
                && VersionUtil.compare(version, item.getDzgmStartVersion()) < 0) {
            return false;
        }
        if (StringUtils.isNotBlank(item.getDzgmEndVersion())
                && VersionUtil.compare(version, item.getDzgmEndVersion()) >= 0) {
            return false;
        }
        return true;
    }

    private List<CarAndPetUncoopShopShelfAttrM> convert2ShelfAttrM(HaimaResponse response) {
        if (response == null || !response.isSuccess() || CollectionUtils.isEmpty(response.getData())) {
            return Lists.newArrayList();
        }
        List<CarAndPetUncoopShopShelfAttrM> result = Lists.newArrayList();
        for (HaimaConfig config : response.getData()) {
            CarAndPetUncoopShopShelfAttrM attrM = new CarAndPetUncoopShopShelfAttrM();
            attrM.setBackCategoryId(config.getExtString("backCategoryId"));
            attrM.setViewType(config.getExtString("viewType"));
            attrM.setClientType(config.getExtString("clientType"));
            attrM.setRule(config.getExtString("rule"));
            attrM.setJumpUrl(config.getExtString("jumpUrl"));
            attrM.setJumpDesc(config.getExtString("jumpDesc"));
            attrM.setModuleName(config.getExtString("moduleName"));
            attrM.setKeyWord(config.getExtString("searchWords"));
            attrM.setDzgmStartVersion(config.getExtString("dzgmStartVersion"));
            attrM.setDzgmEndVersion(config.getExtString("dzgmEndVersion"));
            attrM.setDataList(getDataList(config));
            result.add(attrM);
        }
        return result;
    }

    private List<Map<String, Object>> getDataList(HaimaConfig config) {
        List<Map<String, Object>> dataList = Lists.newArrayList();
        List<HaimaContent> contents = config.getContents();
        if(CollectionUtils.isEmpty(contents)){
            return dataList;
        }
        for (HaimaContent content : contents) {
            String extJson = content.getExtJson();
            if (StringUtils.isNotBlank(extJson)) {
                Set<String> allJsonKeys = getAllJsonKeys(extJson);
                Map<String, Object> contentMap = new HashMap<>();
                contentMap.put("contentId", content.getContentId());
                for (String jsonKey : allJsonKeys) {
                    String originContentStr = content.getContentString(jsonKey);
                    contentMap.put(jsonKey, originContentStr);
                }
                dataList.add(contentMap);
            }
        }
        return dataList;
    }


    private Set<String> getAllJsonKeys(String json) {
        try {
            JsonParser parser = new JsonParser();
            JsonElement element = parser.parse(json);
            JsonObject obj = element.getAsJsonObject();
            return obj.entrySet().stream().filter(Objects::nonNull).map(Map.Entry::getKey).collect(Collectors.toSet());
        } catch (Exception e) {
            log.error("parse json error", e);
            return Collections.emptySet();
        }
    }

    protected String chooseViewType(List<CarAndPetUncoopShopShelfAttrM> data) {
        if (data.size() == 1) {
            return data.get(0).getViewType();
        } else {
            // 超过1个情况，需要根据条件选择viewType
            return COMMON;
        }
    }

    private HaimaRequest buildHaimaRequest(ActivityCxt context, String sceneKey) {
        HaimaRequest request = new HaimaRequest();
        request.setSceneKey(sceneKey);

        HashMap<String, List<String>> multiValueFields = new HashMap<>();
        int platform = ParamsUtil.getIntSafely(context.getParameters(), ShelfActivityConstants.Params.platform);
        multiValueFields.put(HaimaQueryUtils.PLATFORM_KEY_HAI_MA,
                Lists.newArrayList(String.valueOf(platform), HaimaQueryUtils.SHOP_VIEW_PLATFORM_MT_DP));
        // 搜索词
        appendSearchWordInfo(multiValueFields, context);
        request.setMultiValueFields(multiValueFields);
        // 后台类目需要子类目在前，父类目在后
        List<Integer> backCatIds = context.getParam(PmfConstants.Params.shopBackCatIds);
        request.addField(HaimaQueryUtils.BACK_CATE_ID_HAI_MA,
                HaimaQueryUtils.convertList2Str(HaimaQueryUtils.reverseList(backCatIds)));

        return request;
    }

    private void appendSearchWordInfo(Map<String, List<String>> multiValueFields, ActivityCxt context) {
        String keyword = ParamsUtil.getStringSafely(context.getParameters(), ShelfActivityConstants.Params.searchterm);
        List<Integer> backCatIds = context.getParam(PmfConstants.Params.shopBackCatIds);
        if (StringUtils.isBlank(keyword)
                || CollectionUtils.isEmpty(backCatIds)
                || CollectionUtils.isEmpty(noSearchWordsCategories)
                || CollectionUtils.isEmpty(searchWordsCategories)
                || CollectionUtils.isEmpty(searchWordsConfig)) {
            multiValueFields.put("isSearchWord", Collections.singletonList("false"));
            return;
        }

        for (Integer categoryId : backCatIds) {
            if (noSearchWordsCategories.contains(categoryId)) {
                multiValueFields.put("isSearchWord", Collections.singletonList("false"));
                return;
            }
        }

        for (Integer categoryId : backCatIds) {
            if (searchWordsCategories.contains(categoryId)) {
                if (searchWordsConfig.contains(keyword)) {
                    multiValueFields.put("isSearchWord", Collections.singletonList("true"));
                    multiValueFields.put("searchWords", Collections.singletonList(keyword));
                } else {
                    multiValueFields.put("isSearchWord", Collections.singletonList("false"));
                }
                return;
            }
        }
    }

    @Data
    @VPointCfg
    public static class Config {

    }
}
