package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;

import static com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor.NormalValueProcessor.priceProcess;

public class OriginalValueProcessor implements ValueProcessor {

    @Override
    public List<String> process(ValueConfig valueKey, Map<String, String> name2ValueMap, Object data) {
        if (ObjectUtils.isEmpty(valueKey) || ObjectUtils.isEmpty(valueKey.getKey())) {
            return Lists.newArrayList();
        }

        String key = valueKey.getKey();
        if (Boolean.TRUE.equals(valueKey.getPriceProcess())) {
            key = priceProcess(key);
        }
        return Lists.newArrayList(key);
    }

    @Override
    public List<String> convertDisplayValues(String value, ValueConfig valueConfig, Map<String, String> name2ValueMap) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor.OriginalValueProcessor.convertDisplayValues(String,ValueConfig,Map)");
        return null;
    }
}
