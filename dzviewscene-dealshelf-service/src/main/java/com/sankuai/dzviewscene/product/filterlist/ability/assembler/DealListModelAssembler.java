package com.sankuai.dzviewscene.product.filterlist.ability.assembler;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityRequest;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivity;
import com.sankuai.dzviewscene.product.filterlist.model.DealFilterListM;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery.DealMultiQueryFetcher;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery.DealQueryFetcher;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.filterfirst.FilterFirstFetcher;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.productpadding.ProductPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2022/5/1
 */
@Ability(code = DealListModelAssembler.CODE,
        name = "Model-团单筛选列表组装",
        description = "组装团单筛选列内部数据模型。构造 DealFilterListM",
        activities = {DealFilterListActivity.CODE},
        dependency = {ProductPaddingFetcher.CODE, FilterFirstFetcher.CODE, DealQueryFetcher.CODE, ShelfDouHuFetcher.CODE, DealMultiQueryFetcher.CODE}
)
public class DealListModelAssembler extends PmfAbility<DealFilterListM, DealListModelAssembler.Request, DealListModelAssembler.Config> {

    public static final String CODE = "DealListModelAssembler";

    @Override
    public CompletableFuture<DealFilterListM> build(ActivityCxt ctx, Request request, Config config) {
        DealFilterListM dealFilterListM = new DealFilterListM();
        dealFilterListM.setFilterMs(getFilterMs(ctx));
        dealFilterListM.setProductGroupMs(getProductGroups(ctx, config.getFetcherCode()));
        dealFilterListM.setDouHus(ctx.getSource(ShelfDouHuFetcher.CODE));
        return CompletableFuture.completedFuture(dealFilterListM);
    }

    private Map<String, FilterM> getFilterMs(ActivityCxt ctx) {
        return ctx.getSource(FilterFirstFetcher.CODE);
    }

    private Map<String, ProductGroupM> getProductGroups(ActivityCxt ctx, String fetcherCode) {
        if (StringUtils.isNotEmpty(fetcherCode)) {
            return ctx.getSource(fetcherCode);
        }
        if (ctx.getSource(ProductPaddingFetcher.CODE) != null) {
            return ctx.getSource(ProductPaddingFetcher.CODE);
        }
        return ctx.getSource(DealQueryFetcher.CODE);
    }

    @AbilityCfg
    @Data
    public static class Config {
        private String fetcherCode;
    }

    @AbilityRequest
    @Data
    public static class Request {
    }
}