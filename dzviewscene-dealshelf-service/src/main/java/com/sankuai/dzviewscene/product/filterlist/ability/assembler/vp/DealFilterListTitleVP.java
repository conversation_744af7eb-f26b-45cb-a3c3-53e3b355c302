package com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.DealListResponseAssembler;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/5/07
 */
@VPoint(name = "团单筛选标题", description = "团单筛选标题", code = DealFilterListTitleVP.CODE, ability = DealListResponseAssembler.CODE)
public abstract class DealFilterListTitleVP<T> extends PmfVPoint<String, DealFilterListTitleVP.Param, T> {
    public static final String CODE = "DealFilterListTitleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private int dealCount;
        //剧本杀标品title
        private String scriptKillTitle;
    }
}