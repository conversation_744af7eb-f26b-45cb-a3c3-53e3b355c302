package com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/1/9 9:13 下午
 */
@MobileDo(id = 0xb014)
public class SecondaryExaminationItemVO implements Serializable {

    /**
     * 二级检查项目检查意义
     */
    @MobileDo.MobileField(key = 0x3be0)
    private String checkSignificance;

    /**
     * 三级级检查项列表内容（将所有的三级检项目用顿号分隔而成）
     */
    @MobileDo.MobileField(key = 0x3e81)
    private String tertiaryExaminations;

    /**
     * 二级检查项名称
     */
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;

    public String getCheckSignificance() {
        return checkSignificance;
    }

    public void setCheckSignificance(String checkSignificance) {
        this.checkSignificance = checkSignificance;
    }

    public String getTertiaryExaminations() {
        return tertiaryExaminations;
    }

    public void setTertiaryExaminations(String tertiaryExaminations) {
        this.tertiaryExaminations = tertiaryExaminations;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}