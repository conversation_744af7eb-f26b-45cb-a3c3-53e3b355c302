package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.struct.vpoints.DealDetailStructAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrItemModel;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrsModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/28 6:02 下午
 */
@VPointOption(name = "游泳团购详情结构化属性卡片认变化点", description = "游泳团购详情结构化属性卡片认变化点",code = SwimDealDetailStructAttrListVPO.CODE, isDefault = false)
public class SwimDealDetailStructAttrListVPO extends DealDetailStructAttrListVP<SwimDealDetailStructAttrListVPO.Config> {

    public static final String CODE = "SwimDealDetailStructAttrListVPO";

    private static final String ONLY_USABLE_FOR_WOMEN = "仅限女性";

    private static final String ONLY_USABLE_FOR_ADULT = "仅限成人";

    private static final String CLASS_NUM_SKU_ATTR_NAME = "quantity";

    private static final String CLASS_TIME_SKU_ATTR_NAME = "classDurationMin";

    private static final String TICKET_FREE_CONTAIN_SKU_ATTR_NAME = "containFee";

    private static final String TICKET_FREE_STATE = "costDesc";

    private static final String RECOMMEND_HEIGHT_ATTR_NAME = "recommend_height";

    private static final String COURRSE_SCHEDULING_ATTR_NAME = "course_scheduling";

    private static final String NOT_LIMITED_AGE_DEAL_ATTR_VALUE = "不限年龄";

    private static final String SUITABLE_PEOPLE_ATTR_NAME = "suitable_people_edu";

    private static final String SUITABLE_AGE_ATTR_NAME = "eduSuitableAge";

    private static final String COURSE_CONTENT_ATTR_NAME = "courseContent";

    private static final String TRAIN_ATTR_NAME = "培训";

    private static final String TICKET_ATTR_NAME = "门票";

    private static final String FACILITY_ATTR_NAME = "设施";

    private static final String EQUIPMENT_ATTR_NAME = "装备";

    private static final String SEPERATOR = "、";

    private static final String FACILITY_SKU_ATTR_NAME = "supportService";

    private static final String EQUIPMENT_SKU_ATTR_NAME = "provideThing";

    private static final String TRAINER_GENDER_ATTR_NAME = "trainer_gender";

    private static final String CLASS_TYPE_ATTR_NAME = "class_type_with_customize_select";

    private static final String OLD_CLASS_TYPE_ATTR_NAME = "class_type_with_five_select";

    private static final String SWIMMING_STROKE = "swimming_stroke";

    private static final String FEMALE_ONLY_ATTR_VALUE = "female_only";

    private static final String RIGHT = "是";

    private static final String ADULT_AGE_SUFFIX = "及以上";

    private static final String SENSITIVE_MSG_SHOW_STATUS_ATTR_NAME = "dealDisplayControlCheckStatus";

    private static final String SENSITIVE_MSG_SHOW_STATUE_CODE = "1";

    private static final String WOMAN_GENDER = "女教练";

    @Override
    public List<StructAttrsModel> compute(ActivityCxt context, Param param, Config config) {
        List<StructAttrsModel> structAttrsModels = new ArrayList<>();
        //添加课程概览（含icon）
        StructAttrsModel courseViewstructAttrsModelWithIcon = buildCourseVeiwStructAttrsModelWithIcon(param.getDealAttrs(), config);
        if (courseViewstructAttrsModelWithIcon != null) {
            structAttrsModels.add(courseViewstructAttrsModelWithIcon);
        }
        SkuItemDto skuItemDto = getFirstSku(param.getDealDetailDtoModel());
        //添加课程概览（不含icon）
        StructAttrsModel courseViewstructAttrsModel = buildCourseViewStructAttrsModelWithoutIcon(param.getDealAttrs(), skuItemDto, config);
        if (courseViewstructAttrsModel != null) {
            structAttrsModels.add(courseViewstructAttrsModel);
        }
        //添加费用说明
        StructAttrsModel freeStateStructAttrsModel  = buildFreeStateStructAttrsModel(skuItemDto, config);
        if (freeStateStructAttrsModel != null) {
            structAttrsModels.add(freeStateStructAttrsModel);
        }
        //添加课程内容
        StructAttrsModel courseContent = buildCourseContentStateStructAttrsModel(param.getDealAttrs());
        if (courseContent != null) {
            structAttrsModels.add(courseContent);
        }
        if (CollectionUtils.isEmpty(structAttrsModels)) {
            return null;
        }
        return structAttrsModels;
    }


    private SkuItemDto getFirstSku(DealDetailDtoModel dealDetailDtoModel) {
        if (dealDetailDtoModel == null || dealDetailDtoModel.getSkuUniStructuredDto() == null || CollectionUtils.isEmpty(dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups())) {
            return null;
        }
        MustSkuItemsGroupDto mustSkuItemsGroupDto = CollectUtils.firstValue(dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups());
        if (mustSkuItemsGroupDto == null || CollectionUtils.isEmpty(mustSkuItemsGroupDto.getSkuItems())) {
            return null;
        }
        return CollectUtils.firstValue(mustSkuItemsGroupDto.getSkuItems());
    }

    private String getHeight(SkuItemDto skuItemDto, List<AttrM> dealAttrs) {
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return null;
        }
        String preSuitablePeopleAttr = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, SUITABLE_PEOPLE_ATTR_NAME);
        if (!NOT_LIMITED_AGE_DEAL_ATTR_VALUE.equals(preSuitablePeopleAttr)) {
            return null;
        }
        return DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, RECOMMEND_HEIGHT_ATTR_NAME);
    }

    private String getSkuAttrValueBySkuAttrName(SkuItemDto skuItemDto, String skuAttrName) {
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return null;
        }
        return DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), skuAttrName);
    }

    private StructAttrsModel buildCourseContentStateStructAttrsModel(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        String courseContentStr = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, COURSE_CONTENT_ATTR_NAME);
        if(StringUtils.isEmpty(courseContentStr)) {
            return null;
        }
        CourseContentModel courseContentModel = JsonCodec.decode(courseContentStr, CourseContentModel.class);
        if (courseContentModel == null || CollectionUtils.isEmpty(courseContentModel.getContentList())) {
            return null;
        }
        StructAttrItemModel structAttrItemModel = buildStructAttrItemModel(null, courseContentModel.getContentList());
        return buildStructAttrsModel(courseContentModel.getTitle(), Lists.newArrayList(structAttrItemModel));
    }

    private StructAttrItemModel buildStructAttrItemModel(String name, List<String> values) {
        StructAttrItemModel structAttrItemModel = new StructAttrItemModel();
        structAttrItemModel.setAttrName(name);
        structAttrItemModel.setAttrValues(values);
        return structAttrItemModel;
    }

    private StructAttrsModel buildFreeStateStructAttrsModel(SkuItemDto skuItemDto, Config config) {
        List<StructAttrItemModel> structAttrItemModels = new ArrayList<>();
        //添加培训费用说明
        addTraingingFreeState(skuItemDto, config, structAttrItemModels);
        //添加门票费用说明
        addTicketFreeState(skuItemDto, config, structAttrItemModels);
        //添加设施费用说明
        addFacilityFreeState(skuItemDto, config, structAttrItemModels);
        //添加装备费用说明
        addEquipmentFreeState(skuItemDto, config, structAttrItemModels);
        return buildStructAttrsModel(null, structAttrItemModels);
    }

    private void addTraingingFreeState(SkuItemDto skuItemDto, Config config, List<StructAttrItemModel> structAttrItemModels) {
        String attrTitle = getAttrTitleByAttrName(TRAIN_ATTR_NAME, config.getAttrmodels());
        String attrValue = getTrainingFreeState(skuItemDto, config);
        StructAttrItemModel structAttrItemModel = buildStructAttrItemModel(attrTitle, null, attrValue);
        addStructAttrItemModel(structAttrItemModels, structAttrItemModel);
    }

    private void addTicketFreeState(SkuItemDto skuItemDto, Config config, List<StructAttrItemModel> structAttrItemModels) {
        String attrTitle = getAttrTitleByAttrName(TICKET_ATTR_NAME, config.getAttrmodels());
        String attrValue = getTicketFreeState(skuItemDto, config);
        StructAttrItemModel structAttrItemModel = buildStructAttrItemModel(attrTitle, null, attrValue);
        addStructAttrItemModel(structAttrItemModels, structAttrItemModel);
    }

    private void addFacilityFreeState(SkuItemDto skuItemDto, Config config, List<StructAttrItemModel> structAttrItemModels) {
        String attrTitle = getAttrTitleByAttrName(FACILITY_ATTR_NAME, config.getAttrmodels());
        String attrValue = getFacilityFreeState(skuItemDto, config);
        StructAttrItemModel structAttrItemModel = buildStructAttrItemModel(attrTitle, null, attrValue);
        addStructAttrItemModel(structAttrItemModels, structAttrItemModel);
    }

    private void addEquipmentFreeState(SkuItemDto skuItemDto, Config config, List<StructAttrItemModel> structAttrItemModels) {
        String attrTitle = getAttrTitleByAttrName(EQUIPMENT_ATTR_NAME, config.getAttrmodels());
        String attrValue = getEquipmentFreeState(skuItemDto, config);
        StructAttrItemModel structAttrItemModel = buildStructAttrItemModel(attrTitle, null, attrValue);
        addStructAttrItemModel(structAttrItemModels, structAttrItemModel);
    }


    private String getTrainingFreeState(SkuItemDto skuItemDto, Config config) {
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return null;
        }
        String classNum = getSkuAttrValueBySkuAttrName(skuItemDto, CLASS_NUM_SKU_ATTR_NAME);
        String classTime = getSkuAttrValueBySkuAttrName(skuItemDto, CLASS_TIME_SKU_ATTR_NAME);
        if (StringUtils.isEmpty(classNum) || StringUtils.isEmpty(classTime)) {
            return null;
        }
        return String.format(config.getTrainFreeStateFormat(), classNum, classTime);
    }

    private String getTicketFreeState(SkuItemDto skuItemDto, Config config) {
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return null;
        }
        String isContainTicketFree = getSkuAttrValueBySkuAttrName(skuItemDto, TICKET_FREE_CONTAIN_SKU_ATTR_NAME);
        String ticketFreeState = getSkuAttrValueBySkuAttrName(skuItemDto, TICKET_FREE_STATE);
        if (StringUtils.isEmpty(isContainTicketFree) || StringUtils.isEmpty(ticketFreeState)) {
            return null;
        }
        return String.format(config.getTicketFreeStateFormat(), isContainTicketFree, ticketFreeState);
    }

    private String getFacilityFreeState(SkuItemDto skuItemDto, Config config) {
        List<String> allFacility = config.getFacilityList();
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems()) || CollectionUtils.isEmpty(allFacility)) {
            return null;
        }
        String facilityProvided = getSkuAttrValueBySkuAttrName(skuItemDto, FACILITY_SKU_ATTR_NAME);
        if (StringUtils.isEmpty(facilityProvided)) {
            return String.format(config.getFacilityNoProvidedFormat(), StringUtils.join(allFacility, SEPERATOR));
        }
        List<String> facilityProvidedList = Lists.newArrayList(facilityProvided.split(SEPERATOR));
        if (CollectionUtils.isEmpty(facilityProvidedList)) {
            String.format(config.getFacilityNoProvidedFormat(), StringUtils.join(allFacility, SEPERATOR));
        }
        List<String> facilityNoProvidedList = allFacility.stream().filter(facility -> !facilityProvided.contains(facility)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(facilityNoProvidedList)) {
            return String.format(config.getFacilityProvidedAllFormat(), StringUtils.join(allFacility, SEPERATOR));
        }
        return String.format(config.getFacilityProvidedSomeFormat(), StringUtils.join(facilityProvidedList, SEPERATOR), StringUtils.join(facilityNoProvidedList, SEPERATOR));
    }

    private String getEquipmentFreeState(SkuItemDto skuItemDto, Config config) {
        List<String> allFacility = config.getEquipmentList();
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems()) || CollectionUtils.isEmpty(allFacility)) {
            return null;
        }
        String facilityProvided = getSkuAttrValueBySkuAttrName(skuItemDto, EQUIPMENT_SKU_ATTR_NAME);
        if (StringUtils.isEmpty(facilityProvided)) {
            return String.format(config.getEquipmentNoContainFormat(), StringUtils.join(allFacility, SEPERATOR));
        }
        List<String> facilityProvidedList = Lists.newArrayList(facilityProvided.split(SEPERATOR));
        if (CollectionUtils.isEmpty(facilityProvidedList)) {
            String.format(config.getEquipmentNoContainFormat(), StringUtils.join(allFacility, SEPERATOR));
        }
        List<String> facilityNoProvidedList = allFacility.stream().filter(facility -> !facilityProvided.contains(facility)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(facilityNoProvidedList)) {
            return String.format(config.getEquipmentContainedAllFormat(), StringUtils.join(allFacility, SEPERATOR));
        }
        return String.format(config.getEquipmentContainedSomeFormat(), StringUtils.join(facilityProvidedList, SEPERATOR), StringUtils.join(facilityNoProvidedList, SEPERATOR));
    }

    private StructAttrsModel buildCourseViewStructAttrsModelWithoutIcon(List<AttrM> dealAttrs, SkuItemDto skuItemDto, Config config) {
        List<StructAttrItemModel> structAttrItemModels = new ArrayList<>();
        //添加课时数
        addClassNum(skuItemDto, config, structAttrItemModels);
        //添加课时时长
        addClassTime(skuItemDto, config, structAttrItemModels);
        //添加门票信息
        addTicketMsg(skuItemDto, config, structAttrItemModels);
        //添加排课方式
        addClassMethod(skuItemDto, config, structAttrItemModels, dealAttrs);
        //添加建议身高
        addRecommendHeight(skuItemDto, config, structAttrItemModels, dealAttrs);
        return buildStructAttrsModel(null, structAttrItemModels);
    }

    private void addClassNum(SkuItemDto skuItemDto, Config config, List<StructAttrItemModel> structAttrItemModels) {
        String attrTitle = getAttrTitleByAttrName(CLASS_NUM_SKU_ATTR_NAME, config.getAttrmodels());
        String attrValue = getSkuAttrValueBySkuAttrName(skuItemDto, CLASS_NUM_SKU_ATTR_NAME);
        StructAttrItemModel structAttrItemModel = buildStructAttrItemModel(attrTitle, null, attrValue);
        addStructAttrItemModel(structAttrItemModels, structAttrItemModel);
    }

    private void addClassTime(SkuItemDto skuItemDto, Config config, List<StructAttrItemModel> structAttrItemModels) {
        String attrTitle = getAttrTitleByAttrName(CLASS_TIME_SKU_ATTR_NAME, config.getAttrmodels());
        String attrValue = getSkuAttrValueBySkuAttrName(skuItemDto, CLASS_TIME_SKU_ATTR_NAME);
        StructAttrItemModel structAttrItemModel = buildStructAttrItemModel(attrTitle, null, attrValue);
        addStructAttrItemModel(structAttrItemModels, structAttrItemModel);
    }

    private void addTicketMsg(SkuItemDto skuItemDto, Config config, List<StructAttrItemModel> structAttrItemModels) {
        String attrTitle = getAttrTitleByAttrName(TICKET_FREE_CONTAIN_SKU_ATTR_NAME, config.getAttrmodels());
        String attrValue = getSkuAttrValueBySkuAttrName(skuItemDto, TICKET_FREE_CONTAIN_SKU_ATTR_NAME);
        StructAttrItemModel structAttrItemModel = buildStructAttrItemModel(attrTitle, null, attrValue);
        addStructAttrItemModel(structAttrItemModels, structAttrItemModel);
    }

    private void addClassMethod(SkuItemDto skuItemDto, Config config, List<StructAttrItemModel> structAttrItemModels, List<AttrM> dealAttrs) {
        String attrTitle = getAttrTitleByAttrName(COURRSE_SCHEDULING_ATTR_NAME, config.getAttrmodels());
        String attrValue = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, COURRSE_SCHEDULING_ATTR_NAME);
        StructAttrItemModel structAttrItemModel = buildStructAttrItemModel(attrTitle, null, attrValue);
        addStructAttrItemModel(structAttrItemModels, structAttrItemModel);
    }

    private void addRecommendHeight(SkuItemDto skuItemDto, Config config, List<StructAttrItemModel> structAttrItemModels, List<AttrM> dealAttrs) {
        String attrTitle = getAttrTitleByAttrName(RECOMMEND_HEIGHT_ATTR_NAME, config.getAttrmodels());
        String attrValue = getHeight(skuItemDto, dealAttrs);
        StructAttrItemModel structAttrItemModel = buildStructAttrItemModel(attrTitle, null, attrValue);
        addStructAttrItemModel(structAttrItemModels, structAttrItemModel);
    }

    private StructAttrsModel buildCourseVeiwStructAttrsModelWithIcon(List<AttrM> dealAttrs, Config config) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        List<StructAttrItemModel> structAttrItemModels = new ArrayList<>();
        //添加适用年龄
        addSuitableAge(structAttrItemModels, dealAttrs, config);
        //添加适用人群（如果适用年龄属性，则不展示适用人群）
        addSuitablePeople(structAttrItemModels, dealAttrs, config);
        //添加泳姿
        addSwimStroke(structAttrItemModels, dealAttrs, config);
        //添加班型
        addClassType(structAttrItemModels, dealAttrs, config);
        //添加教练性别
        addCoachGender(structAttrItemModels, dealAttrs, config);
        return buildStructAttrsModel(null, structAttrItemModels);
    }

    private StructAttrsModel buildStructAttrsModel(String name, List<StructAttrItemModel> structAttrItemModels) {
        StructAttrsModel structAttrsModel = new StructAttrsModel();
        structAttrsModel.setName(name);
        structAttrsModel.setStructAttrModels(structAttrItemModels);
        return structAttrsModel;
    }

    private void addCoachGender(List<StructAttrItemModel> structAttrItemModels, List<AttrM> dealAttrs, Config config) {
        String attrTitle = getAttrTitleByAttrName(TRAINER_GENDER_ATTR_NAME, config.getAttrModelWithIcons());
        String attrValue = getCoachGender(dealAttrs);
        String attrIcon = getAttrIconByAttrName(TRAINER_GENDER_ATTR_NAME, config.getAttrModelWithIcons());
        StructAttrItemModel structAttrItemModel = buildStructAttrItemModel(attrTitle, attrIcon, attrValue);
        addStructAttrItemModel(structAttrItemModels, structAttrItemModel);
    }

    private void addClassType(List<StructAttrItemModel> structAttrItemModels, List<AttrM> dealAttrs, Config config) {
        String attrTitle = getAttrTitleByAttrName(CLASS_TYPE_ATTR_NAME, config.getAttrModelWithIcons());
        String attrValue =  DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, CLASS_TYPE_ATTR_NAME);
        if (StringUtils.isEmpty(attrValue)) {
            attrValue = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, OLD_CLASS_TYPE_ATTR_NAME);
        }
        String attrIcon = getAttrIconByAttrName(CLASS_TYPE_ATTR_NAME, config.getAttrModelWithIcons());
        StructAttrItemModel structAttrItemModel = buildStructAttrItemModel(attrTitle, attrIcon, attrValue);
        addStructAttrItemModel(structAttrItemModels, structAttrItemModel);
    }

    private void addSwimStroke(List<StructAttrItemModel> structAttrItemModels, List<AttrM> dealAttrs, Config config) {
        String attrTitle = getAttrTitleByAttrName(SWIMMING_STROKE, config.getAttrModelWithIcons());
        String attrValue = getSwimmingStroke(dealAttrs);
        String attrIcon = getAttrIconByAttrName(SWIMMING_STROKE, config.getAttrModelWithIcons());
        StructAttrItemModel structAttrItemModel = buildStructAttrItemModel(attrTitle, attrIcon, attrValue);
        addStructAttrItemModel(structAttrItemModels, structAttrItemModel);
    }

    private void addSuitablePeople(List<StructAttrItemModel> structAttrItemModels, List<AttrM> dealAttrs, Config config) {
        String suitableAge = getSuitableAge(dealAttrs);
        if (StringUtils.isNotEmpty(suitableAge)) {
            return;
        }
        String attrTitle = getAttrTitleByAttrName(SUITABLE_PEOPLE_ATTR_NAME, config.getAttrModelWithIcons());
        String attrValue = getSuitablePeople(dealAttrs);
        String attrIcon = getAttrIconByAttrName(SUITABLE_PEOPLE_ATTR_NAME, config.getAttrModelWithIcons());
        StructAttrItemModel structAttrItemModel = buildStructAttrItemModel(attrTitle, attrIcon, attrValue);
        addStructAttrItemModel(structAttrItemModels, structAttrItemModel);
    }

    private void addSuitableAge(List<StructAttrItemModel> structAttrItemModels, List<AttrM> dealAttrs, Config config) {
        String suitableAge = getSuitableAge(dealAttrs);
        if (StringUtils.isEmpty(suitableAge) || !isShowSensitiveMsg(dealAttrs)) {
            return;
        }
        String attrTitle = getAttrTitleByAttrName(SUITABLE_AGE_ATTR_NAME, config.getAttrModelWithIcons());
        String attrIcon = getAttrIconByAttrName(SUITABLE_AGE_ATTR_NAME, config.getAttrModelWithIcons());
        StructAttrItemModel structAttrItemModel = buildStructAttrItemModel(attrTitle, attrIcon, suitableAge);
        addStructAttrItemModel(structAttrItemModels, structAttrItemModel);
    }

    private boolean isShowSensitiveMsg(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return false;
        }
        String sensitiveMsgShowStatus = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, SENSITIVE_MSG_SHOW_STATUS_ATTR_NAME);
        return sensitiveMsgShowStatus.equals(SENSITIVE_MSG_SHOW_STATUE_CODE);
    }

    private String getSuitableAge(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        return CollectUtils.firstValue(DealDetailUtils.getAttrValueByAttrName(dealAttrs, SUITABLE_AGE_ATTR_NAME));
    }

    private void addStructAttrItemModel(List<StructAttrItemModel> structAttrItemModels, StructAttrItemModel structAttrItemModel) {
        if (structAttrItemModels == null || structAttrItemModel == null) {
            return;
        }
        structAttrItemModels.add(structAttrItemModel);
    }

    private StructAttrItemModel buildStructAttrItemModel(String attrTitle, String attrIcon, String attrValue) {
        if (StringUtils.isEmpty(attrValue) || StringUtils.isEmpty(attrTitle)) {
            return null;
        }
        StructAttrItemModel structAttrItemModel = new StructAttrItemModel();
        structAttrItemModel.setAttrName(attrTitle);
        structAttrItemModel.setAttrValues(Lists.newArrayList(attrValue));
        structAttrItemModel.setIcon(attrIcon);
        return structAttrItemModel;
    }

    private String getSuitablePeople(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        String preSuitablePeopleAttr = CollectUtils.firstValue(DealDetailUtils.getAttrValueByAttrName(dealAttrs, SUITABLE_PEOPLE_ATTR_NAME));
        if (StringUtils.isEmpty(preSuitablePeopleAttr)) {
            return null;
        }
        if (NOT_LIMITED_AGE_DEAL_ATTR_VALUE.equals(preSuitablePeopleAttr)) {
            return preSuitablePeopleAttr;
        }
        String isOnlyUsableForWomen = CollectUtils.firstValue(DealDetailUtils.getAttrValueByAttrName(dealAttrs, FEMALE_ONLY_ATTR_VALUE));
        if (RIGHT.equals(isOnlyUsableForWomen)) {
            return ONLY_USABLE_FOR_WOMEN;
        }
        return ONLY_USABLE_FOR_ADULT;
    }

    private String getSwimmingStroke(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        return DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, SWIMMING_STROKE);
    }

    private String getCoachGender(List<AttrM> dealAttrs) {
        String isOnlyUsableForWomen = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, FEMALE_ONLY_ATTR_VALUE);
        String suitablePeople = getSuitablePeople(dealAttrs);
        String suitableAge = getSuitableAge(dealAttrs);
        String coachGenger = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, TRAINER_GENDER_ATTR_NAME);
        if (RIGHT.equals(isOnlyUsableForWomen) && StringUtils.isNotEmpty(suitableAge) && suitableAge.endsWith(ADULT_AGE_SUFFIX)) {
            return WOMAN_GENDER;
        }
        if (StringUtils.isNotEmpty(suitablePeople) && suitablePeople.equals(ONLY_USABLE_FOR_WOMEN)) {
            return coachGenger;
        }
        return null;
    }

    private AttrModel getAttrModelByAttrName(String attrName, List<AttrModel> attrModels) {
        if (CollectionUtils.isEmpty(attrModels) || StringUtils.isEmpty(attrName)) {
            return null;
        }
        return attrModels.stream().filter(attr -> attrName.equals(attr.getAttrName())).findFirst().orElse(null);
    }

    private String getAttrTitleByAttrName(String attrName, List<AttrModel> attrModels) {
        AttrModel attrModel = getAttrModelByAttrName(attrName, attrModels);
        return attrModel == null ? null : attrModel.getAttrTitle();
    }

    private String getAttrIconByAttrName(String attrName, List<AttrModel> attrModels) {
        AttrModel attrModel = getAttrModelByAttrName(attrName, attrModels);
        return attrModel == null ? null : attrModel.getAttrIcon();
    }

    @Data
    static private class CourseContentModel {
        private List<String> contentList;
        private String title;
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<AttrModel> attrModelWithIcons;
        private List<AttrModel> attrmodels;
        private String trainFreeStateFormat;//包含%s，教学时长%s
        private String ticketFreeStateFormat;//%s，%s
        private List<String> facilityList;//Lists.newArrayList("淋浴室", "储物箱",  "吹风机", "浴巾", "拖鞋", "洗浴用品");
        private String facilityNoProvidedFormat;//不提供%s
        private String facilityProvidedSomeFormat;//免费使用%s，不提供%s
        private String facilityProvidedAllFormat;//免费使用%s
        private List<String> equipmentList;//Lists.newArrayList("泳帽", "泳镜",  "泳衣");
        private String equipmentNoContainFormat;//不包含%s
        private String equipmentContainedSomeFormat;//赠送%s，不包含%s
        private String equipmentContainedAllFormat;//赠送%s
    }

    @Data
    private static class AttrModel {
        private String attrName;
        private String attrTitle;
        private String attrIcon;
    }
}


