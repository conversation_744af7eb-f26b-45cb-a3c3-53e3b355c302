package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.DealSkuListModuleUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@VPointOption(name = "根据纹绣行业的特殊规则作为标题的变化点", description = "丽人纹绣项目标题变化点",code = BeautyTatttooSkuTitleOpt.CODE)
@Slf4j
public class BeautyTatttooSkuTitleOpt extends SkuListModuleVP<BeautyTatttooSkuTitleOpt.Config> {

    public static final String CODE = "BeautyTatttooSkuTitleOpt";

    @Override
    public List<DealDetailSkuListModuleGroupModel> compute(ActivityCxt activityCxt, Param param, Config config) {
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        List<DealDetailSkuListModuleGroupModel> result = Lists.newArrayList();
        List<Map<String,String>> attrMaps = buildAttrMap(dealDetailInfoModel);
        // 构造服务项目模块
        result.add(buildDealModule("服务项目",null,dealDetailInfoModel, attrMaps));
        // 构造免费附赠模块
        result.add(buildDealSkuFreeModule("免费补色", attrMaps));
        return result;
    }

    private List<Map<String,String>> buildAttrMap(DealDetailInfoModel dealDetailInfoModel){
        List<Map<String,String>> result = new ArrayList<>();
        List<SkuItemDto> mustSkuItemList = DealSkuListModuleUtils.extractMustSkuItemList(dealDetailInfoModel);
        if (CollectionUtils.isEmpty(mustSkuItemList)){
            return result;
        }
        for(SkuItemDto mustSkuItem:mustSkuItemList){
            Map<String,String> temRes = new HashMap<>();
            List<SkuAttrItemDto> attrItems = mustSkuItem.getAttrItems();
            if(CollectionUtils.isNotEmpty(attrItems)){
                for(SkuAttrItemDto attrItem:attrItems){
                    if(!temRes.containsKey(attrItem.getAttrName())){
                        temRes.put(attrItem.getAttrName(),attrItem.getAttrValue());
                    }
                }
            }
            result.add(temRes);
        }
        return result;
    }

    private DealDetailSkuListModuleGroupModel buildDealSkuFreeModule(String groupName, List<Map<String,String>> attrMaps) {
        // 目前纹绣业务只有一个Sku，所以这里暂时只取第一个值作为判断
        DealDetailSkuListModuleGroupModel result = new DealDetailSkuListModuleGroupModel();
        if (CollectionUtils.isEmpty(attrMaps)){
            return result;
        }
        Map<String, String> attrMap = attrMaps.get(0);
        if(MapUtils.isNotEmpty(attrMap) && attrMap.containsKey("grant") && "赠送".equals(attrMap.get("grant"))){
            String itemTitle = null;
            if (StringUtils.isNotBlank(attrMap.get("mianfeibuse"))
                    && StringUtils.isNotBlank(attrMap.get("buseshijian"))){
                 itemTitle = attrMap.get("buseshijian")+"个月内免费补色" + attrMap.get("mianfeibuse")+"次";
            }
            result.setGroupName(groupName);
            result.setDealSkuGroupModuleVOS(buildDealSkuGroupListV2(groupName, itemTitle));
            return result;
        }
        return result;
    }

    private DealDetailSkuListModuleGroupModel buildDealModule(String groupName,String subTitle,DealDetailInfoModel dealDetailInfoModel, List<Map<String,String>> attrMaps) {
        DealDetailSkuListModuleGroupModel result = new DealDetailSkuListModuleGroupModel();
        result.setGroupName(groupName);
        result.setGroupSubtitle(subTitle);
        result.setDealSkuGroupModuleVOS(buildDealSkuGroupList(groupName,dealDetailInfoModel, attrMaps));
        return result;
    }

    private List<DealSkuGroupModuleVO> buildDealSkuGroupList(String funcName,DealDetailInfoModel dealDetailInfoModel, List<Map<String,String>> attrMaps) {
        List<DealSkuGroupModuleVO> result = Lists.newArrayList();
        List<SkuItemDto> mustSkuItemList = DealSkuListModuleUtils.extractMustSkuItemList(dealDetailInfoModel);
        if(mustSkuItemList == null || mustSkuItemList.isEmpty()){
            return result;
        }
        for(int i=0;i<mustSkuItemList.size();i++){
            DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
            SkuItemDto mustSkuItem = mustSkuItemList.get(i);
            dealSkuGroupModuleVO.setDealSkuList(buildDealSkuList(funcName,mustSkuItem,attrMaps.get(i)));
            result.add(dealSkuGroupModuleVO);
        }
        return result;
    }

    private List<DealSkuGroupModuleVO> buildDealSkuGroupListV2(String title, String itemTitle) {
        List<DealSkuGroupModuleVO> result = Lists.newArrayList();
        result.add(buildDealSkuGroupModuleVO(title, itemTitle));
        return result;
    }

    private DealSkuGroupModuleVO buildDealSkuGroupModuleVO(String title, String itemTitle){
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        List<DealSkuVO> dealSkuList = Lists.newArrayList();
        dealSkuList.add(buildDealSkuVO(title, itemTitle));
        dealSkuGroupModuleVO.setDealSkuList(dealSkuList);
        return dealSkuGroupModuleVO;
    }

    private List<DealSkuVO> buildDealSkuList(String funcName, SkuItemDto mustSkuItem,Map<String,String> attrMap){
        DealSkuVO dealSkuVO = new DealSkuVO();
        switch (funcName) {
            case "服务项目":
                dealSkuVO = buildServiceBundleSkuList(mustSkuItem, attrMap);
                break;
            default:
                // 其他情况的处理逻辑
                break;
        }
        if(dealSkuVO== null){
            return new ArrayList<>();
        }
        return new ArrayList<>(Collections.singletonList(dealSkuVO));
    }

    private DealSkuVO buildServiceBundleSkuList(SkuItemDto mustSkuItem, Map<String,String> attrMap){
        DealSkuVO dealSkuVO = new DealSkuVO();
        if (Objects.isNull(mustSkuItem) || MapUtils.isEmpty(attrMap)){
            return dealSkuVO;
        }
        String skuTitle = mustSkuItem.getName();
        dealSkuVO.setTitle(buildServiceBundleTitle(skuTitle,attrMap));
        dealSkuVO.setSubTitle(attrMap.get("serviceDuration"));
        dealSkuVO.setPrice("￥"+mustSkuItem.getMarketPrice());
        if(attrMap.containsKey("servicestep")){
            dealSkuVO.setItems(buildServiceBundleItems(attrMap));
        }
        return dealSkuVO;
    }

    private DealSkuVO buildDealSkuVO(String title, String itemTitle){
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(title);
        List<DealSkuItemVO> items = Lists.newArrayList();
        DealSkuItemVO itemVO = new DealSkuItemVO();
        itemVO.setName(itemTitle);
        items.add(itemVO);
        dealSkuVO.setItems(items);
        return dealSkuVO;
    }
    private String buildServiceBundleTitle(String skuTitle,Map<String,String> attrMap){
        if(skuTitle == null || !skuTitle.equals("纹眉")){
            return skuTitle;
        }
        // serviceType为纹眉时的特殊逻辑
        if(attrMap == null) {
            return skuTitle;
        }
        if(attrMap.containsKey("ProjectClassification")){
            return skuTitle+"-"+attrMap.get("ProjectClassification");
        }
        return skuTitle;
    }

    private List<DealSkuItemVO> buildServiceBundleItems(Map<String,String> attrMap){
        if (MapUtils.isEmpty(attrMap)){
            return null;
        }
        String servicestep = attrMap.get("servicestep");
        if(servicestep == null){
            return null;
        }
        List<Map<String, String>> attrs = Lists.newArrayList();
        try {
            attrs = JSON.parseObject(servicestep, new TypeReference<List<Map<String, String>>>(){});
        } catch (Exception e) {
            log.error("invalid serviceStep json", e);
            return null;
        }
        if (CollectionUtils.isEmpty(attrs)){
            return null;
        }
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName("服务步骤");
        List<SkuAttrAttrItemVO> items = new ArrayList<>();
        for(Map<String,String> attr:attrs){
            SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
            skuAttrAttrItemVO.setName(attr.get("stepName"));
            skuAttrAttrItemVO.setValues(buildValues(attr.get("stepDesc")));
            items.add(skuAttrAttrItemVO);
        }
        dealSkuItemVO.setValueAttrs(items);
        return new ArrayList<>(Collections.singletonList(dealSkuItemVO));
    }

    private List<CommonAttrVO> buildValues(String name){
        if (StringUtils.isBlank(name)){
            return null;
        }
        List<CommonAttrVO> values = Lists.newArrayList();
        CommonAttrVO value = new CommonAttrVO();
        value.setName(name);
        values.add(value);
        return values;
    }


    @Data
    @VPointCfg
    public static class Config {
    }
}
