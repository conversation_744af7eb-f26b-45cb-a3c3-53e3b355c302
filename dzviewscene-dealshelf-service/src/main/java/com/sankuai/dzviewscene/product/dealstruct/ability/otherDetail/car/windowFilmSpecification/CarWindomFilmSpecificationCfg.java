package com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.car.windowFilmSpecification;

import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/6 7:46 下午
 */
@AbilityCfg
@Data
public class CarWindomFilmSpecificationCfg {

    /**
     *点评部位说明示意图
     */
    private String dpPartDescUrl;

    /**
     *美团部位说明示意图
     */
    private String mtPartDescUrl;

    /**
     *表明参数信息是来自品牌库的描述文案
     */
    private String normalizedSpecificationDescDoc;

    /**
     * 窗膜信息模块 标题：窗膜信息
     */
    private String carWindowFilmSpecificationModuleTitle;

    /**
     * 窗膜信息模块 弹窗文案：查看详细参数
     */
    private String carWindowFilmSpecificationModulePopupDoc;

    /**
     * 窗膜信息模块 弹窗标题：商品详细参数
     */
    private String carWindowFilmSpecificationModulePopupTitle;

    /**
     * 和车窗玻璃膜规格参数有关的sku属性名列表
     */
    private List<String> carWindomFilmSpecificationRelatedSkuAttrs;

    /**
     *作用部位 在上单页面展示的名称 到 在C端展示的名称 映射Map
     */
    private Map<String, String> partNameMap;

    /**
     *在 窗膜信息 中展示的规格参数 name到title 映射Map
     */
    private Map<String, String> paramsInCarItemVOMap;

    /**
     *在 弹窗 中展示的规格参数 name到title 映射Map
     */
    private Map<String, String> paramsInCarDetailPopupItemVOMap;


}
