package com.sankuai.dzviewscene.product.dealstruct.options.skuList.pet;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/10
 */
@VPointOption(name = "单SKU平铺其属性成为多SKU列表", description = "适用于单个服务项目平铺成多个组的情况", code = "SingleSkuTileMultiSkuListModuleOpt")
public class SingleSkuTileMultiSkuListModuleOpt extends SkuListModuleVP<SingleSkuTileMultiSkuListModuleOpt.Config> {

    @Override
    public List<DealDetailSkuListModuleGroupModel> compute(ActivityCxt activityCxt, Param param, Config config) {
        List<DealSkuVO> dealSkuList = new ArrayList<>();
        List<SkuAttrItemDto> attrItemDtoList = DealDetailUtils.extractFirstMustSkuAttrListFromDealDetailInfoModel(param.getDealDetailInfoModel());
        if (CollectionUtils.isEmpty(attrItemDtoList) || CollectionUtils.isEmpty(config.getSkuCfgList())) {
            return new ArrayList<>();
        }
        for (SingleSkuCfg skuCfg : config.getSkuCfgList()) {
            CollectionUtils.addIgnoreNull(dealSkuList, buildDealSkuVO(skuCfg.getTitle(), DealDetailUtils.getSkuAttrValueBySkuAttrName(attrItemDtoList, skuCfg.getSkuAttrName())));
        }
        return buildResultModel(dealSkuList);
    }

    private DealSkuVO buildDealSkuVO(String title, String attrValue) {
        if (StringUtils.isEmpty(attrValue)) {
            return null;
        }
        List<String> attrValues = Lists.newArrayList(attrValue.split("、"));
        if (CollectionUtils.isEmpty(attrValues)) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        //Ex. 全身洗护
        dealSkuVO.setTitle(title);
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName("包含项目");
        dealSkuItemVO.setType(5);
        dealSkuItemVO.setValueAttrs(attrValues.stream().map(value -> {
            SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
            skuAttrAttrItemVO.setName(value);
            skuAttrAttrItemVO.setInfo(Lists.newArrayList("1份"));
            return skuAttrAttrItemVO;
        }).collect(Collectors.toList()));
        dealSkuVO.setItems(Lists.newArrayList(dealSkuItemVO));
        return dealSkuVO;
    }

    private List<DealDetailSkuListModuleGroupModel> buildResultModel(List<DealSkuVO> dealSkuList) {
        if (CollectionUtils.isEmpty(dealSkuList)) {
            return new ArrayList<>();
        }
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(dealSkuList);
        DealDetailSkuListModuleGroupModel groupModel = new DealDetailSkuListModuleGroupModel();
        groupModel.setDealSkuGroupModuleVOS(Lists.newArrayList(dealSkuGroupModuleVO));
        return Lists.newArrayList(groupModel);
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<SingleSkuCfg> skuCfgList;
    }

    @Data
    public static class SingleSkuCfg {

        private String title;

        private String skuAttrName;
    }
}
