package com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.DealListResponseAssembler;
import lombok.Builder;
import lombok.Data;

/**
 * Created by wangxinyuan on 2022/05/07.
 */
@VPoint(name = "更多文案", description = "更多文案",code = DealFilterListMoreTextVP.CODE, ability = DealListResponseAssembler.CODE)
public abstract class DealFilterListMoreTextVP<T> extends PmfVPoint<String, DealFilterListMoreTextVP.Param, T> {

    public static final String CODE = "DealFilterListMoreTextVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private int dealCount;
    }

}
