/*
 * Create Author : liyanmin
 * Create Date : 2024-04-10
 * Project :
 * File Name : ShopReservationCountPaddingHandler.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.shop;

import com.dianping.vc.enums.VCPlatformEnum;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ContextHandlerResult;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.leads.count.thrift.dto.LeadsCountADTO;
import com.sankuai.leads.count.thrift.dto.LeadsCountRespDTO;
import com.sankuai.leads.count.thrift.enums.SubjectTypeEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * 功能描述:
 * <p>
 *
 * <AUTHOR> yanmin.li
 *         <p>
 * @version 1.0 2024-04-10
 * @since dzviewscene-dealshelf-home 1.0
 */
@Component
public class ShopReservationCountPaddingHandler implements ContextPaddingHandler {

    private final static int LEADS_BIZ_ID = 1012;

    private final static int LOGIC_EXPRESSION_ID = 39;

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<ContextHandlerResult> padding(ActivityCxt ctx, ContextHandlerResult contextHandlerResult,Map<String, Object> params) {
        CompletableFuture<LeadsCountADTO> leadsCountRequestCF = buildLeadsCountADTO(ctx);
        return leadsCountRequestCF.thenCompose(request -> compositeAtomService.queryShopReservationCount(request))
                .thenApply(response -> {
                    fillShopReservationCount(response, contextHandlerResult);
                    return contextHandlerResult;
                });
    }

    private void fillShopReservationCount(LeadsCountRespDTO response, ContextHandlerResult contextHandlerResult) {
        if( Objects.nonNull(response)){
            contextHandlerResult.setShopReservationCount(response.getCount());
        }
    }

    private CompletableFuture<LeadsCountADTO> buildLeadsCountADTO(ActivityCxt ctx) {
        int platform = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.platform);
        long dpShopId = PoiIdUtil.getDpPoiIdL(ctx, ShelfActivityConstants.Params.dpPoiIdL,ShelfActivityConstants.Params.dpPoiId);
        long mtShopId = PoiIdUtil.getMtPoiIdL(ctx, ShelfActivityConstants.Params.mtPoiIdL,ShelfActivityConstants.Params.mtPoiId);
        CompletableFuture<Long> mtShopIdCF = platform == VCPlatformEnum.MT.getType()
                ? CompletableFuture.completedFuture(mtShopId)
                : compositeAtomService.getMtByDpPoiIdL(dpShopId).thenApply(id -> Optional.ofNullable(id).orElse(0L));
        return mtShopIdCF.thenApply(subjectId -> {
            if (subjectId < 0) {
                return null;
            }
            LeadsCountADTO leadsCountRequest = new LeadsCountADTO();
            leadsCountRequest.setSubjectType(SubjectTypeEnum.SHOPID_TYPE.getCode());
            // 美团门店
            leadsCountRequest.setSubjectId(String.valueOf(subjectId));
            leadsCountRequest.setBizId(LEADS_BIZ_ID);
            leadsCountRequest.setLogicExpressionId(LOGIC_EXPRESSION_ID);
            return leadsCountRequest;
        });

    }
}
