package com.sankuai.dzviewscene.product.factory.impl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleItem;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.factory.AbstractDealCategoryStrategy;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/1/11 10:16 上午
 */
@Service("wearableNailCategoryStrategyImpl")
public class WearableNailCategoryStrategyImpl extends AbstractDealCategoryStrategy {

    @Override
    public List<ModuleItem> getModuleList(ActivityCxt activityCxt, DealDetailAssembleCfg assembleCfg) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        DealDetailInfoModel dealDetailInfoModel = CollectUtils.firstValue(dealDetailInfoModels);
        if (Objects.isNull(dealDetailInfoModel)) {
            return assembleCfg.getModuleList();
        }
        boolean isWearableNail = DealDetailUtils.isWearableNail(dealDetailInfoModel.getDealAttrs());
        return isWearableNail ? assembleCfg.getNewModuleList() : assembleCfg.getModuleList();
    }

}
