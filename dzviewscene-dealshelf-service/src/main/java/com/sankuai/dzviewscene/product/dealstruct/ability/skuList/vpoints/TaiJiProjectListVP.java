package com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.DealDetailSkuListsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.TaiJiProjectsModuleBuilder;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@VPoint(name = "团购详情sku属性列表变化点", description = "团购详情sku属性列表题变化点", code = TaiJiProjectListVP.CODE, ability = TaiJiProjectsModuleBuilder.CODE)
public abstract class TaiJiProjectListVP<T> extends PmfVPoint<List<DealSkuItemVO>, TaiJiProjectListVP.Param, T> {
    public static final String CODE = "TaiJiProjectListVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private List<AttrM> dealAttrs;
        private StandardServiceProjectItemDTO taiJiProjectItem;
    }
}
