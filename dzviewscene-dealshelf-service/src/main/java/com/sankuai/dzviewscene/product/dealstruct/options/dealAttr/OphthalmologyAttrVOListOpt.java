package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: wuweizhen
 * @Date: 2023/3/6 10:41
 * @Description:
 */
@VPointOption(name = "眼科DealAttrVO列表变化点", description = "流程步骤构造DealAttrVO列表变化点", code = OphthalmologyAttrVOListOpt.CODE)
public class OphthalmologyAttrVOListOpt extends DealAttrVOListVP<OphthalmologyAttrVOListOpt.StepConfig> {

    public static final String CODE = "OphthalmologyProcessStepAttrVOListOpt";

    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt context, Param param, StepConfig config) {
        return getProcessStepAttrModules(param, config);
    }

    private List<DealDetailStructAttrModuleGroupModel> getProcessStepAttrModules(Param param, StepConfig config) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.OphthalmologyAttrVOListOpt.getProcessStepAttrModules(DealAttrVOListVP$Param,OphthalmologyAttrVOListOpt$StepConfig)");
        if (config == null || CollectionUtils.isEmpty(config.configs) || param == null) {
            return Lists.newArrayList();
        }
        return config.configs.stream().map(modelConfig -> {
            String stepStr = DealDetailUtils.getAttrSingleValueByAttrName(param.getDealAttrs(), modelConfig.getAttrKey());
            if (StringUtils.isEmpty(stepStr)) {
                return null;
            }
            List<Map<String, String>> steps = JsonCodec.decode(stepStr, new TypeReference<List<Map<String, String>>>() {
            });
            if (CollectionUtils.isEmpty(steps)) {
                return null;
            }
            List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOS = buildAttrModules(steps, modelConfig);
            DealDetailStructAttrModuleGroupModel dealDetailStructAttrModuleGroupModel = new DealDetailStructAttrModuleGroupModel();
            dealDetailStructAttrModuleGroupModel.setGroupName(modelConfig.getTitle());
            dealDetailStructAttrModuleGroupModel.setDealDetailStructAttrModuleVOS(dealDetailStructAttrModuleVOS);
            return dealDetailStructAttrModuleGroupModel;
        }).collect(Collectors.toList());
    }

    private List<DealDetailStructAttrModuleVO> buildAttrModules(List<Map<String, String>> steps, Config config) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.OphthalmologyAttrVOListOpt.buildAttrModules(List,OphthalmologyAttrVOListOpt$Config)");
        List<DealDetailStructAttrModuleVO> attrModules = new ArrayList<>();
        steps.forEach(step -> {
            if (MapUtils.isEmpty(step)) {
                return;
            }
            String stepNameKey = step.get(config.getStepNameKey());
            String stepDescKey = step.get(config.getStepDescKey());
            if (StringUtils.isEmpty(stepNameKey) && StringUtils.isEmpty(stepDescKey)) {
                return;
            }
            DealDetailStructAttrModuleVO processStepAttrModule = new DealDetailStructAttrModuleVO();
            if (StringUtils.isNotEmpty(stepNameKey)) {
                processStepAttrModule.setAttrName(stepNameKey);
            }
            if (StringUtils.isNotEmpty(stepDescKey)) {
                processStepAttrModule.setAttrValues(Lists.newArrayList(stepDescKey));
            }
            attrModules.add(processStepAttrModule);
        });
        return attrModules;
    }

    @Data
    @VPointCfg
    public static class StepConfig {
        private List<Config> configs;
    }

    @Data
    public static class Config {
        //标题
        private String title;
        //属性key
        private String attrKey;
        //步骤名称属性key
        private String stepNameKey;
        //步骤描述属性key
        private String stepDescKey;
    }
}