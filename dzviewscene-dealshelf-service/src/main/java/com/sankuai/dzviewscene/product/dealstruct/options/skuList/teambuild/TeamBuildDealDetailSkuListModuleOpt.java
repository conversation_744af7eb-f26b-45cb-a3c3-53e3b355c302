package com.sankuai.dzviewscene.product.dealstruct.options.skuList.teambuild;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bath.enums.TeamBuildFacilitiesEnum;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.DealSkuListModuleUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.formula.functions.T;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: suqiulin
 * 团详部分实现: https://km.sankuai.com/collabpage/2248444447
 */
@VPointOption(name = "团建团详sku列表组变化点", description = "团建团详sku列表组变化点", code = TeamBuildDealDetailSkuListModuleOpt.CODE)
public class TeamBuildDealDetailSkuListModuleOpt extends SkuListModuleVP<TeamBuildDealDetailSkuListModuleOpt.Config> {

    public static final String CODE = "TeamBuildDealDetailSkuListModuleOpt";

    @Override
    public List<DealDetailSkuListModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        List<DealDetailSkuListModuleGroupModel> result = Lists.newArrayList();
        // 构造服务流程模块
        result.add(buildServiceProjects(dealDetailInfoModel));
        // 构造服务设施模块
        result.add(buildServiceFacilityModule(dealDetailInfoModel.getDealAttrs()));
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 构造服务流程模块
     */
    private List<DealSkuGroupModuleVO> buildDealSkuGroupModuleList(DealDetailInfoModel dealDetailInfoModel) {
        List<DealSkuGroupModuleVO> result = Lists.newArrayList();
        // 全部可享
        List<StandardServiceProjectItemDTO> mustSkuItemList = DealSkuListModuleUtils.standardExtractMustSkuItemList(dealDetailInfoModel);
        if (CollectionUtils.isNotEmpty(mustSkuItemList)) {
            result.add(buildMustGroup(mustSkuItemList));
        }
        // m选n
        List<StandardServiceProjectGroupDTO> optionalSkuItemList = DealSkuListModuleUtils.standardExtractOptionalSkuItemsGroupList(dealDetailInfoModel);
        if (CollectionUtils.isNotEmpty(optionalSkuItemList)) {
            if(result.size() == 1 && (result.get(0)!=null)){
                result.get(0).setTitle("全部可享");
            }
            result.addAll(buildOptionalGroup(optionalSkuItemList));
        }
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private DealSkuGroupModuleVO buildMustGroup(List<StandardServiceProjectItemDTO> mustSkuItemList) {
        if (CollectionUtils.isEmpty(mustSkuItemList)) {
            return null;
        }
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setTitleStyle(1);
        dealSkuGroupModuleVO.setDealSkuList(mustSkuItemList.stream().filter(Objects::nonNull).map(this::buildDealSkuVO).collect(Collectors.toList()));
        return dealSkuGroupModuleVO;
    }

    /**
     * 构造服务设施模块
     */
    private DealDetailSkuListModuleGroupModel buildServiceFacilityModule(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        DealDetailSkuListModuleGroupModel dealDetailSkuListModuleGroupModel = new DealDetailSkuListModuleGroupModel();
        dealDetailSkuListModuleGroupModel.setGroupTitle("服务设施");
        //出参前端的skuGroupsModel1，一般只有一个item
        DealSkuGroupModuleVO dealSkuGroupModuleVO = buildFacilityModuleVOS(dealAttrs);
        if(dealSkuGroupModuleVO == null){
            return null;
        }
        dealDetailSkuListModuleGroupModel.setDealSkuGroupModuleVOS(Lists.newArrayList(dealSkuGroupModuleVO));
        //dealDetailAssembleAbility里面的config
        dealDetailSkuListModuleGroupModel.setGroupName("服务设施模块");
        return dealDetailSkuListModuleGroupModel;
    }

    private DealSkuGroupModuleVO buildFacilityModuleVOS(List<AttrM> dealAttrs) {
        //"value": "[\"摄影摄像\",\"服务人员\",\"便利服务\"]"
        //建议对着视觉稿看，不然太抽象了
        List<String> categoryList = Arrays.stream(TeamBuildFacilitiesEnum.values())
                .map(TeamBuildFacilitiesEnum::getName)
                .collect(Collectors.toList());
        List<DealSkuVO> dealSkuList = Lists.newArrayList();
        for (String category : categoryList) {
            TeamBuildFacilitiesEnum enumByServiceName = TeamBuildFacilitiesEnum.getEnumByServiceName(category);
            if (enumByServiceName == null) {
                continue;
            }
            DealSkuVO dealSkuVO = new DealSkuVO();
            dealSkuVO.setIcon(enumByServiceName.getIcon());
            dealSkuVO.setTitle(category);
            List<DealSkuItemVO> items = buildFacilityItems(enumByServiceName.getDetailKey(), dealAttrs);
            if(!CollectionUtils.isEmpty(items)){
                dealSkuVO.setItems(items);
                dealSkuList.add(dealSkuVO);
            }
        }
        if(CollectionUtils.isEmpty(dealSkuList)){
            return null;
        }
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(dealSkuList);
        return dealSkuGroupModuleVO;
    }

    private List<DealSkuItemVO> buildFacilityItems(String detailKey, List<AttrM> dealAttrs) {
        //多个服务标签
        String allServiceTags = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, detailKey);
        if (StringUtils.isEmpty(allServiceTags)){
            return null;
        }
        List<String> tagList = JsonCodec.decode(allServiceTags, new TypeReference<List<String>>() {});
        if (CollectionUtils.isEmpty(tagList)){
            return null;
        }
        return tagList.stream().filter(StringUtils::isNotEmpty).map(tag -> {
            DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
            //固定的图上的勾
            dealSkuItemVO.setIcon("https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png");
            //这里用name,上面用value
            dealSkuItemVO.setName(tag);
            return dealSkuItemVO;
        }).collect(Collectors.toList());
    }

    private DealDetailSkuListModuleGroupModel buildServiceProjects(DealDetailInfoModel dealDetailInfoModel) {
        DealDetailSkuListModuleGroupModel dealDetailSkuListModuleGroupModel = new DealDetailSkuListModuleGroupModel();
        dealDetailSkuListModuleGroupModel.setGroupTitle("活动项目");
        //dealDetailAssembleAbility里面的config
        dealDetailSkuListModuleGroupModel.setGroupName("服务流程模块");
        //出参前端的skuGroupsModel1
        dealDetailSkuListModuleGroupModel.setDealSkuGroupModuleVOS(buildDealSkuGroupModuleList(dealDetailInfoModel));
        return dealDetailSkuListModuleGroupModel;
    }

    private List<DealSkuGroupModuleVO> buildOptionalGroup(List<StandardServiceProjectGroupDTO> optionalSkuItemList) {
        List<DealSkuGroupModuleVO> result = Lists.newArrayList();
        for (StandardServiceProjectGroupDTO optionalSkuItemsGroupDto : optionalSkuItemList) {
            List<StandardServiceProjectItemDTO> skuItemList = optionalSkuItemsGroupDto.getServiceProjectItems();
            if (CollectionUtils.isNotEmpty(skuItemList)) {
                DealSkuGroupModuleVO eitherOrGroup = new DealSkuGroupModuleVO();
                String title = String.format("以下 %d 选 %d (不可重复选)", skuItemList.size(), optionalSkuItemsGroupDto.getOptionalCount());
                eitherOrGroup.setTitle(title);
                eitherOrGroup.setTitleStyle(1);
                eitherOrGroup.setDealSkuList(skuItemList.stream().map(this::buildDealSkuVO).collect(Collectors.toList()));
                result.add(eitherOrGroup);
            }
        }
        return result;
    }

    private DealSkuVO buildDealSkuVO(StandardServiceProjectItemDTO skuItemDto) {
        StandardAttributeDTO standardAttribute = skuItemDto.getStandardAttribute();
        if (CollectionUtils.isEmpty(standardAttribute.getAttrs())) {
            return null;
        }
        List<StandardAttributeItemDTO> attrs = standardAttribute.getAttrs();
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setCopies("（1份）");
        dealSkuVO.setRightText(buildTime(attrs));
        dealSkuVO.setTitle(buildTitle(skuItemDto, attrs));
        dealSkuVO.setFullOccupy(true);
        dealSkuVO.setItems(buildItems(attrs));
        return dealSkuVO;
    }

    private String buildTitle(StandardServiceProjectItemDTO skuItemDto, List<StandardAttributeItemDTO> attrs){
        if(!StringUtils.isEmpty(skuItemDto.getServiceProjectName())){
            return skuItemDto.getServiceProjectName();
        }
        return extractAttrValue(attrs, "projectName");

    }

    private List<DealSkuItemVO> buildItems(List<StandardAttributeItemDTO> attrs) {
        List<DealSkuItemVO> skuItemVOS = Lists.newArrayList();
        String project = extractAttrValue(attrs, "project");
        String process = extractAttrValue(attrs, "processDescription");
        skuItemVOS.add(buildDealSkuItemVO(project));
        skuItemVOS.add(buildDealSkuItemVO(process));
        return skuItemVOS.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private String buildTime(List<StandardAttributeItemDTO> attrs) {
        String isLimitTime = extractAttrValue(attrs, "time_limited_or_not");
        if(StringUtils.isEmpty(isLimitTime) || "不限时".equals(isLimitTime)){
            return "不限时";
        }
        String time = extractAttrValue(attrs, "duration");
        String timeUnit = extractAttrValue(attrs, "time_limit_unit");
        if(StringUtils.isEmpty(time)||StringUtils.isEmpty(timeUnit)){
            return "限时";
        }
        return String.format("%s%s", time, timeUnit);
    }

    private DealSkuItemVO buildDealSkuItemVO(String attrValue) {
        if (StringUtils.isEmpty(attrValue)) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setValue(attrValue);
        return dealSkuItemVO;
    }

    private String extractAttrValue(List<StandardAttributeItemDTO> attrs, String expected) {
        return attrs.stream()
                .filter(standardAttributeItemDTO -> standardAttributeItemDTO.getAttrName().equals(expected))
                .map(StandardAttributeItemDTO::getAttrValues)
                .filter(CollectionUtils::isNotEmpty)
                .map(values -> values.get(0))
                .filter(Objects::nonNull)
                .map(StandardAttributeValueDTO::getSimpleValues)
                .filter(CollectionUtils::isNotEmpty)
                .map(strs -> strs.get(0))
                .findFirst()
                .orElse(null);
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
