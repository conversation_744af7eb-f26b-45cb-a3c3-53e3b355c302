package com.sankuai.dzviewscene.product.dealstruct.options.skuList.beautyyoga.enums;

import com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.enums.MassageServiceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2024/6/6 19:13
 */
@Getter
@AllArgsConstructor
public enum BeautyYogaEnum {
    /**
     * 团课单次卡
     */
    SINGLE_TIME_CARD(507, "团课单次卡"),

    /**
     * 团课多次卡/周期卡
     */
    MULTI_CYCLE_TIMES_CARD(507, "团课多次卡/周期卡"),
    MULTI_CYCLE_TIMES_CARD_(507, "团课多次/周期卡"),
    /**
     * 私教单次卡
     */
    PERSONAL_TIMES_CARD(507, "私教单次卡"),
    /**
     * 私教多次卡/周期卡
     */
    PERSONAL_MULTI_CYCLE_TIMES_CARD(507, "私教多次卡/周期卡"),
    PERSONAL_MULTI_CYCLE_TIMES_CARD_(507, "私教多次/周期卡"),
    /**
     * 组合课包
     */
    COMBINATION_LESSON_PACK(507, "组合课包"),
    /**
     * 教练培训
     */
    COACH_TRAINING(507, "教练培训"),
    /**
     * 舞室租赁
     */
    CLASSROOM_RENTAL(507, "舞室租赁"),
    /**
     * 舞蹈编排
     */
    CHOREOGRAPHY(507, "舞蹈编排"),
    /**
     * 实物商品
     */
    PHYSICAL_GOODS(507, "实物商品"),
    ;

    /**
     * 二级类目id
     */
    private final int dealCategoryId;

    /**
     * 三级类目名称
     */
    private final String serviceType;

    public static BeautyYogaEnum getEnumByServiceType(String serviceType) {
        for (BeautyYogaEnum value : BeautyYogaEnum.values()) {
            if (value.getServiceType().equals(serviceType)) {
                return value;
            }
        }
        return null;
    }

}
