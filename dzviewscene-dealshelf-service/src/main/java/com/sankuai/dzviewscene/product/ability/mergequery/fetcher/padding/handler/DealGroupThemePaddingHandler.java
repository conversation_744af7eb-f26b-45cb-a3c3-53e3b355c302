package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealGroupThemeHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 团单商品填充器，直接复用老的团购主题填充
 */
@Component
public class DealGroupThemePaddingHandler implements PaddingHandler {

    @Resource
    private DealGroupThemeHandler dealGroupThemeHandler;

    @Override
    public CompletableFuture<ProductGroupM> padding(ActivityCxt ctx, ProductGroupM productGroupM, Map<String, Object> params) {
        return dealGroupThemeHandler.padding(ActivityCtxtUtils.toActivityContext(ctx), productGroupM, params);
    }

}
