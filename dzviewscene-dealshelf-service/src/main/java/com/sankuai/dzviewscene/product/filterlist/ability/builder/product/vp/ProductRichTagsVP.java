package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @author: wuwen<PERSON><PERSON>
 * @create: 2024-06-17
 * @description:
 */
@VPoint(name = "商品富文本标签-ProductRichTags", description = "商品富文本标签-ProductRichTags", code = ProductRichTagsVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductRichTagsVP<T> extends PmfVPoint<List<IconRichLabelVO>, ProductRichTagsVP.Param, T> {

    public static final String CODE = "ProductRichTagsVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;
    }
}
