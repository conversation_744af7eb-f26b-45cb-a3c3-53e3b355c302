package com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.enums.MassageServiceTypeEnum;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.OverNightModuleUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.util.NumberUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: created by hang.yu on 2023/3/23 20:27
 */
public abstract class AbstractFootMessageModuleOpt<T> extends SkuListModuleVP<T> {

    public static final List<Long> SPECIAL_PRODUCT_CATEGORY_ID_LIST = Lists.newArrayList(2104617L, 2104618L, 2104662L, 2104663L);

    private static final String WHOLE_BODY = "全身";

    /**
     * 获取各三级分类的服务项目名称
     */
    protected String getServiceFlowSkuName(List<SkuAttrItemDto> skuAttrs, String serviceType) {
        MassageServiceTypeEnum serviceTypeEnum = MassageServiceTypeEnum.getEnumByServiceType(serviceType);
        if (serviceTypeEnum == null) {
            return null;
        }
        List<ServiceFlowParseModel> serviceFlowParseModels = getServiceFlowParseModels(skuAttrs);
        int stepTimeSum = getServiceTimeSum(serviceFlowParseModels);
        switch (serviceTypeEnum) {
            case FOOT_MASSAGE:
                // 足疗：服务时长+三级类目名称，如90分钟足疗
                return stepTimeSum + "分钟" + serviceType;
            case MASSAGE:
            case EAR_PICKING:
            case ESSENTIAL_OIL_SPA:
            case ULNA:
                // 推拿/按摩、采耳、精油SOA、推拿正骨：服务时长+服务手法分类
                String serviceTechnique = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "serviceTechnique");
                return stepTimeSum + "分钟" + (StringUtils.isBlank(serviceTechnique) ? "" : serviceTechnique);
            case MOXIBUSTION:
                // 艾灸：服务时长（拼接三级分类）+服务手法分类+特色灸法， 用 "|"分割。 如 60分钟艾灸｜盒灸｜三伏灸
                serviceTechnique = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "serviceTechnique");
                String moxibustionMethod = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "moxibustionMethod");
                return stepTimeSum + "分钟" + serviceType + (StringUtils.isBlank(serviceTechnique) ? "" : "｜" + serviceTechnique) + (StringUtils.isNotBlank(moxibustionMethod) ? "｜" + moxibustionMethod : "");
            case CUP:
                // 拔罐：三级类目+服务手法分类，，不同字段“｜”分割，如拔罐｜留罐、拔罐｜走罐
                serviceTechnique = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "serviceTechnique");
                return serviceType + (StringUtils.isBlank(serviceTechnique) ? "" : "｜" + serviceTechnique);
            case SCRAPING:
                // 刮痧：三级类目名称，如刮痧
                return serviceType;
            case HEAD:
                // 头疗：三级类目+ "|"+服务部位+服务手法（若服务部位有多个，只取优先级最高的第一个）
                String headServiceBodyRange = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "serviceBodyRange");
                if (StringUtils.isNotBlank(headServiceBodyRange) && headServiceBodyRange.contains("、")) {
                    headServiceBodyRange = headServiceBodyRange.split("、")[0];
                }
                String headServiceTechnique = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "serviceTechnique");
                return serviceType + "｜" + (StringUtils.isNotBlank(headServiceBodyRange) ? headServiceBodyRange : "") + (StringUtils.isNotBlank(headServiceTechnique) ? headServiceTechnique : "");
            default:
                return null;
        }
    }

    /**
     * 获取各三级分类的服务项目名称
     */
    protected String getServiceFlowSkuName2(List<SkuAttrItemDto> skuAttrs, String serviceType) {
        MassageServiceTypeEnum serviceTypeEnum = MassageServiceTypeEnum.getEnumByServiceType(serviceType);
        if (serviceTypeEnum == null) {
            return null;
        }
        switch (serviceTypeEnum) {
            case FOOT_MASSAGE:
            case SCRAPING:
                // 足疗、刮痧：三级类目名称，如刮痧
                return serviceType;
            case EAR_PICKING:
                // 服务手法
                return DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "serviceTechnique");
            case MASSAGE:
            case ESSENTIAL_OIL_SPA:
            case ULNA:
                // 推拿/按摩、精油SOA、推拿正骨：只有当服务部位范围为全身时拼接，为局部部位时直接展示服务手法
                String bodyRegion = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "bodyRegion");
                String serviceTechnique = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "serviceTechnique");
                if (WHOLE_BODY.equals(bodyRegion)) {
                    return WHOLE_BODY + (StringUtils.isNotBlank(serviceTechnique) ? serviceTechnique : "");
                }
                return serviceTechnique;
            case MOXIBUSTION:
                // 艾灸：三级分类+服务手法+特色灸法， 用 "|"分割。 如 艾灸｜盒灸｜三伏灸
                serviceTechnique = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "serviceTechnique");
                String moxibustionMethod = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "moxibustionMethod");
                return serviceType + (StringUtils.isBlank(serviceTechnique) ? "" : "｜" + serviceTechnique) + (StringUtils.isBlank(moxibustionMethod) ? "" : "｜" + moxibustionMethod);
            case CUP:
                // 拔罐：三级类目+服务手法，如拔罐（走罐）
                serviceTechnique = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "serviceTechnique");
                return serviceType + (StringUtils.isBlank(serviceTechnique) ? "" : "（" + serviceTechnique + "）");
            case HEAD:
                // 头疗：三级类目+ "|"+服务部位+服务手法（若服务部位有多个，只取优先级最高的第一个）
                String headServiceBodyRange = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "serviceBodyRange");
                if (StringUtils.isNotBlank(headServiceBodyRange) && headServiceBodyRange.contains("、")) {
                    headServiceBodyRange = headServiceBodyRange.split("、")[0];
                }
                String headServiceTechnique = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "serviceTechnique");
                return serviceType + "｜" + (StringUtils.isNotBlank(headServiceBodyRange) ? headServiceBodyRange : "") + (StringUtils.isNotBlank(headServiceTechnique) ? headServiceTechnique : "");
            default:
                return null;
        }
    }

    /**
     * 获取服务总时长
     */
    protected int getServiceTimeSum(List<ServiceFlowParseModel> serviceFlowParseModels) {
        if (CollectionUtils.isEmpty(serviceFlowParseModels)) {
            return 0;
        }
        List<Integer> stepTime = serviceFlowParseModels.stream().map(ServiceFlowParseModel::getStepTime).collect(Collectors.toList());
        return stepTime.stream().reduce(Integer::sum).orElse(0);
    }

    /**
     * 解析服务流程列表
     */
    protected List<ServiceFlowParseModel> getServiceFlowParseModels(List<SkuAttrItemDto> skuAttrs) {
        String serviceFlowJsonStr = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "serviceProcessArrayNew");
        return JsonCodec.converseList(serviceFlowJsonStr, ServiceFlowParseModel.class);
    }

    /**
     * 构建服务项目列表需要的内容
     */
    protected List<DealSkuItemVO> getServiceItems(List<SkuAttrItemDto> skuAttrs, Long productCategory) {
        List<DealSkuItemVO> result = Lists.newArrayList();
        // 服务部位
        DealSkuItemVO serviceBodyRange = getServiceBodyRange(skuAttrs);
        if (serviceBodyRange != null) {
            result.add(serviceBodyRange);
        }

        // 服务姿势
        DealSkuItemVO servingPosture = getServingPosture(skuAttrs);
        if (servingPosture != null) {
            result.add(servingPosture);
        }

        // 服务流程
        List<DealSkuItemVO> serviceFlowList = getServiceFlowDealSkuItemVO(getServiceFlowParseModels(skuAttrs));
        if (CollectionUtils.isNotEmpty(serviceFlowList)) {
            result.addAll(serviceFlowList);
        }

        // 刮痧、拔罐增加服务时长
        if (SPECIAL_PRODUCT_CATEGORY_ID_LIST.contains(productCategory)) {
            DealSkuItemVO serviceDurationInt = getServiceDurationInt(skuAttrs);
            if (serviceDurationInt != null) {
                result.add(serviceDurationInt);
            }
        }
        return result;
    }

    /**
     * 构建服务项目列表需要的内容（新版团详）
     */
    protected List<DealSkuItemVO> getServiceItems2(List<SkuAttrItemDto> skuAttrs) {
        List<DealSkuItemVO> result = Lists.newArrayList();
        // 服务部位
        DealSkuItemVO serviceBodyRange = getServiceBodyRange2(skuAttrs);
        if (serviceBodyRange != null) {
            result.add(serviceBodyRange);
        }

        // 服务流程
        List<DealSkuItemVO> serviceFlowList = getServiceFlowDealSkuItemVO(getServiceFlowParseModels(skuAttrs));
        if (CollectionUtils.isNotEmpty(serviceFlowList)) {
            result.addAll(serviceFlowList);
        }
        return result;
    }

    /**
     * 获取服务部位
     */
    protected DealSkuItemVO getServiceBodyRange(List<SkuAttrItemDto> skuAttrs) {
        String serviceBodyRange = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "serviceBodyRange");
        if (StringUtils.isBlank(serviceBodyRange)) {
            return null;
        }
        return buildDealSkuItemVO("服务部位", 0, null, serviceBodyRange);
    }

    /**
     * 获取服务部位（新版）
     */
    protected DealSkuItemVO getServiceBodyRange2(List<SkuAttrItemDto> skuAttrs) {
        String serviceBodyRange = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "serviceBodyRange");
        if (StringUtils.isBlank(serviceBodyRange)) {
            return null;
        }
        String bodyRegion = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "bodyRegion");
        if (WHOLE_BODY.equals(bodyRegion)) {
            return buildDealSkuItemVO("服务部位", 0, null, String.format("全身（含%s）", serviceBodyRange));
        }
        return buildDealSkuItemVO("服务部位", 0, null, serviceBodyRange);
    }

    /**
     * 获取服务姿势
     */
    protected DealSkuItemVO getServingPosture(List<SkuAttrItemDto> skuAttrs) {
        String servingPosture = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "servingPosture");
        if (StringUtils.isBlank(servingPosture)) {
            return null;
        }
        return buildDealSkuItemVO("服务姿势", 0, null, servingPosture);
    }

    /**
     * 获取服务时长
     */
    protected DealSkuItemVO getServiceDurationInt(List<SkuAttrItemDto> skuAttrs) {
        String serviceDurationInt = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, "serviceDurationInt");
        if (StringUtils.isBlank(serviceDurationInt)) {
            return null;
        }
        return buildDealSkuItemVO("服务时长", 0, null, String.format("%s分钟", serviceDurationInt));
    }

    /**
     * 构建服务流程
     */
    protected List<DealSkuItemVO> getServiceFlowDealSkuItemVO(List<ServiceFlowParseModel> serviceFlowParseModels) {
        if (CollectionUtils.isEmpty(serviceFlowParseModels)) {
            return null;
        }
        // 构造sku属性列表
        List<SkuAttrAttrItemVO> skuAttrItemList = serviceFlowParseModels.stream().map(serviceFlowStep -> {
            if (serviceFlowStep == null) {
                return null;
            }
            SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
            // 标题：服务部位+服务方式
            String bodyPart = serviceFlowStep.getBodyPart() == null ? "" : serviceFlowStep.getBodyPart();
            String serviceMethod = serviceFlowStep.getServicemethod() == null ? "" : serviceFlowStep.getServicemethod();
            skuAttrAttrItemVO.setName(bodyPart + serviceMethod);
            // info为服务时长
            if (serviceFlowStep.getStepTime() > 0) {
                skuAttrAttrItemVO.setInfo(Lists.newArrayList(serviceFlowStep.getStepTime() + "分钟"));
            }
            return skuAttrAttrItemVO;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(skuAttrItemList)) {
            return null;
        }
        return Lists.newArrayList(buildDealSkuItemVO("服务流程", 2, skuAttrItemList, null));
    }

    /**
     * 构建服务项目
     */
    protected DealSkuItemVO buildDealSkuItemVO(String name, int type, List<SkuAttrAttrItemVO> skuAttrAttrItemVOS, String value) {
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setType(type);
        dealSkuItemVO.setValueAttrs(skuAttrAttrItemVOS);
        dealSkuItemVO.setValue(value);
        return dealSkuItemVO;
    }

    protected String getSkuSubTitle(SkuItemDto skuItemDto) {
        String serviceDurationInt = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), "serviceDurationInt");
        if (StringUtils.isBlank(serviceDurationInt)) {
            return null;
        }
        return String.format("%s分钟", serviceDurationInt);
    }

    protected DealDetailSkuListModuleGroupModel buildDealDetailSkuListModuleGroupModel(String groupName, String subTitle, List<DealSkuVO> dealSkuList) {
        if (CollectionUtils.isEmpty(dealSkuList)) {
            return null;
        }
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(dealSkuList);
        DealDetailSkuListModuleGroupModel dealDetailSkuListModuleGroupModel = new DealDetailSkuListModuleGroupModel();
        dealDetailSkuListModuleGroupModel.setGroupName(groupName);
        dealDetailSkuListModuleGroupModel.setGroupSubtitle(subTitle);
        dealDetailSkuListModuleGroupModel.setDealSkuGroupModuleVOS(Lists.newArrayList(dealSkuGroupModuleVO));
        return dealDetailSkuListModuleGroupModel;
    }

    protected DealDetailSkuListModuleGroupModel buildDealDetailSkuListModuleGroupModel(String groupName, String groupTitle, String subTitle, List<DealSkuVO> dealSkuList) {
        if (CollectionUtils.isEmpty(dealSkuList)) {
            return null;
        }
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(dealSkuList);
        DealDetailSkuListModuleGroupModel dealDetailSkuListModuleGroupModel = new DealDetailSkuListModuleGroupModel();
        dealDetailSkuListModuleGroupModel.setGroupName(groupName);
        dealDetailSkuListModuleGroupModel.setGroupTitle(groupTitle);
        dealDetailSkuListModuleGroupModel.setGroupSubtitle(subTitle);
        dealDetailSkuListModuleGroupModel.setDealSkuGroupModuleVOS(Lists.newArrayList(dealSkuGroupModuleVO));
        return dealDetailSkuListModuleGroupModel;
    }

    /**
     * 构建付费服务模块
     */
    protected DealDetailSkuListModuleGroupModel buildPayServiceModule(List<AttrM> dealAttrs, boolean hitNewIcon) {
        DealSkuVO payOverNight = OverNightModuleUtils.parsePayOverNightSkuModule(dealAttrs, hitNewIcon);
        if (payOverNight == null) {
            return null;
        }
        return buildDealDetailSkuListModuleGroupModel("付费服务模块", "下单时可自选加购", Lists.newArrayList(payOverNight));
    }

    /**
     * 获取套餐里服务项目对应的三级类目名称
     */
    protected String getCombinationServiceType(long productCategory, List<ProductSkuCategoryModel> productCategories) {
        if (CollectionUtils.isEmpty(productCategories)) {
            return null;
        }
        return productCategories.stream().filter(productSkuCategoryModel -> Objects.equals(productSkuCategoryModel.getProductCategoryId(), productCategory))
                .map(ProductSkuCategoryModel::getCnName).findAny().orElse(null);
    }

    protected boolean hitNewIcon(List<DouhuResultModel> douhuResultModels, List<String> expIds) {
        if (CollectionUtils.isEmpty(douhuResultModels) || CollectionUtils.isEmpty(expIds)) {
            return false;
        }
        return douhuResultModels.stream()
                .filter(douhuResultModel -> expIds.contains(douhuResultModel.getExpId()))
                .map(DouhuResultModel::isHitSk).findFirst().orElse(false);
    }

}
