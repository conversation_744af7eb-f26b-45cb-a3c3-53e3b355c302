package com.sankuai.dzviewscene.product.filterlist.option.builder.product.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductRichTitleVP;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTitleVP;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@VPointOption(name = "多次卡团单富文本名称展示", description = "多次卡团单富文本名称展示", code = "TimesDealRichTitleOpt")
public class TimesDealRichTitleOpt extends ProductRichTitleVP<TimesDealRichTitleOpt.Config> {

    @Override
    public String compute(ActivityCxt context, Param param, TimesDealRichTitleOpt.Config config) {
        // 非次卡召回
        if (!param.getProductM().isTimesDealQueryFlag()) {
            return StringUtils.EMPTY;
        }
        // 非团购次卡,即次卡关联的团购本身
        if (!param.getProductM().isTimesDeal()) {
            // 设置richTitle
            return TimesDealUtil.genSingleTimesDealRichTitle(param.getProductM(), context);
        }
        // 获取多次卡的次数
        String times = param.getProductM().getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER);
        String singlePrice = TimesDealUtil.getSinglePrice(param.getProductM().getSalePrice(), times);
        if (StringUtils.isBlank(singlePrice)) {
            return StringUtils.EMPTY;
        }
        // 设置richTitle
        return TimesDealUtil.genMultiTimesDealRichTitle(context, param.getProductM(),
                StringUtils.isEmpty(config.getUnit()) ? "/次" : config.getUnit());
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 单位
         */
        private String unit;

    }

}