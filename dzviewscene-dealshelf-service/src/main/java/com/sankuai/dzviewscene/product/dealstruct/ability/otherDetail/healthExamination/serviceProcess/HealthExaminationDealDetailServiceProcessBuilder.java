package com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.healthExamination.serviceProcess;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.DealAttrVOListModuleBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.CommonAttrsVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealModuleDetailVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination.*;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.uniform.UniformStructContentModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/17 2:30 下午
 */
@Ability(code = HealthExaminationDealDetailServiceProcessBuilder.CODE,
        name = "体检团购详情模块服务流程构造能力",
        description = "体检团购详情模块服务流程构造能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE,
                DealAttrVOListModuleBuilder.CODE
        }
)
public class HealthExaminationDealDetailServiceProcessBuilder extends PmfAbility<DealModuleDetailVO, HealthExaminationDealDetailServiceProcessParam, HealthExaminationDealDetailServiceProcessCfg> {

    public static final String CODE = "healthExaminationDealDetailServiceProcessBuilder";

    @Override
    public CompletableFuture<DealModuleDetailVO> build(ActivityCxt ctx, HealthExaminationDealDetailServiceProcessParam param, HealthExaminationDealDetailServiceProcessCfg cfg) {
        //1.获取构造服务流程所需的团单基本信息
        DealDetailInfoModel dealDetailBasicInfo = getDealDetailBasicInfo(ctx);
        if (dealDetailBasicInfo == null) {
            return null;
        }
        //1.1 获取原始服务流程数据
        List<ServiceProcessItem> serviceProcessItems = getServiceProcessData(dealDetailBasicInfo);
        //1.2 获取团单属性数据
        List<AttrM> dealAttrs = dealDetailBasicInfo.getDealAttrs();
        //2.构造DealModuleDetailVO
        return CompletableFuture.completedFuture(getDealModuleDetailVO(serviceProcessItems, dealAttrs));
    }

    private DealModuleDetailVO getDealModuleDetailVO(List<ServiceProcessItem> serviceProcessItems, List<AttrM> dealAttrs) {
        //1.构造流程信息
        ProcessModuleListVO serviceProcess  = getServiceProcess(serviceProcessItems, dealAttrs);
        //2.构造体检须知
        CommonAttrsVO examinationNotice = buildExaminationNotice(dealAttrs);
        if (serviceProcess == null && examinationNotice == null) {
            return null;
        }
        DealModuleDetailVO dealModuleDetailVO = new DealModuleDetailVO();
        HealthExaminationDealDetailVO healthExaminationDealDetailVO = new HealthExaminationDealDetailVO();
        healthExaminationDealDetailVO.setServiceProcess(serviceProcess);
        healthExaminationDealDetailVO.setExaminationNotice(examinationNotice);
        dealModuleDetailVO.setHealthExaminationDealDetailVO(healthExaminationDealDetailVO);
        return dealModuleDetailVO;
    }

    /**
     * 构造体检须知模块
     *@param
     *@return
     */
    private CommonAttrsVO buildExaminationNotice(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        List<String> examinationNotices = DealDetailUtils.getAttrValueByAttrName(dealAttrs, "info_before_check");
        if (CollectionUtils.isEmpty(examinationNotices)) {
            return null;
        }
        CommonAttrsVO commonAttrsVO = new CommonAttrsVO();
        commonAttrsVO.setName("体检须知");
        commonAttrsVO.setValues(examinationNotices);
        return commonAttrsVO;
    }

    /**
     * 添加发票信息
     *@param
     *@return
     */
    private void addInvoiceInfo(List<AttrM> dealAttrs, List<ProcessModuleVO> result) {
        if (result == null) {
            return;
        }
        List<ProcessModuleItemVO> processes = getProcessModuleItemVOs(dealAttrs);
        if (CollectionUtils.isEmpty(processes)) {
            return;
        }
        ProcessModuleVO processModuleVO = new ProcessModuleVO();
        processModuleVO.setProcessName("获取发票");
        processModuleVO.setProcesses(processes);
        result.add(processModuleVO);
    }

    /**
     * 获取发票信息的子步骤模块列表
     *@param
     *@return
     */
    private List<ProcessModuleItemVO> getProcessModuleItemVOs(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        List<ProcessModuleItemVO> processModuleItemVOS = new ArrayList<>();
        //添加开发票咨询电话
        List<String> invoiceConsultTels = DealDetailUtils.getAttrValueByAttrName(dealAttrs, "bill-tel");
        addProcessModuleItem(processModuleItemVOS, null, null, buildNumProcessSubitemVO("咨询电话", invoiceConsultTels));
        //添加发票开具方式
        String invoiceObtainMethod = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "bill-way");
        addProcessModuleItem(processModuleItemVOS, null, buildProcessSubitemVO("开具方式", invoiceObtainMethod), null);
        return processModuleItemVOS;
    }

    /**
     * 构造服务流程
     *@param
     *@return
     */
    private ProcessModuleListVO getServiceProcess(List<ServiceProcessItem> serviceProcessItems, List<AttrM> dealAttrs) {
        List<ProcessModuleVO> allProcessModuleVOS = new ArrayList<>();
        //添加服务流程信息
        addServiceProcess(allProcessModuleVOS, serviceProcessItems, dealAttrs);
        //添加发票信息
        addInvoiceInfo(dealAttrs, allProcessModuleVOS);
        if (CollectionUtils.isEmpty(allProcessModuleVOS)) {
            return null;
        }
        ProcessModuleListVO processModuleListVO = new ProcessModuleListVO();
        processModuleListVO.setTitle("服务流程");
        processModuleListVO.setProcessModuleList(allProcessModuleVOS);
        return processModuleListVO;
    }

    private void addServiceProcess(List<ProcessModuleVO> result, List<ServiceProcessItem> serviceProcessItems, List<AttrM> dealAttrs) {
        if (result == null || CollectionUtils.isEmpty(serviceProcessItems)) {
            return;
        }
        List<ProcessModuleVO> processModuleVOS = serviceProcessItems.stream()
                .map(item -> buildProcessModuleVO(item, dealAttrs))
                .filter(process -> process != null)
                .collect(Collectors.toList());
        result.addAll(processModuleVOS);
    }

    /**
     * 获取服务流程的单个步骤
     *@param
     *@return
     */
    private ProcessModuleVO buildProcessModuleVO(ServiceProcessItem serviceProcessItem, List<AttrM> dealAttrs) {
        if (serviceProcessItem == null) {
            return null;
        }
        ProcessModuleVO processModuleVO = new ProcessModuleVO();
        //流程步骤名
        processModuleVO.setProcessName(serviceProcessItem.getStepName());
        //流程步骤，由一个或多个流程步骤子模块组成
        List<ProcessModuleItemVO> processModuleItemVOS = getProcessModuleItemVOs(serviceProcessItem, dealAttrs);
        processModuleVO.setProcesses(processModuleItemVOS);
        return processModuleVO;
    }

    /**
     * 获取服务流程单个步骤中的子步骤模块列表
     *@param
     *@return
     */
    private List<ProcessModuleItemVO> getProcessModuleItemVOs(ServiceProcessItem serviceProcessItem, List<AttrM> dealAttrs) {
        List<ProcessModuleItemVO> processModuleItemVOS = new ArrayList<>();
        //添加流程内容ProcessModuleItemVO
        addProcessModuleItem(processModuleItemVOS, serviceProcessItem.getOtherInfo(), null, null);
        //如果流程名称是"预约门店及时间"，则再添加预约时间和预约方式ProcessModuleItemVO
        if ("预约门店及时间".equals(serviceProcessItem.getStepName())) {
            addReserveTimeAndMethod(processModuleItemVOS, dealAttrs);
        }
        //如果流程名称是"查询报告"，则再添加出报告时间和报告获取方式ProcessModuleItemVO
        if ("查询报告".equals(serviceProcessItem.getStepName())) {
            addReportTimeAndAbtainMethod(processModuleItemVOS, dealAttrs);
        }
        if (CollectionUtils.isEmpty(processModuleItemVOS)) {
            return null;
        }
        return processModuleItemVOS;
    }

    /**
     * 添加出报告时间和报告获取方式
     *@param
     *@return
     */
    private void addReportTimeAndAbtainMethod(List<ProcessModuleItemVO> processModuleItemVOS, List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs) || processModuleItemVOS == null) {
            return;
        }
        //出报告时间
        String reportTime = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "physical_examination_get_result_time");
        addProcessModuleItem(processModuleItemVOS, null, buildProcessSubitemVO("出报告时间", reportTime), null);
        //报告获取方式
        processModuleItemVOS.addAll(getReportProcessModuleItemVOs(dealAttrs));
    }

    /**
     * 获取报告获取方式
     *@param
     *@return
     */
    private List<ProcessModuleItemVO> getReportProcessModuleItemVOs(List<AttrM> dealAttrs) {
        List<String> reportObtainMethodTypes = DealDetailUtils.getAttrValueByAttrName(dealAttrs, "physical_examination_report_type");
        if (CollectionUtils.isEmpty(reportObtainMethodTypes)) {
            return new ArrayList<>();
        }
        return reportObtainMethodTypes.stream()
                .map(type -> {
                    String reportObtainMethod = "电子报告".equals(type) ? DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "electronic_reporting_mode") : DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "paper_report_mode");
                    return buildProcessModuleItemVO(null, buildProcessSubitemVO(type, reportObtainMethod), null);
                })
                .filter(vo -> vo != null)
                .collect(Collectors.toList());
    }

    /**
     * 添加预约时间和预约方式，预约时间和预约方式在团单属性中获取
     *@param
     *@returnd
     */
    private void addReserveTimeAndMethod(List<ProcessModuleItemVO> processModuleItemVOS, List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs) || processModuleItemVOS == null) {
            return;
        }
        //判断是否需要预约
        boolean needReserve = "是".equals(DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "reservation_is_needed_or_not"));
        if (!needReserve) {
            return;
        }
        //预约时间
        String reserveTime = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "reservation_detail");
        //删除商品平台带过来的多余换行符
        reserveTime = reserveTime == null ? reserveTime : reserveTime.replaceAll("\n", "");
        addProcessModuleItem(processModuleItemVOS, null, buildProcessSubitemVO("预约时间", reserveTime), null);
        //预约电话
        String reserveTelNumStr = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "reservation_number");
        if (StringUtils.isEmpty(reserveTelNumStr)) {
            return;
        }
        addProcessModuleItem(processModuleItemVOS, null, null, buildNumProcessSubitemVO("预约电话", Lists.newArrayList(reserveTelNumStr.split(","))));
    }

    private NumProcessSubitemVO buildNumProcessSubitemVO(String itemName, List<String> nums) {
        if (StringUtils.isEmpty(itemName) || CollectionUtils.isEmpty(nums)) {
            return null;
        }
        NumProcessSubitemVO numProcessSubitemVO = new NumProcessSubitemVO();
        numProcessSubitemVO.setItemName(itemName);
        numProcessSubitemVO.setNums(nums);
        return numProcessSubitemVO;
    }

    private ProcessSubitemVO buildProcessSubitemVO(String itemName, String itemValue) {
        if (StringUtils.isEmpty(itemName) || StringUtils.isEmpty(itemValue)) {
            return null;
        }
        ProcessSubitemVO processSubitemVO = new ProcessSubitemVO();
        processSubitemVO.setItemName(itemName);
        processSubitemVO.setItemValue(itemValue);
        return processSubitemVO;
    }

    /**
     * 添加ProcessModuleItemVO
     *@param
     *@return
     */
    private void addProcessModuleItem(List<ProcessModuleItemVO> processModuleItemVOS, String processContent, ProcessSubitemVO processItem, NumProcessSubitemVO numProcessItem) {
        if (processModuleItemVOS == null) {
            return;
        }
        ProcessModuleItemVO processModuleItemVO = buildProcessModuleItemVO(processContent, processItem, numProcessItem);
        if (processModuleItemVO == null) {
            return;
        }
        processModuleItemVOS.add(processModuleItemVO);
    }

    private ProcessModuleItemVO buildProcessModuleItemVO(String processContent, ProcessSubitemVO processItem, NumProcessSubitemVO numProcessItem) {
        if (StringUtils.isEmpty(processContent) && processItem == null && numProcessItem == null) {
            return null;
        }
        ProcessModuleItemVO processModuleItemVO = new ProcessModuleItemVO();
        processModuleItemVO.setProcessContent(processContent);
        processModuleItemVO.setProcessItem(processItem);
        processModuleItemVO.setNumProcessItem(numProcessItem);
        return processModuleItemVO;
    }

    private DealDetailInfoModel getDealDetailBasicInfo(ActivityCxt activityCxt) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        return CollectUtils.firstValue(dealDetailInfoModels);
    }

    /**
     * 获取服务流程原始数据
     *@param
     *@return
     */
    private List<ServiceProcessItem> getServiceProcessData(DealDetailInfoModel dealDetailBasicInfo) {
        if (dealDetailBasicInfo == null || CollectionUtils.isEmpty(dealDetailBasicInfo.getDealModuleAttrs())) {
            return null;
        }
        UniformStructContentModel dealModuleAttrsModel = dealDetailBasicInfo.getDealModuleAttrs()
                .stream()
                .filter(moduleAttr -> "physicalExaminationServiceNote".equals(moduleAttr.getType()))
                .findFirst().orElse(null);
        if (dealModuleAttrsModel == null || dealModuleAttrsModel.getData() == null) {
            return null;
        }
        ServiceProcess serviceProcess = JsonCodec.decode(JsonCodec.encodeWithUTF8(dealModuleAttrsModel.getData()), ServiceProcess.class);
        if (serviceProcess == null || CollectionUtils.isEmpty(serviceProcess.getGroups())) {
            return null;
        }
        return serviceProcess.getGroups();
    }

    @Data
    private static class ServiceProcess {
        private List<ServiceProcessItem> groups;
    }

    @Data
    private static class ServiceProcessItem{
        private String otherInfo;
        private String stepName;
    }

}
