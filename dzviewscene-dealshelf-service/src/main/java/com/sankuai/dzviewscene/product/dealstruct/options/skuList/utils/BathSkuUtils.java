package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bath.enums.BathServiceProjectEnum;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: created by hang.yu on 2023/10/18 10:31
 */
public class BathSkuUtils {
    /**
     * 根据标题是否包含这些字段，判断团购是否为浴资票
     */
    private static final Set<String> TICKET_TITLE_LIST = new HashSet<>(Lists.newArrayList("浴资票", "浴资门票", "门票", "浴资", "浴资券",
            "净桑套餐", "净桑", "票"));
    /**
     * 当团购不为浴资票时，不展示这些属性
     */
    private static final Set<String> NON_TICKET_DISPLAY_FIELDS = new HashSet<>(Lists.newArrayList("usepeoplenum", "ApplicableDuration"));

    public static List<DealSkuItemVO> buildSkuItems(SkuItemDto skuItemDto) {
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return null;
        }
        BathServiceProjectEnum serviceProject = BathServiceProjectEnum.getEnumByServiceProjectId(skuItemDto.getProductCategory());
        if (serviceProject == null) {
            return null;
        }

        List<SkuAttrItemDto> attrItems = new ArrayList<>(skuItemDto.getAttrItems());
        // 若团购的标题不包含这些字段，可以认为不是浴资票类别。需要排除相关属性
        if (StringUtils.isNotEmpty(skuItemDto.getName()) && TICKET_TITLE_LIST.stream().noneMatch(a -> skuItemDto.getName().contains(a))) {
            attrItems = skuItemDto.getAttrItems().stream().filter(b -> !NON_TICKET_DISPLAY_FIELDS.contains(b.getAttrName())).collect(Collectors.toList());
        }
        return getProjectDesc(serviceProject, attrItems);
    }

    public static List<DealSkuItemVO> getProjectDesc(BathServiceProjectEnum serviceProject, List<SkuAttrItemDto> attrItems) {
        switch (serviceProject) {
            case BATH_TICKET:
                return getBathTicketDesc(attrItems);
            case SCRUB:
                return getScrubDesc(attrItems);
            case MASSAGE:
                return getMassageDesc(attrItems);
            case FOOD_1:
            case FOOD_2:
                return getFoodDesc(attrItems);
            case FOOD_3:
                return getFoodDesc2(attrItems);
            case JOY:
                return getJoyDesc(attrItems);
            case SPA:
                return getSpaDesc(attrItems);
            case REST:
                return getRestDesc(attrItems);
            case SERVICE_FEE_1:
            case SERVICE_FEE_2:
                return getServiceFeeDesc(attrItems);
            default:
                return null;
        }
    }

    public static String buildSkuInfo(SkuItemDto skuItemDto) {
        List<DealSkuItemVO> list = buildSkuItems(skuItemDto);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(DealSkuItemVO::getValue).collect(Collectors.joining("|"));
    }

    private static List<DealSkuItemVO> getBathTicketDesc(List<SkuAttrItemDto> attrItems) {
        // 适用人数>使用时长>项目内容
        String usePeopleNum = attrItems.stream().filter(attr -> "usepeoplenum".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String applicableDuration = attrItems.stream().filter(attr -> "ApplicableDuration".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String content = attrItems.stream().filter(attr -> "content".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        return join(formatApplicablePeople(usePeopleNum), formatApplicableDuration(applicableDuration), content);
    }


    private static List<DealSkuItemVO> getScrubDesc(List<SkuAttrItemDto> attrItems) {
        // 搓澡材料>额外服务>适用人群>项目内容>开始服务时间、结束服务时间
        String scrubbingMaterials = attrItems.stream().filter(attr -> "ScrubbingMaterials".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String additionalServices2 = attrItems.stream().filter(attr -> "AdditionalServices2".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String suitCrowds = attrItems.stream().filter(attr -> "suitCrowds".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String projectDesc = attrItems.stream().filter(attr -> "content".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String startTime = attrItems.stream().filter(attr -> "start_time".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String endTime = attrItems.stream().filter(attr -> "end_time".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        return join(scrubbingMaterials, formatAdditionalService(additionalServices2), suitCrowds, projectDesc, formatStartAndEndTime(startTime, endTime));
    }

    private static List<DealSkuItemVO> getMassageDesc(List<SkuAttrItemDto> attrItems) {
        // 项目分类>部位>服务时长>项目内容>开始服务时间、结束服务时间
        String spuCategory = attrItems.stream().filter(attr -> "spuCategory".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String servicePosition = attrItems.stream().filter(attr -> "ServicePosition".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String serviceDuration = attrItems.stream().filter(attr -> "serviceDuration".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String projectDesc = attrItems.stream().filter(attr -> "content".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String endTime = attrItems.stream().filter(attr -> "end_time".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String startTime = attrItems.stream().filter(attr -> "start_time".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        return join(spuCategory, servicePosition, serviceDuration, projectDesc, formatStartAndEndTime(startTime, endTime));
    }

    private static List<DealSkuItemVO> getFoodDesc(List<SkuAttrItemDto> attrItems) {
        // 餐饮种类>>餐食内容（正餐自助内容、水果自助内容、甜点/小吃自助内容、酒水饮料自助内容）>项目内容>开始服务时间、结束服务时间
        String cuisineTypes = attrItems.stream().filter(attr -> "CuisineTypes".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String buffetContent = attrItems.stream().filter(attr -> "BuffetContent".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String fruitBuffetContent = attrItems.stream().filter(attr -> "FruitBuffetContent".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String dessertsSnacksContent = attrItems.stream().filter(attr -> "DessertsSnackscontent".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String beverageContent = attrItems.stream().filter(attr -> "BeverageContent".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String startTime = attrItems.stream().filter(attr -> "start_time".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String projectDesc = attrItems.stream().filter(attr -> "content".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String endTime = attrItems.stream().filter(attr -> "end_time".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        return join(cuisineTypes, formatFood(buffetContent), formatFood(fruitBuffetContent),
                formatFood(dessertsSnacksContent), formatFood(beverageContent), projectDesc, formatStartAndEndTime2(startTime, endTime));
    }

    private static List<DealSkuItemVO> getFoodDesc2(List<SkuAttrItemDto> attrItems) {
        // 自助餐种类>时段>正餐自助内容；水果自助内容；甜点/小吃内容；酒水饮料内容；>项目内容>开始服务时间和结束服务时间
        String cuisineTypes = attrItems.stream().filter(attr -> "CuisineTypes".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String mealContent = attrItems.stream().filter(attr -> "mealContent".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String specialtyDishes = attrItems.stream().filter(attr -> "SpecialtyDishes".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String period = attrItems.stream().filter(attr -> "period".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        // 正餐自助内容
        String buffetContent = attrItems.stream().filter(attr -> "BuffetContent".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        // 水果自助内容
        String fruitBuffetContent = attrItems.stream().filter(attr -> "FruitBuffetContent".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        // 甜点/小吃内容
        String dessertsSnackscontent = attrItems.stream().filter(attr -> "DessertsSnackscontent".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        // 酒水饮料内容
        String beverageContent = attrItems.stream().filter(attr -> "BeverageContent".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String projectDesc = attrItems.stream().filter(attr -> "content".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String startTime = attrItems.stream().filter(attr -> "start_time".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String endTime = attrItems.stream().filter(attr -> "end_time".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        if ("单点".equals(cuisineTypes)) {
            return join(formatFood(mealContent), projectDesc, formatStartAndEndTime2(startTime, endTime));
        }
        return join(specialtyDishes, period, formatFood(buffetContent), formatFood(fruitBuffetContent), formatFood(dessertsSnackscontent),
                formatFood(beverageContent), projectDesc, formatStartAndEndTime2(startTime, endTime));
    }

    private static List<DealSkuItemVO> getJoyDesc(List<SkuAttrItemDto> attrItems) {
        // 玩乐类型>服务时长+时长单位>项目内容>开始服务时间、结束服务时间
        String playType = attrItems.stream().filter(attr -> "PlayType".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String serviceDuration = attrItems.stream().filter(attr -> "serviceDuration".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String durationUnit = attrItems.stream().filter(attr -> "DurationUnit".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String projectDesc = attrItems.stream().filter(attr -> "content".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String endTime = attrItems.stream().filter(attr -> "end_time".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String startTime = attrItems.stream().filter(attr -> "start_time".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        return join(playType, formatJoin(serviceDuration, durationUnit), projectDesc, formatStartAndEndTime(startTime, endTime));
    }

    private static List<DealSkuItemVO> getSpaDesc(List<SkuAttrItemDto> attrItems) {
        // 功效>服务类型>项目内容>开始服务时间、结束服务时间
        String serviceeffect = attrItems.stream().filter(attr -> "serviceeffect".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String spuCategory = attrItems.stream().filter(attr -> "spuCategory".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String projectDesc = attrItems.stream().filter(attr -> "content".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String startTime = attrItems.stream().filter(attr -> "start_time".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String endTime = attrItems.stream().filter(attr -> "end_time".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        return join(serviceeffect, spuCategory, projectDesc, formatStartAndEndTime(startTime, endTime));
    }

    private static List<DealSkuItemVO> getRestDesc(List<SkuAttrItemDto> attrItems) {
        // 房间种类>服务时长>餐食服务拼接餐食内容>项目内容>退房时间
        String roomType = attrItems.stream().filter(attr -> "roomType".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String serviceDuration = attrItems.stream().filter(attr -> "serviceDuration".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String doyouoffermeals = attrItems.stream().filter(attr -> "Doyouoffermeals".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String mealContent = attrItems.stream().filter(attr -> "mealContent".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String projectDesc = attrItems.stream().filter(attr -> "content".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        String checkOutTime = attrItems.stream().filter(attr -> "CheckOutTime".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        return join(roomType, serviceDuration, formatMeal(doyouoffermeals, mealContent), projectDesc, formatCheckOutTime(checkOutTime));
    }

    private static String formatMeal(String doyouoffermeals, String mealContent) {
        if (StringUtils.isBlank(doyouoffermeals) || StringUtils.isBlank(mealContent)) {
            return null;
        }
        if ("不含早".equals(doyouoffermeals)) {
            return null;
        }

        return String.format("%s(%s)", doyouoffermeals, mealContent);
    }

    private static List<DealSkuItemVO> getServiceFeeDesc(List<SkuAttrItemDto> attrItems) {
        String serviceFeeRatio = attrItems.stream().filter(attr -> "ServiceFeeRatio".equals(attr.getAttrName())).map(SkuAttrItemDto::getAttrValue).findFirst().orElse("");
        if (StringUtils.isBlank(serviceFeeRatio)) {
            return null;
        }
        return join(String.format("%s服务费", serviceFeeRatio));
    }

    private static List<DealSkuItemVO> join(String... arr) {
        return Arrays.stream(arr)
                .filter(StringUtils::isNotBlank)
                .map(BathSkuUtils::buildSkuItem)
                .collect(Collectors.toList());
    }

    private static DealSkuItemVO buildSkuItem(String value) {
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setValue(value);
        return dealSkuItemVO;
    }

    private static String formatApplicablePeople(String usePeopleNum) {
        if (StringUtils.isBlank(usePeopleNum)) {
            return null;
        }
        return String.format("适用%s", usePeopleNum);
    }

    private static String formatApplicableDuration(String applicableDuration) {
        if (StringUtils.isBlank(applicableDuration)) {
            return null;
        }
        return String.format("可用%s", applicableDuration);
    }

    private static String formatAdditionalService(String additionalService) {
        if (StringUtils.isBlank(additionalService)) {
            return null;
        }
        return String.format("额外%s", additionalService);
    }

    private static String formatFood(String foodContent) {
        if (StringUtils.isBlank(foodContent)) {
            return null;
        }
        return foodContent;
    }

    private static String formatStartAndEndTime(String startTime, String endTime) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return null;
        }
        return String.format("%s-%s可用", startTime, endTime);
    }

    private static String formatStartAndEndTime2(String startTime, String endTime) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return null;
        }
        return String.format("%s-%s内供应", startTime, endTime);
    }

    private static String formatCheckOutTime(String checkOutTime) {
        if (StringUtils.isBlank(checkOutTime)) {
            return null;
        }
        return String.format("%s前退房", checkOutTime);
    }

    private static String formatJoin(String a, String b) {
        if (StringUtils.isBlank(a) || StringUtils.isBlank(b)) {
            return null;
        }
        return String.format("%s%s", a, b);
    }
}
