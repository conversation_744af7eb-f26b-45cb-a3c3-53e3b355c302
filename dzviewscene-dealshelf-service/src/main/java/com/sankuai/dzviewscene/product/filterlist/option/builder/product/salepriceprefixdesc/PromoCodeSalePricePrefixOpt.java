package com.sankuai.dzviewscene.product.filterlist.option.builder.product.salepriceprefixdesc;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePricePrefixDescVP;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@VPointOption(name = "优惠码团单价格前缀前缀",
        description = "优惠码团单价格前缀处理",
        code = PromoCodeSalePricePrefixOpt.CODE)
public class PromoCodeSalePricePrefixOpt extends ProductSalePricePrefixDescVP<PromoCodeSalePricePrefixOpt.Config> {

    public static final String CODE = "PromoCodeSalePricePrefixOpt";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (param.getProductM() != null && param.getProductM().isTimesDeal()) {
            return config.getTimeDealPrefix();
        }
        return StringUtils.EMPTY;
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 团单多次卡前缀
         */
        private String timeDealPrefix = "单次";
    }
}
