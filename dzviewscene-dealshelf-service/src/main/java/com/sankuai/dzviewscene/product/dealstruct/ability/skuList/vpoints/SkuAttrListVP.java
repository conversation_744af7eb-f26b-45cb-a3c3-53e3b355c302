package com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.DealDetailSkuListsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/27 9:32 下午
 */

@VPoint(name = "团购详情sku属性列表变化点", description = "团购详情sku属性列表题变化点", code = SkuAttrListVP.CODE, ability = DealDetailSkuListsBuilder.CODE)
public abstract class SkuAttrListVP<T> extends PmfVPoint<List<DealSkuItemVO>, SkuAttrListVP.Param, T> {

    public static final String CODE = "SkuAttrListVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private SkuItemDto skuItemDto;
        private List<ProductSkuCategoryModel> productCategories;
        private List<AttrM> dealAttrs;
    }
}
