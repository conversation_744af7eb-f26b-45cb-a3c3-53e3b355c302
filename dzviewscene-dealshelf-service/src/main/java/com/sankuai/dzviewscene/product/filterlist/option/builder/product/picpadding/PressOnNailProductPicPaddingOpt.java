package com.sankuai.dzviewscene.product.filterlist.option.builder.product.picpadding;

import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPicPaddingVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzActivityTagVO;
import com.sankuai.dzviewscene.product.filterlist.vo.DzProductVO;
import com.sankuai.dzviewscene.productshelf.vu.enums.FloatTagPositionEnums;
import com.sankuai.dzviewscene.shelf.platform.common.model.DealProductMaterialM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Optional;

@VPointOption(name = "穿戴甲",
        description = "穿戴甲",
        code = "PressOnNailProductPicPaddingOpt")
public class PressOnNailProductPicPaddingOpt extends ProductPicPaddingVP<PressOnNailProductPicPaddingOpt.Config> {

    protected static final DzActivityTagVO SHOP_RECOMMEND_TOP_TAG = buildShopRecommendTag();

    private static DzActivityTagVO buildShopRecommendTag() {
        DzActivityTagVO floatTagPic = new DzActivityTagVO();
        floatTagPic.setImgUrl("https://p0.meituan.net/ingee/1ac37e05a687e899ecd5ad8622a390923657.png");
        floatTagPic.setPosition(FloatTagPositionEnums.LEFT_TOP.getPosition());
        return floatTagPic;
    }

    @Override
    public Void compute(ActivityCxt context, Param param, Config config) {
        ProductM productM = param.getProductM();
        String picUrl = productM.getPicUrl();
        boolean isShopRec = false;
        List<DealProductMaterialM> materialList = productM.getMaterialList();
        if (CollectionUtils.isNotEmpty(materialList)) {
            Optional<DealProductMaterialM> materialMOptional = materialList.stream().filter(t -> StringUtils.isNotBlank(t.getName())).findAny();
            if (materialMOptional.isPresent() && StringUtils.isNotBlank(materialMOptional.get().getPic())) {
                DealProductMaterialM materialM = materialMOptional.get();
                picUrl = materialM.getPic();
                isShopRec = materialM.getRecommended() == 1;
            }
        }
        if (StringUtils.isEmpty(picUrl)) {
            return null;
        }
        DzProductVO paddingProduct = param.getDzProductVO();
        paddingProduct.setPicScale(config.getHeaderPicAspectRadio());
        paddingProduct.setPic(PictureURLBuilders.toHttpsUrl(picUrl, config.getPicWidth(), config.getPicHeight(), PictureURLBuilders.ScaleType.Cut));
        //可选的塞入角标
        if (isShopRec) {
            paddingProduct.setActivityTags(Lists.newArrayList(SHOP_RECOMMEND_TOP_TAG));
        }
        return null;
    }

    @Data
    @VPointCfg
    public static class Config {

        private double headerPicAspectRadio = 1;

        private int picWidth = 300;

        private int picHeight = 300;
    }
}
