package com.sankuai.dzviewscene.product.dealstruct.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

@MobileDo(id = 0x4648)
public class StandardServiceContentVO implements Serializable {

    /**
     * 描述
     */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
     * 标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * icon
     */
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }
}
