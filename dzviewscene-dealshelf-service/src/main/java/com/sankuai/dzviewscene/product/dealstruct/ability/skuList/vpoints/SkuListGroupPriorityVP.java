package com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.DealDetailSkuListsBuilder;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/26 5:39 下午
 */

@VPoint(name = "团购详情sku列表组优先级序号变化点", description = "团购详情sku列表组顺优先级序号变化点，支持配置",code = SkuListGroupPriorityVP.CODE, ability = DealDetailSkuListsBuilder.CODE)
public abstract class SkuListGroupPriorityVP<T> extends PmfVPoint<Integer, SkuListGroupPriorityVP.Param, T> {

    public static final String CODE = "SkuListGroupPriorityVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private List<SkuItemDto> skuItems;
        private boolean isMustGroup;
        private int optionalCount;
        private List<ProductSkuCategoryModel> productCategories;
    }

}
