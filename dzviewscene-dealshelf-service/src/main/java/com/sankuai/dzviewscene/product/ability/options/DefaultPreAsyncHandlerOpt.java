package com.sankuai.dzviewscene.product.ability.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreAsyncHandlerVP;
import lombok.Data;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2023/7/27 3:49 下午
 */
@VPointOption(name = "默认async处理", description = "默认async处理", code = DefaultPreAsyncHandlerOpt.CODE, isDefault = true)
public class DefaultPreAsyncHandlerOpt extends PreAsyncHandlerVP<DefaultPreAsyncHandlerOpt.Config> {

    public static final String CODE = "DefaultPreAsyncHandlerOpt";

    @Override
    public CompletableFuture<Object> compute(ActivityCxt activityCxt, Param param, Config config) {
        return null;
    }

    @Data
    @VPointCfg
    public static class Config {
    }

}