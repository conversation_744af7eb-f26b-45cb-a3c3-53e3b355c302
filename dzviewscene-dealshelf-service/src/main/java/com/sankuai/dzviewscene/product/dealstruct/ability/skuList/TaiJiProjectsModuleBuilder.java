package com.sankuai.dzviewscene.product.dealstruct.ability.skuList;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.douhu.DealDetailDouhuFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.*;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealSkuGroupSequenceModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealSkuSequenceModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.*;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Ability(code = TaiJiProjectsModuleBuilder.CODE,
        name = "团购详情模块太极列表构造能力",
        description = "团购详情模块太极列表构造能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE,
                DealDetailDouhuFetcher.CODE
        }
)
public class TaiJiProjectsModuleBuilder extends PmfAbility<List<List<DealSkuGroupModuleVO>>, TaijiSkuListsParam, TaiJiListsCfg> implements ModuleStrategy {
    public static final String CODE = "taiJiProjectsModuleBuilder";
    private static final String ALL_CHOOSE = "以下全部可享";
    private static final String M_CHOOSE_N_FORMAT = "以下%d选%d";

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        List<List<DealSkuGroupModuleVO>> dealSkuGroupModuleVoLists = activityCxt.getSource(TaiJiProjectsModuleBuilder.CODE);
        List<DealSkuGroupModuleVO> dealSkuGroupModuleVOS = CollectUtils.firstValue(dealSkuGroupModuleVoLists);
        if (CollectionUtils.isEmpty(dealSkuGroupModuleVOS)) {
            return null;
        }

        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        DealDetailInfoModel dealDetailInfoModel = CollectUtils.firstValue(dealDetailInfoModels);
        if(StringUtils.isEmpty(dealSkuGroupModuleVOS.get(0).getTitle())){
            String mChooseNStr = getMChooseNStr(dealDetailInfoModel);
            dealSkuGroupModuleVOS.get(0).setTitle(mChooseNStr);
        }
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        dealDetailModuleVO.setSkuGroupsModel1(dealSkuGroupModuleVOS);

        // 获取团购次卡标题
        if (TimesDealUtil.isTimesDeal(dealDetailInfoModel)) {
            dealDetailModuleVO.setName(TimesDealUtil.getTimesTitle(dealDetailInfoModel.getDealAttrs()));
        }

        return dealDetailModuleVO;
    }

    private String getMChooseNStr(DealDetailInfoModel dealDetailInfoModel) {
        if(dealDetailInfoModel == null){
            return null;
        }
        StandardServiceProjectDTO standardServiceProjectDTO = dealDetailInfoModel.getStandardServiceProjectDTO();
        if (CollectionUtils.isNotEmpty(standardServiceProjectDTO.getMustGroups())){
            return "以下全部可享";
        }
        if (CollectionUtils.isEmpty(standardServiceProjectDTO.getOptionalGroups())) {
            return null;
        }

        StandardServiceProjectGroupDTO standardServiceProjectGroupDTO = standardServiceProjectDTO.getOptionalGroups().get(0);
        int total = standardServiceProjectGroupDTO.getServiceProjectItems().size();
        if(total <= 0){
            return null;
        }
        int select = standardServiceProjectGroupDTO.getOptionalCount();
        if(select < 0){
            return null;
        }
        if(total == select){
            return "以下全部可享";
        }else if(select < total){
            return String.format("以下%d选%d", total, select);
        }
        return null;
    }

    @Override
    public CompletableFuture<List<List<DealSkuGroupModuleVO>>> build(ActivityCxt activityCxt, TaijiSkuListsParam taijiSkuListsParam, TaiJiListsCfg taiJiListsCfg) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        List<DouhuResultModel> douhuResultModels = activityCxt.getSource(DealDetailDouhuFetcher.CODE);
        if (CollectionUtils.isEmpty(dealDetailInfoModels)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        List<List<DealSkuGroupModuleVO>> collect = dealDetailInfoModels.stream().map(detailModel -> {
            //有效性校验
            if (!isValidDealDetail(detailModel)) {
                return null;
            }
            StandardServiceProjectDTO standardServiceProjectDTO = detailModel.getStandardServiceProjectDTO();
            if (standardServiceProjectDTO == null) {
                return null;
            }
            return buildSkuUniModel(activityCxt,standardServiceProjectDTO, detailModel.getProductCategories(), detailModel.getDealAttrs(), douhuResultModels, taiJiListsCfg);
        }).filter(model -> model != null).collect(Collectors.toList());
        return CompletableFuture.completedFuture(collect);
    }

    private List<DealSkuGroupModuleVO> buildSkuUniModel(ActivityCxt activityCxt, StandardServiceProjectDTO standardServiceProjectDTO, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs, List<DouhuResultModel> douhuResultModels, TaiJiListsCfg taiJiListsCfg) {
        if (standardServiceProjectDTO == null) {
            return null;
        }
        List<DealSkuGroupSequenceModel> dealSkuGroupSequenceModels = new ArrayList<>();
        //通过全部可享sku列表构造sku列表组
        List<StandardServiceProjectGroupDTO> mustGroups = standardServiceProjectDTO.getMustGroups();
        if (CollectionUtils.isNotEmpty(mustGroups)) {
            List<DealSkuGroupSequenceModel> mustItems = buildMustSkuListGroups(activityCxt, mustGroups, productCategories, dealAttrs, douhuResultModels, taiJiListsCfg);
            dealSkuGroupSequenceModels.addAll(mustItems);
        }
        //通过部分可享sku列表构造sku列表组
        List<StandardServiceProjectGroupDTO> optionalGroups = standardServiceProjectDTO.getOptionalGroups();
        if (CollectionUtils.isNotEmpty(optionalGroups)) {
            List<DealSkuGroupSequenceModel> optionalItems = buildOptionalSkuListGroups(activityCxt, optionalGroups, productCategories, dealAttrs, douhuResultModels, taiJiListsCfg);
            dealSkuGroupSequenceModels.addAll(optionalItems);

        }
        if (CollectionUtils.isEmpty(dealSkuGroupSequenceModels)) {
            return null;
        }
        return dealSkuGroupSequenceModels.stream()
                .filter(model -> model != null) //滤空
                .map(model -> model.getDealSkuGroupModuleVO()) // 提取
                .collect(Collectors.toList());
    }

    private List<DealSkuGroupSequenceModel> buildMustSkuListGroups(ActivityCxt activityCxt, List<StandardServiceProjectGroupDTO> mustTaiJiGroups, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs, List<DouhuResultModel> douhuResultModels, TaiJiListsCfg taiJiListsCfg) {
        if (CollectionUtils.isEmpty(mustTaiJiGroups)) {
            return Lists.newArrayList();
        }
        return mustTaiJiGroups.stream()
                .map(mustSkuGroup -> buildSkuGroup(activityCxt, mustSkuGroup.getServiceProjectItems(), true, CollectionUtils.size(mustSkuGroup.getServiceProjectItems()), productCategories, dealAttrs, douhuResultModels, taiJiListsCfg))
                .filter(model -> model != null).collect(Collectors.toList());
    }

    private List<DealSkuGroupSequenceModel> buildOptionalSkuListGroups(ActivityCxt activityCxt, List<StandardServiceProjectGroupDTO> optionalTaiJiGroups, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs, List<DouhuResultModel> douhuResultModels, TaiJiListsCfg taiJiListsCfg) {
        if (CollectionUtils.isEmpty(optionalTaiJiGroups)) {
            return Lists.newArrayList();
        }
        return optionalTaiJiGroups.stream()
                .map(optionalSkuGroup -> buildSkuGroup(activityCxt, optionalSkuGroup.getServiceProjectItems(), false, optionalSkuGroup.getOptionalCount(), productCategories, dealAttrs, douhuResultModels, taiJiListsCfg))
                .filter(model -> model != null).collect(Collectors.toList());
    }

    private DealSkuGroupSequenceModel buildSkuGroup(ActivityCxt activityCxt, List<StandardServiceProjectItemDTO> taiJiProjectItems, boolean isMustGroup, int optionalNum, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs, List<DouhuResultModel> douhuResultModels, TaiJiListsCfg taiJiListsCfg) {
        if (CollectionUtils.isEmpty(taiJiProjectItems)) {
            return null;
        }
        DealSkuGroupSequenceModel dealSkuGroupSequenceModel = new DealSkuGroupSequenceModel();

        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        //构造sku列表
        List<DealSkuVO> taijiVOList = taiJiProjectItems.stream()
                .map(taijiProjectItem -> buildDealSkuSequenceModel(activityCxt, taijiProjectItem, productCategories, dealAttrs)) //构造
                .filter(model -> model != null) //滤空
                .sorted(Comparator.comparingInt(DealSkuSequenceModel::getSequenceIndex)) //排序
                .map(model -> model.getSkuVO()) //提取
                .collect(Collectors.toList());
        setDealSkuGroupModuleVOTitle(dealSkuGroupModuleVO, isMustGroup, optionalNum, taiJiProjectItems.size(), taiJiListsCfg.isSupportMustAndOptionSwitch());

        dealSkuGroupModuleVO.setDealSkuList(taijiVOList);
        dealSkuGroupSequenceModel.setDealSkuGroupModuleVO(dealSkuGroupModuleVO);
        return dealSkuGroupSequenceModel;
    }

    private void setDealSkuGroupModuleVOTitle(DealSkuGroupModuleVO dealSkuGroupModuleVO, boolean isMustGroup, int optionalNum, int optionalSize, boolean supportMustAndOptionSwitch) {
        // 支持 同时显示必须和可选的标题 开关
        if(!supportMustAndOptionSwitch) {
            return;
        }
        if(isMustGroup) {
            dealSkuGroupModuleVO.setTitle(ALL_CHOOSE);
        } else{
            dealSkuGroupModuleVO.setTitle(String.format(M_CHOOSE_N_FORMAT, optionalSize, optionalNum));
        }
    }

    private DealSkuSequenceModel buildDealSkuSequenceModel(ActivityCxt activityCxt, StandardServiceProjectItemDTO taiJiProjectItem, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs) {
        DealSkuSequenceModel dealSkuSequenceModel = new DealSkuSequenceModel();
        //构造DealSkuVO
        DealSkuVO dealSkuVO = buildDealSkuVO(activityCxt, taiJiProjectItem, productCategories, dealAttrs);
        if (dealSkuVO == null) {
            return null;
        }
        dealSkuSequenceModel.setSkuVO(dealSkuVO);
        return dealSkuSequenceModel;
    }

    private DealSkuVO buildDealSkuVO(ActivityCxt activityCxt, StandardServiceProjectItemDTO taiJiProjectItem, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs) {
        if (taiJiProjectItem == null) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        //名称
        TaiJiProjectTitleVP<?> taiJiProjectTitleVP = findVPoint(activityCxt, TaiJiProjectTitleVP.CODE);
        String skuTitle = taiJiProjectTitleVP.execute(activityCxt, TaiJiProjectTitleVP.Param.builder().taiJiProjectItem(taiJiProjectItem).dealAttrs(dealAttrs).build());
        dealSkuVO.setTitle(skuTitle);
        //属性列表
        TaiJiProjectListVP<?> taiJiProjectListVP = findVPoint(activityCxt, TaiJiProjectListVP.CODE);
        List<DealSkuItemVO> skuAttrList = taiJiProjectListVP.execute(activityCxt, TaiJiProjectListVP.Param.builder().taiJiProjectItem(taiJiProjectItem).dealAttrs(dealAttrs).build());
        dealSkuVO.setItems(skuAttrList);
        return dealSkuVO;
    }

    private boolean isValidDealDetail(DealDetailInfoModel detailModel) {
        if(detailModel == null || detailModel.getDealDetailDtoModel() == null) {
            return false;
        }
        return true;
    }
}
