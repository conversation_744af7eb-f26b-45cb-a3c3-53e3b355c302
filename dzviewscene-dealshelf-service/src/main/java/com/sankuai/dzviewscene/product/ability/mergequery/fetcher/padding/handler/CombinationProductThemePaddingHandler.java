package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.CombinationProductPaddingHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 组合产品填充处理器
 */
@Component
public class CombinationProductThemePaddingHandler implements PaddingHandler {

    @Resource
    private CombinationProductPaddingHandler combinationProductPaddingHandler;

    @Override
    public CompletableFuture<ProductGroupM> padding(ActivityCxt ctx, ProductGroupM productGroupM, Map<String, Object> params) {
        return combinationProductPaddingHandler.padding(ActivityCtxtUtils.toActivityContext(ctx), productGroupM, params);
    }

}
