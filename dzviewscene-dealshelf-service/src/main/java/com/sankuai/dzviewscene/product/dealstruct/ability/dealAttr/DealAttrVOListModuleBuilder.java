package com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.IVPoint;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.ability.extCtx.PreHandlerContextAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.douhu.DealDetailDouhuFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/20 4:30 下午
 */
@Ability(code = DealAttrVOListModuleBuilder.CODE,
        name = "团购详情团单属性VO列表模块构造能力",
        description = "团购详情团单属性VO列表模块构造能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE,
                DealDetailDouhuFetcher.CODE,
                PreHandlerContextAbility.CODE
        }
)
public class DealAttrVOListModuleBuilder extends PmfAbility<List<DealDetailStructAttrModuleGroupModel>, DealDetailStructAttrListParam, DealDetailStructAttrListCfg> implements ModuleStrategy {

    public static final String CODE = "dealAttrVOListModuleBuilder";

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        List<DealDetailStructAttrModuleGroupModel> dealDetailStructAttrModuleGroupModels = activityCxt.getSource(DealAttrVOListModuleBuilder.CODE);
        if (CollectionUtils.isEmpty(dealDetailStructAttrModuleGroupModels) || StringUtils.isEmpty(config)) {
            return null;
        }
        DealDetailStructAttrModuleGroupModel dealDetailStructAttrModuleGroupModel = dealDetailStructAttrModuleGroupModels.stream().filter(group -> config.equals(group.getGroupName()))
                .sorted(Comparator.comparing(DealDetailStructAttrModuleGroupModel::getOrder,Comparator.reverseOrder()))
                .findFirst().orElse(null);
        return buildDealDetailModuleVO(dealDetailStructAttrModuleGroupModel, false);
    }

    private DealDetailModuleVO buildDealDetailModuleVO(DealDetailStructAttrModuleGroupModel groupModel, boolean isNestedLayer) {
        if (groupModel == null || (CollectionUtils.isEmpty(groupModel.getDealDetailStructAttrModuleVOS())
                                    && CollectionUtils.isEmpty(groupModel.getDealDetailModuleList2())
                                    && Objects.isNull(groupModel.getVideoModuleVO()))) {
            return null;
        }
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        dealDetailModuleVO.setDealStructAttrsModel1(groupModel.getDealDetailStructAttrModuleVOS());
        dealDetailModuleVO.setSubTitle(groupModel.getGroupSubtitle());
        dealDetailModuleVO.setDotType(groupModel.getDotType());
        dealDetailModuleVO.setShowNum(groupModel.getShowNum());
        dealDetailModuleVO.setFoldStr(groupModel.getFoldStr());
        dealDetailModuleVO.setJumpUrl(groupModel.getJumpUrl());
        // 处理嵌套层
        if (groupModel.getDealDetailModuleList2() != null) {
            dealDetailModuleVO.setDealDetailModuleList2(groupModel.getDealDetailModuleList2().stream().map(model -> buildDealDetailModuleVO(model, true)).collect(Collectors.toList()));
        }
        // 如果为嵌套层，直接设置模块名称
        if (isNestedLayer) {
            dealDetailModuleVO.setName(groupModel.getGroupName());
        }
        // 增加标题附加字段
        dealDetailModuleVO.setSubTitleItems(groupModel.getSubTitleItems());
        // 设置模块内嵌视频/图文组件
        if (groupModel.getVideoModuleVO() != null) {
            dealDetailModuleVO.setVideoModuleVO(groupModel.getVideoModuleVO());
        }
        return dealDetailModuleVO;
    }

    @Override
    public CompletableFuture<List<DealDetailStructAttrModuleGroupModel>> build(ActivityCxt ctx, DealDetailStructAttrListParam dealDetailStructAttrListParam, DealDetailStructAttrListCfg dealDetailStructAttrListCfg) {
        List<DealDetailInfoModel> dealDetailInfoModels = ctx.getSource(DealDetailFetcher.CODE);
        DealDetailInfoModel dealDetailInfoModel = CollectUtils.firstValue(dealDetailInfoModels);
        if (dealDetailInfoModel == null) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        // 添加是否为太极团单标识
        addUnifyProductFlagParam(ctx, dealDetailInfoModel);
        List<DealDetailStructAttrModuleGroupModel> dealDetailStructAttrModuleGroupModels = getMultiDealDetailStructAttrModuleVOList(ctx, dealDetailInfoModel);
        return CompletableFuture.completedFuture(dealDetailStructAttrModuleGroupModels);
    }

    private List<DealDetailStructAttrModuleGroupModel> getMultiDealDetailStructAttrModuleVOList(ActivityCxt ctx, DealDetailInfoModel dealDetailInfo) {
        if (dealDetailInfo == null) {
            return Lists.newArrayList();
        }
        //1.获取数据源
        //1.1 团单属性
        List<AttrM> dealAttrs = dealDetailInfo.getDealAttrs();
        //1.2 货信息
        DealDetailDtoModel dealDetailDtoModel = dealDetailInfo.getDealDetailDtoModel();
        //1.3 货类别
        List<ProductSkuCategoryModel> productCategories = dealDetailInfo.getProductCategories();
        //2.构造VO列表
        List<IVPoint> dealAttrVOListVPS = findVPoints(ctx, DealAttrVOListVP.CODE);
        if (CollectionUtils.isEmpty(dealAttrVOListVPS)) {
            return null;
        }
        List<DealDetailStructAttrModuleGroupModel> allDealDetailStructAttrModuleGroupModels = new ArrayList<>();
        for(IVPoint ivPoint : dealAttrVOListVPS) {
            DealAttrVOListVP<?> dealAttrVOListVP = (DealAttrVOListVP) ivPoint;
            if (dealAttrVOListVP == null) {
                continue;
            }
            List<DealDetailStructAttrModuleGroupModel> dealDetailStructAttrModuleGroupModels = dealAttrVOListVP
                    .execute(ctx, DealAttrVOListVP.Param.builder()
                            .dealDetailInfoModel(dealDetailInfo)
                            .dealAttrs(dealAttrs)
                            .dealDetailDtoModel(dealDetailDtoModel)
                            .productCategories(productCategories)
                            .build());
            if (CollectionUtils.isEmpty(dealDetailStructAttrModuleGroupModels)) {
                continue;
            }
            allDealDetailStructAttrModuleGroupModels.addAll(dealDetailStructAttrModuleGroupModels);
        }
        return allDealDetailStructAttrModuleGroupModels;
    }

    private void addUnifyProductFlagParam(ActivityCxt ctx, DealDetailInfoModel dealDetailInfoModel) {
        ctx.addParam(ProductDetailActivityConstants.Params.isUnifyProduct, dealDetailInfoModel.isUnifyProduct());
    }
}
