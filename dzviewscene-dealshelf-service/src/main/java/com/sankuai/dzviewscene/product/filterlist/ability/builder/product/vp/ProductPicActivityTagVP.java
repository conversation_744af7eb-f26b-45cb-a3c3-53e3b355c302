package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.product.filterlist.vo.DzActivityTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @description : 图片活动标签，
 * @date : 2025/4/17
 */
@VPoint(name = "填充图片活动标签", description = "图片活动标签，例如：买过标签", code = ProductPicActivityTagVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductPicActivityTagVP<T> extends PmfVPoint<List<DzActivityTagVO>, ProductPicActivityTagVP.Param, T> {
    public static final String CODE = "ProductPicActivityTagVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;
        private int platform;
    }
}
