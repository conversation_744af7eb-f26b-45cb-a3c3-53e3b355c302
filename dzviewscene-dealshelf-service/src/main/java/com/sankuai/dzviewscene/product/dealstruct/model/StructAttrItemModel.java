package com.sankuai.dzviewscene.product.dealstruct.model;

import lombok.Data;

import java.util.List;

/**
 * created by zhangzhiyuan04 in 2021/12/9
 */
@Data
public class StructAttrItemModel {

    /**
     * 属性值
     */
    private List<String> attrValues;

    /**
     * 属性图标url
     */
    private String icon;

    /**
     * 属性名
     */
    private String attrName;

    /**
     * 关联的主属性名：会根据这个名称，去填充到对应的 attr 的 desc
     * 说明：为什么要加这个属性？
     * 原因：
     * 1、属性变化点只做属性的翻译，页面展示元素的配置放到更上一层的聚合点
     * 2、为什么不直接在当前 model 下加一个 List desc，也就是把展示模型的东西 copy 下来？当前属性加工能力不强，主属性和解释属性的翻译做在一起会相互影响，不如直接分成两个。
     * 留个坑：上层对应的展示模型 {DealDetailAttrDescVO} 是个复杂模型，attrValue 是塞不下的，当前只有一个 title 硬编码即可，后续建议 List<String> attrValues 改成 K-V
     */
    private String relateMainAttrName;
    
    /**
     * 优先级
     */
    private int priority;
}
