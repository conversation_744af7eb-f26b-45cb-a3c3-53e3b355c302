package com.sankuai.dzviewscene.product.filterlist.option.factory.impl;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.filterlist.option.factory.AbstractMassageStrategy;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: created by hang.yu on 2024/4/9 16:20
 */
@Component
public class HeadStrategyImpl extends AbstractMassageStrategy {

    @Override
    public String getFilterListTitle(SkuItemDto skuItemDto, String serviceType) {
        String serviceDuration = getServiceDuration(skuItemDto);
        String serviceBodyRange = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, SERVICE_BODY_RANGE);
        String serviceTechnique = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, SERVICE_TECHNIQUE);
        return String.format("%s%s%s", serviceDuration, serviceBodyRange, serviceTechnique);
    }

    @Override
    public List<Long> getProductCategorys() {
        return Lists.newArrayList(ProductCategoryEnum.HEAD.getProductCategoryId());
    }
}
