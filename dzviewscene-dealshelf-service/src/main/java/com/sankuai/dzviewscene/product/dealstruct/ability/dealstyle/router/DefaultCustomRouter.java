package com.sankuai.dzviewscene.product.dealstruct.ability.dealstyle.router;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealstyle.DealStyleSwitchCfg;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.detailSwitch.SwitchModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.detailSwitch.SwitchModuleModel;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/5/27 3:57 PM
 * 默认使用返回custom使得dealbase完了调用dealmodoule
 */
@Component
public class DefaultCustomRouter implements DealCategoryRouter {

    @Override
    public boolean identify(DealStyleSwitchCfg cfg, int dealCategory) {
        return cfg.isReturnCustom();
    }

    @Override
    public SwitchModel compute(ActivityCxt context, Param param, DealStyleSwitchCfg config) {
        SwitchModuleModel moduleModel = new SwitchModuleModel();
        moduleModel.setModuleValue(CUSTOM_STRUCTURE_MODULE);
        moduleModel.setModuleKey(param.getModuleKey());

        SwitchModel switchModel = new SwitchModel();
        switchModel.setModules(Lists.newArrayList(moduleModel));
        return switchModel;
    }

}
