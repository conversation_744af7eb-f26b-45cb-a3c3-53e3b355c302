package com.sankuai.dzviewscene.product.dealstruct.ability.price.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.price.DealDetailPriceBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailPriceModel;
import lombok.Builder;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2021/11/25 2:41 下午
 */
@VPoint(name = "团购详情价格组件变化点", description = "团购详情价格组变化点件，支持组件名称和单位配置",code = DealDetailPriceVP.CODE, ability = DealDetailPriceBuilder.CODE)
public abstract class DealDetailPriceVP<T> extends PmfVPoint<DealDetailPriceModel, DealDetailPriceVP.Param, T> {

    public static final String CODE = "DealDetailPriceVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private String salePrice;
        private String marketPrice;
        private int dealId;
        /*
        * 0元预约展示价格：1:正常展示，2:隐藏，3:展示起
        * */
        private String retailPriceStyle;
    }
}


