package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/18 5:32 下午
 */
@VPoint(name = "商品列表页-商品列表后置处理变化点", description = "根据外部配置进行加工变换", code = ProductListVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductListVP<T> extends PmfVPoint<List<ProductM>, ProductListVP.Param, T> {

    public static final String CODE = "ProductListVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private List<ProductM> productMS;
        private List<DouHuM> douHuMS;
    }
}
