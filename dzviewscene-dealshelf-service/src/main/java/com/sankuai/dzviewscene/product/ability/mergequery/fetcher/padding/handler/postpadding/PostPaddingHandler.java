package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.postpadding;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

public interface PostPaddingHandler {
    /**
     * 填充一组商品
     *
     * @param ctx
     * @param productGroupM
     * @param params
     * @return
     */
    CompletableFuture<ProductGroupM> postPadding(ActivityCxt ctx, ProductGroupM productGroupM, Map<String, Object> params);
}
