package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.showtype;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListShowTypeVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/4/11 19:24
 */
@VPointOption(name = "根据斗斛生成showType",
        description = "根据斗斛生成showType",
        code = "ShowTypeByDouhuOpt")
public class ShowTypeByDouhuOpt extends DealFilterListShowTypeVP<ShowTypeByDouhuOpt.Config> {

    @Override
    public Integer compute(ActivityCxt context, Param param, Config config) {
        return getShowTypeByDouHu(param.getDouHuList(), config);
    }

    private Integer getShowTypeByDouHu(List<DouHuM> douHuList, Config config) {
        return douHuList.stream()
                .filter(douHuM -> config.douHuSk2ShowType.containsKey(douHuM.getSk()))
                .findFirst()
                .map(douHuM -> config.douHuSk2ShowType.get(douHuM.getSk()))
                .orElse(getDefaultShowType());
    }

    private Integer getDefaultShowType() {
        return null;
    }


    @Data
    @VPointCfg
    public static class Config {
        private Map<String, Integer> douHuSk2ShowType;
    }
}
