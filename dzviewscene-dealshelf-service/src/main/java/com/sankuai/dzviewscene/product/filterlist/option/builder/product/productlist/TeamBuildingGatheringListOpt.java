package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.alibaba.fastjson.JSON;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.apache.commons.collections4.CollectionUtils;


import java.util.List;
import java.util.stream.Collectors;

@VPointOption(name = "休闲娱乐-团建/聚会过滤与排序",
        description = "休闲娱乐-团建/聚会过滤与排序",
        code = "TeamBuildingGatheringListOpt")
@Slf4j
public class TeamBuildingGatheringListOpt extends ProductListVP<TeamBuildingGatheringListOpt.Config> {

    private static final int categoryId = 324;
    private static final String TEAM_BUILDING_GATHERING = "团建/聚会";
    private static final String SERVICE_TYPE = "service_type";
    private static final String minPeople = "minParticipantsCheck";
    private static final String maxPeople = "maxParticipantsCheck";


    @Override
    public List<ProductM> compute(ActivityCxt context, Param param, @NotNull Config config) {
        if (!config.isEnable()) {
            return Lists.newArrayList();
        }
        List<ProductM> productMS = param.getProductMS();
        if (CollectionUtils.isEmpty(productMS)) {
            return Lists.newArrayList();
        }

        // 获取当前团单信息
        ProductM currentProduct = productMS.stream()
                .filter(productM -> productM.getProductId() == ParamsUtil.getIntSafely(context, PmfConstants.Params.entityId))
                .findFirst()
                .orElse(productMS.get(0));

        String currentServiceType = currentProduct.getAttr(SERVICE_TYPE);
        if (!TEAM_BUILDING_GATHERING.equals(currentServiceType)) {
            return Lists.newArrayList();
        }

        // 过滤出团建/聚会团单
        List<ProductM> result = productMS.stream()
                .filter(item -> item.getCategoryId() == categoryId
                        && StringUtils.isNotEmpty(item.getAttr(SERVICE_TYPE))
                        && TEAM_BUILDING_GATHERING.equals(item.getAttr(SERVICE_TYPE)))
                .map(item -> {
                    // 拼接标题
                    String title = splicingTitles(item, config);
                    if (StringUtils.isNotEmpty(title)) {
                        item.setTitle(title);
                    }
                    return item;
                })
                .filter(item -> StringUtils.isNotEmpty(item.getTitle()))
                .collect(Collectors.toList());


        //重新排序将当前团单放置到首位
        moveToFirst(result, currentProduct);
        // 超过2个团单聚合展示，否则展示原先样式
        return result.size() >= 2 ? result : Lists.newArrayList();
    }



    /**
     * 将指定元素移动到列表的第一个位置
     *
     * @param list
     * @param element
     * @param <T>
     */
    public static <T> void moveToFirst(List<T> list, T element) {
        int index = list.indexOf(element);
        if (index > 0) {
            list.remove(index);
            list.add(0, element);
        }
    }


    /**
     * 拼接标题-> 根据配置的人数拼接标题->人数+团建套餐
     * 示例 （2-10人团建套餐） 未配置人数则不拼接
     *
     * @param productM
     * @return
     */
    public static String splicingTitles(ProductM productM, Config config) {
        try {
            StringBuilder sb = new StringBuilder();
            String minParticipantsStr = productM.getAttr(minPeople);
            String maxParticipantsStr = productM.getAttr(maxPeople);
            if (StringUtils.isBlank(minParticipantsStr) || StringUtils.isBlank(maxParticipantsStr)) {
                return null;
            }
            int minNum = Integer.parseInt(minParticipantsStr);
            int maxNum = Integer.parseInt(maxParticipantsStr);

            if (minNum == maxNum && minNum != 0) {
                sb.append(minNum);
            } else if (minNum != 0 && maxNum != 0 && minNum < maxNum) {
                sb.append(minNum).append("-").append(maxNum);
            }
            sb.append(config.getUnit());
            sb.append(config.getSuffix());
            return sb.toString();
        } catch (NumberFormatException e) {
            log.error("Invalid number format in SplicingTitles, request: {}", JSON.toJSONString(productM), e);
        } catch (Exception e) {
            log.error("TeamBuildingGatheringListOpt.SplicingTitles error, request: {}", JSON.toJSONString(productM), e);
        }
        return null;
    }


    @VPointCfg
    @Data
    public static class Config {
        public boolean enable;
        public String unit;
        public String suffix;
    }

}
