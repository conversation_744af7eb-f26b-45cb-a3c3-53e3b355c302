package com.sankuai.dzviewscene.product.dealstruct.ability.dealstyle.router;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealstyle.DealStyleSwitchCfg;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.detailSwitch.SwitchModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.detailSwitch.SwitchModuleDouhuConfigModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.detailSwitch.SwitchModuleModel;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/19 5:47 下午
 */
public interface DealCategoryRouter {

    String CUSTOM_STRUCTURE_MODULE = "custom_structured_module";

    String HTML = "html";

    boolean identify(DealStyleSwitchCfg cfg, int dealCategory);

    SwitchModel compute(ActivityCxt context, DealCategoryRouter.Param param, DealStyleSwitchCfg config);

    default SwitchModel buildSwitchModel(SwitchModuleModel modules, SwitchModuleDouhuConfigModel moduleAbConfigs) {
        SwitchModel switchModel = new SwitchModel();
        if(modules != null) {
            switchModel.setModules(Lists.newArrayList(modules));
        }
        if(moduleAbConfigs != null) {
            switchModel.setModuleAbConfigs(Lists.newArrayList(moduleAbConfigs));
        }
        return switchModel;
    }

    @Data
    @Builder
    @VPointParam
    class Param {
        private int dealCategoryId;
        private int platform;
        private String moduleKey;
        private ActivityCxt ctx;
        private List<DouhuResultModel> douHuResultModels;
        private DealDetailInfoModel dealDetailInfoModel;
        private String mpSource;
    }
}
