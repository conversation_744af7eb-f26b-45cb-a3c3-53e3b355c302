package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.desc.DealDetailDescBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDescModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * created by z<PERSON><PERSON><PERSON>04 in 2021/12/14
 * 描述模块构建策略
 */
@Component("desc")
public class DescStrategy implements ModuleStrategy {


    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        String abilityCode = DealDetailDescBuilder.CODE;
        List<DealDetailDescModel> descModelList = activityCxt.getSource(abilityCode);
        if (CollectionUtils.isEmpty(descModelList)) {
            return null;
        }

        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        DealDetailDescModel dealDetailDescModel = CollectUtils.firstValue(descModelList);
        dealDetailModuleVO.setDescModel(dealDetailDescModel.getDesc());
        return dealDetailModuleVO;
    }

}
