package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dp.arts.utils.recycler.Recycler;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.SkuAttrItemsVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.BeautyCosmetologySkuAttrListOpt;
import com.sankuai.dzviewscene.product.dealstruct.vo.CommonAttrVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.SetUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.dzviewscene.product.dealstruct.ability.utils.Constants.DEFAULT_STEP_TYPE;

/**
 * <AUTHOR>
 * @date 2021/11/27 10:16 下午
 */
@VPointOption(name = "丽人-美妆团购详情货attr列表默认变化点", description = "丽人-美妆团购详情货attr列表默认变化点，支持配置", code = BeautyCosmeticsSkuAttrItemsVPO.CODE, isDefault = false)
public class BeautyCosmeticsSkuAttrItemsVPO extends SkuAttrItemsVP<BeautyCosmeticsSkuAttrItemsVPO.Config> {

    public static final String CODE = "BeautyCosmeticsSkuAttrItemsVPO";

    private static final String PROJECT = "产品";

    private static final String EQUIPMENT = "仪器";

    private static final String DESC = "说明";

    private static final String SUITABLE_PART = "部位";

    private static final String STEP_TIME_FORMAT = "%s分钟";

    private static final String TIME_SKU_CHN_NAMW = "时长";

    private static final String TIME_SKU_SHOW_NAMW = "参考时长";

    private static final String STEP_SKU_CHN_NAMW = "服务步骤";

    private static final String STEP_SKU_SHOW_NAMW = "服务流程";

    private static final String STEP_SKU_NUM_FORMAT = "共%s个步骤";

    private static final String ADDITIONAL_SKU_CHN_NAMW = "项目附赠";

    private static final String ADDITIONAL_SKU_SHOW_NAMW = "项目附赠";

    static final String SKIN_DETECT_STEP_NAME = "皮肤检测";

    static final String SKIN_DETECT_STEP_FORMAT = "%s（可选步骤）";

    final String ZERO_STEP_TIME_SHOW_DOC = "不计入总时长";

    private static final String SERVICE_EFFECT = "服务功效";

    private static final String APPLICATION_SCOPE = "适用范围";

    @Override
    public List<DealSkuItemVO> compute(ActivityCxt context, Param param, Config config) {
        SkuItemDto skuItemDto = param.getSkuItemDto();
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return null;
        }
        List<SkuDisplayModel> skuDisplayModels = getSkuDisplayModels(config, skuItemDto.getProductCategory());

        List<SkuAttrItemDto> skuAttrItemDtos = skuItemDto.getAttrItems();
        List<DealSkuItemVO> dealSkuItemVOS = new ArrayList<>();
        //服务功效
        String serviceEffect = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItemDtos, "serviceeffect");
        if (StringUtils.isNotEmpty(serviceEffect)) {
            dealSkuItemVOS.add(buildDealSkuItemVO(SERVICE_EFFECT, serviceEffect, null, 0));
        }
        //适用范围
        String applicationScope = getSuitableSkinQualityAttrValue(skuDisplayModels, skuAttrItemDtos);
        if (StringUtils.isNotEmpty(applicationScope)) {
            applicationScope = applicationScope.trim();
            dealSkuItemVOS.add(buildDealSkuItemVO(APPLICATION_SCOPE, applicationScope, null, 0));
        }
        //服务时长
        String serviceTime = DealDetailUtils.getSkuAttrValueBySkuAttrChnName(skuAttrItemDtos, TIME_SKU_CHN_NAMW);
        if (StringUtils.isNotEmpty(serviceTime)) {
            dealSkuItemVOS.add(buildDealSkuItemVO(TIME_SKU_SHOW_NAMW, serviceTime, null, 0));
        }
        //服务步骤
        boolean isStandardProduct = isStandardProduct(param.getDealAttrs(), config);
        String serviceProcess = DealDetailUtils.getSkuAttrValueBySkuAttrChnName(skuAttrItemDtos, STEP_SKU_CHN_NAMW);
        if (StringUtils.isNotEmpty(serviceProcess)) {
            List<SkuAttrAttrItemVO> valueAttrs = getSkuAttrAttrItemVOs(serviceProcess, isStandardProduct);
            if (CollectionUtils.isNotEmpty(valueAttrs)) {
                int stepType = config.getStepType() > 0 ? config.getStepType() : DEFAULT_STEP_TYPE;
                dealSkuItemVOS.add(buildDealSkuItemVO(STEP_SKU_SHOW_NAMW, buildFormatStemNum(STEP_SKU_NUM_FORMAT, valueAttrs.size(), config), null, 0));
                dealSkuItemVOS.add(buildDealSkuItemVO(StringUtils.EMPTY, null, valueAttrs, stepType));
            }
        }
        //项目附赠
        String additionalProgram = DealDetailUtils.getSkuAttrValueBySkuAttrChnName(skuAttrItemDtos, ADDITIONAL_SKU_CHN_NAMW);
        if (StringUtils.isNotEmpty(additionalProgram)) {
            dealSkuItemVOS.add(buildDealSkuItemVO(ADDITIONAL_SKU_SHOW_NAMW, additionalProgram, null, 0));
        }

        if (CollectionUtils.isEmpty(dealSkuItemVOS)) {
            return null;
        }
        return dealSkuItemVOS;
    }

    private List<SkuDisplayModel> getSkuDisplayModels(Config config, long productCategory){
        if (Objects.nonNull(config) && MapUtils.isNotEmpty(config.getCategory2SkuDisplayModelList())){
           return config.getCategory2SkuDisplayModelList().get(productCategory);
        }
        return null;
    }

    private String getSuitableSkinQualityAttrValue(List<SkuDisplayModel> skuDisplayModels, List<SkuAttrItemDto> skuAttrItems) {
        String suitableSkinQuality = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "applySkin");
        String suitableBodyParts = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "bodyname");
        if (StringUtils.equals(suitableSkinQuality, "适用所有肤质")) {
            String attrValue = getAttrValueByConfig(skuDisplayModels, "applySkin", skuAttrItems);
            return StringUtils.isEmpty(suitableBodyParts) ? attrValue : attrValue + "    " + suitableBodyParts;
        }
        String suitablePartSkinQuality = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "skinType");
        if (StringUtils.isEmpty(suitablePartSkinQuality)) {
            return suitableBodyParts;
        }
        return StringUtils.isEmpty(suitableBodyParts) ? suitablePartSkinQuality : suitablePartSkinQuality + "    " + suitableBodyParts;
    }

    /**
     *
     * @param skuDisplayModels
     * @param attrName
     * @param skuAttrItems
     * @return
     */
    public String getAttrValueByConfig(List<SkuDisplayModel> skuDisplayModels, String attrName, List<SkuAttrItemDto> skuAttrItems){
        SkuDisplayModel skuDisplayModel = getSkuDisplayModel(skuDisplayModels, attrName);
        if (Objects.nonNull(skuDisplayModel)){
            return getAttrValueBySkuDisplayModel(skuDisplayModel, skuAttrItems);
        }
        return DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "applySkin");
    }

    /**
     * 获取配置
     * @param skuDisplayModels
     * @param skuAttrName
     * @return
     */
    public SkuDisplayModel getSkuDisplayModel(List<SkuDisplayModel> skuDisplayModels, String skuAttrName){
        if (CollectionUtils.isNotEmpty(skuDisplayModels)){
            Map<String, SkuDisplayModel> attrNameToSkuDisplayModel = skuDisplayModels.stream().collect(Collectors.toMap(e->e.getSkuAttrName(), e->e , (e1,e2)->e2));
            return MapUtils.isNotEmpty(attrNameToSkuDisplayModel) ? attrNameToSkuDisplayModel.get(skuAttrName) : null;
        }
        return null;
    }

    private String getAttrValueBySkuDisplayModel(SkuDisplayModel skuDisplayModel, List<SkuAttrItemDto> skuAttrItems){
        // 将货的属“skuAttrName” 的原来值skuAttrValue  替换为 skuAttrReplaceValue
        if (StringUtils.equals(skuDisplayModel.getSkuAttrValue(), DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, skuDisplayModel.getSkuAttrName()))){
            return skuDisplayModel.getSkuAttrReplaceValue();
        }
        return DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, skuDisplayModel.getSkuAttrName());
    }

    /**
     * 判断该团单是否是标品
     *
     * @param dealAttrs 团单行业属性列表
     * @param config    变化点配置
     * @return
     */
    private boolean isStandardProduct(List<AttrM> dealAttrs, Config config) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return false;
        }
        if (config == null || StringUtils.isEmpty(config.getBeautySpaStandardProductKey()) || StringUtils.isEmpty(config.getBeautySpaStandardProductFlag())) {
            return false;
        }
        String standardProductAttrValue = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, config.getBeautySpaStandardProductKey());
        return config.getBeautySpaStandardProductFlag().equals(standardProductAttrValue);
    }

    private String buildFormatStemNum(String defaultFormat, int size, Config config) {
        if (config == null || StringUtils.isEmpty(config.getStandardStepSKuNumFormat())) {
            return String.format(defaultFormat, size);
        }
        return String.format(config.getStandardStepSKuNumFormat(), size);
    }

    private List<SkuAttrAttrItemVO> getSkuAttrAttrItemVOs(String serviceProcess, boolean isStandardProduct) {
        if (StringUtils.isEmpty(serviceProcess)) {
            return null;
        }
        List<ServiceStepModel> serviceStepModels = JsonCodec.converseList(serviceProcess, ServiceStepModel.class);
        if (CollectionUtils.isEmpty(serviceStepModels)) {
            return null;
        }
        //有1个以上步骤且所有步骤的适用部位都一样时不展示适用部位
        boolean isIdenticalBodyPartForServiceStep = isIdenticalBodyPartForServiceStep(serviceStepModels);
        return serviceStepModels.stream().map(model -> buildSkuAttrAttrItemVO(model, isIdenticalBodyPartForServiceStep, isStandardProduct)).filter(vo -> vo != null).collect(Collectors.toList());
    }

    private boolean isIdenticalBodyPartForServiceStep(List<ServiceStepModel> serviceStepModels) {
        if (CollectionUtils.isEmpty(serviceStepModels)) {
            return true;
        }
        List<String> bodyParts = serviceStepModels.stream().map(step -> step.getBodyPart()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bodyParts) || bodyParts.size() == 1) {
            return true;
        }
        Set<String> bodyPartSet = new HashSet<>(bodyParts);
        if (bodyPartSet == null) {
            return true;
        }
        return bodyPartSet.size() == 1;
    }

    private SkuAttrAttrItemVO buildSkuAttrAttrItemVO(ServiceStepModel serviceStepModel, boolean isIdenticalBodyPartForServiceStep, boolean isStandardProduct) {
        if (serviceStepModel == null) {
            return null;
        }
        SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
        String stepName = getNormalizedName(serviceStepModel.getSubStepName(), isStandardProduct);
        skuAttrAttrItemVO.setName(stepName);
        List<String> stepTime = getNormalizedStepTime(serviceStepModel.getStepTime(), isStandardProduct);
        skuAttrAttrItemVO.setInfo(stepTime);
        List<CommonAttrVO> commonAttrVOS = new ArrayList<>();
        if (StringUtils.isNotEmpty(serviceStepModel.getBodyPart()) && !isIdenticalBodyPartForServiceStep) {
            commonAttrVOS.add(buildCommonAttrVO(SUITABLE_PART, serviceStepModel.getBodyPart()));
        }
        if (StringUtils.isNotBlank(serviceStepModel.getProduct())) {
            commonAttrVOS.add(buildCommonAttrVO(PROJECT, serviceStepModel.getProduct()));
        }
        if (StringUtils.isNotBlank(serviceStepModel.getEquipment())) {
            commonAttrVOS.add(buildCommonAttrVO(EQUIPMENT, serviceStepModel.getEquipment()));
        }
        if (StringUtils.isNotBlank(serviceStepModel.getStepDesc())) {
            commonAttrVOS.add(buildCommonAttrVO(DESC, serviceStepModel.getStepDesc()));
        }
        if (CollectionUtils.isNotEmpty(commonAttrVOS)) {
            skuAttrAttrItemVO.setValues(commonAttrVOS);
        }
        return skuAttrAttrItemVO;
    }

    /**
     * 如果stepTime为0则展示"不计入总时长"，否则展示"%s分钟"
     *
     * @param
     * @return
     */
    private List<String> getNormalizedStepTime(String stepTime, boolean isStandardProduct) {
        if (StringUtils.isEmpty(stepTime)) {
            return null;
        }
        if (Integer.toString(0).equals(stepTime) && isStandardProduct) {
            return Lists.newArrayList(ZERO_STEP_TIME_SHOW_DOC);
        }
        return Lists.newArrayList(String.format(STEP_TIME_FORMAT, stepTime));
    }

    /**
     * 如果步骤名是"皮肤检测"，则展示为"皮肤检测（可选步骤）"
     *
     * @param preName
     * @return
     */
    private String getNormalizedName(String preName, boolean isStandardProduct) {
        if (SKIN_DETECT_STEP_NAME.equals(preName) && isStandardProduct) {
            return String.format(SKIN_DETECT_STEP_FORMAT, preName);
        }
        return preName;
    }

    private CommonAttrVO buildCommonAttrVO(String name, String value) {
        CommonAttrVO commonAttrVO = new CommonAttrVO();
        commonAttrVO.setName(name);
        commonAttrVO.setValue(value);
        return commonAttrVO;
    }


    private DealSkuItemVO buildDealSkuItemVO(String name, String value, List<SkuAttrAttrItemVO> valueAttrs, int type) {
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setType(type);
        dealSkuItemVO.setValue(value);
        dealSkuItemVO.setValueAttrs(valueAttrs);
        return dealSkuItemVO;
    }

    @Data
    @VPointCfg
    public static class Config {
        private String standardStepSKuNumFormat;
        //判断是否是标品的行业属性key
        private String beautySpaStandardProductKey;
        //判断是否是标品的行业属性value
        private String beautySpaStandardProductFlag;
        // 服务步骤或服务流程的展示类型
        private int stepType;
        /**
         * 货分类和展示固定文案-货属性名称映射关系
         */
        Map<Long, List<SkuDisplayModel>> category2SkuDisplayModelList;

    }

    @Data
    public static class ServiceStepModel {
        //仪器
        public String equipment;
        //产品
        public String product;
        //步骤名称
        public String stepName;
        //步骤子名称
        public String subStepName;
        //步骤时长
        public String stepTime;
        //说明
        public String stepDesc;
        //部位
        public String bodyPart;
    }

    @Data
    private static class SkuDisplayModel {
        //货属性展示标题
        private String skuTitle;

        //货属性名称
        private String skuAttrName;
        //货属性值
        private String skuAttrValue;
        //货属性替换值 ：将货属性值替换为该值
        private String skuAttrReplaceValue;
    }

}
