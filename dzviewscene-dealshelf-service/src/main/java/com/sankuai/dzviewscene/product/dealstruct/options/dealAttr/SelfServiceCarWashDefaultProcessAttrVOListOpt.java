package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.dianping.customer.dto.CustomerShopNew;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@VPointOption(name = "洗车机-自助服务流程DealAttrVO列表变化点", description = "洗车机-自助服务流程DealAttrVO列表变化点",code = SelfServiceCarWashDefaultProcessAttrVOListOpt.CODE)
public class SelfServiceCarWashDefaultProcessAttrVOListOpt extends DealAttrVOListVP<SelfServiceDefaultProcessAttrVOListOpt.Config> {
    public static final String CODE = "SelfServiceCarWashDefaultProcessAttrVOListOpt";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Resource
    private AtomFacadeService atomFacadeService;

    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt context, DealAttrVOListVP.Param param, SelfServiceDefaultProcessAttrVOListOpt.Config config) {
        if(config == null || !config.getProcessSwitch()) {
            return null;
        }

        Boolean needDefaultSelfServiceProcess = needDefaultSelfServiceProcess(context);

        return SelfServiceDefaultProcessAttrVOListOpt.buildDefaultDealDetailStructAttrModuleGroupModel(config,context, needDefaultSelfServiceProcess);
    }

    //是否需要填充默认自助服务流程
    private Boolean needDefaultSelfServiceProcess(ActivityCxt activityContext){
        long dpShopId = PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
        int productId = activityContext.getParam(ProductDetailActivityConstants.Params.productId);

        //查海马配置，洗车机是白名单/台球是黑名单
        List<CustomerShopNew> customerShopsByShopIdsNew = atomFacadeService.getCustomerShopsByShopIdsNew(Lists.newArrayList(dpShopId)).join();
        if (CollectionUtils.isEmpty(customerShopsByShopIdsNew)){
            //未查到有效客户信息，直接失败
            return false;
        }
        List<Integer> customerIds = customerShopsByShopIdsNew.stream().map(CustomerShopNew::getCustomerID).collect(Collectors.toList());
        HaimaRequest hmRequest = new HaimaRequest();
        hmRequest.setSceneKey("general_groupbuy_auto_car_wash_customer_config");
        customerIds.forEach(customerId -> {
            hmRequest.addField("customerId",String.valueOf(customerId));
        });
        CompletableFuture<HaimaResponse> haiMaResponse = compositeAtomService.getHaiMaResponse(hmRequest);
        HaimaResponse hmResponse = haiMaResponse.join();

        return SelfServiceDefaultProcessAttrVOListOpt.matchList(hmResponse,customerIds,productId,dpShopId);
    }
}
