package com.sankuai.dzviewscene.product.filterlist.option.builder.filter.endintercept;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.filter.vp.FilterEndInterceptVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.Map;
import java.util.Objects;

/**
 * 指定筛选ID映射图片
 *
 * <AUTHOR>
 * @since 2023/3/20 16:32
 */
@VPointOption(name = "指定筛选ID映射图片",
        description = "根据指定 filterId，赋值指定按钮的icon",
        code = "filterIdMapFilterBtnIconOpt")
public class filterIdMapFilterBtnIconOpt extends FilterEndInterceptVP<filterIdMapFilterBtnIconOpt.Config> {

    @Override
    public Void compute(ActivityCxt activityCxt, Param param, Config config) {
        if (CollectionUtils.isEmpty(param.getFilterList()) || Objects.isNull(config)
                || MapUtils.isEmpty(config.getChildrenFilterId2ImageCfg())) {
            return null;
        }
        DzFilterVO filterVO = param.getFilterList().get(0);
        if (Objects.isNull(filterVO) || CollectionUtils.isEmpty(filterVO.getChildren())) {
            return null;
        }

        // 一级筛选列表判断
        filterVO.getChildren().forEach(a -> {
            if (config.getChildrenFilterId2ImageCfg().containsKey(a.getFilterId())) {
                a.setIcon(config.getChildrenFilterId2ImageCfg().get(a.getFilterId()));
            }
        });

        return null;
    }

    @Data
    @VPointCfg
    public static class Config {
        /**
         * key:filterId，value：图片URL
         */
        private Map<Long, String> childrenFilterId2ImageCfg;
    }
}
