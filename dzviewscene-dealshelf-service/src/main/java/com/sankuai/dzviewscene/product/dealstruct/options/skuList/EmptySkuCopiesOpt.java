package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuCopiesVP;
import lombok.Data;

@VPointOption(name = "sku份数变化点-空", description = "sku份数变化点-空", code = EmptySkuCopiesOpt.CODE)
public class EmptySkuCopiesOpt extends SkuCopiesVP<EmptySkuCopiesOpt.Config> {

    public static final String CODE = "EmptySkuCopiesOpt";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        return null;
    }

    @Data
    @VPointCfg
    public static class Config {

    }

}
