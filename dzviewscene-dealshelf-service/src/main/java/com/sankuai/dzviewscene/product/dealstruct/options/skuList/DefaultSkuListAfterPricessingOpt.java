package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListAfterProcessingVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/8 5:29 下午
 */
@VPointOption(name = "sku列表后置处理默认变化点", description = "sku列表后置处理默认变化点",code = DefaultSkuListAfterPricessingOpt.CODE, isDefault = true)
public class DefaultSkuListAfterPricessingOpt extends SkuListAfterProcessingVP<DefaultSkuListAfterPricessingOpt.Config> {

    public static final String CODE = "DefaultSkuListAfterPricessingOpt";

    @Override
    public List<DealSkuVO> compute(ActivityCxt context, Param param, Config config) {
        return param.getDealSkuVOS();
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
