package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/7/28 12:00
 */
@VPoint(name = "价格标签描述", description = "价格描述标签", code = ProductSalePriceDescVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductSalePriceDescVP<T> extends PmfVPoint<String, ProductSalePriceDescVP.Param, T> {

    public static final String CODE = "ProductSalePriceDescVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;
    }

}
