package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 运动健身sku属性列表变化点
 * 适用于团单内的服务项目为【训练营-2104606】【团课-2104605】【自助健身-2104600】【私教体验课-2104575】【私教正式课-2104576】
 */
@VPointOption(name = "运动健身sku属性列表变化点", description = "运动健身sku属性列表变化点", code = SportFitnessSkuAttrListOpt.CODE)
public class SportFitnessSkuAttrListOpt extends SkuAttrListVP<SportFitnessSkuAttrListOpt.Config> {

    public static final String CODE = "SportFitnessSkuAttrListOpt";

    /**
     *【团课】服务项目Id
     */
    private static final long GROUP_CLASS_CATEGORY_ID = 2104605L;

    /**
     *【训练营】服务项目Id
     */
    private static final long TRAINING_CAMP_CATEGORY_ID = 2104606L;



    @Override
    public List<DealSkuItemVO> compute(ActivityCxt context, Param param, Config config) {
        //团单属性
        SkuItemDto skuItemDto = param.getSkuItemDto();
        List<ProductSkuCategoryModel> productCategories = param.getProductCategories();
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems()) || config == null) {
            return null;
        }
        List<SkuAttrItemDto> skuAttrItems = skuItemDto.getAttrItems();
        long productCategory = param.getDealAttrs() == null ? 0 : param.getSkuItemDto().getProductCategory();
        List<SkuDisplayModel> skuDisplayModels = getSkuDisplayModels(config, productCategory, productCategories);
        if(CollectionUtils.isEmpty(skuDisplayModels)){
            return null;
        }
        List<AttrM> dealAttrs = param.getDealAttrs();
        return skuDisplayModels.stream()
                .map(skuDisplayModel -> buildDealSkuItemByDisplayModel(skuDisplayModel, skuAttrItems, productCategory, productCategories, dealAttrs))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<SkuDisplayModel> getSkuDisplayModels(Config config, long productCategory, List<ProductSkuCategoryModel> productCategories) {
        Map<Long, List<SkuDisplayModel>> category2SkuDisplayModelList = config.getCategory2SkuDisplayModelList();
        Map<String, List<SkuDisplayModel>> categoryName2SkuDisplayModelList = config.getCategoryName2SkuDisplayModelList();
        if (MapUtils.isNotEmpty(category2SkuDisplayModelList)) {
            return category2SkuDisplayModelList.get(productCategory);
        }
        //不同的业务可能categoryName（台费、项目体验）一样，需求一样，但categoryid不一样，所以要用categoryName
        if (MapUtils.isNotEmpty(categoryName2SkuDisplayModelList) && CollectionUtils.isNotEmpty(productCategories)) {
            String name = productCategories.stream()
                    .filter(productSkuCategoryModel -> productSkuCategoryModel.getProductCategoryId() == productCategory)
                    .map(ProductSkuCategoryModel::getCnName)
                    .findFirst().orElse(StringUtils.EMPTY);
            return categoryName2SkuDisplayModelList.get(name);
        }
        return null;
    }

    /**
     * @param skuDisplayModel 配置的模型
     * @param skuAttrItems 服务项目属性
     * @param productCategory 服务项目id
     * @param productCategories
     * @return
     */
    private DealSkuItemVO buildDealSkuItemByDisplayModel(SkuDisplayModel skuDisplayModel, List<SkuAttrItemDto> skuAttrItems, long productCategory, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs){

        if (skuDisplayModel == null || StringUtils.isEmpty(skuDisplayModel.getSkuAttrName()) || StringUtils.isEmpty(skuDisplayModel.getSkuTitle())) {
            return null;
        }
        String skuAttrValue = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, skuDisplayModel.getSkuAttrName());
        if(StringUtils.equals(skuDisplayModel.getSkuAttrName(), "rights")){
            skuAttrValue = buildRightSkuAttrValue(skuAttrValue, skuDisplayModel.getSkuValueOrder());
        }
        if(StringUtils.equals(skuDisplayModel.getSkuAttrName(), "classCategory")){
            skuAttrValue = buildClassCategory(skuAttrItems, productCategory, skuDisplayModel.getSkuValueOrder());
        }
        if(StringUtils.equals(skuDisplayModel.getSkuAttrName(), "serviceEffect")){
            skuAttrValue = buildServiceEffect(skuAttrItems, skuDisplayModel.getSkuValueOrder());
        }
        if (StringUtils.equals(skuDisplayModel.getSkuAttrName(), "projectClassification")) {
            //projectClassification是人为定义
            skuAttrValue = buildProjectClassification(productCategory, productCategories);
        }
        if(StringUtils.equals(skuDisplayModel.getSkuAttrName(), "projectProcessArray") || StringUtils.equals(skuDisplayModel.getSkuAttrName(), "serviceProcess")){
            return buildProcessDealSkuItem(skuAttrItems, skuDisplayModel, productCategory);
        }
        if(StringUtils.equals(skuDisplayModel.getSkuAttrName(), "peopleNum")){
            return buildPeopleNumDealSkuItem(skuAttrItems, skuDisplayModel);
        }
        // 适用球桌
        if (StringUtils.equals(skuDisplayModel.getSkuAttrName(), "applicableTable")) {
            return buildApplicableTableDealSkuItem(skuAttrItems, skuDisplayModel);
        }
        // 使用时间
        if (StringUtils.equals(skuDisplayModel.getSkuAttrName(), "available_time")) {
            return buildAvailableTimeDealSkuItem(dealAttrs, skuDisplayModel);
        }
        if(StringUtils.isEmpty(skuAttrValue)){
            return null;
        }
        if(StringUtils.isNotEmpty(skuDisplayModel.getSkuAttrValueFormat())){
            skuAttrValue = String.format(skuDisplayModel.getSkuAttrValueFormat(), skuAttrValue);
        }
        return buildDealSkuItemVO(skuDisplayModel.getSkuTitle(), skuAttrValue, null, 0);
    }

    public DealSkuItemVO buildAvailableTimeDealSkuItem(List<AttrM> dealAttrs, SkuDisplayModel skuDisplayModel) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        String availableTime = DealDetailUtils.findAttrValue(dealAttrs, "available_time");
        if (StringUtils.isBlank(availableTime)) {
            return null;
        }
        String displayValue = String.format(skuDisplayModel.getSkuAttrValueFormat(), availableTime);
        return buildDealSkuItemVO(skuDisplayModel.getSkuTitle(), displayValue, null, 0);
    }

    public DealSkuItemVO buildApplicableTableDealSkuItem(List<SkuAttrItemDto> skuAttrItems, SkuDisplayModel skuDisplayModel) {
        String applicableTable = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "applicableTable");
        String applicableTableText = null;
        if (Objects.equals(applicableTable, "全部适用")) {
            applicableTableText = "全部";
        } else if (Objects.equals(applicableTable, "部分适用")) {
            applicableTableText = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "applicableTableName");
        }
        if (StringUtils.isBlank(applicableTableText)) {
            return null;
        }
        String displayValue = String.format(skuDisplayModel.getSkuAttrValueFormat(), applicableTableText);
        return buildDealSkuItemVO(skuDisplayModel.getSkuTitle(), displayValue, null, 0);
    }

    private String buildProjectClassification(long productCategory, List<ProductSkuCategoryModel> productCategories) {
        if (CollectionUtils.isEmpty(productCategories)) {
            return null;
        }
        ProductSkuCategoryModel productSkuCategoryModel = productCategories.stream().filter(category -> category.getProductCategoryId() != null && productCategory == category.getProductCategoryId()).findFirst().orElse(null);
        if (productSkuCategoryModel == null) {
            return null;
        }
        return productSkuCategoryModel.getCnName();
    }

    private DealSkuItemVO buildPeopleNumDealSkuItem(List<SkuAttrItemDto> skuAttrItems, SkuDisplayModel displayModel){
        String maxPeopleNum = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "maxpeoplenum");
        String minPeopleNum = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "minpeoplenum");
        if(StringUtils.isEmpty(maxPeopleNum) || StringUtils.isEmpty(minPeopleNum) || StringUtils.isEmpty(displayModel.getSkuAttrValueFormat())){
            return null;
        }
        String displayValue = String.format(displayModel.getSkuAttrValueFormat(), minPeopleNum, maxPeopleNum);
        return buildDealSkuItemVO(displayModel.getSkuTitle(), displayValue, null, 0);
    }

    private DealSkuItemVO buildProcessDealSkuItem(List<SkuAttrItemDto> skuAttrItems, SkuDisplayModel displayModel, long productCategoryId){
        String process = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, displayModel.getSkuAttrName());
        List<Map<String, String>> processInfoList = JsonCodec.decode(process, new TypeReference<List<Map<String, String>>>() {
        });
        StepInfoModel stepInfo = displayModel.getStepInfo();
        if(CollectionUtils.isEmpty(processInfoList) || stepInfo == null){
            return null;
        }
        List<SkuAttrAttrItemVO> processList = processInfoList.stream()
                .map(processInfo -> buildSkuAttrAttrItemVO(processInfo.get(stepInfo.getStepName()), processInfo.get(stepInfo.getStepDesc()), productCategoryId))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(processList)){
            return null;
        }
        return buildDealSkuItemVO(displayModel.getSkuTitle(), null, processList, 2);
    }

    private SkuAttrAttrItemVO buildSkuAttrAttrItemVO(String stepName, String stepDesc, long productCategoryId){
        if(StringUtils.isEmpty(stepName) || StringUtils.isEmpty(stepDesc)){
            return null;
        }
        if(productCategoryId == TRAINING_CAMP_CATEGORY_ID){
            stepDesc = String.format("第%s天", stepDesc);
        }
        else{
            stepDesc = stepDesc + "分钟";
        }
        SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
        skuAttrAttrItemVO.setName(stepName);
        skuAttrAttrItemVO.setInfo(Lists.newArrayList(stepDesc));
        return skuAttrAttrItemVO;
    }

    /**
     * @param skuAttrItems
     * @param serviceEffectOrder
     * 拼接【针对部位】和【服务功效】
     * 服务功效展示顺序 减脂＞塑形＞增肌＞体态改善＞舒缓放松＞运动能力提升＞商户自定义
     */
    private String buildServiceEffect(List<SkuAttrItemDto> skuAttrItems, Map<String, Integer> serviceEffectOrder){
        String bodyName = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "bodyname");
        String serviceEffect = getServiceEffect(skuAttrItems, serviceEffectOrder);
        if(StringUtils.isEmpty(bodyName) || StringUtils.isEmpty(serviceEffect)){
            return serviceEffect;
        }
        return bodyName + " | " + serviceEffect;
    }

    private String getServiceEffect(List<SkuAttrItemDto> skuAttrItems, Map<String, Integer> serviceEffectOrder){
        String serviceEffect = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "serviceeffect");
        if(MapUtils.isEmpty(serviceEffectOrder) || StringUtils.isEmpty(serviceEffect)){
            return serviceEffect;
        }
        List<String> effects = Lists.newArrayList(serviceEffect.split("、"));
        effects = effects.stream()
                .sorted(Comparator.comparingInt(o -> getSkuAttrOrder(serviceEffectOrder, o)))
                .collect(Collectors.toList());
        return String.join("", effects);
    }

    /**
     * @param skuAttrItems
     * 拼接【课程分类】和【适用人群】信息，服务项目为团课时拼接【课程分类】、【适用人群】和 【服务功效】
     * 课程分类勾选【其他新型私教】【多课程自选】时，课程分类信息展示货属性otherInfo的内容
     * 课程分类勾选【其他新团课】时，课程分类信息展示货属性className的内容
     * 【适用人群】勾选【全部人群】不展示
     *
     */
    private String buildClassCategory(List<SkuAttrItemDto> skuAttrItems, long productCategory, Map<String, Integer> serviceEffectOrder){
        String classCat = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "classCat");
        String applicableCrowds = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "applicableCrowds");
        if(StringUtils.equals(classCat, "其他新型私教") || StringUtils.equals(classCat, "多课程自选")){
            classCat = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "otherInfo");
        }
        if(StringUtils.equals(classCat, "其他新团课")){
            classCat = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "className");
        }
        String displayValue = StringUtils.equals(applicableCrowds, "全部人群") ? classCat : applicableCrowds + classCat;
        String serviceEffect = getServiceEffect(skuAttrItems, serviceEffectOrder);
        if(productCategory == GROUP_CLASS_CATEGORY_ID && StringUtils.isNotEmpty(serviceEffect)){
            return displayValue + " | " + serviceEffect;
        }
        return displayValue;
    }

    /**
     * 最多展示2个 附加权益 ，展示次序为游泳＞拳击＞瑜伽＞团操＞单车＞私教＞商户自定义
     */
    private String buildRightSkuAttrValue(String attrValue, Map<String, Integer> rightOrder){
        if(MapUtils.isEmpty(rightOrder) || StringUtils.isEmpty(attrValue)){
            return attrValue;
        }
        List<String> rights = Lists.newArrayList(attrValue.split("、"));
        if(CollectionUtils.isEmpty(rights)){
            return attrValue;
        }
        rights = rights
                .stream()
                .sorted(Comparator.comparingInt(o -> getSkuAttrOrder(rightOrder, o)))
                .collect(Collectors.toList());
        return rights.size() >= 2 ? String.join("、", rights.subList(0, 2)) : rights.get(0);
    }

    private int getSkuAttrOrder(Map<String, Integer> skuValueOrder, String skuValue){
        if(!skuValueOrder.containsKey(skuValue)){
            return Integer.MAX_VALUE;
        }
        return skuValueOrder.get(skuValue);
    }



    private DealSkuItemVO buildDealSkuItemVO(String name, String value, List<SkuAttrAttrItemVO> valueAttrs, int type) {
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setType(type);
        dealSkuItemVO.setValue(value);
        dealSkuItemVO.setValueAttrs(valueAttrs);
        return dealSkuItemVO;
    }

    @Data
    @VPointCfg
    public static class Config {
        /**
         * 货分类和展示固定文案-货属性名称映射关系
         */
        private Map<Long, List<SkuDisplayModel>> category2SkuDisplayModelList;

        private Map<String, List<SkuDisplayModel>> categoryName2SkuDisplayModelList;
    }

    @Data
    private static class StepInfoModel {
        //步骤名称
        private String stepName;

        //步骤说明
        private String stepDesc;
    }

    @Data
    public static class SkuDisplayModel {
        //货属性展示标题
        private String skuTitle;

        //货属性名称
        private String skuAttrName;

        //货属性展示format
        private String skuAttrValueFormat;

        //步骤内容字段信息
        private StepInfoModel stepInfo;

        //skuValue展示顺序
        private Map<String, Integer> skuValueOrder;
    }
}
