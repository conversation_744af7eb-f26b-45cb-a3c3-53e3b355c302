package com.sankuai.dzviewscene.product.filterlist.option.builder.filter.endintercept;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.filter.vp.FilterEndInterceptVP;
import com.sankuai.dzviewscene.product.filterlist.model.FlagshipStoreM;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterBtnVO;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterVO;
import com.sankuai.dzviewscene.shelf.business.utils.UrlUtils;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.mtstore.aggregate.thrift.dto.decorate.StoreRecommendProjectItemDTO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 少于3个返回空
 * 多余6个截断
 * 填充分平台跳转链接
 * 填充筛选图片
 * 树状结构改为平铺
 *
 * <AUTHOR>
 * @date 2023/2/16
 */
@VPointOption(name = "旗舰店-推荐项目-筛选结构",
        description = "控制展示个数，硬编码跳转链接",
        code = "FlagshipStoreRecommendServiceFilterEndInterceptOpt")
public class FlagshipStoreRecommendServiceFilterEndInterceptOpt extends FilterEndInterceptVP<FlagshipStoreRecommendServiceFilterEndInterceptOpt.Config> {
    @Override
    public Void compute(ActivityCxt activityCxt, Param param, Config config) {
        if (CollectionUtils.isEmpty(param.getFilterList())) {
            return null;
        }
        DzFilterVO filterVO = param.getFilterList().get(0);
        if (filterVO == null || CollectionUtils.isEmpty(filterVO.getChildren())) {
            return null;
        }
        List<DzFilterBtnVO> notTreeBtnList = new ArrayList<>();
        FlagshipStoreM flagshipStoreM = activityCxt.getParam(ShelfActivityConstants.Ctx.ctxFlagshipStore);
        //新逻辑存在商家自定义推荐项目
        if(Objects.nonNull(flagshipStoreM) && CollectionUtils.isNotEmpty(flagshipStoreM.getProjectList())){
            notTreeBtnList = convertTreeList2NormalListNew(filterVO.getChildren(), config);
            notTreeBtnList = paddingImgNew(flagshipStoreM.getProjectList(), notTreeBtnList);
        } else {
            //将导航树结构转换成平铺的导航按钮，顺序为一级节点+二级节点
            notTreeBtnList = convertTreeList2NormalList(filterVO.getChildren(), config);
            //填充图片
            paddingImg(config.getFilterId2Img(), notTreeBtnList);
        }
        //删除无图片节点
        removeNoPicFilter(notTreeBtnList);
        //截断或控制上限
        limitOrControlHide(notTreeBtnList);
        //填充跳转链接
        paddingJumpUrl(config, notTreeBtnList, param.getUserAgent(),activityCxt);
        //最后清除子节点，因为之前有操作会依赖子节点
        clearChildNode(notTreeBtnList);
        //替换源筛选
        filterVO.getChildren().clear();
        if (CollectionUtils.isNotEmpty(notTreeBtnList)) {
            filterVO.getChildren().addAll(notTreeBtnList);
        }
        return null;
    }

    private void removeNoPicFilter(List<DzFilterBtnVO> filterBtnList) {
        Iterator<DzFilterBtnVO> iterator = filterBtnList.iterator();
        while (iterator.hasNext()) {
            DzFilterBtnVO btnVO = iterator.next();
            if (StringUtils.isEmpty(btnVO.getLink())) {
                iterator.remove();
            }
        }
    }

    private void paddingImg(Map<Long, String> filterId2Img, List<DzFilterBtnVO> filterBtnList) {
        if (MapUtils.isEmpty(filterId2Img)) {
            return;
        }
        for (DzFilterBtnVO child : filterBtnList) {
            String img = filterId2Img.get(child.getFilterId());
            if (StringUtils.isNotEmpty(img)) {
                child.setLink(img);
            }
        }
    }

    private List<DzFilterBtnVO> paddingImgNew(List<StoreRecommendProjectItemDTO> projectList, List<DzFilterBtnVO> filterBtnList) {
        if (CollectionUtils.isEmpty(projectList)) {
            return filterBtnList;
        }
        List<DzFilterBtnVO> newFilterBtnList = new ArrayList<>();
        for(StoreRecommendProjectItemDTO itemDTO : projectList) {
            for (DzFilterBtnVO child : filterBtnList) {
                if(itemDTO.getProjectId() == child.getFilterId()){
                    child.setLink(itemDTO.getBackgroundPic());
                    newFilterBtnList.add(child);
                }
            }
        }
        return newFilterBtnList;
    }

    private void paddingJumpUrl(Config config, List<DzFilterBtnVO> filterBtnList, int userAgent,ActivityCxt activityCxt) {
        UrlCfg urlCfg = config.doGetPlatformUrlCfgByUserAgent(userAgent);
        boolean isUrlCfgInvalid = urlCfg == null || StringUtils.isEmpty(urlCfg.getUrlFormat());
        boolean hasFlagshipStoreUrl = StringUtils.isNotEmpty(activityCxt.getParam(ShelfActivityConstants.Ctx.ctxFlagshipStoreUrl));
        if (isUrlCfgInvalid && !hasFlagshipStoreUrl) {
            return;
        }
        for (DzFilterBtnVO btnVO : filterBtnList) {
            //存在子节点需取子节点id进行跳转
            long filterId = CollectionUtils.isNotEmpty(btnVO.getChildren()) ? btnVO.getChildren().get(0).getFilterId() : btnVO.getFilterId();
            String resUrl;
            if(hasFlagshipStoreUrl){
                resUrl = activityCxt.getParam(ShelfActivityConstants.Ctx.ctxFlagshipStoreUrl)+"&filterbtnid="+filterId;
            } else {
                String urlBeforeEncode = String.format(urlCfg.getUrlFormat(), filterId);
                resUrl = urlBeforeEncode;
                if (StringUtils.isNotEmpty(urlCfg.getPrefixWithURLEncode())) {
                    resUrl = urlCfg.getPrefixWithURLEncode() + UrlUtils.urlEncode(urlBeforeEncode);
                }
            }
            Map<String, Object> extraMap = new HashMap<>(1);
            extraMap.put("jumpUrl", resUrl);
            btnVO.setExtra(JsonCodec.encodeWithUTF8(extraMap));
        }
    }

    /**
     * 一级节点带图的多于6个则截断
     *
     * @param filterBtnList
     */
    private void limitOrControlHide(List<DzFilterBtnVO> filterBtnList) {
        if (filterBtnList.isEmpty()) {
            return;
        }
        if (filterBtnList.size() > 6) {
            filterBtnList.subList(6, filterBtnList.size()).clear();
        }
    }

    /**
     * 如果有图节点 > 1 个则展示
     * 如果有图节点 == 1 且配置了使用二级节点，则返回二级节点树
     * @param treeList
     * @param config
     * @return
     */
    private List<DzFilterBtnVO> convertTreeList2NormalList(List<DzFilterBtnVO> treeList, Config config) {
        if (CollectionUtils.isEmpty(treeList) || MapUtils.isEmpty(config.getFilterId2Img())) {
            return new ArrayList<>();
        }
        //一级节点
        List<DzFilterBtnVO> oneLevelBtnList = new ArrayList<>(treeList);
        //有图片的一级节点数量
        long oneLevelHasImgCnt = oneLevelBtnList.stream().filter(btn -> config.getFilterId2Img().containsKey(btn.getFilterId())).count();
        if (oneLevelHasImgCnt > 1) {
            return oneLevelBtnList;
        }
        if (oneLevelHasImgCnt == 1 && config.isUseSecondFilter()) {
            List<DzFilterBtnVO> twoLevelBtnList = new ArrayList<>();
            for (DzFilterBtnVO btnVO : treeList) {
                if (CollectionUtils.isNotEmpty(btnVO.getChildren())) {
                    //子节点也可参与出图
                    twoLevelBtnList.addAll(btnVO.getChildren());
                }
            }
            return twoLevelBtnList;
        }
        return new ArrayList<>();
    }

    private List<DzFilterBtnVO> convertTreeList2NormalListNew(List<DzFilterBtnVO> treeList, Config config) {
        if (CollectionUtils.isEmpty(treeList)) {
            return new ArrayList<>();
        }
        //一级节点
        List<DzFilterBtnVO> oneLevelBtnList = new ArrayList<>(treeList);
        if (config.isUseSecondFilter()) {
            List<DzFilterBtnVO> twoLevelBtnList = new ArrayList<>();
            for (DzFilterBtnVO btnVO : treeList) {
                if (CollectionUtils.isNotEmpty(btnVO.getChildren())) {
                    //子节点也可参与出图
                    twoLevelBtnList.addAll(btnVO.getChildren());
                }
            }
            oneLevelBtnList.addAll(twoLevelBtnList);
        }
        return oneLevelBtnList;
    }

    private void clearChildNode(List<DzFilterBtnVO> filterBtnList) {
        if (CollectionUtils.isEmpty(filterBtnList)) {
            return;
        }
        for (DzFilterBtnVO btnVO : filterBtnList) {
            btnVO.setChildren(null);
        }
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 筛选id -> 图片
         * 仅填充一级节点
         */
        private Map<Long, String> filterId2Img;

        /**
         * 平台与指定的跳转链接
         */
        private Map<Integer, UrlCfg> platform2Url;

        /**
         * @param userAgent
         * @return 根据平台获取url
         */
        public FlagshipStoreRecommendServiceFilterEndInterceptOpt.UrlCfg doGetPlatformUrlCfgByUserAgent(int userAgent) {
            if (MapUtils.isEmpty(this.platform2Url)) {
                //返回空
                return null;
            }
            return this.platform2Url.get(userAgent);
        }

        /**
         * 是否使用二级筛选构造
         */
        private boolean useSecondFilter;
    }

    @Data
    public static class UrlCfg {
        private String urlFormat;

        /**
         * 固定前缀，在生成 URL 后，需对 URL 进行 Encode 后，拼接固定前缀
         * 若为空，则不进行 Encode
         */
        private String prefixWithURLEncode;

        public UrlCfg(String urlFormat) {
            this.urlFormat = urlFormat;
        }

        public UrlCfg() {
        }
    }
}