package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.dianping.frog.sdk.util.CollectionUtils;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.filterlist.utils.PromoCodeUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.sankuai.dztheme.generalproduct.enums.GeneralProductAttrEnum.ATTR_SEARCH_HIDDEN_STATUS;


/**
 * @description : 优惠码商品处理能力类
 * @date : 2025/4/17
 */
@VPointOption(name = "优惠码商品列表处理类", description = "优惠码商品列表处理类，目前只对商品是否在该门店下购买过进行置顶", code = "PromoCodeProductListOpt")
public class PromoCodeProductListOpt extends ProductListVP<PromoCodeProductListOpt.Config> {
    private static final String USER_PURCHASE_COUNT = "dealUserPurchaseCountAttr";
    private static final String DEAL_STATUS_ATTR = "dealStatusAttr";
    private static final String PRODUCT_TYPE = "productType";
    private static final String PRE_SALE_ATTR = "attr_is_pre_sale";  // 是否是预售商品
    @Override
    public List<ProductM> compute(ActivityCxt context, Param param, Config config) {
        if (CollectionUtils.isEmpty(param.getProductMS()) || config == null) {
            return param.getProductMS();
        }
        List<ProductM> result = param.getProductMS();
        if (config.isUseFilterValid()) {
            result = result.stream().filter(this::isValid).collect(Collectors.toList());
        }
        if (config.isUseTop()) {
            result = result.stream().sorted(this::sortByUserShopPurchaseCount).collect(Collectors.toList());
        }
        if (config.isUseChangePriceFilter()
                && PromoCodeUtils.checkShopAutoVerify(context)) {
            result = result.stream().filter(this::isChangePriceFilter).collect(Collectors.toList());
        }
        return result;
    }


    private boolean isChangePriceFilter(ProductM productM) {
        return !(checkProductHasVerifyTimeGap(productM) || Objects.equals("true", productM.getAttr(PRE_SALE_ATTR)));
    }

    private static boolean checkProductHasVerifyTimeGap(ProductM productM) {
        return productM.getUseRuleM() != null && productM.getUseRuleM().getDisableDate() != null &&
                (CollectionUtils.isNotEmpty(productM.getUseRuleM().getDisableDate().getDisableDays())
                        || CollectionUtils.isNotEmpty(productM.getUseRuleM().getDisableDate().getDisableDateRangeDTOS()));
    }

    protected boolean isValid(ProductM productM) {
        return productM != null && productM.getSalePrice() != null
                && "deal".equals(productM.getAttr(PRODUCT_TYPE))
                && "true".equals(productM.getAttr(DEAL_STATUS_ATTR))
                && "false".equals(productM.getAttr(ATTR_SEARCH_HIDDEN_STATUS.key))
                && productM.getSale() != null;
    }

    /**
     * 用户购买的商品置顶
     */
    private int sortByUserShopPurchaseCount(ProductM m1, ProductM m2) {
        boolean m1Purchased = m1.getAttr(USER_PURCHASE_COUNT) != null && Integer.parseInt(m1.getAttr(USER_PURCHASE_COUNT)) > 0;
        boolean m2Purchased = m2.getAttr(USER_PURCHASE_COUNT) != null && Integer.parseInt(m2.getAttr(USER_PURCHASE_COUNT)) > 0;
        if (m1Purchased && !m2Purchased) {
            return -1;  // m1应该在前
        } else if (!m1Purchased && m2Purchased) {
            return 1;   // m2应该在前
        } else {
            return 0;   // 保持原有顺序
        }
    }

    @VPointCfg
    @Data
    public static class Config {
        private boolean useFilterValid = false; //是否过滤有效商品
        private boolean useTop = false;     //是否需要置顶
        private boolean useChangePriceFilter = false;  // 是否过滤改价场景商品
    }

}
