package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.meituan.mpproduct.general.trade.api.request.PoiSpuIdQueryRequest;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.QueryConstants;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductIdM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
public class PoiSpuProductsQueryHandler implements QueryHandler {
    /**
     * 一口价类标品的id类型（资源类型）
     */
    private static final int COMMON_SPU_ID_TYPE = 16;

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<ProductGroupM> query(ActivityCxt ctx, String groupName, Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler.PoiSpuProductsQueryHandler.query(com.sankuai.athena.viewscene.framework.ActivityCxt,java.lang.String,java.util.Map)");
        return compositeAtomService.querySpuIdsByPoi(buildRequest(ctx, params)).thenApply(spuIds -> {
            ProductGroupM productGroupM = new ProductGroupM();
            productGroupM.setProducts(buildProducts(groupName, spuIds));
            productGroupM.setTotal(CollectionUtils.isEmpty(spuIds)? 0 : spuIds.size());
            return productGroupM;
        });
    }

    private List<ProductM> buildProducts(String groupName, List<Long> spuIds) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler.PoiSpuProductsQueryHandler.buildProducts(java.lang.String,java.util.List)");
        if (CollectionUtils.isEmpty(spuIds)) {
            return Lists.newArrayList();
        }
        return spuIds.stream().map(spuId -> {
            ProductM productM = new ProductM();
            productM.setGroupName(groupName);
            productM.setId(new ProductIdM(spuId, COMMON_SPU_ID_TYPE));
            return productM;
        }).collect(Collectors.toList());
    }

    private PoiSpuIdQueryRequest buildRequest(ActivityCxt ctx, Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler.PoiSpuProductsQueryHandler.buildRequest(com.sankuai.athena.viewscene.framework.ActivityCxt,java.util.Map)");
        PoiSpuIdQueryRequest request = new PoiSpuIdQueryRequest();
        request.setPoiId(ParamsUtil.getLongSafely(ctx, PmfConstants.Params.mtPoiIdL));
        request.setCategoryId(ParamsUtil.getLongSafely(params, QueryConstants.Params.secondCategoryId));
        return request;
    }
}
