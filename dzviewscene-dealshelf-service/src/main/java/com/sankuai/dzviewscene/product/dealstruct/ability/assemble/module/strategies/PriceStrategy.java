package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.desc.DealDetailDescBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.price.DealDetailPriceBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailPriceModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailPriceModuleVO;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * created by zhangzhiyuan04 in 2021/12/14
 */
@Component("price")
public class PriceStrategy implements ModuleStrategy {

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        String abilityCode = DealDetailPriceBuilder.CODE;
        List<DealDetailPriceModel> priceModels = activityCxt.getSource(abilityCode);
        if (CollectionUtils.isEmpty(priceModels)) {
            return null;
        }
        DealDetailPriceModel dealDetailPriceModel = CollectUtils.firstValue(priceModels);
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        DealDetailPriceModuleVO dealDetailPriceModuleVO = new DealDetailPriceModuleVO();
        dealDetailPriceModuleVO.setSalePrice(dealDetailPriceModel.getSalePrice());
        dealDetailPriceModuleVO.setSalePriceTitle(dealDetailPriceModel.getSalePriceTitle());
        dealDetailPriceModuleVO.setOriginalPrice(dealDetailPriceModel.getOriginalPrice());
        dealDetailPriceModuleVO.setOriginalPriceTitle(dealDetailPriceModel.getOriginalPriceTitle());
        dealDetailModuleVO.setPriceModel(dealDetailPriceModuleVO);
        return dealDetailModuleVO;
    }
}
