package com.sankuai.dzviewscene.product.filterlist.option.builder.product.marketprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductMarketPriceVP;

/**
 * <AUTHOR>
 * @date 2023/2/2
 */
@VPointOption(name = "直接返回市场价",
        description = "",
        code = "OriginProductMarketPriceOpt")
public class OriginProductMarketPriceOpt extends ProductMarketPriceVP<Void> {
    @Override
    public String compute(ActivityCxt activityCxt, Param param, Void unused) {
        return param.getProductM().getMarketPrice();
    }
}
