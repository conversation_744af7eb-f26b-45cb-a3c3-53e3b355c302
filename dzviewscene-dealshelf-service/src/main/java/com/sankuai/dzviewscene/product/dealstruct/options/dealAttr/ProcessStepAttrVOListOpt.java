package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailAttrDescVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * * @date 2022/12/12 5:29 下午
 */
@VPointOption(name = "流程步骤构造DealAttrVO列表变化点", description = "流程步骤构造DealAttrVO列表变化点", code = ProcessStepAttrVOListOpt.CODE)
public class ProcessStepAttrVOListOpt extends DealAttrVOListVP<ProcessStepAttrVOListOpt.StepConfig> {

    public static final String CODE = "ProcessStepAttrVOListOpt";

    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt context, Param param, StepConfig config) {
        List<DealDetailStructAttrModuleVO> processStepAttrModules = getProcessStepAttrModules(param, config);
        if (CollectionUtils.isEmpty(processStepAttrModules)) {
            return null;
        }
        DealDetailStructAttrModuleGroupModel dealDetailStructAttrModuleGroupModel = new DealDetailStructAttrModuleGroupModel();
        dealDetailStructAttrModuleGroupModel.setGroupName(config.getTitle());
        dealDetailStructAttrModuleGroupModel.setDealDetailStructAttrModuleVOS(processStepAttrModules);
        return Lists.newArrayList(dealDetailStructAttrModuleGroupModel);
    }

    private List<DealDetailStructAttrModuleVO> getProcessStepAttrModules(Param param, StepConfig config) {
        if (config == null || param == null) {
            return null;
        }
        String stepStr;
        if (config.isFromFirstSku()) {
            List<SkuAttrItemDto> skuAttrItems = DealDetailUtils.getFirstMustGroupFirstSkuAttrList(param.getDealDetailDtoModel());
            stepStr = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, config.getAttrKey());
        } else {
            stepStr = DealDetailUtils.getAttrSingleValueByAttrName(param.getDealAttrs(), config.getAttrKey());
        }
        if (StringUtils.isEmpty(stepStr)) {
            return null;
        }
        List<Map<String, String>> steps = JsonCodec.decode(stepStr, new TypeReference<List<Map<String, String>>>() {
        });
        if (CollectionUtils.isEmpty(steps)) {
            return null;
        }
        return buildProcessStepAttrModules(steps, config);
    }

    private List<DealDetailStructAttrModuleVO> buildProcessStepAttrModules(List<Map<String, String>> steps, StepConfig config) {
        List<DealDetailStructAttrModuleVO> processStepAttrModules = new ArrayList<>();
        steps.forEach(step -> {
            if (MapUtils.isEmpty(step)) {
                return;
            }
            String stepNameKey = step.get(config.getStepNameKey());
            String stepDescKey = step.get(config.getStepDescKey());
            if (StringUtils.isEmpty(stepNameKey) && StringUtils.isEmpty(stepDescKey)) {
                return;
            }
            DealDetailStructAttrModuleVO processStepAttrModule = new DealDetailStructAttrModuleVO();
            if (StringUtils.isNotEmpty(stepNameKey)) {
                processStepAttrModule.setAttrName(stepNameKey);
            }
            if (StringUtils.isNotEmpty(stepDescKey)) {
                processStepAttrModule.setAttrValues(Lists.newArrayList(stepDescKey));
            }
            if (CollectionUtils.isNotEmpty(config.getStepAttrs())) {
                List<DealDetailAttrDescVO> attrDescVOS = config.getStepAttrs().stream()
                        .map(item -> buildAttrDescVO(step, item.getStepAttrKey(), item.getStepAttrFormat()))
                        .filter(Objects::nonNull).collect(Collectors.toList());
                processStepAttrModule.setDesc(attrDescVOS);
            }
            processStepAttrModules.add(processStepAttrModule);
        });
        return processStepAttrModules;
    }

    private DealDetailAttrDescVO buildAttrDescVO(Map<String, String> step, String stepAttrKey, String stepAttrFormat) {
        if (StringUtils.isEmpty(stepAttrKey) || StringUtils.isEmpty(step.get(stepAttrKey))) {
            return null;
        }
        String stepAttrValue = step.get(stepAttrKey);
        DealDetailAttrDescVO attrDescVO = new DealDetailAttrDescVO();
        attrDescVO.setTitle(StringUtils.isEmpty(stepAttrFormat) ? stepAttrValue : String.format(stepAttrFormat, stepAttrValue));
        return attrDescVO;
    }

    @Data
    @VPointCfg
    public static class StepConfig {
        //标题
        private String title;
        //属性key
        private String attrKey;
        //步骤名称属性key
        private String stepNameKey;
        //步骤描述属性key
        private String stepDescKey;
        //是否从第一个sku的货属性中拿服务步骤
        private boolean fromFirstSku = false;
        //步骤额外属性列表
        private List<StepAttrModule> stepAttrs;
    }

    @Data
    static private class StepAttrModule {
        //步骤额外属性key，例如时长
        private String stepAttrKey;
        //步骤额外属性format，例如"%s分钟"
        private String stepAttrFormat;
    }

}
