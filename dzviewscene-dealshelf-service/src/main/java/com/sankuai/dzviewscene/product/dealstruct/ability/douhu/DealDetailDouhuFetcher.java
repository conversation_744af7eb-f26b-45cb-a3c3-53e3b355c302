package com.sankuai.dzviewscene.product.dealstruct.ability.douhu;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.douhu.absdk.bean.DouHuRequest;
import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailConstants;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/4 11:45 上午
 */
@Ability(code = DealDetailDouhuFetcher.CODE,
        name = "团购详情模块斗斛能力",
        description = "团购详情模块斗斛能力",
        activities = { DealDetailActivity.CODE}
)
public class DealDetailDouhuFetcher extends PmfAbility<List<DouhuResultModel>, DealDetailDouhuFetcherParam, DealDetailDouhuFetcherCfg> {

    public static final String CODE = "dealDetailDouhuFetcher";

    @Resource
    private AtomFacadeService atomFacadeService;

    @Override
    public CompletableFuture<List<DouhuResultModel>> build(ActivityCxt ctx, DealDetailDouhuFetcherParam dealDetailDouhuFetcherParam, DealDetailDouhuFetcherCfg dealDetailDouhuFetcherCfg) {
        if (dealDetailDouhuFetcherCfg == null) {
            return CompletableFuture.completedFuture(null);
        }
        ActivityContext activityContext = ActivityCtxtUtils.toActivityContext(ctx);
        int platform = ParamsUtil.getIntSafely(activityContext, DealDetailConstants.Params.platform);
        String mpSource = ParamsUtil.getStringSafely(activityContext, ProductDetailActivityConstants.Params.mpSource);
        Map<String, String> expId2HitskMap = getExpId2HitskMap(dealDetailDouhuFetcherCfg, platform);
        if (MapUtils.isEmpty(expId2HitskMap)) {
            return CompletableFuture.completedFuture(null);
        }
        List<String> expIds = filterExpIds(expId2HitskMap.keySet(), mpSource, dealDetailDouhuFetcherCfg.getIgnoreExps());
        if (CollectionUtils.isEmpty(expIds)) {
            return CompletableFuture.completedFuture(null);
        }
        String unionId = ctx.getParam(DealDetailConstants.Params.unionId);
        String deviceId = ctx.getParam(DealDetailConstants.Params.deviceId);
        CompletableFuture<List<DouHuResponse>> douHuResponseListFuture = batchQueryABTest(platform, unionId, deviceId, expIds);
        Map<String, String> finalExpId2HitskMap = expId2HitskMap;
        return douHuResponseListFuture.thenCompose(douHuResponseList -> convertDouHuRespToDouhuResultModelList(douHuResponseList, finalExpId2HitskMap));
    }

    private List<String> filterExpIds(Set<String> keySet, String mpSource, List<String> ignoreExps) {
        if (PlatformUtil.isAnyXcx(mpSource) && CollectionUtils.isNotEmpty(ignoreExps)) {
            //小程序场景过滤实验
            return keySet.stream().filter(exp -> !ignoreExps.contains(exp)).collect(Collectors.toList());
        }
        return new ArrayList<>(keySet);
    }

    private Map<String, String> getExpId2HitskMap(DealDetailDouhuFetcherCfg dealDetailDouhuFetcherCfg, int platform) {
        if (PlatformUtil.isMT(platform)) {
            return dealDetailDouhuFetcherCfg.getMtExpId2HitSkMap();
        }
        return dealDetailDouhuFetcherCfg.getDpExpId2HitSkMap();
    }

    private CompletableFuture<List<DouhuResultModel>> convertDouHuRespToDouhuResultModelList(List<DouHuResponse> douHuResponseList, Map<String, String> expId2HitskMap) {
        if (CollectionUtils.isEmpty(douHuResponseList)) {
            return CompletableFuture.completedFuture(null);
        }
        douHuResponseList = douHuResponseList.stream().filter(douHuResp -> douHuResp != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(douHuResponseList)) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.completedFuture(buildDouhuResultModelList(douHuResponseList, expId2HitskMap));
    }

    private List<DouhuResultModel> buildDouhuResultModelList(List<DouHuResponse> douHuResponseList, Map<String, String> expId2HitskMap) {
        if (MapUtils.isEmpty(expId2HitskMap)) {
            return null;
        }
        List<DouhuResultModel> douHuModelList = new ArrayList<>();
        douHuResponseList.forEach(douHuResp -> {
            DouhuResultModel douhuResultModel = new DouhuResultModel();
            douhuResultModel.setExpId(douHuResp.getExpId());
            douhuResultModel.setSk(douHuResp.getSk());
            String hitSk = expId2HitskMap.get(douHuResp.getExpId());
            douhuResultModel.setHitSk(StringUtils.isNotEmpty(douHuResp.getSk()) && douHuResp.getSk().equals(hitSk));
            douhuResultModel.setAbQueryId(douHuResp.getAbQueryId());
            douHuModelList.add(douhuResultModel);
        });
        return douHuModelList;
    }

    private CompletableFuture<List<DouHuResponse>> batchQueryABTest(int platform, String unionId, String deviceId, List<String> expIds) {
        List<CompletableFuture<DouHuResponse>> douHuResponseFutures = new ArrayList<>();
        expIds.forEach(expId -> {
            DouHuRequest douHuRequest = buildDouHuRequest(platform, unionId, deviceId, expId);
            CompletableFuture<DouHuResponse> douHuResponseFuture = atomFacadeService.getPoiABTest(douHuRequest);
            douHuResponseFutures.add(douHuResponseFuture);
        });
        return assemble(douHuResponseFutures);
    }

    private <T> CompletableFuture<List<T>> assemble(List<CompletableFuture<T>> futures) {
        CompletableFuture<Void> allDoneFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        return allDoneFuture.thenApply(v -> futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
    }

    private DouHuRequest buildDouHuRequest(int platform, String unionId, String deviceId, String expId) {
        DouHuRequest douHuRequest = new DouHuRequest();
        douHuRequest.setExpId(expId);
        douHuRequest.setUnionId(unionId);
        if (PlatformUtil.isMT(platform)) {
            douHuRequest.setUuid(deviceId);
        } else {
            douHuRequest.setDpid(deviceId);
        }
        return douHuRequest;
    }
}
