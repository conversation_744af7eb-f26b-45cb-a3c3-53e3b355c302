package com.sankuai.dzviewscene.product.filterlist.option.builder.product.priceabovetags;


import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceAboveTagVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@VPointOption(name = "通用门店信息价格前标签",
        description = "标品返回可用门店数量，团单返回最近可用门店信息",
        code = ShopInfoPriceAboveTagsOpt.CODE)
public class ShopInfoPriceAboveTagsOpt extends ProductPriceAboveTagVP<ShopInfoPriceAboveTagsOpt.Config> {

    public static final String CODE = "shopInfoPriceAboveTags";
    public static final int HOT_SPU_PRODUCT_ID_TYPE = 10003;


    @Override
    public List<DzTagVO> compute(ActivityCxt activityCxt, Param param, Config config) {
        if (param.getProductM() == null) {
            return Lists.newArrayList();
        }
        ProductM productM = param.getProductM();
        return processTag(productM, activityCxt, config);
    }

    private List<DzTagVO> processTag(ProductM productM, ActivityCxt activityCxt, Config config) {
        List<DzTagVO> tags = Lists.newArrayList();
        // 处理标品
        if (productM.getProductType() == ProductTypeEnum.GENERAL_SPU.getType()) {
            return buildAvailableShopTag(productM, config);
        }
        return tags;
    }
    private DzTagVO buildApplyShopNumTag(ProductM productM, Config config) {
        // 团单适用门店数需要从attr指定属性中取
        int applyShopNum = getApplyShopNum(productM, config);
        String applyShopNumStr = processApplyShopNum(applyShopNum);
        if (StringUtils.isEmpty(applyShopNumStr)) {
            return null;
        }
        String tagText = String.format(config.getApplyShopNumTemplate(), applyShopNumStr);
        return createBaseTagWithValue(tagText);
    }

    private int getApplyShopNum(ProductM productM, Config config) {
        if (productM.getProductType() == ProductTypeEnum.DEAL.getType() && StringUtils.isNotEmpty(config.getDealApplyShopAttrKey())) {
            return getIntValFromStr(productM.getAttr(config.getDealApplyShopAttrKey()));
        } else if (productM.getProductType() == ProductTypeEnum.GENERAL_SPU.getType()) {
            return productM.getShopNum();
        }
        return 0;
    }

    private String processApplyShopNum(int applyShopNum) {
        if (applyShopNum <= 0) {
            return null;
        } else if (applyShopNum <= 49) {
            return String.valueOf(applyShopNum);
        } else if (applyShopNum <= 99) {
            return applyShopNum / 10 * 10 + "+";
        } else if (applyShopNum <= 999) {
            return applyShopNum / 100 * 100 + "+";
        } else {
            return applyShopNum / 1000 * 1000 + "+";
        }
    }

    private int getIntValFromStr(String str) {
        if (!StringUtils.isNumeric(str) || StringUtils.isEmpty(str)) {
            return 0;
        }
        return Integer.parseInt(str);
    }

    private List<DzTagVO> buildAvailableShopTag(ProductM productM, Config config) {
        DzTagVO shopNumTag = buildApplyShopNumTag(productM, config);
        DzTagVO nearestDistTag = buildDistTag(productM);
        return filterNonNullTags(shopNumTag, nearestDistTag);
    }

    private List<DzTagVO> filterNonNullTags(DzTagVO... tags) {
        return Lists.newArrayList(tags).stream().filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private DzTagVO buildDistTag(ProductM productM) {
        String distStr = getDistanceString(productM);
        return createBaseTagWithValue(distStr);
    }

    private String getDistanceString(ProductM productM) {

        if (productM.getProductType() == ProductTypeEnum.DEAL.getType() && hasNearestShop(productM)) {
            return productM.getShopMs().get(0).getDistance();
        } else if (isHotSpuProduct(productM) && hasNearestShop(productM)) {
            return productM.getShopMs().get(0).getDistance();
        } else if (isHotSpuProduct(productM) || isGeneralSpuNotHotProduct(productM)) {
            return productM.getNearestShopDesc();
        }
        return null;
    }

    private boolean hasNearestShop(ProductM productM) {
        return CollectionUtils.isNotEmpty(productM.getShopMs()) && productM.getShopMs().get(0) != null;
    }

    private boolean isHotSpuProduct(ProductM productM) {
        if (productM == null || productM.getProductType() <= 0) {
            return false;
        }
        int productType = productM.getProductType();
        int productIdType = productM.getId() != null ? productM.getId().getType() : 0;
        return productType == ProductTypeEnum.GENERAL_SPU.getType() && productIdType == HOT_SPU_PRODUCT_ID_TYPE;
    }

    private boolean isGeneralSpuNotHotProduct(ProductM productM) {
        if (productM == null || productM.getProductType() <= 0) {
            return false;
        }
        int productType = productM.getProductType();
        int productIdType = productM.getId() != null ? productM.getId().getType() : 0;
        return productType == ProductTypeEnum.GENERAL_SPU.getType() && productIdType != HOT_SPU_PRODUCT_ID_TYPE;
    }

    private DzTagVO createBaseTagWithValue(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        DzTagVO dzTagVO = new DzTagVO();
        dzTagVO.setText(name);
        return dzTagVO;
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 适用门店数量模板
         */
        private String applyShopNumTemplate = "%s家门店通用";

        /**
         * 最近可用门店模板
         */
        private String nearestShopTemplate = "%s";

        /**
         * 前置图标链接
         */
        private String preIconUrl;

        /**
         * 前置图片宽高比
         */
        private double preAspectRadio;

        /**
         * 前置图片高
         */
        private int prePicHeight;

        /**
         * 团单适用门店数量属性key
         */
        private String dealApplyShopAttrKey;

    }
}
