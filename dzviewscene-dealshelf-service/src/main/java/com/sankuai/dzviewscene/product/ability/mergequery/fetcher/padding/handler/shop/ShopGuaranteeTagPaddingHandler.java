package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.shop;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ContextHandlerResult;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.mpproduct.publish.common.enums.TerminalTypeEnum;
import com.sankuai.nib.price.operation.api.common.dto.SessionContextDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.GuaranteeObjectQueryDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.UserInfoDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.ChannelDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.QueryTagOptionDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.request.BatchQueryGuaranteeTagRequest;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.service.GuaranteeQueryService;
import com.sankuai.nib.price.operation.common.guarantee.enums.ReturnModeEnum;
import com.sankuai.nib.sp.common.enums.Owner;
import com.sankuai.nib.sp.common.enums.TradeType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;
import com.sankuai.nib.price.operation.common.guarantee.enums.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2024/1/10 11:07
 */
@Component
public class ShopGuaranteeTagPaddingHandler implements ContextPaddingHandler {

    @RpcClient(remoteAppkey = "com.sankuai.priceoperation.service")
    private GuaranteeQueryService guaranteeQueryService;

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<ContextHandlerResult> padding(ActivityCxt ctx, ContextHandlerResult contextHandlerResult, Map<String, Object> params) {
        CompletableFuture<BatchQueryGuaranteeTagRequest> requestClf = buildRequestClf(ctx);
        return requestClf.thenCompose(request -> batchQueryProductGuarantee(buildSessionContext(), request)).thenApply(response -> {
            fillContextHandlerResult(response, contextHandlerResult);
            return contextHandlerResult;
        });
    }

    public void fillContextHandlerResult(List<ObjectGuaranteeTagDTO> objectGuaranteeTagDTOList, ContextHandlerResult contextHandlerResult) {
        if (CollectionUtils.isEmpty(objectGuaranteeTagDTOList)) {
            return;
        }
        if (Objects.isNull(objectGuaranteeTagDTOList.get(0)) || Objects.isNull(objectGuaranteeTagDTOList.get(0).getBestPriceGuaranteeTagDTO())) {
            return;
        }
        contextHandlerResult.setShopHasShopGuaranteeTag(objectGuaranteeTagDTOList.get(0).getBestPriceGuaranteeTagDTO().getValid());
    }

    public CompletableFuture<BatchQueryGuaranteeTagRequest> buildRequestClf(ActivityCxt ctx) {
        long dpUserId = ParamsUtil.getLongSafely(ctx, ShelfActivityConstants.Params.dpUserId);
        long mtUserId = ParamsUtil.getLongSafely(ctx, ShelfActivityConstants.Params.mtUserId);

        return convertMtShopID(ctx).thenApply(mtShopId -> {
            BatchQueryGuaranteeTagRequest request = new BatchQueryGuaranteeTagRequest();
            request.setObjects(new HashSet<>(Lists.newArrayList(GuaranteeObjectQueryDTO.poi(mtShopId))));
            Long userId = PlatformUtil.isMT(ParamsUtil.getIntSafely(ctx, PmfConstants.Params.platform)) ? mtUserId : dpUserId;
            UserInfoDTO userInfo = new UserInfoDTO();
            userInfo.setUserId(userId);
            ChannelDTO channel = new ChannelDTO();
            channel.setPlatform(ParamsUtil.getIntSafely(ctx, PmfConstants.Params.platform) == 1 ? PlatformEnum.DP_PLATFORM.getCode() : PlatformEnum.MT_PLATFORM.getCode());
            channel.setTerminalType(TerminalTypeEnum.APP.getValue());
            channel.setChannelNos(Lists.newArrayList(ChannelNoEnum.MAIN_LINK.getCode()));
            userInfo.setChannel(channel);
            request.setUserInfo(userInfo);
            request.setGuaranteeTypes(Sets.newHashSet(GuaranteeTypeEnum.BEST_PRICE_GUARANTEE.getCode(), GuaranteeTypeEnum.PRICE_PROTECTION.getCode()));
            QueryTagOptionDTO queryTagOption = new QueryTagOptionDTO();
            queryTagOption.setReturnMode(ReturnModeEnum.PRIORITY_SORT_ONE_GROUP_BY_GUARANTEE_TYPE.getCode());
            request.setQueryTagOption(queryTagOption);
            return request;
        });
    }

    private SessionContextDTO buildSessionContext() {
        SessionContextDTO sessionContext = new SessionContextDTO();
        sessionContext.setOwner(Owner.NIB_GENERAL.getValue());
        sessionContext.setTradeType(TradeType.GROUPBUY_PAY.getCode());
        return sessionContext;
    }

    private CompletableFuture<Long> convertMtShopID(ActivityCxt ctx) {
        long dpShopId = PoiIdUtil.getDpPoiIdL(ctx, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
        long mtShopId = PoiIdUtil.getMtPoiIdL(ctx, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
        // 美团
        if (ParamsUtil.getIntSafely(ctx, PmfConstants.Params.platform) == VCPlatformEnum.MT.getType()) {
            return CompletableFuture.completedFuture(mtShopId);
        }
        // 点评
        return compositeAtomService.getMtByDpPoiIdL(dpShopId).thenApply(id -> Optional.ofNullable(id).orElse(0L));
    }

    /**
     * 查询门店的买贵必赔信息(不是团单的)
     *
     * @param sessionContext
     * @param request
     * @return
     */
    public CompletableFuture<List<ObjectGuaranteeTagDTO>> batchQueryProductGuarantee(SessionContextDTO sessionContext, BatchQueryGuaranteeTagRequest request) {
        try {
            return AthenaInf.getRpcCompletableFuture(guaranteeQueryService.batchQueryGuaranteeTag(sessionContext, request))
                    .thenApply(response -> {
                        if (response == null || response.isNotSuccess() || CollectionUtils.isEmpty(response.getData())) {
                            return null;
                        }
                        return response.getData();
                    }).exceptionally(ex -> {
                        Cat.logEvent("batchQueryProductGuarantee error", request.toString());
                        return null;
                    });
        } catch (TException e) {
            Cat.logEvent("batchQueryProductGuarantee error ", request.toString());
            return null;
        }
    }
}
