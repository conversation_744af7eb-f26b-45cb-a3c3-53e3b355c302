package com.sankuai.dzviewscene.product.filterlist.ability.builder.filter;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityRequest;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.DealListModelAssembler;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.filter.vp.FilterEndInterceptVP;
import com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivity;
import com.sankuai.dzviewscene.product.filterlist.model.DealFilterListM;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterBtnVO;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.shelf.utils.ShelfErrorUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2022/5/1
 */
@Ability(code = DealFilterBuilder.CODE,
        name = "VO-筛选构造",
        description = "筛选构造。构造 List<DzFilterVO>",
        activities = {DealFilterListActivity.CODE},
        dependency = {DealListModelAssembler.CODE}
)
public class DealFilterBuilder extends PmfAbility<List<DzFilterVO>, DealFilterBuilder.Request, DealFilterBuilder.Config> {

    public static final String CODE = "DealFilterBuilder";

    @Override
    public CompletableFuture<List<DzFilterVO>> build(ActivityCxt ctx, Request request, Config config) {
        DealFilterListM dealFilterListM = ctx.getSource(DealListModelAssembler.CODE);
        if (dealFilterListM == null) {
            return CompletableFuture.completedFuture(new ArrayList<>());
        }
        Map<String, FilterM> filterMap = dealFilterListM.getFilterMs();
        if (MapUtils.isEmpty(filterMap)) {
            return CompletableFuture.completedFuture(new ArrayList<>());
        }
        List<DzFilterVO> filterList = new ArrayList<>();
        for (Map.Entry<String, FilterM> filterEntry : filterMap.entrySet()) {
            if (filterEntry.getValue() == null) {
                continue;
            }
            filterList.add(buildDzFilterVO(ctx, filterEntry.getValue(), config));
        }
        endIntercept(ctx, filterList, request);
        return CompletableFuture.completedFuture(filterList);
    }

    private DzFilterVO buildDzFilterVO(ActivityCxt activityCxt, FilterM filterM, Config config) {
        DzFilterVO filterVO = new DzFilterVO();
        filterVO.setType(filterM.getType());
        filterVO.setId(filterM.getId());
        filterVO.setLink(filterM.getLink());
        filterVO.setName(filterM.getName());
        filterVO.setSelected(filterM.isSelected());
        filterVO.setExtra(buildExtra(filterM.getExtra()));
        filterVO.setChildren(buildFilterBtnList(activityCxt, filterM.getType(), filterM.getFilters()));
        if(config != null) {
            filterVO.setMinShowNum(config.getGlobalMinShowNum());
        }
        return filterVO;
    }

    private String buildExtra(Map<String, Object> extra) {
        return MapUtils.isEmpty(extra) ? "" : JsonCodec.encode(extra);
    }

    private List<DzFilterBtnVO> buildFilterBtnList(ActivityCxt activityCxt, int type, List<FilterBtnM> filterBtns) {
        if (CollectionUtils.isEmpty(filterBtns)) {
            return Lists.newArrayList();
        }
        int fatherType;
        List<DzFilterBtnVO> dzFilterBtnVOs = new ArrayList<>();
        for (FilterBtnM filterBtnM : filterBtns) {
            fatherType = filterBtnM.getType() > 0 ? filterBtnM.getType() : type;
            DzFilterBtnVO dzFilterBtnVO = new DzFilterBtnVO();
            if (CollectionUtils.isNotEmpty(filterBtnM.getChildren())) {
                dzFilterBtnVO.setChildren(buildFilterBtnList(activityCxt, fatherType, filterBtnM.getChildren()));
            }
            dzFilterBtnVO.setFilterId(filterBtnM.getFilterId());
            dzFilterBtnVO.setName(filterBtnM.getTitle());
            dzFilterBtnVO.setSubTitle(filterBtnM.getSubTitle());
            dzFilterBtnVO.setType(filterBtnM.getType() > 0 ? filterBtnM.getType() : type);
            dzFilterBtnVO.setSelectable(true);
            dzFilterBtnVO.setTag(filterBtnM.getTag());
            dzFilterBtnVO.setMultiSelect(filterBtnM.isMultiSelect());
            dzFilterBtnVO.setSelected(filterBtnM.isSelected());
            dzFilterBtnVO.setExtra(filterBtnM.getExtra());

            dzFilterBtnVOs.add(dzFilterBtnVO);
        }
        return dzFilterBtnVOs;
    }

    /**
     * 结束时最后对整个对象的修改，一般结构调整可以放这里
     */
    private void endIntercept(ActivityCxt activityCxt, List<DzFilterVO> filterList, Request request) {
        try {
            FilterEndInterceptVP<?> varyPoint = findVPoint(activityCxt, FilterEndInterceptVP.CODE);
            varyPoint.execute(activityCxt,
                    FilterEndInterceptVP.Param.builder().filterList(filterList).userAgent(request.getUserAgent()).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
        }
    }

    @AbilityCfg
    @Data
    public static class Config {
        /**
         * 最小展示数量
         */
        private int globalMinShowNum;
    }

    @AbilityRequest
    @Data
    public static class Request {
        /**
         * {@link ShelfActivityConstants.Params#userAgent}
         */
        private int userAgent;
    }
}
