package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.bean.copy.processor.internal.util.Collections;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListAfterProcessingVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/8 5:29 下午
 */
@VPointOption(name = "疫苗sku列表后置处理变化点", description = "疫苗sku列表后置处理变化点",code = VaccineSkuListAfterPricessingOpt.CODE, isDefault = false)
public class VaccineSkuListAfterPricessingOpt extends SkuListAfterProcessingVP<VaccineSkuListAfterPricessingOpt.Config> {

    public static final String CODE = "VaccineSkuListAfterPricessingOpt";

    private static final String VACCINE_EXTRA_COST_PRICE_ATTR_NAME = "VaccineExtraCostPrice";

    private static final String VACCINE_SUPPORTING_PROJECT_ATTR_NAME = "VaccineSupportingProject";

    @Override
    public List<DealSkuVO> compute(ActivityCxt context, Param param, Config config) {
        List<DealSkuVO> dealSkuVOS = param.getDealSkuVOS();
        if (CollectionUtils.isEmpty(dealSkuVOS)) {
            return null;
        }
        List<AttrM> dealAttrs = param.getDealAttrs();
        //给原来的sku添加额外费用说明信息
        addExtraCostDesc(Collections.first(dealSkuVOS), dealAttrs);
        //添加配套项目sku
        DealSkuVO supportingProjectDealSkuVO = buildSupportingProjectDealSkuVO(dealAttrs, config);
        if (supportingProjectDealSkuVO != null) {
            dealSkuVOS.add(supportingProjectDealSkuVO);
        }
        return dealSkuVOS;
    }

    /**
     * 给sku添加额外费用说明desc信息
     *@param
     *@return
     */
    private void addExtraCostDesc(DealSkuVO dealSkuVO, List<AttrM> dealAttrs) {
        if (dealSkuVO == null || CollectionUtils.isEmpty(dealAttrs)) {
            return;
        }
        String extraCostDesc = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, VACCINE_EXTRA_COST_PRICE_ATTR_NAME);
        dealSkuVO.setDesc(extraCostDesc);
    }

    /**
     * 构造配套项目sku
     *@param
     *@return
     */
    private DealSkuVO buildSupportingProjectDealSkuVO(List<AttrM> dealAttrs, Config config) {
        String vaccineSupportingProjectJson = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, VACCINE_SUPPORTING_PROJECT_ATTR_NAME);
        if (StringUtils.isEmpty(vaccineSupportingProjectJson)) {
            return null;
        }
        List<VaccineSupportingProjecItemModel> vaccineSupportingProjecItemModels = JsonCodec.converseList(vaccineSupportingProjectJson, VaccineSupportingProjecItemModel.class);
        if (CollectionUtils.isEmpty(vaccineSupportingProjecItemModels)) {
            return null;
        }
        List<DealSkuItemVO> dealSkuItemVOS = vaccineSupportingProjecItemModels.stream().map(item -> buildDealSkuItemVO(item.getProjectName(), item.getCount(), config)).filter(vo -> vo != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        return buildDealSkuVO(dealSkuItemVOS, config);
    }

    private DealSkuItemVO buildDealSkuItemVO(String name, String value, Config config) {
        if (StringUtils.isEmpty(name) || StringUtils.isEmpty(value)) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(name);
        List<SkuAttrAttrItemVO> skuAttrAttrItemVOS = buildSkuAttrAttrItemVO(value, config);
        dealSkuItemVO.setValueAttrs(skuAttrAttrItemVOS);
        dealSkuItemVO.setType(5);
        return dealSkuItemVO;
    }

    /**
     * 构造数量SkuAttrAttrItemVO
     *@param
     *@return
     */
    private List<SkuAttrAttrItemVO> buildSkuAttrAttrItemVO(String count, Config config) {
        if (StringUtils.isEmpty(count)) {
            return null;
        }
        SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
        skuAttrAttrItemVO.setInfo(Lists.newArrayList(count));
        if (StringUtils.isNotEmpty(config.getSupportingProjectSkuCountFormat())) {
            skuAttrAttrItemVO.setInfo(Lists.newArrayList(String.format(config.getSupportingProjectSkuCountFormat(), count)));
        }
        return Lists.newArrayList(skuAttrAttrItemVO);
    }

    private DealSkuVO buildDealSkuVO(List<DealSkuItemVO> dealSkuItemVOS, Config config) {
        if (CollectionUtils.isEmpty(dealSkuItemVOS)) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(config.getSupportingProjectSkuName());
        dealSkuVO.setItems(dealSkuItemVOS);
        return dealSkuVO;
    }



    @Data
    @VPointCfg
    public static class Config {
        private String supportingProjectSkuName;
        private String supportingProjectSkuCountFormat;
    }

    @Data
    static private class VaccineSupportingProjecItemModel {
        private String count;
        private String projectName;
    }
}
