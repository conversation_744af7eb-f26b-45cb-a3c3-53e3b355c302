package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 召回处理器(新框架使用)
 */
public interface QueryHandler {

    /**
     * 填充一组商品
     *
     * @param ctx
     * @param groupName
     * @param params
     * @return
     */
    CompletableFuture<ProductGroupM> query(ActivityCxt ctx,  String groupName, Map<String, Object> params);
}
