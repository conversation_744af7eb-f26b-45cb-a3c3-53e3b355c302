package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.product.filterlist.vo.DzActivityTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/02/23
 */
@VPoint(name = "填充商品活动信息", description = "商品活动信息，包括推荐、特惠角标", code = ProductActivityTagsVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductActivityTagsVP<T> extends PmfVPoint<List<DzActivityTagVO>, ProductActivityTagsVP.Param, T> {
    public static final String CODE = "ProductActivityTagsVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;

        private int platform;

        private long filterId;
    }
}
