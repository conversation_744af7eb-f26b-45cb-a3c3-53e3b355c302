package com.sankuai.dzviewscene.product.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 团购二级分类，入参团单ID
 * http://publish.sankuai.com
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum DealCategoryEnum {
    PET_HOSPITAL(1702, "宠物医院"),
    PET_SHOP(1701, "宠物店"),
    CLEANING_WASH(409, "保洁清洗"),
    ;

    private final int code;

    private final String desc;


    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

