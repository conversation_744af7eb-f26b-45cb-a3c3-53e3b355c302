package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.beauty.fundamental.base.common.util.Optional;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.TaiJiProjectListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.TaiJiProjectProcessAttrModel;
import com.sankuai.dzviewscene.product.dealstruct.model.TaiJiProjectProcessAttrValueModel;
import com.sankuai.dzviewscene.product.dealstruct.model.TaiJiProjectProcessModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.*;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO;
import com.sankuai.general.product.query.center.client.enums.StandardAttrValueTypeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.stream.Collectors;

@VPointOption(name = "中医sku属性列表变化点", description = "中医sku属性列表变化点，根据配置展示sku属性,只针对太极团单", code = ZhongyiSkuAttrListOpt.CODE)
@Slf4j
public class ZhongyiSkuAttrListOpt extends TaiJiProjectListVP<ZhongyiSkuAttrListOpt.Config> {

    private static final String[] chineseNums = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
    private static final String ten = "十";

    public static final String CODE = "ZhongyiSkuAttrListOpt";

    private static final String SERVICE_PROCESS_ARRAY_NEW = "serviceProcessArrayNew";

    //服务部位
    private static final String SERVICE_PART = "Number_of_parts";

    //适用部位
    private static final String APPLICABLE_PARTS = "Applicable_parts";

    private static final String SHOW_DIRECT = "showDirect";

    private static final String SHOW_ALIAS = "showAlias";

    private static final String SPLICE_STR = "spliceStr";


    @Override
    public List<DealSkuItemVO> compute(ActivityCxt context, Param param, Config config) {
        if (config == null || param == null || config.configs == null) {
            return null;
        }
        StandardServiceProjectItemDTO taiJiProjectItem = param.getTaiJiProjectItem();
        StandardAttributeDTO standardAttribute = taiJiProjectItem.getStandardAttribute();
        if (standardAttribute == null) {
            return null;
        }
        List<DealSkuItemVO> dealSkuItemVOS = new ArrayList<>();
        List<ZhongyiSkuAttrListOpt.DisplayRuleCfg> displayRuleCfgs = Optional.ofNullable(config)
                .map(ZhongyiSkuAttrListOpt.Config::getConfigs)
                .orElse(new ArrayList<>());

        displayRuleCfgs.stream().forEach(cfg -> {
            if (Objects.equals(cfg.key, SERVICE_PROCESS_ARRAY_NEW)) {
                //针对服务流程特殊处理
                DealSkuItemVO projectItemVO = buildProjectItemVO(cfg, standardAttribute, dealSkuItemVOS);
                dealSkuItemVOS.add(projectItemVO);
            } else {
                DealSkuItemVO dealSkuItemVO = buildDealSkuItemVO(cfg, standardAttribute, null);
                if (dealSkuItemVO == null) {
                    return;
                }
                dealSkuItemVOS.add(dealSkuItemVO);
            }

        });
        return dealSkuItemVOS;
    }

    private DealSkuItemVO buildProjectItemVO(DisplayRuleCfg cfg, StandardAttributeDTO standardAttribute, List<DealSkuItemVO> dealSkuItemVOS) {
        if (!Objects.equals(cfg.getKey(), SERVICE_PROCESS_ARRAY_NEW)) {
            return new DealSkuItemVO();
        }
        String serviceProcessArrayNew = getTaiJiAttrValueByName(standardAttribute, cfg.getKey());
        if (StringUtils.isEmpty(serviceProcessArrayNew)) {
            return new DealSkuItemVO();
        }
        List<TaiJiProjectProcessModel> taiJiProjectProcessModels = JsonCodec.converseList(serviceProcessArrayNew, TaiJiProjectProcessModel.class);
        if (CollectionUtils.isEmpty(taiJiProjectProcessModels)) {
            return new DealSkuItemVO();
        }
        //服务步骤数量
        int count = taiJiProjectProcessModels.size();
        if (count <= 0) {
            return new DealSkuItemVO();
        }

        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();

        dealSkuItemVO.setType(0);
        dealSkuItemVO.setName(cfg.getDisplayName());
        dealSkuItemVO.setValue("共" + count + "步");


        ProjectProcessCfg projectProcessCfg = cfg.getProjectProcessCfg();
        if (projectProcessCfg == null) {
            return null;
        }

        List<SkuAttrAttrItemVO> valueAttrList = new ArrayList<>();
        for (TaiJiProjectProcessModel taiJiProjectProcessModel : taiJiProjectProcessModels) {
            SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
            skuAttrAttrItemVO.setValues(buildSkuAttrAttrItemValues(taiJiProjectProcessModel, projectProcessCfg));
            skuAttrAttrItemVO.setName(buildSkuAttrAttrItemName(taiJiProjectProcessModel, projectProcessCfg));
            valueAttrList.add(skuAttrAttrItemVO);
        }

        dealSkuItemVO.setValueAttrs(valueAttrList);
        return dealSkuItemVO;
    }

    private String buildSkuAttrAttrItemName(TaiJiProjectProcessModel taiJiProjectProcessModel, ZhongyiSkuAttrListOpt.ProjectProcessCfg config) {
        if (StringUtils.isEmpty(config.contentKey)) {
            return null;
        }
        List<TaiJiProjectProcessAttrModel> attrs = taiJiProjectProcessModel.getAttrs();
        return getAttrValuesByKey(attrs, config.contentKey);
    }

    private List<CommonAttrVO> buildSkuAttrAttrItemValues(TaiJiProjectProcessModel taiJiProjectProcessModel, ZhongyiSkuAttrListOpt.ProjectProcessCfg config) {
        if (StringUtils.isEmpty(config.introKey)) {
            return new ArrayList<>();
        }
        List<TaiJiProjectProcessAttrModel> attrs = taiJiProjectProcessModel.getAttrs();
        String s = getAttrValuesByKey(attrs, config.introKey);
        if (StringUtils.isEmpty(s)) {
            return new ArrayList<>();
        }
        List<CommonAttrVO> result = new ArrayList<>();

        CommonAttrVO commonAttrVO = new CommonAttrVO();
        commonAttrVO.setName(s);
        result.add(commonAttrVO);
        return result;
    }

    private String getAttrValuesByKey(List<TaiJiProjectProcessAttrModel> attrs, String key) {
        return Optional.ofNullable(attrs).orElse(Collections.emptyList()).stream()
                .filter(attr -> Objects.nonNull(attr) && key.equals(attr.getAttrName()))
                .findFirst()
                .map(TaiJiProjectProcessAttrModel::getAttrValues)
                .filter(CollectionUtils::isNotEmpty)
                .map(attrValues -> attrValues.get(0))
                .map(TaiJiProjectProcessAttrValueModel::getSimpleValues)
                .filter(CollectionUtils::isNotEmpty)
                .map(simpleValues -> simpleValues.get(0))
                .orElse(null);
    }

    private DealSkuItemVO buildDealSkuItemVO(ZhongyiSkuAttrListOpt.DisplayRuleCfg cfg, StandardAttributeDTO standardAttribute, List<SkuAttrAttrItemVO> valueAttrs) {
        String originalValue = buildTaiJiAttrValue(standardAttribute, cfg);
        if (StringUtils.isEmpty(originalValue)) {
            return null;
        }
        ValueFormatter formatter = getValueFormatter(cfg.getStrategy());

        String value = formatter.format(originalValue, cfg);

        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(cfg.getDisplayName());
        dealSkuItemVO.setValue(value);
        dealSkuItemVO.setValueAttrs(valueAttrs);
        dealSkuItemVO.setType(cfg.getType());
        return dealSkuItemVO;
    }

    private ValueFormatter getValueFormatter(String strategy) {
        switch (strategy) {
            case SHOW_DIRECT:
                return this::showDirect;
            case SHOW_ALIAS:
                return this::containValueThenConvertAlias;
            case SPLICE_STR:
                return this::spliceStr;
            default:
                return this::showDirect;
        }
    }

    private String spliceStr(String value, DisplayRuleCfg cfg) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        String prefix = Optional.ofNullable(cfg.getPrefix()).orElse("");
        String suffix = Optional.ofNullable(cfg.getSuffix()).orElse("");
        return prefix+value+suffix;
    }


    private String containValueThenConvertAlias(String value, ZhongyiSkuAttrListOpt.DisplayRuleCfg cfg) {
        return Optional.ofNullable(value)
                .filter(StringUtils::isNotEmpty)
                .map(v -> Optional.ofNullable(cfg.getDisplayAlias())
                        .map(alias -> alias.get(v))
                        .orElse(null))
                .orElse(null);
    }

    private String showDirect(String value, ZhongyiSkuAttrListOpt.DisplayRuleCfg cfg) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        return value;
    }

    @FunctionalInterface
    private interface ValueFormatter {
        String format(String value, ZhongyiSkuAttrListOpt.DisplayRuleCfg cfg);
    }

    private String buildTaiJiAttrValue(StandardAttributeDTO standardAttribute, ZhongyiSkuAttrListOpt.DisplayRuleCfg cfg) {
        String taiJiAttrValueByName = getTaiJiAttrValueByName(standardAttribute, cfg.getKey());
        //对服务部位做特殊处理，后续可以改为配置化
        if (Objects.equals(cfg.getKey(), SERVICE_PART)) {
            if (("全身").equals(taiJiAttrValueByName)) {
                return "全身";
            }
            if(("单部位").equals(taiJiAttrValueByName)){
                String singlePart = getTaiJiAttrValueByName(standardAttribute, APPLICABLE_PARTS);
                if (StringUtils.isEmpty(singlePart)) {
                    return null;
                }
                String[] strings = singlePart.split("、");
                if (strings.length<=1){
                    return singlePart;
                }
                return singlePart+numberConvert(strings.length)+"选一";

            }
            if (("多部位").equals(taiJiAttrValueByName)) {
                String multiPart = getTaiJiAttrValueByName(standardAttribute, APPLICABLE_PARTS);
                if (StringUtils.isEmpty(multiPart)) {
                    return null;
                }
                return multiPart;
            }
        }
        //其余简单，做直接展示处理
        return taiJiAttrValueByName;
    }

    public String numberConvert(int number) {
        if (number < 0 || number >= 100) {
            throw new IllegalArgumentException("数字超出范围（0-99）");
        }

        if (number < 10) {
            return chineseNums[number];
        } else if (number < 20) {
            return ten + chineseNums[number % 10];
        } else {
            String result = chineseNums[number / 10] + ten;
            if (number % 10 != 0) {
                result += chineseNums[number % 10];
            }
            return result;
        }
    }

    private String getTaiJiAttrValueByName(StandardAttributeDTO standardAttribute, String name) {
        return Optional.ofNullable(standardAttribute)
                .map(StandardAttributeDTO::getAttrs)
                .orElseGet(Collections::emptyList)
                .stream()
                .filter(attr -> name.equals(attr.getAttrName()))
                .findFirst()
                .map(StandardAttributeItemDTO::getAttrValues)
                .orElseGet(Collections::emptyList)
                .stream()
                .findFirst()
                .map(this::extractValuesFromAttribute)
                .orElse(StringUtils.EMPTY);
    }

    private String extractValuesFromAttribute(StandardAttributeValueDTO value) {
        if (value == null) {
            return null;
        }
        if (value.getType() == StandardAttrValueTypeEnum.SIMPLE.getType()) {
            if (value.getSimpleValues() == null) {
                return null;
            }
            String result = value.getSimpleValues().get(0);
            if (isArrayStringType(result)) {
                List<String> strings = JsonCodec.converseList(result, String.class);
                return String.join("、", strings);
            }
            return result;
        } else if (value.getType() == StandardAttrValueTypeEnum.COMPLEX.getType()) {
            return value.getComplexValues();
        }
        return null;
    }

    /*
     * 判读字符串是数组类型还是普通字符串类型，数组类型true
     * */
    public boolean isArrayStringType(String input) {
        if (input == null || input.trim().isEmpty()) {
            return false;
        }
        input = input.trim();
        // 检查是否以 [ 开头和 ] 结尾
        if (input.startsWith("[") && input.endsWith("]")) {
            // 尝试解析为JSON数组
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                objectMapper.readTree(input);
                return true;
            } catch (JsonProcessingException e) {
                log.error("ZhongyiSkuAttrListOpt.isArrayStringType  error, input: {}", input);
                return false;
            }
        }
        return false;
    }


    @Data
    @VPointCfg
    public static class Config {
        private List<ZhongyiSkuAttrListOpt.DisplayRuleCfg> configs;
    }

    @Data
    public static class DisplayRuleCfg {
        //从attr中获取的key，检查项目key：inspection_item
        private String key;

        //展示的name
        private String displayName;

        private String strategy;

        private Map<String, String> displayAlias;

        //类型 0-文案 1-流程类型(按照processDisplayRule展示)
        private int type = 0;

        //服务流程配置
        private ProjectProcessCfg projectProcessCfg;

        private String prefix;

        private String suffix;
    }

    @Data
    public static class ProjectProcessCfg {
        private String contentKey;
        private String introKey;
    }

}
