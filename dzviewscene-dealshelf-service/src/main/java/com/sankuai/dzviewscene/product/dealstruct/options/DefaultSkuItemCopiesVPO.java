package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.SkuCopiesVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints.SkuItemCopiesVP;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/11/26 6:37 下午
 */
@VPointOption(name = "团购详情sku货份数变化点", description = "团购详情sku货份数变化点，支持配置文案Format",code = DefaultSkuItemCopiesVPO.CODE, isDefault = true)
public class DefaultSkuItemCopiesVPO extends SkuItemCopiesVP<DefaultSkuItemCopiesVPO.Config> {

    public static final String CODE = "DefaultSkuItemCopiesVPO";

    private static final String DEFAULT_COPIES_FORMAT = "%s个";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if(param.getSkuItemDto() == null) {
            return null;
        }
        String format = StringUtils.isEmpty(config.getSkuCopiesFormat()) ? DEFAULT_COPIES_FORMAT : config.getSkuCopiesFormat();
        return String.format(format, param.getSkuItemDto().getCopies());
    }

    @Data
    @VPointCfg
    public static class Config {
        private String skuCopiesFormat;
    }
}
