package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.desc.vpoints.DealDetailDescVP;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/28 5:04 下午
 */
@VPointOption(name = "团购详情补充描述组件默认变化点", description = "团购详情补充描述组件默认变化点，支持配置", code = DealDetailDescVPO.CODE, isDefault = true)
public class DealDetailDescVPO extends DealDetailDescVP<DealDetailDescVPO.Config> {

    public static final String CODE = "DealDetailDescVPO";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (param == null) {
            return null;
        }
        return param.getDesc();
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
