package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.utils.DealAttrUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.VideoModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: xiongyonghong
 * @create: 2024-09-14
 * @description:
 **/
@VPointOption(name = "通用摄影构造DealAttrVO列表变化点", description = "通用摄影构造DealAttrVO列表变化点", code = CommonPhotoDealAttrVOListOpt.CODE, isDefault = false)
@Slf4j
public class CommonPhotoDealAttrVOListOpt extends DealAttrVOListVP<CommonPhotoDealAttrVOListOpt.Config> {

    public static final String CODE = "CommonPhotoDealAttrVOListOpt";
    public static final List<String> KEY_WITH_LIST_VALUE = Lists.newArrayList("Makeup_brand", "photoEnvInfo", "ProvideClothingObjects", "photoBackground","CosmeticsBrand","applyscene",  "availablePeopleNum", "photoPlateGrant", "photoOutput");

    private static final String INTENSIVE_REPAIR_NUM = "intensiveRepairNum";

    private static final String INTENSIVE_REPAIR_NUM_DESC = "intensiveRepairNum_Desc";

    private static final String FINISHED_DELIVERABLE_CONNECTOR = "×";

    private static final String ZERO_VALUE_WITH_UNIT = "0张";

    private static final String ZERO_VALUE = "0";

    private  static final String LEFT_SQUARE_BRACKETS = "[";

    private  static final String RIGHT_SQUARE_BRACKETS = "]";

    private  static final String SEPERATOR = "、";

    private static final String CONTAIN_STAFF = "ContainStaff";

    private  static final List<String> KEY_WITH_LIST_WITHOUT_SPLIT = Lists.newArrayList("photo_style", "Locations_available") ;
    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        if (config == null || param == null) {
            return null;
        }
        List<AttrM> attrs = new ArrayList<>();

        //1.获取第一个sku属性  服务项目
        List<AttrM> firstSkuAttrs = DealAttrUtils.extractFirstSkuAttrFromDealDetailDtoModel(param.getDealDetailDtoModel());
        if (CollectionUtils.isNotEmpty(firstSkuAttrs)) {
            attrs.addAll(firstSkuAttrs);
        }
        //2.获取团单属性   行业属性
        List<AttrM> dealAttrs = param.getDealAttrs();
        if (CollectionUtils.isNotEmpty(dealAttrs)) {
            attrs.addAll(dealAttrs);
        }
        //3. 团单属性信息 嵌套解析
        List<AttrM> dealDetailInfoAttrs = DealAttrUtils.getDealDetailInfoAttrsFromAttrs(dealAttrs);
        if (CollectionUtils.isNotEmpty(dealDetailInfoAttrs)){
            attrs.addAll(dealDetailInfoAttrs);
        }
        // 太极团单特殊判断逻辑
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        if (Objects.nonNull(dealDetailInfoModel) && dealDetailInfoModel.isUnifyProduct()) {
            config.setTaiji(dealDetailInfoModel.isUnifyProduct());
        }

        //3.构造DealDetailStructAttrModuleVO列表
        return config.getAttrListGroupModels().stream().map(model -> buildDealDetailStructAttrModuleVOList(model, attrs, config.isTaiji())).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private DealDetailStructAttrModuleGroupModel buildDealDetailStructAttrModuleVOList(AttrListGroupModel attrListGroupModel, List<AttrM> attrs, boolean isTaiji) {
        if (attrListGroupModel == null || CollectionUtils.isEmpty(attrs)
                || (CollectionUtils.isEmpty(attrListGroupModel.getAttrModelList())
                        && CollectionUtils.isEmpty(attrListGroupModel.getAttrListGroupModels2()))) {
            return null;
        }
        List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOS = Optional.ofNullable(attrListGroupModel.getAttrModelList())
                .map(list -> list.stream().map(model -> buildDealDetailStructAttrModuleVO(model, attrs, isTaiji)).filter(Objects::nonNull).collect(Collectors.toList()))
                .orElseGet(ArrayList::new);
        // 化妆造型 - 加项说明 - 动态标题展示逻辑
        if(attrListGroupModel.getAttrModelList()!=null){
            dealDetailStructAttrModuleVOS.addAll(attrListGroupModel.getAttrModelList().stream()
                    .map(model -> buildDealDetailStructAttrModuleVOByDynamicAttrName(model, attrs))
                    .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList()));
        }
        List<DealDetailStructAttrModuleGroupModel> dealDetailModuleList2 = attrListGroupModel.getAttrListGroupModels2() != null ? attrListGroupModel.getAttrListGroupModels2().stream().map(model -> buildDealDetailStructAttrModuleVOList(model, attrs, isTaiji)).filter(Objects::nonNull).collect(Collectors.toList()) : null;
        List<DealDetailStructAttrModuleVO> subtitleItems = attrListGroupModel.getGroupSubtitleAttrModelList() != null ? attrListGroupModel.getGroupSubtitleAttrModelList().stream().map(model -> buildDealDetailStructAttrModuleVO(model, attrs, isTaiji)).filter(model -> model != null).collect(Collectors.toList()) : null;
        VideoModuleVO videoModuleVO = buildVideoModuleVOByIntensiveAttr(attrListGroupModel.getVideoModuleVO(), attrs);
        return buildDealDetailStructAttrModuleGroupModel(attrListGroupModel.getGroupName(), dealDetailStructAttrModuleVOS, dealDetailModuleList2, subtitleItems, videoModuleVO);
    }

    private List<DealDetailStructAttrModuleVO> buildDealDetailStructAttrModuleVOByDynamicAttrName(
            MergeSortMapJoinFilterAttrModel attrModel, List<AttrM> attrs) {
        if (StringUtils.isBlank(attrModel.getDynamicDisplayNameKey())) {
            return null;
        }
        return buildExtraItemFeeStructAttrModuleVOs(attrModel.getDynamicDisplayNameKey(), attrs);
    }

    private List<DealDetailStructAttrModuleVO> buildExtraItemFeeStructAttrModuleVOs(String nameKey, List<AttrM> attrs) {
        List<ExtraItemFeeModel> extraItemFeeModels = getExtraItemFeeModels(nameKey, attrs);
        if (CollectionUtils.isEmpty(extraItemFeeModels)) {
            return null;
        }
        return extraItemFeeModels.stream().map(model -> buildDealDetailStructAttrModuleVO(model.getXiangmuneirong(),
                Arrays.asList(model.getFeiyong()), null)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<ExtraItemFeeModel> getExtraItemFeeModels(String name, List<AttrM> attrs) {
        AttrM attrM = attrs.stream().filter(attr -> name.equals(attr.getName())).findFirst().orElse(null);
        if (Objects.isNull(attrM) || StringUtils.isBlank(attrM.getValue())) {
            return null;
        }
        try {
            return JSON.parseArray(attrM.getValue(), ExtraItemFeeModel.class);
        } catch (Exception e) {
            log.error("CommonPhotoDealAttrVOListOpt.getExtraItemFeeModels JSON解析失败, name:{}, value:{}", name,
                    attrM.getValue(), e);
            return Lists.newArrayList();
        }
    }
    // 视频模块构建特殊逻辑 : 根据精修字段判断展示逻辑
    private VideoModuleVO buildVideoModuleVOByIntensiveAttr(VideoModuleVO videoModuleVO, List<AttrM> attrs) {
        if (Objects.isNull(videoModuleVO)) {
            return null;
        }
        if (isValidIntensiveRepairDesc(attrs) || isValidIntensiveRepairNum(attrs)){
            return videoModuleVO;
        }
        return replaceVideoUrlWithVideoDesc(videoModuleVO);
    }

    private boolean isValidIntensiveRepairDesc(List<AttrM> attrs) {
        AttrM attrM = attrs.stream().filter(attr -> attr.getName().equals(INTENSIVE_REPAIR_NUM_DESC)).findFirst().orElse(null);
        if (Objects.isNull(attrM) || StringUtils.isBlank(attrM.getValue()) || isInvalidIntensiveValue(attrM.getValue())) {
            return false;
        }
        return true;
    }

    private boolean isValidIntensiveRepairNum(List<AttrM> attrs) {
        AttrM attrM = attrs.stream().filter(attr -> attr.getName().equals(INTENSIVE_REPAIR_NUM)).findFirst().orElse(null);
        if (Objects.isNull(attrM) || StringUtils.isBlank(attrM.getValue()) || isInvalidIntensiveValue(attrM.getValue())) {
            return false;
        }
        return true;
    }

    private boolean isInvalidIntensiveValue(String value) {
        return ZERO_VALUE_WITH_UNIT.equals(value) || ZERO_VALUE.equals(value);
    }

    private VideoModuleVO replaceVideoUrlWithVideoDesc(VideoModuleVO videoModuleVO) {
        if (StringUtils.isBlank(videoModuleVO.getDesc())){
            return videoModuleVO;
        }
        videoModuleVO.setUrl(videoModuleVO.getDesc());
        return videoModuleVO;
    }

    private DealDetailStructAttrModuleGroupModel buildDealDetailStructAttrModuleGroupModel(String groupName, List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOS, List<DealDetailStructAttrModuleGroupModel> dealDetailStructAttrModuleGroupModels, List<DealDetailStructAttrModuleVO> subtitleItems, VideoModuleVO videoModuleVO) {
        if (CollectionUtils.isEmpty(dealDetailStructAttrModuleVOS) && Objects.isNull(videoModuleVO)
                && CollectionUtils.isEmpty(dealDetailStructAttrModuleGroupModels)) {
            return null;
        }
        DealDetailStructAttrModuleGroupModel dealDetailStructAttrModuleGroupModel = new DealDetailStructAttrModuleGroupModel();
        dealDetailStructAttrModuleGroupModel.setGroupName(groupName);
        dealDetailStructAttrModuleGroupModel.setDealDetailStructAttrModuleVOS(dealDetailStructAttrModuleVOS);
        dealDetailStructAttrModuleGroupModel.setDealDetailModuleList2(dealDetailStructAttrModuleGroupModels);
        dealDetailStructAttrModuleGroupModel.setSubTitleItems(subtitleItems);
        dealDetailStructAttrModuleGroupModel.setVideoModuleVO(videoModuleVO);
        return dealDetailStructAttrModuleGroupModel;
    }

    private DealDetailStructAttrModuleVO buildDealDetailStructAttrModuleVO(MergeSortMapJoinFilterAttrModel attrModel, List<AttrM> attrs, boolean isTaiji) {
        if (CollectionUtils.isEmpty(attrs) || attrModel == null) {
            return null;
        }
        //属性值获取 如果有format则属性值会进行format转换
        List<String> attrValues = getAttrValues(attrModel, attrs, isTaiji);
        //获取排序/过滤/映射模型
        List<AttrValueMapModel> attrValueMapModels = attrModel.getAttrValueMapModels();
        //属性值排序
        attrValues = sortAttrValues(attrValues, attrValueMapModels);
        //属性值映射 + 过滤（映射对象为null即被过滤）
        attrValues = mapAndFilterAttrValues(attrValues, attrValueMapModels);
        //属性值拼接
        attrValues = joinAttrValue(attrValues, attrModel);
        return buildDealDetailStructAttrModuleVO(attrModel.getDisplayName(), attrValues, attrModel.getIcon());
    }
    private DealDetailStructAttrModuleVO buildDealDetailStructAttrModuleVO(String displayName, List<String> attrValues, String icon) {
        if (CollectionUtils.isEmpty(attrValues)) {
            return null;
        }
        DealDetailStructAttrModuleVO dealDetailStructAttrModuleVO = new DealDetailStructAttrModuleVO();
        dealDetailStructAttrModuleVO.setAttrName(displayName);
        dealDetailStructAttrModuleVO.setAttrValues(attrValues);
        dealDetailStructAttrModuleVO.setIcon(icon);
        return dealDetailStructAttrModuleVO;
    }

    private List<String> mapAndFilterAttrValues(List<String> attrValues, List<AttrValueMapModel> attrValueMapModels) {
        if (CollectionUtils.isEmpty(attrValueMapModels) || CollectionUtils.isEmpty(attrValues)) {
            return attrValues;
        }
        return attrValues.stream().map(value -> getAttrDisplayValueByAttrValue(attrValueMapModels, value)).filter(value -> StringUtils.isNotEmpty(value)).collect(Collectors.toList());
    }

    private List<String> joinAttrValue(List<String> attrValues, MergeSortMapJoinFilterAttrModel model) {
        if (model == null || model.getSeperator() == null || CollectionUtils.isEmpty(attrValues)) {
            return attrValues;
        }
        return Lists.newArrayList(StringUtils.join(attrValues, model.getSeperator()));
    }

    private String getAttrDisplayValueByAttrValue(List<AttrValueMapModel> attrValueMapModels, String attrValue) {
        AttrValueMapModel attrValueMapModel = getAttrValueMapModelByAttrValue(attrValueMapModels, attrValue);
        if (attrValueMapModel == null) {
            return attrValue;
        }
        return attrValueMapModel.getDisplayValue();
    }

    private List<String> sortAttrValues(List<String> attrValues, List<AttrValueMapModel> attrValueMapModels) {
        if (CollectionUtils.isNotEmpty(attrValueMapModels) || CollectionUtils.isEmpty(attrValues)) {
            return attrValues;
        }
        return attrValues.stream().sorted(Comparator.comparingInt(o -> getAttrPriority(attrValueMapModels, o))).collect(Collectors.toList());
    }

    private int getAttrPriority(List<AttrValueMapModel> attrValueMapModels, String attrValue) {
        AttrValueMapModel attrValueMapModel = getAttrValueMapModelByAttrValue(attrValueMapModels, attrValue);
        if (attrValueMapModel == null || attrValueMapModel.getPriority() == 0) {
            return Integer.MAX_VALUE;
        }
        return attrValueMapModel.getPriority();
    }

    private AttrValueMapModel getAttrValueMapModelByAttrValue(List<AttrValueMapModel> attrValueMapModels, String attrValue) {
        if (CollectionUtils.isEmpty(attrValueMapModels) || StringUtils.isEmpty(attrValue)) {
            return null;
        }
        return attrValueMapModels.stream().filter(model -> attrValue.equals(model.getAttrValue())).findFirst().orElse(null);
    }

    private List<String> getAttrValues(MergeSortMapJoinFilterAttrModel model, List<AttrM> dealAttrs, boolean isTaiji) {
        return model.getAttrNameList().stream().flatMap(name -> getValueWithFormat(dealAttrs, name, model.getAttrFormatModels(), isTaiji).stream())
                .filter(value -> StringUtils.isNotEmpty(value)).collect(Collectors.toList());
    }

    private List<String> getValueWithFormat(List<AttrM> dealAttrs, String attrName, List<AttrFormatModel> attrFormatModels, boolean isTaiji) {
        List<String> values;
        // 景点跟拍特殊逻辑-成品交付物
        if (StringUtils.equals("FinishedDeliverables", attrName)) {
            values = getAttrValueByFinishedDeliverables(dealAttrs, attrName);
        } else if (KEY_WITH_LIST_WITHOUT_SPLIT.contains(attrName)) {     // 摄影-关联款式特殊逻辑
            values = DealDetailUtils.getAttrValueByAttrNameWithoutSplit(dealAttrs, attrName);
        } else if (StringUtils.equals("intensiveRepairNum_Desc", attrName)) {
            // 个写(太极)-精修特殊逻辑
            values = DealDetailUtils.getAttrValueByAttrName(dealAttrs, attrName);
            if (Objects.isNull(values)) {
                values = DealDetailUtils.parseAttrValueByAttrName(dealAttrs, "intensiveRepairNum");
                // 兼容老团详-“精修”字段格式化
                attrName = "intensiveRepairNum";
            }
        } else if (StringUtils.equals("ShootBodyInstruction", attrName)) {
            // 身幅特殊逻辑
            values = getAttrValueByListValue(dealAttrs, attrName);
            if (CollectionUtils.isEmpty(values)) {
                values = getAttrValueByListValue(dealAttrs, "photoShoot");
                // 兼容老团详-“精修”字段格式化
                attrName = "photoShoot";
            }
        } else if (StringUtils.equals("containDress", attrName)) {
            // 换装体验 - 妆造服务特殊逻辑
            values = getAttrValueByListValue(dealAttrs, attrName);
            return getContainDress(values, dealAttrs);

        } else if (KEY_WITH_LIST_VALUE.contains(attrName)) {
            values = getAttrValueByListValue(dealAttrs, attrName);
        } else if (StringUtils.equals(CONTAIN_STAFF, attrName)) {
            values = getContainStaffValues(dealAttrs);
        } else {
            values = DealDetailUtils.getAttrValueByAttrName(dealAttrs, attrName);
        }
        if (CollectionUtils.isEmpty(values)) {
            return Lists.newArrayList();
        }
        // 非太极团单- 单位拼接特殊逻辑
        if (!isTaiji) {
            attrName = attrName + "Old";
        }
        // 亲子 - 孕婴童摄影 - 提供同底原片特殊逻辑
        if ("IsTheFilmPresentedFree".equals(attrName)) {
            return getFreePresentedFilm(values, dealAttrs);
        }
        // 团体照 精修底片特殊逻辑
        if ("photoPlateGrant".equals(attrName)) {
            return getPhotoPlateGrant(values, dealAttrs);
        }
        AttrFormatModel attrFormatModel = getAttrFormatMapModelByAttrValue(attrFormatModels, attrName);
        if (attrFormatModel == null) {
            return values;
        }
        //过滤：将attrName2FilteredAttrValueMap中key作为属性名查询到的属性值为value时不展示该属性
        boolean isFiltered = isFiltered(attrFormatModel.getAttrName2FilteredAttrValueMap(), dealAttrs);
        if (isFiltered) {
            return Lists.newArrayList();
        }
        List<String> result = Lists.newArrayList();

        // 用getAttrName2JoinValues作为属性名查询到的不同属性值对value进行format
        if (attrFormatModel.getAttrName2JoinValues() != null && isValidJoinValues(dealAttrs, attrFormatModel.getAttrName2JoinValues())) {
            List<String> joinValues = getAttrValueByListValue(dealAttrs, attrFormatModel.getAttrName2JoinValues());
            values = values.stream()
                    .flatMap(value -> joinValues.stream().map(joinValue -> joinValue + value))
                    .collect(Collectors.toList());
        }
        // 新版团详format特殊逻辑 : 老团单有单位、 太极无单位
        for (String value : values) {
            // 将属性值按照指定字符分割返回多个
            if (StringUtils.isNotBlank(attrFormatModel.getAttrValueSeperator()) && StringUtils.isEmpty(attrFormatModel.getDisplayFormat())) {
                result.addAll(Lists.newArrayList(value.split(attrFormatModel.getAttrValueSeperator())));
                continue;
            }
            //补集构造模型
            if (attrFormatModel.getComplementarySetBuilderModel() != null) {
                value = addComplementarySet(attrFormatModel.getComplementarySetBuilderModel(), value);
            }
            //将属性值中的某个字符串替换为另一个字符串
            if (CollectionUtils.isNotEmpty(attrFormatModel.getAttrValueReplaceModels())) {
                for (AttrValueReplaceModel attrValueReplaceModel : attrFormatModel.getAttrValueReplaceModels()) {
                    if (!isValidAttrFormatModel(attrValueReplaceModel)) {
                        continue;
                    }
                    value = value.replaceAll(attrValueReplaceModel.getPreStr(), attrValueReplaceModel.getStr());
                }
            }
            //将属性值format
            if (StringUtils.isNotEmpty(attrFormatModel.getDisplayFormat())) {
                // 统计属性值个数并将个数拼接入字符串
                if (attrFormatModel.getDisplayCount() != null && attrFormatModel.getDisplayCount() && StringUtils.isNotBlank(attrFormatModel.getAttrValueSeperator())) {
                    value = String.format(attrFormatModel.getDisplayFormat(), value.split(attrFormatModel.getAttrValueSeperator()).length, value);
                } else {
                    value = String.format(attrFormatModel.getDisplayFormat(), value);
                }
            }
            result.add(value);
        }
        return result;
    }

    // 旅游婚纱照-ContainStaff特殊逻辑
    private List<String> getContainStaffValues(List<AttrM> attrs) {
        List<ContainStaff> containStaffList = getContainStaffList(attrs);
        if (CollectionUtils.isEmpty(containStaffList)) {
            return Lists.newArrayList();
        }
        return getValuesFromContainStaffList(containStaffList);
    }

    private List<String> getValuesFromContainStaffList(List<ContainStaff> containStaffList) {
        return containStaffList.stream().filter(this::isValidContainStaff)
                .map(containStaff -> containStaff.getStaffType() + FINISHED_DELIVERABLE_CONNECTOR + containStaff.getStaffNum())
                .collect(Collectors.toList());
    }

    private boolean isValidContainStaff(ContainStaff staff) {
        return staff != null
                && StringUtils.isNotEmpty(staff.getStaffType())
                && StringUtils.isNotEmpty(staff.getStaffNum());
    }

    private List<ContainStaff> getContainStaffList(List<AttrM> attrs) {
        AttrM attrM = attrs.stream().filter(attr -> CONTAIN_STAFF.equals(attr.getName())).findFirst().orElse(null);
        if (attrM == null || StringUtils.isEmpty(attrM.getValue())) {
            return Lists.newArrayList();
        }
        String jsonValue = attrM.getValue();
        if (invalidJsonStart(jsonValue) && invalidJsonEnd(jsonValue)) {
            jsonValue = LEFT_SQUARE_BRACKETS + jsonValue + RIGHT_SQUARE_BRACKETS;
        }
        try {
            return JSONObject.parseArray(jsonValue, ContainStaff.class);
        } catch (Exception e) {
            log.error("CommonPhotoDealAttrVOListOpt#getContainStaffList error:{}", com.sankuai.it.iam.common.base.gson.bridge.JSON.toJSON(attrM), e);
            return Lists.newArrayList();
        }
    }

    // 自拍/换装体验 - 服装包含特殊逻辑
    private List<String> getContainDress(List<String> containDress, List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(containDress)) {
            return Lists.newArrayList();
        }
        String containDressStr = containDress.get(0);
        List<String> dressNumStrs = getAttrValueByListValue(dealAttrs, "dressNumStr");
        if (CollectionUtils.isEmpty(dressNumStrs)) {
            return containDress;
        }
        if ("店内提供".equals(containDressStr)) {
            return Lists.newArrayList("店内提供服装" + dressNumStrs.get(0));
        }
        if ("服饰租赁".equals(containDressStr)) {
            return Lists.newArrayList("可租赁服饰" + dressNumStrs.get(0));
        }
        return containDress;
    }

    // 团体照 - 精修底片特殊逻辑
    private List<String> getPhotoPlateGrant(List<String> values, List<AttrM> dealAttrs) {
        if (values.contains("原始底片") && values.contains("精修底片")) {
            return Lists.newArrayList("底片全送");
        }
        if( !values.contains("精修底片")){
            return values;
        }
        List<String> resultValues = getAttrValueByListValue(dealAttrs, "intensiveRepairNum");
        if (CollectionUtils.isEmpty(resultValues) ){
            return resultValues;
        }
        return Lists.newArrayList(getFormatValue(resultValues.get(0), "底片赠送%s", "张"));
    }

    private String getFormatValue(String value, String format, String unit) {
        if (value.contains(unit)) {
            return String.format(format, value);
        }
        return String.format(format + unit, value);
    }

    // 亲子 - 孕婴童摄影 - 提供同底原片特殊逻辑
    private List<String> getFreePresentedFilm(List<String> values, List<AttrM> dealAttrs) {
        if (!containsFreePresentedFilmValues(values)) {
            return values;
        }
        String filmCountStr = getFilmCountStr(dealAttrs);
        if (StringUtils.isEmpty(filmCountStr)) {
            return values;
        }
        return createFilmPresentationMessage(filmCountStr);
    }
    
    private boolean containsFreePresentedFilmValues(List<String> values) {
        return values.contains("同底原片") || values.contains("提供同底原片");
    }

    private String getFilmCountStr(List<AttrM> dealAttrs) {
        List<String> resultValues = getAttrValueByListValue(dealAttrs, "intensiveRepairNum_Desc");
        if(CollectionUtils.isEmpty(resultValues)){
            resultValues = getAttrValueByListValue(dealAttrs, "intensiveRepairNum");
        }
        return CollectionUtils.isNotEmpty(resultValues) ? resultValues.get(0) : "";
    }

    private List<String> createFilmPresentationMessage(String filmCount) {
        if (hasUnit(filmCount, "张")) {
            return Lists.newArrayList(String.format("底片赠送%s", filmCount));
        }
        return Lists.newArrayList(String.format("底片赠送%s张", filmCount));
    }
    
    private boolean hasUnit(String value, String unit) {
        return value.contains(unit);
    }

    private boolean isValidAttrFormatModel(AttrValueReplaceModel attrValueReplaceModel) {
        return attrValueReplaceModel.getStr() != null && attrValueReplaceModel.getPreStr() != null;
    }

    private List<String> getAttrValueByListValue(List<AttrM> dealAttrs, String attrName) {
        String valueStr = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, attrName);
        if (StringUtils.isEmpty(valueStr)) {
            return Lists.newArrayList();
        }
        try {
            if (valueStr.startsWith(LEFT_SQUARE_BRACKETS) && valueStr.endsWith(RIGHT_SQUARE_BRACKETS)) {
                return JSON.parseArray(valueStr, String.class);
            }
            if (valueStr.contains(SEPERATOR)) {
                return Lists.newArrayList(valueStr.split(SEPERATOR));
            }
            return Lists.newArrayList(valueStr);
        } catch (Exception e) {
            log.error("com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.CommonPhotoDealAttrVOListOpt.getAttrValueByListValue error, attrName:{}, valueStr:{}", attrName, valueStr, e);
            return Lists.newArrayList();
        }
    }

    private boolean isValidJoinValues(List<AttrM> dealAttrs, String attrName2JoinValue){
        List<String> joinValues = getAttrValueByListValue(dealAttrs, attrName2JoinValue);
        return CollectionUtils.isNotEmpty(joinValues);
    }

    /**
     * 景点跟拍逻辑-获取可拍交付物拼接后attrValue
     *
     * @param dealAttrs
     * @param name
     * @return
     */
    private List<String> getAttrValueByFinishedDeliverables(List<AttrM> dealAttrs, String name) {
        if (CollectionUtils.isEmpty(dealAttrs) || StringUtils.isEmpty(name)) {
            return null;
        }
        List<FinishedDeliverableModel> finishedDeliverableModels = getFinishedDeliverableModelList(dealAttrs, name);
        if (CollectionUtils.isEmpty(finishedDeliverableModels)) {
            return null;
        }
        return getFinishedDeliverableStrList(finishedDeliverableModels);
    }

    private List<String> getFinishedDeliverableStrList(List<FinishedDeliverableModel> finishedDeliverableModels) {
        if (CollectionUtils.isEmpty(finishedDeliverableModels)) {
            return null;
        }
        List<String> finishedDeliverableStrList = Lists.newArrayList();
        for (int i = 0; i < finishedDeliverableModels.size(); i++) {
            FinishedDeliverableModel finishedDeliverableModel = finishedDeliverableModels.get(i);
            if (StringUtils.isEmpty(finishedDeliverableModel.getFinishedProductName()) || isEmptyQuantity(finishedDeliverableModel)) {
                return null;
            }
            if (StringUtils.isNotEmpty(finishedDeliverableModel.getGrant())) {
                // 个写-太极特殊逻辑
                finishedDeliverableStrList.add(finishedDeliverableModel.getFinishedProductName() + FINISHED_DELIVERABLE_CONNECTOR + finishedDeliverableModel.getGrant());
                continue;
            }
            // 个写 - 老团单特殊逻辑
            if (StringUtils.isNotEmpty(finishedDeliverableModel.getQuantity())) {
                finishedDeliverableStrList.add(finishedDeliverableModel.getFinishedProductName() + FINISHED_DELIVERABLE_CONNECTOR + finishedDeliverableModel.getQuantity());
                continue;
            }
            finishedDeliverableStrList.add(finishedDeliverableModel.getFinishedProductName() + FINISHED_DELIVERABLE_CONNECTOR + finishedDeliverableModel.getChengpinshuliang());
        }
        return finishedDeliverableStrList;
    }

    private boolean isEmptyQuantity(FinishedDeliverableModel finishedDeliverableModel) {
        return StringUtils.isEmpty(finishedDeliverableModel.getChengpinshuliang()) && StringUtils.isEmpty(finishedDeliverableModel.getGrant())
                && StringUtils.isEmpty(finishedDeliverableModel.getQuantity());
    }

    private List<FinishedDeliverableModel> getFinishedDeliverableModelList(List<AttrM> dealAttrs, String name) {
        AttrM attrM = dealAttrs.stream().filter(attr -> name.equals(attr.getName())).findFirst().orElse(null);
        if (attrM == null || StringUtils.isEmpty(attrM.getValue())) {
            return null;
        }
        String jsonValue = attrM.getValue();
        if (invalidJsonStart(jsonValue) && invalidJsonEnd(jsonValue)) {
            jsonValue = LEFT_SQUARE_BRACKETS + jsonValue + RIGHT_SQUARE_BRACKETS;
        }
        return JSONObject.parseArray(jsonValue, FinishedDeliverableModel.class);
    }

    private boolean invalidJsonStart(String jsonValue) {
        return !jsonValue.startsWith(LEFT_SQUARE_BRACKETS);
    }

    private boolean invalidJsonEnd(String jsonValue) {
        return !jsonValue.endsWith(RIGHT_SQUARE_BRACKETS);
    }
    /**
     * 在原属性值基础上加上补集。例如"装备"这一属性，上单页面该属性可以选择雪板、雪杖、雪服、学鞋、头盔、雪镜、护具、手套中的多个，商家选择了雪板、雪杖和雪服，供应链传过来的属性值是"雪板、雪杖、雪服"，产品希望在c端展示为："包含雪板、雪杖、雪服，不包含学鞋、头盔、雪镜、护具、手套"
     *
     * @param
     * @return
     */
    private String addComplementarySet(ComplementarySetBuilderModel complementarySetBuilderModel, String value) {
        if (complementarySetBuilderModel == null || StringUtils.isEmpty(complementarySetBuilderModel.getSeperator()) || CollectionUtils.isEmpty(complementarySetBuilderModel.getUniversalSet())) {
            return value;
        }
        String seperator = complementarySetBuilderModel.getSeperator();
        String prevalueFormat = complementarySetBuilderModel.getPreValueFormat();
        String complementaryValueFormat = complementarySetBuilderModel.getComplementaryValueFormat();
        List<String> originalSet = StringUtils.isEmpty(value) ? Lists.newArrayList() : Lists.newArrayList(value.split(seperator));
        List<String> complementarySet = complementarySetBuilderModel.getUniversalSet();
        complementarySet.removeAll(originalSet);
        String prevalue = prevalueFormat == null || value == null ? StringUtils.EMPTY : String.format(prevalueFormat, value);
        if (CollectionUtils.isEmpty(complementarySet)) {
            return prevalue;
        }
        String complementaryValue = complementaryValueFormat == null ? StringUtils.EMPTY : String.format(complementaryValueFormat, StringUtils.join(complementarySet, seperator));
        return prevalue + complementaryValue;
    }

    private boolean isFiltered(Map<String, String> attrName2FilteredAttrValueMap, List<AttrM> dealAttrs) {
        //没有配置该模型时，不进行过滤处理
        if (MapUtils.isEmpty(attrName2FilteredAttrValueMap)) {
            return false;
        }
        return attrName2FilteredAttrValueMap.entrySet().stream().map(entry -> {
            String value = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, entry.getKey());
            //该属性值为null或者和期望值不同时，进行过滤处理
            if (value == null) {
                return true;
            }
            return !value.equals(entry.getValue());
        }).filter(Boolean::booleanValue).findFirst().orElse(false);
    }

    private AttrFormatModel getAttrFormatMapModelByAttrValue(List<AttrFormatModel> attrFormatModels, String attrName) {
        if (CollectionUtils.isEmpty(attrFormatModels) || StringUtils.isEmpty(attrName)) {
            return null;
        }
        return attrFormatModels.stream().filter(model -> attrName.equals(model.getAttrName())).findFirst().orElse(null);
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<AttrListGroupModel> attrListGroupModels;
        private boolean isTaiji;
    }

    @Data
    public static class AttrListGroupModel {
        private List<MergeSortMapJoinFilterAttrModel> attrModelList;
        private String groupName;
        private List<AttrListGroupModel> attrListGroupModels2;      // 多层嵌套配置
        private List<MergeSortMapJoinFilterAttrModel> groupSubtitleAttrModelList;      // 标题附加字段配置
        private VideoModuleVO videoModuleVO;    // 同一模块嵌入视频组件
    }

    // 额外加项
    @Data
    public static class ExtraItemFeeModel {
        private String xiangmuneirong;
        private String feiyong;
    }

    /**
     * 融合命名/排序/映射/拼接/过滤功能的属性配置模型
     */
    @Data
    public static class MergeSortMapJoinFilterAttrModel {
        //属性展示名称
        private String displayName;
        //属性名列表
        private List<String> attrNameList;
        //多个属性之间的拼接符
        private String seperator;
        //属性值 - 展示值映射 ； 展示顺序按照map中的展示顺序
        private List<AttrValueMapModel> attrValueMapModels;
        //属性名 - 展示format映射
        private List<AttrFormatModel> attrFormatModels;
        //图标
        private String icon;
        // 化妆造型 - 加项说明 - 动态属性名展示Key
        private String dynamicDisplayNameKey;
    }

    @Data
    public static class AttrValueReplaceModel {
        private String preStr;
        private String str;
    }

    @Data
    public static class AttrValueMapModel {
        private String attrValue;
        private String displayValue;
        //优先级从1开始
        private int priority;
    }

    /**
     * 补集构造模型。例如"装备"这一属性，上单页面该属性可以选择雪板、雪杖、雪服、学鞋、头盔、雪镜、护具、手套中的多个，商家选择了雪板、雪杖和雪服，供应链传过来的属性值是"雪板、雪杖、雪服"，产品希望在c端展示为："包含雪板、雪杖、雪服，不包含学鞋、头盔、雪镜、护具、手套"
     *
     * @param
     * @return
     */
    @Data
    public static class ComplementarySetBuilderModel {
        //分隔符，属性按照该分隔符分开后得到集合列表
        private String seperator;
        //全集列表
        private List<String> universalSet;
        //原集属性值构造format
        private String preValueFormat;
        //补集属性值构造format
        private String complementaryValueFormat;
    }

    @Data
    public static class AttrFormatModel {
        private String attrName;
        private String displayFormat;
        // 在返回结果里展示属性值的总数
        private Boolean displayCount;
        // 分割属性值的字符串
        private String attrValueSeperator;
        //将属性值中的指定字符串替换成另一个字符串
        private List<AttrValueReplaceModel> attrValueReplaceModels;
        //过滤模型：只有将key作为属性名查询到的属性值为value时才展示该属性
        private Map<String, String> attrName2FilteredAttrValueMap;
        //补集构造模型
        private ComplementarySetBuilderModel complementarySetBuilderModel;
        // 用attrName2JoinValues作为属性名查询到的不同属性值对value进行拼接
        private String attrName2JoinValues;
    }

    // 成品交付物
    @Data
    public static class FinishedDeliverableModel {
        //成品名称
        private String finishedProductName;
        //成品数量
        private String chengpinshuliang;
        // 成平数量 - 个写(老团单)
        private String quantity;
        // 赠送服务 (个写太极团单)
        private String grant;
    }

    @Data
    private static class ContainStaff {
        private String staffType;
        private String staffNum;

        public String getStaffNum() {
            if (staffNum != null) {
                try {
                    return String.valueOf((int) NumberUtils.toDouble(staffNum));
                } catch (NumberFormatException e) {
                    log.error("CommonPhotoDealAttrVOListOpt.ContainStaff#getStaffNum error:{}", staffNum, e);
                    return "";
                }
            }
            return "";
        }
    }
}
