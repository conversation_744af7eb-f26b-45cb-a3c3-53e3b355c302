package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;

public class MappingValueProcessor extends AbstractValueProcessor {
    @Override
    public List<String> convertDisplayValues(String value, ValueConfig valueConfig, Map<String, String> name2ValueMap) {
        if (validate(value, valueConfig)) {
            return Lists.newArrayList();
        }
        value = valueConfig.getMapping().get(value);
        if (ObjectUtils.isEmpty(value)) {
            return Lists.newArrayList();
        }
        if (!ObjectUtils.isEmpty(valueConfig.getFormat())) {
            value = String.format(valueConfig.getFormat(), value);
        }
        if (ObjectUtils.isEmpty(value)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(value);
    }

    private boolean validate(String value, ValueConfig valueConfig) {
        return ObjectUtils.isEmpty(value) || ObjectUtils.isEmpty(valueConfig)
                || ObjectUtils.isEmpty(valueConfig.getMapping());
    }
}
