package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.SkuPriceVP;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/11/26 6:20 下午
 */
@VPointOption(name = "团购详情sku货价格变化点", description = "团购详情sku货价格变化点，支持配置文案Format",code = DefaultSkuPriceVPO.CODE, isDefault = true)
public class DefaultSkuPriceVPO extends SkuPriceVP<DefaultSkuPriceVPO.Config> {

    public static final String CODE = "DefaultSkuPriceVPO";

    private static final String DEFAULT_PRICE_FORMAT = "%s元";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if(StringUtils.isEmpty(param.getPrice()) || config.isNoShowPrice()) {
            return null;
        }
        String priceFormat = StringUtils.isEmpty(config.getSkuPriceFormat()) ? DEFAULT_PRICE_FORMAT : config.getSkuPriceFormat();
        return String.format(priceFormat, param.getPrice());
    }

    @Data
    @VPointCfg
    public static class Config {
        //不展示价格标识
        private boolean noShowPrice;
        private String skuPriceFormat;
    }
}
