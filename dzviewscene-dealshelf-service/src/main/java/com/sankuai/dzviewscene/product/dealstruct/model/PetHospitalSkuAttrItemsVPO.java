package com.sankuai.dzviewscene.product.dealstruct.model;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.SkuAttrItemsVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/27 10:16 下午
 */
@VPointOption(name = "宠物医院团购详情货attr列表变化点", description = "宠物医院团购详情货attr列表变化点", code = PetHospitalSkuAttrItemsVPO.CODE, isDefault = false)
public class PetHospitalSkuAttrItemsVPO extends SkuAttrItemsVP<PetHospitalSkuAttrItemsVPO.Config> {

    public static final String CODE = "PetHospitalSkuAttrItemsVPO";

    private static final String SKU_CATEGORY_ATTR_NAME = "货类别";

    private static final String VACCINE_TYPE_ATTR_NAME = "疫苗类型";

    private static final String VACCINE_TYPE_SKU_ATTR_NAME = "vaccineType";

    private static final String PRODUCT_DESC_ATTR_NAME = "产品描述";

    private static final long VACCINE_SKU_CATEGRY = 1174L;

    @Override
    public List<DealSkuItemVO> compute(ActivityCxt context, Param param, Config config) {
        SkuItemDto skuItemDto = param.getSkuItemDto();
        if (skuItemDto == null || StringUtils.isEmpty(skuItemDto.getName())) {
            return null;
        }
        List<DealSkuItemVO> dealSkuItemVOS = new ArrayList<>();
        //添加sku类别
        DealSkuItemVO skuCategory = buildDealSkuItemVO(SKU_CATEGORY_ATTR_NAME, getSkuCategory(skuItemDto.getProductCategory(), param.getProductCategories()));
        dealSkuItemVOS.add(skuCategory);
        //添加原sku属性
        List<DealSkuItemVO> skuAttrItems = convertSkuAttrs2DealSkuItemVOs(skuItemDto.getAttrItems());
        dealSkuItemVOS.addAll(skuAttrItems);
        if (skuItemDto.getProductCategory() == VACCINE_SKU_CATEGRY) {
            //疫苗类型的sku添加sku属性
            String vaccineType = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), VACCINE_TYPE_SKU_ATTR_NAME);
            DealSkuItemVO vaccineTypeItem = buildDealSkuItemVO(VACCINE_TYPE_ATTR_NAME, vaccineType);
            dealSkuItemVOS.add(vaccineTypeItem);
            return dealSkuItemVOS;
        }
        //非疫苗类型的sku添加产品描述
        DealSkuItemVO productDescItem = buildDealSkuItemVO(PRODUCT_DESC_ATTR_NAME, skuItemDto.getName());
        dealSkuItemVOS.add(productDescItem);
        return dealSkuItemVOS;
    }

    private List<DealSkuItemVO> convertSkuAttrs2DealSkuItemVOs(List<SkuAttrItemDto> attrItems) {
        if (CollectionUtils.isEmpty(attrItems)) {
            return new ArrayList<>();
        }
        return attrItems.stream().map(attr -> buildDealSkuItemVO(attr.getAttrName(), attr.getAttrValue())).collect(Collectors.toList());
    }

    private String getSkuCategory(long skuCategoryId, List<ProductSkuCategoryModel> productCategories) {
        if (CollectionUtils.isEmpty(productCategories)) {
            return null;
        }
        ProductSkuCategoryModel productSkuCategoryModel = productCategories.stream().filter(category -> category.getProductCategoryId() != null && skuCategoryId == category.getProductCategoryId()).findFirst().orElse(null);
        if (productSkuCategoryModel == null) {
            return null;
        }
        return productSkuCategoryModel.getCnName();
    }

    private DealSkuItemVO buildDealSkuItemVO(String name, String value) {
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setType(0);
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setValue(value);
        return dealSkuItemVO;
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
