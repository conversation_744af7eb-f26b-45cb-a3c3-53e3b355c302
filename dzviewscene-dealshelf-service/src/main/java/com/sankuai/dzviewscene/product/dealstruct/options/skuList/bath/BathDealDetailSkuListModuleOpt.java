package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bath;

import com.dianping.deal.struct.query.api.entity.dto.OptionalSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bath.enums.BathServiceProjectEnum;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.AdditionalDealUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.BathServiceFacilityUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.BathSkuUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.DealSkuListModuleUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: created by hang.yu on 2023/10/7 15:20
 */
@VPointOption(name = "洗浴团详sku列表组变化点", description = "洗浴团详sku列表组变化点", code = BathDealDetailSkuListModuleOpt.CODE)
public class BathDealDetailSkuListModuleOpt extends SkuListModuleVP<BathDealDetailSkuListModuleOpt.Config> {

    public static final String CODE = "BathDealDetailSkuListModuleOpt";

    public static final List<String> ADDITIONAL_SORT = Lists.newArrayList("按摩", "搓澡", "餐饮", "住宿", "玩乐", "美容");

    @Override
    public List<DealDetailSkuListModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        List<DealDetailSkuListModuleGroupModel> result = Lists.newArrayList();
        // 构造服务流程模块
        result.add(buildDealDetailSkuListModuleGroupModel("服务流程模块", null, dealDetailInfoModel));
        // 构造自选套餐模块
        DealDetailSkuListModuleGroupModel additionalProject = AdditionalDealUtils.parseAdditionalProject(dealDetailInfoModel.getAdditionalProjectList(), config.getTagFormatMap(), config.getTitleReplaceList());
        sort(additionalProject);
        result.add(additionalProject);
        // 构造服务设施模块
        result.add(buildServiceFacilityModule(dealDetailInfoModel.getDealAttrs()));
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private void sort(DealDetailSkuListModuleGroupModel additionalProject) {
        if (additionalProject == null || CollectionUtils.isEmpty(additionalProject.getDealSkuGroupModuleVOS())){
            return;
        }
        // 按照加项分类进行排序
        additionalProject.getDealSkuGroupModuleVOS().sort(Comparator.comparingInt(o -> ADDITIONAL_SORT.indexOf(o.getTitle())));
    }

    /**
     * 构造服务流程模块
     */
    private List<DealSkuGroupModuleVO> buildDealSkuGroupModuleList(DealDetailInfoModel dealDetailInfoModel) {
        List<DealSkuGroupModuleVO> result = Lists.newArrayList();
        // 全部可享
        List<SkuItemDto> mustSkuItemList = DealSkuListModuleUtils.extractMustSkuItemList(dealDetailInfoModel);
        if (CollectionUtils.isNotEmpty(mustSkuItemList)) {
            // 将全部可享的项目进行productCategory的分组
            Map<Long, List<SkuItemDto>> productCategotyMap = mustSkuItemList.stream().collect(Collectors.groupingBy(SkuItemDto::getProductCategory));
            // 餐饮合并
            if (productCategotyMap.containsKey(BathServiceProjectEnum.FOOD_2.getServiceProjectId()) &&
                    productCategotyMap.containsKey(BathServiceProjectEnum.FOOD_3.getServiceProjectId())) {
                List<SkuItemDto> list = productCategotyMap.remove(BathServiceProjectEnum.FOOD_2.getServiceProjectId());
                productCategotyMap.get(BathServiceProjectEnum.FOOD_3.getServiceProjectId()).addAll(list);
            }
            if (productCategotyMap.containsKey(BathServiceProjectEnum.FOOD_1.getServiceProjectId()) &&
                    productCategotyMap.containsKey(BathServiceProjectEnum.FOOD_3.getServiceProjectId())) {
                List<SkuItemDto> list = productCategotyMap.remove(BathServiceProjectEnum.FOOD_1.getServiceProjectId());
                productCategotyMap.get(BathServiceProjectEnum.FOOD_3.getServiceProjectId()).addAll(list);
            }
            Map<Long, List<SkuItemDto>> sortedProductCategotyMap = Maps.newLinkedHashMap();
            for (Long serviceProjectId : BathServiceProjectEnum.SORTED_LIST) {
                if (productCategotyMap.containsKey(serviceProjectId)){
                    sortedProductCategotyMap.put(serviceProjectId, productCategotyMap.get(serviceProjectId));
                }
            }
            sortedProductCategotyMap.forEach((productCategoryId, skuList) -> {
                result.add(buildAllAvailableGroup(productCategoryId, skuList));
            });
        }
        // m选n
        List<OptionalSkuItemsGroupDto> optionalSkuItemList = DealSkuListModuleUtils.extractOptionalSkuItemsGroupList(dealDetailInfoModel);
        if (CollectionUtils.isNotEmpty(optionalSkuItemList)) {
            result.addAll(buildOptionalGroup(optionalSkuItemList));
        }
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 构造服务设施模块
     */
    private DealDetailSkuListModuleGroupModel buildServiceFacilityModule(List<AttrM> dealAttrs) {
        List<DealSkuVO> dealSkuList = BathServiceFacilityUtils.parseServiceFacility(dealAttrs);
        if (CollectionUtils.isEmpty(dealSkuList)) {
            return null;
        }
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(dealSkuList);
        return buildDealDetailSkuListModuleGroupModel("服务设施模块", null, Lists.newArrayList(dealSkuGroupModuleVO));
    }

    public DealDetailSkuListModuleGroupModel buildDealDetailSkuListModuleGroupModel(String groupName, String subTitle, DealDetailInfoModel dealDetailInfoModel) {
        DealDetailSkuListModuleGroupModel dealDetailSkuListModuleGroupModel = new DealDetailSkuListModuleGroupModel();
        dealDetailSkuListModuleGroupModel.setGroupName(groupName);
        dealDetailSkuListModuleGroupModel.setGroupSubtitle(subTitle);
        dealDetailSkuListModuleGroupModel.setDealSkuGroupModuleVOS(buildDealSkuGroupModuleList(dealDetailInfoModel));
        dealDetailSkuListModuleGroupModel.setGroupTitle(TimesDealUtil.isTimesDeal(dealDetailInfoModel) ? TimesDealUtil.getTimesTitle(dealDetailInfoModel.getDealAttrs()) : null);
        return dealDetailSkuListModuleGroupModel;
    }

    private DealDetailSkuListModuleGroupModel buildDealDetailSkuListModuleGroupModel(String groupName, String subTitle, List<DealSkuGroupModuleVO> dealSkuGroupModuleVOS) {
        DealDetailSkuListModuleGroupModel dealDetailSkuListModuleGroupModel = new DealDetailSkuListModuleGroupModel();
        dealDetailSkuListModuleGroupModel.setGroupName(groupName);
        dealDetailSkuListModuleGroupModel.setGroupSubtitle(subTitle);
        dealDetailSkuListModuleGroupModel.setDealSkuGroupModuleVOS(dealSkuGroupModuleVOS);
        return dealDetailSkuListModuleGroupModel;
    }

    private DealSkuGroupModuleVO buildAllAvailableGroup(Long productCategoryId, List<SkuItemDto> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return null;
        }
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        BathServiceProjectEnum bathServiceProjectEnum = BathServiceProjectEnum.getEnumByServiceProjectId(productCategoryId);
        dealSkuGroupModuleVO.setTitle(bathServiceProjectEnum != null ? bathServiceProjectEnum.getServiceProjectName() : null);
        dealSkuGroupModuleVO.setTitleStyle(1);
        dealSkuGroupModuleVO.setDealSkuList(skuList.stream().map(this::buildDealSkuVO).collect(Collectors.toList()));
        return dealSkuGroupModuleVO;
    }

    private List<DealSkuGroupModuleVO> buildOptionalGroup(List<OptionalSkuItemsGroupDto> optionalSkuItemList) {
        List<DealSkuGroupModuleVO> result = Lists.newArrayList();
        for (OptionalSkuItemsGroupDto optionalSkuItemsGroupDto : optionalSkuItemList) {
            List<SkuItemDto> skuItemList = optionalSkuItemsGroupDto.getSkuItems();
            if (CollectionUtils.isNotEmpty(skuItemList)) {
                DealSkuGroupModuleVO eitherOrGroup = new DealSkuGroupModuleVO();
                String title = String.format("以下 %d 选 %d ", skuItemList.size(), optionalSkuItemsGroupDto.getOptionalCount());
                eitherOrGroup.setTitle(title);
                eitherOrGroup.setTitleStyle(1);
                eitherOrGroup.setDealSkuList(skuItemList.stream().map(this::buildDealSkuVO).collect(Collectors.toList()));
                result.add(eitherOrGroup);
            }
        }
        return result;
    }

    private DealSkuVO buildDealSkuVO(SkuItemDto skuItemDto) {
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(skuItemDto.getName());
        dealSkuVO.setCopies(String.format("(%d份)", skuItemDto.getCopies()));
        dealSkuVO.setPrice(skuItemDto.getMarketPrice() == null ? null : "¥" + skuItemDto.getMarketPrice().stripTrailingZeros().toPlainString());
        dealSkuVO.setItems(BathSkuUtils.buildSkuItems(skuItemDto));
        return dealSkuVO;
    }

    @Data
    @VPointCfg
    public static class Config {

        private Map<String, String> tagFormatMap;

        private List<String> titleReplaceList;

    }
}
