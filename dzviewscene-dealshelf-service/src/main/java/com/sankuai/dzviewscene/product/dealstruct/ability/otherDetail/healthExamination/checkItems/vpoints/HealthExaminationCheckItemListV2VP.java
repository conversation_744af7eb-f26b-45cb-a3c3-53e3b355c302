package com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.healthExamination.checkItems.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.healthExamination.checkItems.HealthExaminationDealDetailCheckItemsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.healthExamination.checkItems.HealthExaminationDealDetailCheckItemsV2Builder;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination.HealthExaminationItemsGroupVO;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/1/10 11:58 上午
 */
@VPoint(name = "体检检查项列表变化点V2", description = "体检检查项列表变化点V2，该变化点用于构造含有二级检查项检查意义的检查项列表",code = HealthExaminationCheckItemListV2VP.CODE, ability = HealthExaminationDealDetailCheckItemsV2Builder.CODE)
public abstract class HealthExaminationCheckItemListV2VP<T> extends PmfVPoint<HealthExaminationItemsGroupVO, HealthExaminationCheckItemListV2VP.Param, T> {

    public static final String CODE = "healthExaminationCheckItemListV2VP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private DealDetailInfoModel dealDetailBasicInfo;
    }
}
