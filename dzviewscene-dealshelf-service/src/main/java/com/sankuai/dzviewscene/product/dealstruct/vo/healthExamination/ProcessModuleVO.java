package com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/18 11:00 上午
 */
@MobileDo(id = 0x2230)
public class ProcessModuleVO implements Serializable {
    /**
     * 流程步骤列表
     */
    @MobileDo.MobileField(key = 0xf5e2)
    private List<ProcessModuleItemVO> processes;

    /**
     * 流程名
     */
    @MobileDo.MobileField(key = 0x3195)
    private String processName;

    public List<ProcessModuleItemVO> getProcesses() {
        return processes;
    }

    public void setProcesses(List<ProcessModuleItemVO> processes) {
        this.processes = processes;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }
}
