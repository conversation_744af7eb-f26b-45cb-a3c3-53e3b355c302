package com.sankuai.dzviewscene.product.filterlist.option.builder.product.saleprice;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePriceVP;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.card.CardFetcher;
import com.sankuai.dzviewscene.product.utils.CardPromoUtils;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.utils.ShelfErrorUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

@VPointOption(name = "优惠码商家会员会员价",
        description = "优惠码业务使用，对满足条件的商家展示会员价或者新会员价商品的售卖价格处理",
        code = "PromoCodeMerchantMemberProductSalePriceOpt")
@Slf4j
public class PromoCodeMerchantMemberProductSalePriceOpt extends ProductSalePriceVP<PromoCodeMerchantMemberProductSalePriceOpt.MerchantMemberConfig> {
    @Override
    public String compute(ActivityCxt activityCxt, Param param, MerchantMemberConfig config) {
        ProductM productM = param.getProductM();
        try {
            ProductPromoPriceM userProductPromoPriceM = PriceUtils.getUserHasPromoPrice(param.getProductM(), param.getCardM());
            if(Objects.nonNull(userProductPromoPriceM) && StringUtils.isNotBlank(userProductPromoPriceM.getPromoPriceTag())){
                return userProductPromoPriceM.getPromoPriceTag();
            }
        } catch (Exception e) {
            log.error("PromoCodeMerchantMemberProductSalePriceOpt error, productM: {}", JsonCodec.encode(productM), e);
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
        }
        //团购
        return productM.getBasePriceTag();
    }

    @VPointCfg
    @Data
    public static class MerchantMemberConfig {

    }
}
