package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints.SkuItemFilterVP;
import com.sankuai.dzviewscene.product.dealstruct.model.SkuItemModel;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/10 4:58 下午
 */
@VPointOption(name = "按照sku类别Sku对元素进行过滤变化点", description = "按照sku类别Sku对元素进行过滤变化点",code = SkuItemFilterBySkuCategoryVPO.CODE, isDefault = false)
public class SkuItemFilterBySkuCategoryVPO extends SkuItemFilterVP<SkuItemFilterBySkuCategoryVPO.Config> {

    public static final String CODE = "SkuItemFilterBySkuCategoryVPO";

    @Override
    public Boolean compute(ActivityCxt context, Param param, Config config) {
        if (config == null || CollectionUtils.isEmpty(config.getFilteredSkuCategoryId())) {
            return true;
        }
        SkuItemModel skuItemModel = param.getSkuItemModel();
        if (skuItemModel == null) {
            return false;
        }
        return !config.getFilteredSkuCategoryId().contains(skuItemModel.getProductCategory());
    }

    @Data
    @VPointCfg
    public static class Config {
        //过滤掉的sku ID列表
        private List<Long> filteredSkuCategoryId;
    }
}
