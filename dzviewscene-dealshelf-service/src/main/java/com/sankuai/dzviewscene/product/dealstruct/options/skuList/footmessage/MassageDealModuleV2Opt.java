package com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.OverNightModuleUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.MassageFreeFoodUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.MassageServiceFacilityUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: created by hang.yu on 2023/9/12 15:38
 */
@VPointOption(name = "足疗团详sku列表组变化点V2", description = "足疗团详sku列表组变化点V2", code = MassageDealModuleV2Opt.CODE)
public class MassageDealModuleV2Opt extends AbstractFootMessageModuleOpt<MassageDealModuleV2Opt.Config> {

    public static final String CODE = "MassageDealModuleV2Opt";

    @Override
    public List<DealDetailSkuListModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        // 获取第一个必选组的第一个skuItemDto(足疗标准化团单特点)
        SkuItemDto skuItemDto = DealDetailUtils.extractFirstMustSkuFromDealDetailInfoModel(dealDetailInfoModel);
        if (skuItemDto == null) {
            return Lists.newArrayList();
        }
        boolean hitNewIcon = hitNewIcon(param.getDouhuResultModels(), config.getExpIds());
        List<DealDetailSkuListModuleGroupModel> result = Lists.newArrayList();
        List<AttrM> dealAttrs = dealDetailInfoModel.getDealAttrs();
        // 构造服务流程模块
        result.add(buildServiceFlowModule(skuItemDto, dealAttrs, dealDetailInfoModel));
        // 构造免费服务模块 包括餐食、过夜
        result.add(buildFreeServiceModule(skuItemDto, dealAttrs, hitNewIcon));
        // 构造付费服务模块 过夜服务
        result.add(buildPayServiceModule(dealAttrs, hitNewIcon));
        // 构造服务设施模块
        result.add(buildServiceFacilityModule(skuItemDto, hitNewIcon));
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 构建服务流程模块
     */
    private DealDetailSkuListModuleGroupModel buildServiceFlowModule(SkuItemDto skuItemDto, List<AttrM> dealAttrs, DealDetailInfoModel dealDetailInfoModel) {
        String serviceType = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "service_type");
        String title = getServiceFlowSkuName2(skuItemDto.getAttrItems(), serviceType);
        String price = skuItemDto.getMarketPrice() == null ? null : "￥" + skuItemDto.getMarketPrice().stripTrailingZeros().toPlainString();
        DealSkuVO dealSkuVO = buildDealSkuVO(title, getSkuSubTitle(skuItemDto), price, getServiceItems2(skuItemDto.getAttrItems()));
        String groupTitle = TimesDealUtil.isTimesDeal(dealDetailInfoModel) ? TimesDealUtil.getTimesTitle(dealAttrs) : null;
        return buildDealDetailSkuListModuleGroupModel("服务流程模块", groupTitle, null, Lists.newArrayList(dealSkuVO));
    }

    /**
     * 构建免费服务模块
     */
    private DealDetailSkuListModuleGroupModel buildFreeServiceModule(SkuItemDto skuItemDto, List<AttrM> dealAttrs, boolean hitNewIcon) {
        List<DealSkuVO> freeServices = buildFreeServices(skuItemDto, dealAttrs, hitNewIcon);
        if (CollectionUtils.isEmpty(freeServices)) {
            return null;
        }
        return buildDealDetailSkuListModuleGroupModel("免费服务模块", "服务加码不加价", freeServices);
    }

    /**
     * 构建服务设施模块
     */
    private DealDetailSkuListModuleGroupModel buildServiceFacilityModule(SkuItemDto skuItemDto, boolean hitNewIcon) {
        List<DealSkuVO> list = MassageServiceFacilityUtils.parseServiceFacility(skuItemDto, hitNewIcon);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return buildDealDetailSkuListModuleGroupModel("服务设施模块", null, list);
    }

    private List<DealSkuVO> buildFreeServices(SkuItemDto skuItemDto, List<AttrM> dealAttrs, boolean hitNewIcon) {
        List<DealSkuVO> result = Lists.newArrayList();
        // 附赠项目
        result.add(buildGrantProduct(skuItemDto));
        // 免费餐食
        result.add(MassageFreeFoodUtils.parseFreeFood(skuItemDto, hitNewIcon));
        // 过夜服务
        result.add(OverNightModuleUtils.parseFreeOverNightSkuModule(dealAttrs, hitNewIcon));
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 构建附赠项目
     *
     * @param skuItemDto
     * @return
     */
    private DealSkuVO buildGrantProduct(SkuItemDto skuItemDto) {
        String grantProductStr = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), "grantProduct");
        if (StringUtils.isEmpty(grantProductStr)) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle("附赠流程");
        dealSkuVO.setSubTitle("不计入项目总时长");
        DealSkuItemVO skuItemVO = new DealSkuItemVO();
        skuItemVO.setType(0);
        skuItemVO.setName(grantProductStr);
        skuItemVO.setIcon("https://p0.meituan.net/ingee/4ea3ffc3dfd33d26592339cfcaf5e7212161.png");
        dealSkuVO.setItems(Lists.newArrayList(skuItemVO));
        return dealSkuVO;
    }

    private DealSkuVO buildDealSkuVO(String title, String subTitle, String price, List<DealSkuItemVO> dealSkuItemVO) {
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(title);
        dealSkuVO.setSubTitle(subTitle);
        dealSkuVO.setPrice(price);
        dealSkuVO.setItems(dealSkuItemVO);
        return dealSkuVO;
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<String> expIds;
    }

}
