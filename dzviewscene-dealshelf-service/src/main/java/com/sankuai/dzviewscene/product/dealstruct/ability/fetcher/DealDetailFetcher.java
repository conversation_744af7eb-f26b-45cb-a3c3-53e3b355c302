package com.sankuai.dzviewscene.product.dealstruct.ability.fetcher;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dztheme.deal.res.standardservice.*;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.MassageFreeFoodUtils;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.uniform.UniformStructContentModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.uniform.UniformStructModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealStructModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.DealAdditionalProjectM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/22 7:47 下午
 * TODO:之后要改成不再依赖multiGroupPaddingFetcher的开发方式
 */
@Ability(code = DealDetailFetcher.CODE,
        name = "团购详情模块取数",
        description = "团购详情模块取数能力，团单属性名支持配置",
        activities = {DealDetailActivity.CODE}
)
public class DealDetailFetcher extends PmfAbility<List<DealDetailInfoModel>, DealDetailFecherParam, DealDetailFetcherCfg> {

    public static final String CODE = "dealDetailFetcher";

    private static final String DEAL_STRUCT_CONTENTT_ATTR_NAME = "dealStructContent";

    public static final String DETAIL_INFO = "detailInfo";

    private static final String RICH_TEXT = "richtext";
    //standardDealStructContent
    private static final String  STANDARD_DEAL_STRUCT_CONTENT  = "standardDealStructContent";

    @Resource
    private MultiGroupPaddingFetcher multiGroupPaddingFetcher;

    @Override
    public CompletableFuture<List<DealDetailInfoModel>> build(ActivityCxt activityCxt, DealDetailFecherParam fecherParam, DealDetailFetcherCfg fetcherCfg) {
        //1.增加配置化参数
        addDealQueryParams(activityCxt, fetcherCfg);
        //2.设置团单id
        activityCxt.attach(PaddingFetcher.Attachments.productGroups, CompletableFuture.completedFuture(buildGroup2Products(activityCxt)));
        //3.调用原统一融合查询能力
        return multiGroupPaddingFetcher.build(ActivityCtxtUtils.toActivityContext(activityCxt)).thenApply(groupName2ProductGroupMMap -> {
            ProductGroupM productGroupM = groupName2ProductGroupMMap.get("deal");
            if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())) {
                addFoodUseNewData(activityCxt, Lists.newArrayList(), fetcherCfg.isFoodUseNewDataSetValue());
                return Lists.newArrayList();
            }
            List<DealDetailInfoModel> dealDetailInfoModels = productGroupM.getProducts().stream()
                    .map(productM -> convertProductGroupM2DealDetailInfoModel(productM))
                    .filter(model -> model != null).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(dealDetailInfoModels)) {
                addFoodUseNewData(activityCxt, Lists.newArrayList(), fetcherCfg.isFoodUseNewDataSetValue());
                return Lists.newArrayList();
            }
            // 餐食是否使用新数据
            addFoodUseNewData(activityCxt, dealDetailInfoModels, fetcherCfg.isFoodUseNewDataSetValue());
            return dealDetailInfoModels;
        });
    }

    /**
     * 餐食是否使用新数据设值
     */
    private void addFoodUseNewData(ActivityCxt activityCxt, List<DealDetailInfoModel> dealDetailInfoModels, boolean foodUseNewDataSetValue) {
        if (!foodUseNewDataSetValue) {
            return;
        }
        if (CollectionUtils.isEmpty(dealDetailInfoModels)) {
            activityCxt.addParam(QueryFetcher.Params.foodUseNewData, false);
            return;
        }
        DealDetailInfoModel dealDetailInfoModel = dealDetailInfoModels.get(0);
        SkuItemDto skuItemDto = DealDetailUtils.extractFirstMustSkuFromDealDetailInfoModel(dealDetailInfoModel);
        if (skuItemDto == null) {
            activityCxt.addParam(QueryFetcher.Params.foodUseNewData, false);
            return;
        }
        String freeFood = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), "freeFood");
        // 老餐食为空时 默认走新样式
        String foodContentArray = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), "foodContentArray");
        activityCxt.addParam(QueryFetcher.Params.foodUseNewData, MassageFreeFoodUtils.FOOD_NEW_DATA.contains(freeFood) || StringUtils.isBlank(foodContentArray));
    }

    /**
     *
     */
    private void addDealQueryParams(ActivityCxt activityCxt, DealDetailFetcherCfg fetcherCfg) {
        List<String> groupNames = Lists.newArrayList("deal");
        Map<String, Map<String, Object>> groupParams = Maps.newHashMap();
        Map<String, Object> dealParams = Maps.newHashMap();
        groupParams.put("deal", dealParams);
        dealParams.put("paddingType", "dealThemePadding");
        dealParams.put("planId", fetcherCfg.getPlanId());
        dealParams.put("attributeKeys", fetcherCfg.getAttributeKeys());
        // 套餐属性配置
        dealParams.put("dealAttributeKeys", fetcherCfg.getDealAttributeKeys());
        dealParams.put("bpDealGroupTypes", fetcherCfg.getBpDealGroupTypeList());
        if (fetcherCfg.getRelatedContentConfig() != null) {
            dealParams.put("contentSubBizTypeList", fetcherCfg.getRelatedContentConfig().getContentSubBizTypeList());
            dealParams.put("fetchContentDetailTag", fetcherCfg.getRelatedContentConfig().getFetchContentDetailTag());
        }
        if (MapUtils.isNotEmpty(fetcherCfg.getParams())) {
            dealParams.putAll(fetcherCfg.getParams());
        }
        // 加项属性key
        dealParams.put("additionalAttrKeys", fetcherCfg.getAdditionalAttrKeys());
        dealParams.put("combineType", fetcherCfg.getCombineType());
        activityCxt.addParam(QueryFetcher.Params.groupNames, groupNames);
        activityCxt.addParam(QueryFetcher.Params.groupParams, groupParams);
    }

    private Map<String, ProductGroupM> buildGroup2Products(ActivityCxt ctx) {
        String groupName = getGroupName(ctx);
        Map<String, ProductGroupM> result = new HashMap<>();
        ProductGroupM productGroupM = getProductGroupM(ctx);
        result.put(groupName, productGroupM);
        return result;
    }

    private ProductGroupM getProductGroupM(ActivityCxt ctx) {
        ProductGroupM productGroupM = new ProductGroupM();
        List<ProductM> products = new ArrayList<>();
        ProductM productM = new ProductM();
        productM.setProductId(ctx.getParam(ProductDetailActivityConstants.Params.productId));
        products.add(productM);
        productGroupM.setProducts(products);
        return productGroupM;
    }

    private String getGroupName(ActivityCxt activityContext) {
        List<String> groupNames = activityContext.getParam(QueryFetcher.Params.groupNames);
        if (CollectionUtils.isEmpty(groupNames)) {
            return "";
        }
        return groupNames.get(0);
    }

    private DealDetailInfoModel convertProductGroupM2DealDetailInfoModel(ProductM productM) {
        if (productM == null) {
            return null;
        }
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        //团购详情ID
        dealDetailInfoModel.setDealId(productM.getProductId());
        //团购详情sku货结构化模型
        DealDetailDtoModel dealDetailDtoModel = getDealDetailModel(productM);
        if (dealDetailDtoModel != null) {
            dealDetailInfoModel.setDealDetailDtoModel(dealDetailDtoModel);
        }
        //团购详情sku货类目列表
        List<ProductSkuCategoryModel> productCategories = getProductCategories(productM);
        if (CollectionUtils.isNotEmpty(productCategories)) {
            dealDetailInfoModel.setProductCategories(productCategories);
        }
        //团购详情富文本Desc信息
        String desc = getDesc(productM);
        if (StringUtils.isNotEmpty(desc)) {
            dealDetailInfoModel.setDesc(desc);
        }
        //团单属性信息
        dealDetailInfoModel.setDealAttrs(productM.getExtAttrs());
        //团单组件属性信息
        dealDetailInfoModel.setDealModuleAttrs(getDealModuleAttrs(productM));
        //团单标题
        dealDetailInfoModel.setDealTitle(productM.getTitle());
        //市场价 不含单位
        dealDetailInfoModel.setMarketPrice(productM.getMarketPrice());
        //售价
        if (productM.getBasePrice() != null) {
            dealDetailInfoModel.setSalePrice(productM.getBasePrice().stripTrailingZeros().toPlainString());
        }
        //标准化服务项目
        dealDetailInfoModel.setStandardServiceProjectDTO(getStandardDealStructContent(productM));
        dealDetailInfoModel.setDealStandardServiceProjectDTO(productM.getStandardServiceProjectDTO());
        // 结构化内容的服务项目
        dealDetailInfoModel.setDealDetailStructuredDTO(productM.getDealDetailStructuredDTO());
        // 是否为太极团单
        dealDetailInfoModel.setUnifyProduct(productM.isUnifyProduct());
        // 团购交易类型
        dealDetailInfoModel.setTradeType(productM.getTradeType());
        // 团单加项列表
        List<DealAdditionalProjectM> additionProjects = Optional.ofNullable(productM.getAdditionalProjectList())
                .orElse(Lists.newArrayList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
        dealDetailInfoModel.setAdditionalProjectList(additionProjects);
        return dealDetailInfoModel;
    }

    private StandardServiceProjectDTO getStandardDealStructContent(ProductM productM) {
        if (Objects.isNull(productM)) {
            return null;
        }
        DealStandardServiceProjectDTO dealStandardServiceProjectDTO = productM.getStandardServiceProjectDTO();
        if (Objects.nonNull(dealStandardServiceProjectDTO)) {
            return convertToStandardServiceProjectDTO(dealStandardServiceProjectDTO);
        }
        String standardServiceProject = productM.getAttr(STANDARD_DEAL_STRUCT_CONTENT);
        if (StringUtils.isBlank(standardServiceProject)) {
            return null;
        }
        return JsonCodec.decode(standardServiceProject, StandardServiceProjectDTO.class);
    }

    private static final String DEAL_MODULE_ATTRS_NAME = "dealModuleAttrs";

    /**
     * 获取团单组件属性信息，即上单页面通过组件形式上单的团单属性信息，一般是以组件对应的json数据形式返回
     *
     * @param
     * @return
     */
    List<UniformStructContentModel> getDealModuleAttrs(ProductM productM) {
        if (productM == null || StringUtils.isEmpty(productM.getAttr(DEAL_MODULE_ATTRS_NAME))) {
            return null;
        }
        return JsonCodec.converseList(productM.getAttr(DEAL_MODULE_ATTRS_NAME), UniformStructContentModel.class);
    }

    private List<ProductSkuCategoryModel> getProductCategories(ProductM productM) {
        if (productM == null || StringUtils.isEmpty(productM.getAttr(DEAL_STRUCT_CONTENTT_ATTR_NAME))) {
            return null;
        }
        String dealDetail = productM.getAttr(DEAL_STRUCT_CONTENTT_ATTR_NAME);
        DealStructModel dealStructModel = getDealStructModel(dealDetail);
        if (dealStructModel == null) {
            return null;
        }
        return dealStructModel.getProductCategories();
    }

    private DealDetailDtoModel getDealDetailModel(ProductM productM) {
        if (productM == null || StringUtils.isEmpty(productM.getAttr(DEAL_STRUCT_CONTENTT_ATTR_NAME))) {
            return null;
        }
        String dealDetail = productM.getAttr(DEAL_STRUCT_CONTENTT_ATTR_NAME);
        DealStructModel dealStructModel = getDealStructModel(dealDetail);
        if (dealStructModel == null || StringUtils.isEmpty(dealStructModel.getStract())) {
            return null;
        }
        DealDetailDtoModel dealDetailDtoModel = JsonCodec.decode(dealStructModel.getStract(), DealDetailDtoModel.class);
        if (dealDetailDtoModel == null || dealDetailDtoModel.getSkuUniStructuredDto() == null) {
            return null;
        }
        return dealDetailDtoModel;
    }

    private DealStructModel getDealStructModel(String dealDetail) {
        if (StringUtils.isEmpty(dealDetail)) {
            return null;
        }
        DealStructModel dealStructModel = JsonCodec.decode(dealDetail, DealStructModel.class);
        if (dealStructModel == null || StringUtils.isEmpty(dealStructModel.getStract())) {
            return null;
        }
        return dealStructModel;
    }

    private String getDesc(ProductM productM) {
        if (productM == null || StringUtils.isEmpty(productM.getAttr(DETAIL_INFO))) {
            return null;
        }
        String dealDetailText = productM.getAttr(DETAIL_INFO);
        UniformStructModel uniformStruct = JsonCodec.decode(dealDetailText, UniformStructModel.class);
        if (uniformStruct == null) {
            return null;
        }
        return getRichText(uniformStruct.getContent());
    }

    private String getRichText(List<UniformStructContentModel> structContentModels) {
        if (CollectionUtils.isEmpty(structContentModels)) {
            return null;
        }
        Object struct = structContentModels.stream().filter(model -> StringUtils.isNotEmpty(model.getType()) && model.getType().equals(RICH_TEXT)).findFirst().orElse(null);
        if (struct == null) {
            return null;
        }
        if (!(struct instanceof UniformStructContentModel)) {
            return null;
        }
        UniformStructContentModel richTextModel = (UniformStructContentModel) struct;
        if (richTextModel == null || richTextModel.getData() == null) {
            return null;
        }
        return richTextModel.getData().toString();
    }

    private StandardServiceProjectDTO convertToStandardServiceProjectDTO(DealStandardServiceProjectDTO dealStandardServiceProjectDTO) {
        if (dealStandardServiceProjectDTO == null) {
            return null;
        }
        StandardServiceProjectDTO standardServiceProjectDTO = new StandardServiceProjectDTO();
        // 转换必选分组
        List<StandardServiceProjectGroupDTO> mustGroups = convertGroups(dealStandardServiceProjectDTO.getMustGroups());
        standardServiceProjectDTO.setMustGroups(mustGroups);
        // 转换可选分组
        List<StandardServiceProjectGroupDTO> optionalGroups = convertGroups(dealStandardServiceProjectDTO.getOptionalGroups());
        standardServiceProjectDTO.setOptionalGroups(optionalGroups);
        return standardServiceProjectDTO;
    }

    private List<StandardServiceProjectGroupDTO> convertGroups(List<DealStandardServiceProjectGroupDTO> dealGroups) {
        if (CollectionUtils.isEmpty(dealGroups)) {
            return Collections.emptyList();
        }
        List<StandardServiceProjectGroupDTO> groups = new ArrayList<>();
        for (DealStandardServiceProjectGroupDTO dealGroup : dealGroups) {
            StandardServiceProjectGroupDTO group = new StandardServiceProjectGroupDTO();
            group.setOptionalCount(dealGroup.getOptionalCount());
            // 转换服务项目条目
            List<StandardServiceProjectItemDTO> items = CollectionUtils.isEmpty(dealGroup.getServiceProjectItems())
                    ? Collections.emptyList()
                    : dealGroup.getServiceProjectItems().stream().filter(Objects::nonNull).map(this::convertItem).collect(Collectors.toList());
            group.setServiceProjectItems(items);
            groups.add(group);
        }
        return groups;
    }

    private StandardServiceProjectItemDTO convertItem(DealStandardServiceProjectItemDTO dealItem) {
        StandardServiceProjectItemDTO item = new StandardServiceProjectItemDTO();
        item.setServiceProjectName(dealItem.getServiceProjectName());
        // 转换服务项目属性
        StandardAttributeDTO attributeDTO = convertAttribute(dealItem.getStandardAttribute());
        item.setStandardAttribute(attributeDTO);
        return item;
    }

    private StandardAttributeDTO convertAttribute(DealStandardAttributeDTO dealAttribute) {
        if (dealAttribute == null || CollectionUtils.isEmpty(dealAttribute.getAttrs())) {
            return null;
        }
        StandardAttributeDTO attributeDTO = new StandardAttributeDTO();
        // 转换属性条目
        List<StandardAttributeItemDTO> attrs = dealAttribute.getAttrs().stream()
                .map(this::convertAttributeItem)
                .collect(Collectors.toList());
        attributeDTO.setAttrs(attrs);
        // 此处没有cpvObjectId和cpvObjectVersion的直接映射，所以不进行赋值
        return attributeDTO;
    }

    private StandardAttributeItemDTO convertAttributeItem(DealStandardAttributeItemDTO dealItem) {
        StandardAttributeItemDTO itemDTO = new StandardAttributeItemDTO();
        itemDTO.setAttrName(dealItem.getAttrName());
        itemDTO.setAttrCnName(dealItem.getAttrCnName());
        // 转换属性值
        List<StandardAttributeValueDTO> attrValues = CollectionUtils.isEmpty(dealItem.getAttrValues()) ? Collections.emptyList()
                : dealItem.getAttrValues().stream().map(this::convertAttributeValue).collect(Collectors.toList());
        itemDTO.setAttrValues(attrValues);
        return itemDTO;
    }

    private StandardAttributeValueDTO convertAttributeValue(DealStandardAttributeValueDTO dealValue) {
        StandardAttributeValueDTO valueDTO = new StandardAttributeValueDTO();
        valueDTO.setType(dealValue.getType());
        valueDTO.setSimpleValues(dealValue.getSimpleValues());
        valueDTO.setComplexValues(dealValue.getComplexValues());
        return valueDTO;
    }
}
