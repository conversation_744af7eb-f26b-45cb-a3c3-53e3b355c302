package com.sankuai.dzviewscene.product.dealstruct.options.skuList.edu;


import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.OptionalSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.EduSkuUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@VPointOption(name = "教育-团单的课程列表变化点", description = "教育-团单的课程列表变化点，来自服务项目", code = EduClassItemFromSkuOpt.CODE)
public class EduClassItemFromSkuOpt extends SkuListModuleVP<EduClassItemFromSkuOpt.Config> {

    public static final String CODE = "eduClassItemFromSkuOpt";

    public static final int SKU_ITEM_TYPE_NORMAL_LIST = 1;
    public static final int SKU_ITEM_TYPE_ATTR_LIST = 2;
    public static final String LEARNING_OBJECTIVE = "learningObjective";
    public static final String CLASS_HIGHLIGHTS = "classHighlights";
    public static final String CLASS_NATURE = "classNature";
    public static final String EDU_COURSE_TYPE_SHORT_COURSE = "短期课";
    public static final String EDU_COURSE_TYPE = "eduCourseType";
    public static final String EDU_SERVICE_ITEM_TABLE = "eduserviceitemtable";
    public static final String SERVICE_POSITION = "servicePosition";
    public static final String COURSE_DURATION_NUMBER = "CourseDurationNumber";
    public static final String OPEN_PERIOD_APPEND = "天内完成课程学习";
    public static final String SPLIT = "，";
    public static final String OBJECT_AS_PLATFORM = "采用平台统一描述";
    public static final String OBJECT_AS_SELF = "自定义课程目标";
    // 开课频率
    public static final String CLASS_FREQUENCY = "kecianpai";
    public static final String FORMAT_OPEN_CLASS = "每周%s次，每次%s分钟";
    public static final String ATTR_CLASS_CONTENT_NIGHT_SCHOOL = "kechengneirong";
    public static final String MATERIAL_COST_EXPLAIN = "materialCostExplain";
    public static final String GIFT_FOR_NIGHT_SCHOOL = "mianfeizengsong";
    public static final String OTHER_FEE = "buchongshoufei";
    public static final String NOT_NEED_MATERIAL_FEE = "课程无需材料";
    public static final String CLASS_DURATION_TEMPLATE = "(每节%s分钟)";

    private static final Pattern NUMBER_REGEX = Pattern.compile("\\d+");

    @Override
    public List<DealDetailSkuListModuleGroupModel> compute(ActivityCxt activityCxt, Param param, Config config) {
        List<DealSkuVO> dealSkuList = buildSkuVO(activityCxt, param.getDealDetailInfoModel(), config);
        if (CollectionUtils.isEmpty(dealSkuList)) {
            return null;
        }
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(dealSkuList);
        DealDetailSkuListModuleGroupModel dealDetailSkuListModuleGroupModel = new DealDetailSkuListModuleGroupModel();
        dealDetailSkuListModuleGroupModel.setDealSkuGroupModuleVOS(Lists.newArrayList(dealSkuGroupModuleVO));
        return Lists.newArrayList(dealDetailSkuListModuleGroupModel);
    }

    /**
     * 构建SkuVo
     *
     * @param activityCxt
     * @param dealDetailInfoModel
     * @param config
     * @return
     */
    private List<DealSkuVO> buildSkuVO(ActivityCxt activityCxt, DealDetailInfoModel dealDetailInfoModel, Config config) {
        if (config.isJustShowOnShortCourse() && !isSortCourse(dealDetailInfoModel)) {
            return null;
        }
        List<CoursePlan> coursePlanList = getCoursePlanList(dealDetailInfoModel);
        List<DealSkuItemVO> skuItemList = buildSkuItemList(dealDetailInfoModel, config, coursePlanList);
        if (CollectionUtils.isEmpty(skuItemList)) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(getTitle(dealDetailInfoModel, config));
        dealSkuVO.setSubTitle(getTotalClassHours(dealDetailInfoModel, coursePlanList, config));
        dealSkuVO.setPrice(getCoursePrice(dealDetailInfoModel, coursePlanList, config));
        dealSkuVO.setItems(skuItemList);
        return Lists.newArrayList(dealSkuVO);
    }

    private String getTotalClassHours(DealDetailInfoModel dealDetailInfoModel, List<CoursePlan> coursePlanList, Config config) {
        BigDecimal totalCourseNum = getTotalCourseNum(dealDetailInfoModel, coursePlanList, config);
        if (totalCourseNum == null || totalCourseNum.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        return totalCourseNum.stripTrailingZeros().toPlainString() + config.getCourseUnitStr();

    }

    private List<CoursePlan> getCoursePlanList(DealDetailInfoModel dealDetailInfoModel) {
        String json = DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                dealDetailInfoModel.getDealDetailDtoModel(), EDU_SERVICE_ITEM_TABLE);
        if (StringUtils.isBlank(json)) {
            json = DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                    dealDetailInfoModel.getDealDetailDtoModel(), ATTR_CLASS_CONTENT_NIGHT_SCHOOL);
        }
        if (StringUtils.isBlank(json)) {
            return null;
        }
        List<EduServiceItemTable> courseList = JsonCodec.converseList(json, EduServiceItemTable.class);
        if (CollectionUtils.isEmpty(courseList)) {
            return null;
        }
        return courseList.stream().map(item -> {
            CoursePlan coursePlan = new CoursePlan();
            coursePlan.setCourseTitle(item.getEduContent());
            return coursePlan;
        }).collect(Collectors.toList());
    }

    private BigDecimal getTotalCourseNum(DealDetailInfoModel dealDetailInfoModel, List<CoursePlan> coursePlanList, Config config) {
        if (CollectionUtils.isEmpty(coursePlanList)) {
            return getCourseNumFromSku(dealDetailInfoModel, config);
        }
        BigDecimal totalCourseNum = BigDecimal.ZERO;
        for (CoursePlan coursePlan : coursePlanList) {
            if (coursePlan == null || coursePlan.getCourseTimeNum() == null) {
                continue;
            }
            totalCourseNum = totalCourseNum.add(coursePlan.getCourseTimeNum());
        }
        if (totalCourseNum.compareTo(BigDecimal.ZERO) > 0) {
            return totalCourseNum;
        }
        return getCourseNumFromSku(dealDetailInfoModel, config);
    }

    @NotNull
    private BigDecimal getCourseNumFromSku(DealDetailInfoModel dealDetailInfoModel, Config config) {
        String attrValue = getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel, config.getClassCountAttrKeys());
        if (StringUtils.isNotBlank(attrValue)) {
            return new BigDecimal(getNumberStr(attrValue));
        }
        return BigDecimal.ZERO;
    }

    private String getCoursePrice(DealDetailInfoModel dealDetailInfoModel, List<CoursePlan> coursePlanList, Config config) {
        if (!config.isShowCoursePrice()) {
            return null;
        }
        BigDecimal totalPrice = getTotalPrice(dealDetailInfoModel, coursePlanList);
        if (totalPrice == null || totalPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        return String.format(config.getCoursePriceTemplate(),
                totalPrice.setScale(BigDecimal.ROUND_HALF_UP, 2).stripTrailingZeros().toPlainString());
    }

    private BigDecimal getTotalPrice(DealDetailInfoModel dealDetailInfoModel, List<CoursePlan> coursePlanList) {
        if (CollectionUtils.isEmpty(coursePlanList)) {
            return getTotalPriceFromSku(dealDetailInfoModel);
        }
        BigDecimal totalPrice = BigDecimal.ZERO;
        for (CoursePlan coursePlan : coursePlanList) {
            if (coursePlan == null || coursePlan.getCoursePrice() == null) {
                continue;
            }
            totalPrice = totalPrice.add(coursePlan.getCoursePrice());
        }
        if (totalPrice.compareTo(BigDecimal.ZERO) > 0) {
            return totalPrice;
        }
        return getTotalPriceFromSku(dealDetailInfoModel);
    }

    private BigDecimal getTotalPriceFromSku(DealDetailInfoModel dealDetailInfoModel) {
        if (dealDetailInfoModel.getDealDetailDtoModel() != null && dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto() != null) {
            SkuItemDto courseItem = findFirstCourseItem(dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto());
            if (courseItem == null || courseItem.getMarketPrice() == null) {
                return getTotalPriceFromSkuAsTotal(dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto());
            }
            return courseItem.getMarketPrice();
        }
        return null;
    }

    private BigDecimal getTotalPriceFromSkuAsTotal(DealDetailSkuUniStructuredDto skuUniStructuredDto) {
        if (skuUniStructuredDto.getMarketPrice() != null) {
            return new BigDecimal(skuUniStructuredDto.getMarketPrice());
        }
        return null;
    }

    private SkuItemDto findFirstCourseItem(DealDetailSkuUniStructuredDto skuUniStructuredDto) {
        if (CollectionUtils.isNotEmpty(skuUniStructuredDto.getMustGroups())) {
            SkuItemDto findSkuItem = findFirstCourseItemFromMust(skuUniStructuredDto.getMustGroups());
            if (findSkuItem != null) {
                return findSkuItem;
            }
        }
        if (CollectionUtils.isEmpty(skuUniStructuredDto.getOptionalGroups())) {
            return null;
        }
        return findFirstCourseItemFromOption(skuUniStructuredDto.getOptionalGroups());
    }

    private SkuItemDto findFirstCourseItemFromOption(List<OptionalSkuItemsGroupDto> groupDtos) {
        for (OptionalSkuItemsGroupDto optionGroup : groupDtos) {
            if (CollectionUtils.isEmpty(optionGroup.getSkuItems())) {
                return null;
            }
            SkuItemDto findSkuItem = optionGroup.getSkuItems().stream()
                    .filter(item -> item != null && StringUtils.isNotBlank(DealDetailUtils.getSkuAttrValueBySkuAttrName(item.getAttrItems(), EDU_SERVICE_ITEM_TABLE)))
                    .findFirst().orElse(null);
            if (findSkuItem != null) {
                return findSkuItem;
            }
        }
        return null;
    }

    private SkuItemDto findFirstCourseItemFromMust(List<MustSkuItemsGroupDto> mustGroups) {
        for (MustSkuItemsGroupDto mustGroup : mustGroups) {
            if (CollectionUtils.isEmpty(mustGroup.getSkuItems())) {
                return null;
            }
            SkuItemDto findSkuItem = mustGroup.getSkuItems().stream()
                    .filter(item -> item != null && StringUtils.isNotBlank(DealDetailUtils.getSkuAttrValueBySkuAttrName(item.getAttrItems(), EDU_SERVICE_ITEM_TABLE)))
                    .findFirst().orElse(null);
            if (findSkuItem != null) {
                return findSkuItem;
            }
        }
        return null;
    }

    private String getTitle(DealDetailInfoModel dealDetailInfoModel, Config config) {
        if (CollectionUtils.isEmpty(config.getModelNameFromSkuAttrKeys())) {
            return dealDetailInfoModel.getDealTitle();
        }
        String title = getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel, config.getModelNameFromSkuAttrKeys());
        if (StringUtils.isNotBlank(title)) {
            return title;
        }
        // 取服务项目的名称
        if (dealDetailInfoModel.getDealDetailDtoModel() != null && StringUtils.isNotBlank(config.getModelNameFromSkuNameShoHasAttrKey())) {
            SkuItemDto showItem = DealDetailUtils.findFirstSkuItemWhoHasAttr(dealDetailInfoModel.getDealDetailDtoModel(), config.getModelNameFromSkuNameShoHasAttrKey());
            if (showItem != null) {
                return showItem.getName();
            }
        }
        return null;
    }

    /**
     * 构建SkuItem列表
     *
     * @param dealDetailInfoModel
     * @param config
     * @param coursePlanList
     * @return
     */
    private List<DealSkuItemVO> buildSkuItemList(DealDetailInfoModel dealDetailInfoModel, Config config, List<CoursePlan> coursePlanList) {
        if (CollectionUtils.isEmpty(config.getShowItems())) {
            return null;
        }
        Map<String, DealSkuItemVO> skuItemMap = buildTotalSkuItems(dealDetailInfoModel, coursePlanList, config);
        List<DealSkuItemVO> skuItemList = Lists.newArrayList();
        for (String showItem : config.getShowItems()) {
            addItemIfNotNull(skuItemList, skuItemMap.get(showItem));
        }
        return skuItemList.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private Map<String, DealSkuItemVO> buildTotalSkuItems(DealDetailInfoModel dealDetailInfoModel, List<CoursePlan> coursePlanList, Config config) {
        List<DealSkuItemVO> skuItemVOS = new ArrayList<>();
        // 课程内容
        addItemIfNotNull(skuItemVOS, buildCourseListItem(coursePlanList, config));
        // 开班时间
        addItemIfNotNull(skuItemVOS, EduSkuUtils.buildOpenClassTimeSkuItem(dealDetailInfoModel));
        // 课程安排
        addItemIfNotNull(skuItemVOS, buildCoursePlanItem(dealDetailInfoModel, config));
        // 课程目标
        addItemIfNotNull(skuItemVOS, buildCourseTargetItem(dealDetailInfoModel, config));
        // 材料费信息
        addItemIfNotNull(skuItemVOS, buildMaterialFeeItem(dealDetailInfoModel, config));
        if (CollectionUtils.isEmpty(skuItemVOS)) {
            return Maps.newHashMap();
        }
        return skuItemVOS.stream().filter(Objects::nonNull).collect(
                Collectors.toMap(DealSkuItemVO::getName, Function.identity(), (k1, k2) -> k1));
    }

    private DealSkuItemVO buildMaterialFeeItem(DealDetailInfoModel dealDetailInfoModel, Config config) {
        String materialFeeType = DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                dealDetailInfoModel.getDealDetailDtoModel(), MATERIAL_COST_EXPLAIN);
        String gift = DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                dealDetailInfoModel.getDealDetailDtoModel(), GIFT_FOR_NIGHT_SCHOOL);
        String otherFee = DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                dealDetailInfoModel.getDealDetailDtoModel(), OTHER_FEE);
        if (StringUtils.isBlank(materialFeeType) || NOT_NEED_MATERIAL_FEE.equals(materialFeeType)) {
            return null;
        }
        if (StringUtils.isNotBlank(gift)) {
            return buildSingleDealSkuItemVO(config.getMaterialFeeTitle(), gift);
        }
        if (StringUtils.isNotBlank(otherFee)) {
            return buildSingleDealSkuItemVO(config.getMaterialFeeTitle(), otherFee);
        }
        return null;
    }

    /**
     * 课程目标
     *
     * @param dealDetailInfoModel
     * @param config
     * @return
     */
    private DealSkuItemVO buildCourseTargetItem(DealDetailInfoModel dealDetailInfoModel, Config config) {
        String target = getSortCourseTarget(dealDetailInfoModel);
        if (StringUtils.isBlank(target)) {
            return null;
        }
        return buildSingleDealSkuItemVO(config.getCourseTargetTitle(), target);
    }

    @NotNull
    private DealSkuItemVO buildSingleDealSkuItemVO(String title, String content) {
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(title);
        dealSkuItemVO.setType(SKU_ITEM_TYPE_NORMAL_LIST);
        dealSkuItemVO.setValue(content);
        return dealSkuItemVO;
    }

    private boolean isSortCourse(DealDetailInfoModel dealDetailInfoModel) {
        String eduCourseType = DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                dealDetailInfoModel.getDealDetailDtoModel(), EDU_COURSE_TYPE);
        return EDU_COURSE_TYPE_SHORT_COURSE.equals(eduCourseType);
    }

    private String getSortCourseTarget(DealDetailInfoModel dealDetailInfoModel) {
        String learningObjective = DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                dealDetailInfoModel.getDealDetailDtoModel(), LEARNING_OBJECTIVE);
        if (OBJECT_AS_PLATFORM.equals(learningObjective)) {
            return DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                    dealDetailInfoModel.getDealDetailDtoModel(), CLASS_HIGHLIGHTS);
        }
        if (OBJECT_AS_SELF.equals(learningObjective) || StringUtils.isBlank(learningObjective)) {
            return DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                    dealDetailInfoModel.getDealDetailDtoModel(), CLASS_NATURE);
        }
        return null;
    }

    private DealSkuItemVO buildCoursePlanItem(DealDetailInfoModel dealDetailInfoModel, Config config) {
        if (isSortCourse(dealDetailInfoModel)) {
            return buildSortCoursePlanItem(dealDetailInfoModel, config);
        }
        // 夜校的
        String openClassFrequency = DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                dealDetailInfoModel.getDealDetailDtoModel(), CLASS_FREQUENCY);
        String classDuration = getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel, config.getClassDurationAttrKeys());
        if (StringUtils.isAllBlank(openClassFrequency, classDuration)) {
            return null;
        }
        return buildSingleDealSkuItemVO(config.getCoursePlanTitle(), String.format(FORMAT_OPEN_CLASS, openClassFrequency, classDuration));
    }

    /**
     * 班型+授课地点+科目+课时数(课时时长)，消课周期
     * @param dealDetailInfoModel
     * @param config
     * @return
     */
    private DealSkuItemVO buildSortCoursePlanItem(DealDetailInfoModel dealDetailInfoModel, Config config) {
        // 班型
        String classType = getClassType(dealDetailInfoModel, config);
        // 授课地点
        String servicePosition = DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                dealDetailInfoModel.getDealDetailDtoModel(), SERVICE_POSITION);
        // 科目
        String subject = getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel, config.getClassSubjectAttrKeys());
        // 课时数
        String classNum = getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel, config.getClassCountAttrKeys());
        classNum = StringUtils.isBlank(classNum) ? "" : getNumberStr(classNum) + "节课";
        // 课时时长
        String classDuration = getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel, config.getClassDurationAttrKeys());
        classDuration = StringUtils.isBlank(classDuration) ? "" : String.format(CLASS_DURATION_TEMPLATE, getNumberStr(classDuration));
        // 消课周期
        String openPeriod = DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                dealDetailInfoModel.getDealDetailDtoModel(), COURSE_DURATION_NUMBER);
        openPeriod = StringUtils.isBlank(openPeriod) ? "" : SPLIT + getNumberStr(openPeriod) + OPEN_PERIOD_APPEND;
        if (StringUtils.isAllBlank(classType, servicePosition, subject, classNum, classDuration)) {
            return null;
        }
        StringBuffer content = new StringBuffer();
        appendIfNotNull(content, classType);
        appendIfNotNull(content, servicePosition);
        appendIfNotNull(content, subject);
        appendIfNotNull(content, classNum);
        appendIfNotNull(content, classDuration);
        appendIfNotNull(content, openPeriod);
        return buildSingleDealSkuItemVO(config.getCoursePlanTitle(), content.toString());
    }

    private String getNumberStr(String str) {
        if (StringUtils.isBlank(str)) {
            return "";
        }
        Matcher matcher = NUMBER_REGEX.matcher(str);
        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    private String getClassType(DealDetailInfoModel dealDetailInfoModel, Config config) {
        // 首先尝试读取服务属性
        String classType = getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel, config.getClassTypeSkuAttrKeys());
        if (StringUtils.isNotBlank(classType)) return classType;
        // 然后尝试读取行业属性
        return getClassTypeFromAttr(dealDetailInfoModel, config);
    }

    private String getClassTypeFromAttr(DealDetailInfoModel dealDetailInfoModel, Config config) {
        if (CollectionUtils.isNotEmpty(config.getClassTypeAttrKeys())) {
            for (String classTypeAttrKey : config.getClassTypeAttrKeys()) {
                String classType = DealDetailUtils.getAttrSingleValueByAttrName(dealDetailInfoModel.getDealAttrs(), classTypeAttrKey);
                if (StringUtils.isNotBlank(classType)) {
                    return classType;
                }
            }
        }
        return null;
    }

    private void appendIfNotNull(StringBuffer content, String appendValue) {
        if (StringUtils.isBlank(appendValue)) {
            return;
        }
        content.append(appendValue);
    }

    private String getFirstSkuAttrModelValueByAttrName(DealDetailInfoModel dealDetailInfoModel, List<String> attrKeys) {
        if (CollectionUtils.isEmpty(attrKeys)) {
            return null;
        }
        for (String attrKey : attrKeys) {
            String classType = DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                    dealDetailInfoModel.getDealDetailDtoModel(), attrKey);
            if (StringUtils.isNotBlank(classType)) {
                return classType;
            }
        }
        return null;
    }

    private void addItemIfNotNull(List<DealSkuItemVO> skuItemList, DealSkuItemVO dealSkuItemVO) {
        if (dealSkuItemVO == null) {
            return;
        }
        skuItemList.add(dealSkuItemVO);
    }

    private DealSkuItemVO buildCourseListItem(List<CoursePlan> coursePlanList, Config config) {
        if (CollectionUtils.isEmpty(coursePlanList)) {
            return null;
        }
        List<SkuAttrAttrItemVO> valueAttrs = coursePlanList.stream()
                .filter(plan -> plan != null && StringUtils.isNotBlank(plan.getCourseTitle()))
                .map(plan -> {
                    SkuAttrAttrItemVO attrItemVO = new SkuAttrAttrItemVO();
                    if (plan.getCourseTimeNum() != null && config.isCourseListShowNum()) {
                        attrItemVO.setInfo(Lists.newArrayList(
                                plan.getCourseTimeNum().setScale(1, BigDecimal.ROUND_DOWN)
                                        .stripTrailingZeros().toPlainString() + config.getCourseUnitStr()));
                    }
                    attrItemVO.setName(plan.getCourseTitle());
                    return attrItemVO;
                }).collect(Collectors.toList());
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(config.getCourseListTitle());
        dealSkuItemVO.setType(SKU_ITEM_TYPE_ATTR_LIST);
        dealSkuItemVO.setValueAttrs(valueAttrs);
        return dealSkuItemVO;
    }


    @Data
    @VPointCfg
    public static class Config {

        /**
         * 只有短期课才显示
         */
        private boolean justShowOnShortCourse = true;

        /**
         * 显示的item，按名称来
         */
        private List<String> showItems;

        /**
         * 课程列表标题
         */
        private String courseListTitle = "课程内容";

        /**
         * 课程目标标题
         */
        private String courseTargetTitle = "课程目标";

        /**
         * 课程安排标题
         */
        private String coursePlanTitle = "课程安排";

        /**
         * 材料费信息标题
         */
        private String materialFeeTitle = "材料费信息";

        /**
         * 是否显示课程数量
         */
        private boolean courseListShowNum = false;

        /**
         * 是否展示课程价格
         */
        private boolean showCoursePrice = true;

        /**
         * 课程单位
         */
        private String courseUnitStr = "节";

        /**
         * 标题区价格模板
         */
        private String coursePriceTemplate = "¥%s";

        /**
         * 标题区单价的模板
         */
        private String courseUnitPriceTemplate = "(¥%s/次)";

        /**
         * 模块名称读取的skuAttr，需要的话可以通过配置扩展，不配置代表使用团购名称
         */
        private List<String> modelNameFromSkuAttrKeys;

        /**
         * 模块名称来自skuName，该sku必须有指定的attr
         */
        private String modelNameFromSkuNameShoHasAttrKey;

        /**
         * 班型的行业属性字段，需要的话可以通过配置扩展
         */
        private List<String> classTypeAttrKeys = Lists.newArrayList("class_type_with_five_select", "edu_class_type",
                "class_type_with_customize_select", "class_type_five", "class_type_with_three_select", "class_type_with_two_select");

        /**
         * 班型的服务属性字段，需要的话可以通过配置扩展
         */
        private List<String> classTypeSkuAttrKeys = Lists.newArrayList("classNum");

        /**
         * 科目的服务属性字段，需要的话可以通过配置扩展
         */
        private List<String> classSubjectAttrKeys = Lists.newArrayList("category2", "type", "classcat", "classCat");

        /**
         * 课时数的服务属性字段，需要的话可以通过配置扩展
         */
        private List<String> classCountAttrKeys = Lists.newArrayList("count", "quantity");

        /**
         * 课时时长的服务属性字段，需要的话可以通过配置扩展
         */
        private List<String> classDurationAttrKeys = Lists.newArrayList("classDuration", "courseDuration", "classDurationMin");
    }


    @Data
    public static class CoursePlan {

        /**
         * 课程
         */
        private String courseTitle;

        /**
         * 课时数
         */
        private BigDecimal courseTimeNum;

        /**
         * 课程价格
         */
        private BigDecimal coursePrice;

    }

    @Data
    public static class EduServiceItemTable implements Serializable {

        /**
         * 课程名称
         */
        @JsonProperty("educontent")
        private String eduContent;

    }
}
