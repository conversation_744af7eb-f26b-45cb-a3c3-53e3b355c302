package com.sankuai.dzviewscene.product.dealstruct.ability.dealstyle.router;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealstyle.DealStyleSwitchCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.detailSwitch.SwitchModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.detailSwitch.SwitchModuleModel;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

/**
 * 电动车
 *
 * @author: created by hang.yu on 2024/4/18 11:29
 */
@Component
public class ElectricVehicleDetailRouter implements DealCategoryRouter {

    @Override
    public boolean identify(DealStyleSwitchCfg cfg, int dealCategory) {
        return Lists.newArrayList(716).contains(dealCategory);
    }

    @Override
    public SwitchModel compute(ActivityCxt context, Param param, DealStyleSwitchCfg config) {
        String serviceType = DealDetailUtils.getAttrSingleValueByAttrName(param.getDealDetailInfoModel().getDealAttrs(), "service_type");
        // 判断
        if (CollectionUtils.isNotEmpty(config.getCustomStyleServiceTypes()) && !config.getCustomStyleServiceTypes().contains(serviceType)) {
            return buildDefaultStyleSwitch(param, config, serviceType);
        }
        if (!config.isMiniProgramShowCustomStyle() && PlatformUtil.isAnyXcx(param.getMpSource())) {
            return buildDefaultStyleSwitch(param, config, serviceType);
        }
        return buildCustomerStyleSwitch(param);
    }

    SwitchModel buildCustomerStyleSwitch(Param param) {
        SwitchModel switchModel = new SwitchModel();
        SwitchModuleModel switchModuleModel = new SwitchModuleModel();
        switchModuleModel.setModuleValue(CUSTOM_STRUCTURE_MODULE);
        switchModuleModel.setModuleKey(param.getModuleKey());
        switchModel.setModules(Lists.newArrayList(switchModuleModel));
        return switchModel;
    }

    SwitchModel buildDefaultStyleSwitch(Param param, DealStyleSwitchCfg config, String serviceType) {
        SwitchModel switchModel = new SwitchModel();
        SwitchModuleModel switchModuleModel = new SwitchModuleModel();
        if (MapUtils.isNotEmpty(config.getServiceTypeStyleMap())) {
            switchModuleModel.setModuleValue(config.getServiceTypeStyleMap().getOrDefault(serviceType, HTML));
        } else {
            switchModuleModel.setModuleValue(HTML);
        }
        switchModuleModel.setModuleKey(param.getModuleKey());
        switchModel.setModules(Lists.newArrayList(switchModuleModel));
        return switchModel;
    }

}
