package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@VPointOption(
        name = "结婚-彩妆造型-兴趣培训构造DealAttrVO列表变化点", description = "结婚-彩妆造型-兴趣培训构造DealAttrVO列表变化点",
        code = WeddingMakeUpCourseContentAttrVOListOpt.CODE, isDefault = false
)
public class WeddingMakeUpCourseContentAttrVOListOpt
        extends DealAttrVOListVP<WeddingMakeUpCourseContentAttrVOListOpt.Config> {

    public static final String CODE = "WeddingMakeUpCourseContentAttrVOListOpt";

    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        // 获取课程内容
        if (Objects.isNull(param) || Objects.isNull(config)) {
            return null;
        }
        // 获取团单属性 行业属性
        List<AttrM> dealAttrs = param.getDealAttrs();

        List<String> courseContents = getCourseContentsByAttrName(dealAttrs, config.getAttrName());
        // 构造DealDetailStructAttrModuleGroupModel列表
        return buildDealDetailStructAttrModuleGroupModels("课程内容", courseContents);
    }

    /**
     * 获取课程内容列表
     * 
     * @param
     * @return
     */
    private List<String> getCourseContentsByAttrName(List<AttrM> dealAttrs, String attrName) {
        if (CollectionUtils.isEmpty(dealAttrs) || StringUtils.isEmpty(attrName)) {
            return null;
        }
        List<String> courseContentModelStrs = DealDetailUtils.getAttrValueByAttrName(dealAttrs, attrName);
        List<CourseContentModel> courseContentModels = parseCourseContentModels(courseContentModelStrs);
        if (CollectionUtils.isEmpty(courseContentModels)) {
            return Lists.newArrayList();
        }
        return courseContentModels.stream().map(CourseContentModel::getCosmeticsCourseContent)
                .collect(Collectors.toList());
    }

    private List<CourseContentModel> parseCourseContentModels(List<String> courseContentModelsStr) {
        if (CollectionUtils.isEmpty(courseContentModelsStr)) {
            return Lists.newArrayList();
        }
        try {
            return courseContentModelsStr.stream()
                    .map(courseContent -> JSON.parseObject(courseContent, CourseContentModel.class))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error(
                    "WeddingMakeUpCourseContentAttrVOListOpt.parseCourseContentModels error, courseContentModelsStr:{}",
                    courseContentModelsStr, e);
            return Lists.newArrayList();
        }
    }

    private List<DealDetailStructAttrModuleGroupModel> buildDealDetailStructAttrModuleGroupModels(String groupName,
            List<String> courseContents) {
        DealDetailStructAttrModuleVO dealDetailStructAttrModuleVOS = buildDealDetailStructAttrModuleVO(courseContents);
        if (dealDetailStructAttrModuleVOS == null) {
            return null;
        }
        DealDetailStructAttrModuleGroupModel dealDetailStructAttrModuleGroupModel = new DealDetailStructAttrModuleGroupModel();
        dealDetailStructAttrModuleGroupModel.setGroupName(groupName);
        dealDetailStructAttrModuleGroupModel
                .setDealDetailStructAttrModuleVOS(Lists.newArrayList(dealDetailStructAttrModuleVOS));
        return Lists.newArrayList(dealDetailStructAttrModuleGroupModel);
    }

    private DealDetailStructAttrModuleVO buildDealDetailStructAttrModuleVO(List<String> courseContents) {
        if (CollectionUtils.isEmpty(courseContents)) {
            return null;
        }
        DealDetailStructAttrModuleVO dealDetailStructAttrModuleVO = new DealDetailStructAttrModuleVO();
        dealDetailStructAttrModuleVO.setAttrValues(courseContents);
        return dealDetailStructAttrModuleVO;
    }

    @Data
    private static class CourseContentModel {
        private String cosmeticsCourseContent;
    }

    @Data
    @VPointCfg
    public static class Config {
        private String attrName;
    }
}
