package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuTitleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> on 2023/2/22 4:04 PM
 **/
@VPointOption(name = "取行业属性作为sku标题的变化点", description = "取行业属性作为sku标题的变化点",code = AttrDealSkuTitleOpt.CODE)
public class AttrDealSkuTitleOpt extends SkuTitleVP<AttrDealSkuTitleOpt.Config> {
    public static final String CODE = "AttrDealSkuTitleOpt";

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Config config) {
        if(config.getAttrKey() == null){
            return param.getSkuTitle();
        }
        String value = DealDetailUtils.getAttrSingleValueByAttrName(param.getDealAttrs(), config.getAttrKey());
        if(StringUtils.isEmpty(value)){
            return param.getSkuTitle();
        }
        return Optional.ofNullable(config.getValueAliaMap()).orElse(Maps.newHashMap()).getOrDefault(value,value);
    }

    @Data
    @VPointCfg
    public static class Config {
        private String attrKey;
        private Map<String,String> valueAliaMap;
    }
}
