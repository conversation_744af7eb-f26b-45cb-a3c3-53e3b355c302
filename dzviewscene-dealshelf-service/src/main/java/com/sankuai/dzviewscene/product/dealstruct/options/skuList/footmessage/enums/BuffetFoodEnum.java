package com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 自助餐
 *
 * @author: created by hang.yu on 2023/9/12 20:57
 */
@Getter
@AllArgsConstructor
public enum BuffetFoodEnum {

    /**
     * 海鲜自助
     */
    SEA_FOOD("SeafoodBuffet", "海鲜自助", "https://p0.meituan.net/ingee/925a3488153fb29779d17bcdcd8e43312049.png", "https://p0.meituan.net/ingee/345d6f306b3ee82408c670da3789477e7653.png"),

    /**
     * 中式小炒
     */
    STIRFRY("ChineseStirFry", "中式小炒", "https://p0.meituan.net/ingee/9f5e2d6ae5402db4a3947b37b15ceddc1670.png", "https://p0.meituan.net/ingee/ebc46448c808dec32d065b547fd598e26220.png"),

    /**
     * 粉面主食
     */
    STAPLE_FOOD("NoodlesMainCourse", "粉面主食", "https://p0.meituan.net/ingee/98735055ceaa6fbc9b0d8be0b62e6e901143.png", "https://p0.meituan.net/ingee/1921501f6e7e2cfe77203c2ef3737f5a5566.png"),

    /**
     * 小吃甜品
     */
    SNACKS_AND_DESSERTS("SnacksAndDesserts", "小吃甜品", "https://p0.meituan.net/ingee/dcace1d5e94cbbcaa37548feec6d49b71349.png", "https://p0.meituan.net/ingee/1abc6e2e84250427dddbf81ff0f1b31d5856.png"),

    /**
     * 养生汤品&粥
     */
    SOUP_AND_PORRIDGE("HealthTonicSoupAndCongee", "养生汤品&粥", "https://p0.meituan.net/ingee/772d0450d2788740403406f41e8e48531334.png", "https://p0.meituan.net/ingee/aeee335729a8235a760e9dcdffb09b9e6069.png"),

    /**
     * 水果
     */
    FRUIT("Fruit", "水果", "https://p0.meituan.net/ingee/4335100d146f0a6810f56da126c8d22c1938.png", "https://p0.meituan.net/ingee/2c6456c553f0b46f18cdc1bc0ecd9b087311.png"),

    /**
     * 饮品
     */
    DRINK("Beverage", "饮品", "https://p0.meituan.net/ingee/7a9a8e6af9395a4ecc467624edd55ee01750.png", "https://p0.meituan.net/ingee/20406cab21b3979930c5ebc0ba563baa6355.png"),

    /**
     * 茶水
     */
    TEA("TeaWater", "茶水", "https://p0.meituan.net/ingee/8db0bf2b944241778e710d79481ccdda1162.png", "https://p0.meituan.net/ingee/489891a1f949a33163db1cc464d5ad845570.png"),

    /**
     * 零食
     */
    SNACK("Snack", "零食", "https://p0.meituan.net/ingee/2afa0d8973672b9dcdd3fcb59ae9ac921536.png", "https://p0.meituan.net/ingee/7ecedda14335d0a35fd3139f32596cbf6295.png"),
    ;

    /**
     * 食物code
     */
    private final String foodCode;

    /**
     * 食物名称
     */
    private final String foodName;

    /**
     * 食物icon
     */
    private final String foodIcon;

    /**
     * 新的 食物icon
     */
    private final String newFoodIcon;

}
