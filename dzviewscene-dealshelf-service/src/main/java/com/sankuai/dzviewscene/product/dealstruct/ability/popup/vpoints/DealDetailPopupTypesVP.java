package com.sankuai.dzviewscene.product.dealstruct.ability.popup.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.popup.DealDetailPopupTypesBuilder;
import com.sankuai.dzviewscene.product.dealstruct.vo.PopupTypeVO;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/30 9:21 下午
 */
@VPoint(name = "团购详情弹窗样式变化点", description = "团购详情弹窗样式变化点，支持配置", code = DealDetailPopupTypesVP.CODE, ability = DealDetailPopupTypesBuilder.CODE)
public abstract class DealDetailPopupTypesVP<T> extends PmfVPoint<List<PopupTypeVO>, DealDetailPopupTypesVP.Param, T> {

    public static final String CODE = "DealDetailPopupTypesVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
    }

}
