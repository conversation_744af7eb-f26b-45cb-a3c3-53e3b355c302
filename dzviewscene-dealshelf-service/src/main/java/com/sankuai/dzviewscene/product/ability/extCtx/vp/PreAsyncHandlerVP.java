package com.sankuai.dzviewscene.product.ability.extCtx.vp;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.ability.extCtx.PreHandlerContextAbility;
import lombok.Builder;
import lombok.Data;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2023/7/25 12:19 下午
 */
@VPoint(name = "并行预处理变化点", description = "并行预处理变化点", code = PreAsyncHandlerVP.CODE, ability = PreHandlerContextAbility.CODE)
public abstract class PreAsyncHandlerVP<T> extends PmfVPoint<CompletableFuture<Object>, PreAsyncHandlerVP.Param, T> {

    public static final String CODE = "PreAsyncHandlerVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        /**
         * 上下文信息
         */
        private ActivityCxt ctx;

        private int platform;
    }
}