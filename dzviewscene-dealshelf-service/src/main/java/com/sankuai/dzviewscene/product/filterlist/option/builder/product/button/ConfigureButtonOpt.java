package com.sankuai.dzviewscene.product.filterlist.option.builder.product.button;

import com.dianping.cat.Cat;
import com.dianping.lion.common.util.JsonUtils;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.deal.dto.enums.DealActivityTypeEnum;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductButtonVP;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
/**
 * <AUTHOR>
 * @since 2023/4/9 21:59
 */
@VPointOption(name = "配置化的按钮",
        description = "根据配置生成商品按钮",
        code = "ConfigureButtonOpt")
@Slf4j
public class ConfigureButtonOpt extends ProductButtonVP<ConfigureButtonOpt.Config> {
    @Override
    public DzSimpleButtonVO compute(ActivityCxt context, Param param, Config config) {
        DzSimpleButtonVO button = new DzSimpleButtonVO();
        button.setJumpUrl(getJumpUrl(param.getProductM(), config));
        button.setName(getName(param.getProductM(), config));
        button.setType(getType(param.getProductM(), config));
        button.setShow(config.isShow());
        return button;
    }

    public String getJumpUrl(ProductM productM, Config config) {
        // 若明确使用团详页跳链
        if (config.isUseJumpUrl()) {
            return productM.getJumpUrl();
        }
        // 默认使用提单页链接
        if (StringUtils.isNotEmpty(productM.getOrderUrl())) {
            return productM.getOrderUrl();
        }
        // 兜底使用团详页跳链
        return productM.getJumpUrl();
    }

    private int getType(ProductM productM, Config config) {
        //如果是特价团购且立抢类型配置有效，则展示立抢类型
        if (config.getPanicBuyingBtnType() > 0 && productM.getActivityByShelfActivityType(DealActivityTypeEnum.SPECIAL_PRICE_DEAL.getType()) != null) {
            return config.getPanicBuyingBtnType();
        }
        return config.getType();
    }

    private String getName(ProductM productM, Config config) {
        //配置了秒杀，则展示秒杀
        if (StringUtils.isNotEmpty(config.getRainbowSecKillBtnName())) {
            if (config.isCheckOnlyRainbowSecKill()) {
                if (RainbowSecKillUtils.isRainbowSecKillDealFromRainbow(productM)) {
                    return config.getRainbowSecKillBtnName();
                }
            } else if (RainbowSecKillUtils.isRainbowSecKillDeal(productM)) {
                return config.getRainbowSecKillBtnName();
            }
        }
        if (isShowPreSaleBtnName(productM, config)) {
            return config.getPreSaleBtnName();
        }
        return config.getName();
    }

    private boolean isShowPreSaleBtnName(ProductM productM, Config config) {
        return PreSaleUtils.isPreSaleDeal(productM) && StringUtils.isNotBlank(config.getPreSaleBtnName());
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 1：按钮，2：箭头 3.带价格的按钮
         * com.sankuai.dzviewscene.productshelf.vo.enums.ButtonTypeEnums
         */
        private int type;

        /**
         * 跳转链接
         */
        private String jumpUrl;

        /**
         * 按钮名称
         */
        private String name;

        /**
         * 彩虹秒杀按钮的名字
         */
        private String rainbowSecKillBtnName;

        /**
         * 是否仅获取彩虹秒杀信息作为倒计时
         */
        private boolean checkOnlyRainbowSecKill = false;

        /**
         * 预售的按钮名称
         */
        private String preSaleBtnName;

        /**
         * 立抢按钮的类型
         * com.sankuai.dzviewscene.productshelf.vo.enums.ButtonTypeEnums
         */
        private int panicBuyingBtnType;

        //按钮是否展示
        private boolean show = false;

        /**
         * 购买跳链是否使用团详页（默认提单页）
         */
        private boolean useJumpUrl;
    }
}
