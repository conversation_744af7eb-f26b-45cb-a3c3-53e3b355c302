package com.sankuai.dzviewscene.product.ability.extCtx;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.productdetail.activity.SpuDetailActivity;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivity;
import com.sankuai.dzviewscene.product.shelf.activity.product.ProductShelfActivity;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.enums.FieldsEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Ability(code = ExtContextAbility.CODE,
        name = "扩展上下文能力",
        description = "提供可选的上下文填充(仅上下文中没有该字段则填充)，包含门店信息: 0, 点评门店ID: 1, 美团门店ID: 2, 点评城市ID:3, 美团城市ID: 4，门店点评城市ID:5，门店美团城市IDL: 6",
        activities = {ProductShelfActivity.CODE, DealShelfActivity.CODE, SpuDetailActivity.CODE}
)
public class ExtContextAbility extends PmfAbility<Void, Void, ExtContextCfg> {

    private static final int CTX_SHOP = 0;
    private static final int DP_SHOP_ID = 1;
    private static final int MT_SHOP_ID = 2;
    private static final int DP_CITY_ID = 3;
    private static final int MT_CITY_ID = 4;
    private static final int SHOP_DP_CITY_ID = 5;
    private static final int SHOP_MT_CITY_ID = 6;

    public static final String CODE = "ExtContextAbility";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<Void> build(ActivityCxt ctx, Void param, ExtContextCfg cfg) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.ability.extCtx.ExtContextAbility.build(com.sankuai.athena.viewscene.framework.ActivityCxt,java.lang.Void,com.sankuai.dzviewscene.product.ability.extCtx.ExtContextCfg)");
        if (cfg == null || CollectionUtils.isEmpty(cfg.getNeedFields())) {
            return CompletableFuture.completedFuture(null);
        }
        int platform = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.platform);
        boolean isMT = PlatformUtil.isMT(platform);
        if (isMT) {
            return initCtxForMT(ctx, cfg);
        }
        return initCtxForDP(ctx, cfg);
    }

    private CompletableFuture<Void> initCtxForDP(ActivityCxt ctx, ExtContextCfg cfg) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.ability.extCtx.ExtContextAbility.initCtxForDP(com.sankuai.athena.viewscene.framework.ActivityCxt,com.sankuai.dzviewscene.product.ability.extCtx.ExtContextCfg)");
        long dpPoiId = ParamsUtil.getLongSafely(ctx, PmfConstants.Params.dpPoiIdL);
        int dpCityId = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.dpCityId);

        CompletableFuture<Long> mtPoiIdCf = convertPoiId(dpPoiId, VCPlatformEnum.DP.getType(), ctx, cfg.getNeedFields());
        CompletableFuture<Integer> mtCityIdCf = convertCityId(dpCityId, VCPlatformEnum.DP.getType(), ctx, cfg.getNeedFields());
        CompletableFuture<ShopM> ctxShopCf = loadShop(dpPoiId, ctx, cfg.getNeedFields());
        CompletableFuture<Integer> shopDpCityIdCf = ctxShopCf.thenApply(this::loadShopDpCityId);
        CompletableFuture<Integer> shopMtCityIdCf = ctxShopCf.thenCompose(ctxShop -> loadShopMtCityId(ctxShop, ctx, cfg.getNeedFields()));
        return CompletableFuture.allOf(mtPoiIdCf, mtCityIdCf, ctxShopCf, shopDpCityIdCf, shopMtCityIdCf).thenAccept(aVoid -> {
            paddingParams(ctx, cfg.getNeedFields(), dpPoiId, dpCityId, mtPoiIdCf.join(), mtCityIdCf.join(),
                    ctxShopCf.join(), shopDpCityIdCf.join(), shopMtCityIdCf.join());
        });
    }

    private void paddingParams(ActivityCxt ctx, List<Integer> needFields, long dpPoiId,
                               int dpCityId,                                                                                                                                                       long mtPoiId, int mtCityId,
                               ShopM shopM, int shopDpCityId, int shopMtCityId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.ability.extCtx.ExtContextAbility.paddingParams(ActivityCxt,List,long,int,long,int,ShopM,int,int)");
        if (needFields.contains(DP_SHOP_ID)) {
            ctx.addParam(PmfConstants.Params.dpPoiIdL, dpPoiId);
        }
        if (needFields.contains(DP_CITY_ID)) {
            ctx.addParam(PmfConstants.Params.dpCityId, dpCityId);
        }
        if (needFields.contains(MT_SHOP_ID)) {
            ctx.addParam(PmfConstants.Params.mtPoiIdL, mtPoiId);
        }
        if (needFields.contains(MT_CITY_ID)) {
            ctx.addParam(PmfConstants.Params.mtCityId, mtCityId);
        }
        if (needFields.contains(SHOP_DP_CITY_ID)) {
            ctx.addParam(PmfConstants.Params.shopDpCityId, shopDpCityId);
        }
        if (needFields.contains(SHOP_MT_CITY_ID)) {
            ctx.addParam(PmfConstants.Params.shopMtCityId, shopMtCityId);
        }
        if (needFields.contains(CTX_SHOP)) {
            ctx.addParam(ShelfActivityConstants.Ctx.ctxShop, shopM);
        }
    }


    private CompletableFuture<Void> initCtxForMT(ActivityCxt ctx, ExtContextCfg cfg) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.ability.extCtx.ExtContextAbility.initCtxForMT(com.sankuai.athena.viewscene.framework.ActivityCxt,com.sankuai.dzviewscene.product.ability.extCtx.ExtContextCfg)");
        long mtPoiId = ParamsUtil.getLongSafely(ctx, PmfConstants.Params.mtPoiIdL);
        int mtCityId = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.mtCityId);
        CompletableFuture<Long> dpPoiIdCf = convertPoiId(mtPoiId, VCPlatformEnum.MT.getType(), ctx, cfg.getNeedFields());
        CompletableFuture<Integer> dpCityIdCf = convertCityId(mtCityId, VCPlatformEnum.MT.getType(), ctx, cfg.getNeedFields());
        CompletableFuture<ShopM> ctxShopCf = dpPoiIdCf.thenCompose(dpPoiId -> loadShop(dpPoiId, ctx, cfg.getNeedFields()));
        CompletableFuture<Integer> shopDpCityIdCf = ctxShopCf.thenApply(this::loadShopDpCityId);
        CompletableFuture<Integer> shopMtCityIdCf = ctxShopCf.thenCompose(ctxShop -> loadShopMtCityId(ctxShop, ctx, cfg.getNeedFields()));
        return CompletableFuture.allOf(dpPoiIdCf, dpCityIdCf, ctxShopCf, shopDpCityIdCf, shopMtCityIdCf).thenAccept(aVoid -> {
            paddingParams(ctx, cfg.getNeedFields(), dpPoiIdCf.join(), dpCityIdCf.join(), mtPoiId, mtCityId, ctxShopCf.join(), shopDpCityIdCf.join(), shopMtCityIdCf.join());
        });
    }

    private CompletableFuture<Integer> loadShopMtCityId(ShopM ctxShop, ActivityCxt ctx, List<Integer> needFields) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.ability.extCtx.ExtContextAbility.loadShopMtCityId(ShopM,ActivityCxt,List)");
        int shopMtCityId = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.shopMtCityId);
        if (shopMtCityId > 0) {
            return CompletableFuture.completedFuture(shopMtCityId);
        }
        if (ctxShop == null || ctxShop.getCityId() <= 0 || !needFields.contains(SHOP_MT_CITY_ID)) {
            return CompletableFuture.completedFuture(0);
        }
        return compositeAtomService.getMtCityIdByDp(ctxShop.getCityId()).thenApply(id -> id == null ? 0 : id);
    }

    private int loadShopDpCityId(ShopM ctxShop) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.ability.extCtx.ExtContextAbility.loadShopDpCityId(com.sankuai.dzviewscene.shelf.platform.common.model.ShopM)");
        return ctxShop == null ? 0 : ctxShop.getCityId();
    }

    private CompletableFuture<Integer> convertCityId(int cityId, int platform, ActivityCxt ctx, List<Integer> needFields) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.ability.extCtx.ExtContextAbility.convertCityId(int,int,com.sankuai.athena.viewscene.framework.ActivityCxt,java.util.List)");
        if (!checkNeedConvertCityId(cityId, platform, ctx, needFields)) {
            return CompletableFuture.completedFuture(loadDefaultCityId(platform, ctx));
        }
        if (PlatformUtil.isMT(platform)) {
            return compositeAtomService.getDpCityIdByMt(cityId).thenApply(id -> id == null ? 0 : id);
        }
        return compositeAtomService.getMtCityIdByDp(cityId).thenApply(id -> id == null ? 0 : id);
    }

    private CompletableFuture<Long> convertPoiId(long poiId, int platform, ActivityCxt ctx, List<Integer> needFields) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.ability.extCtx.ExtContextAbility.convertPoiId(long,int,com.sankuai.athena.viewscene.framework.ActivityCxt,java.util.List)");
        if (!checkNeedConvertPoiId(poiId, platform, ctx, needFields)) {
            return CompletableFuture.completedFuture(loadDefaultPoiId(platform, ctx));
        }
        if (PlatformUtil.isMT(platform)) {
            return compositeAtomService.getDpByMtPoiIdL(poiId).thenApply(id -> Optional.ofNullable(id).orElse(0L));
        }
        return compositeAtomService.getMtByDpPoiIdL(poiId).thenApply(id -> Optional.ofNullable(id).orElse(0L));
    }

    private long loadDefaultPoiId(int platform, ActivityCxt ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.ability.extCtx.ExtContextAbility.loadDefaultPoiId(int,com.sankuai.athena.viewscene.framework.ActivityCxt)");
        long mtPoiId = ParamsUtil.getLongSafely(ctx, PmfConstants.Params.mtPoiIdL);
        long dpPoiId = ParamsUtil.getLongSafely(ctx, PmfConstants.Params.dpPoiIdL);
        return platform == VCPlatformEnum.DP.getType()? mtPoiId : dpPoiId;
    }

    private int loadDefaultCityId(int platform, ActivityCxt ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.ability.extCtx.ExtContextAbility.loadDefaultCityId(int,com.sankuai.athena.viewscene.framework.ActivityCxt)");
        int mtCityId = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.mtCityId);
        int dpCityId = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.dpCityId);
        return platform == VCPlatformEnum.DP.getType()? mtCityId : dpCityId;
    }

    private boolean checkNeedConvertPoiId(long poiId, int platform, ActivityCxt ctx, List<Integer> needFields) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.ability.extCtx.ExtContextAbility.checkNeedConvertPoiId(long,int,com.sankuai.athena.viewscene.framework.ActivityCxt,java.util.List)");
        long mtPoiId = ParamsUtil.getLongSafely(ctx, PmfConstants.Params.mtPoiIdL);
        long dpPoiId = ParamsUtil.getLongSafely(ctx, PmfConstants.Params.dpPoiIdL);
        if (poiId <= 0) {
            return false;
        }
        if (platform == VCPlatformEnum.DP.getType()) {
            return mtPoiId <= 0 && needFields.contains(MT_SHOP_ID);
        }
        return dpPoiId <= 0 && needFields.contains(DP_SHOP_ID);
    }

    private boolean checkNeedConvertCityId(int cityId, int platform, ActivityCxt ctx, List<Integer> needFields) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.ability.extCtx.ExtContextAbility.checkNeedConvertCityId(int,int,com.sankuai.athena.viewscene.framework.ActivityCxt,java.util.List)");
        long mtCityId = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.mtCityId);
        long dpCityId = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.dpCityId);
        if (cityId <= 0) {
            return false;
        }
        if (platform == VCPlatformEnum.DP.getType()) {
            return mtCityId <= 0 && needFields.contains(MT_CITY_ID);
        }
        return dpCityId <= 0 && needFields.contains(DP_CITY_ID);
    }

    private DpPoiRequest buildDpPoiRequest(long dpPoiId) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.ability.extCtx.ExtContextAbility.buildDpPoiRequest(long)");
        DpPoiRequest dpPoiRequest = new DpPoiRequest();
        dpPoiRequest.setShopIds(Lists.newArrayList(dpPoiId));
        dpPoiRequest.setFields(Lists.newArrayList(FieldsEnum.SHOP_SERVER.getFields()));
        return dpPoiRequest;
    }


    private CompletableFuture<ShopM> loadShop(long dpPoiId, ActivityCxt ctx, List<Integer> needFields) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.ability.extCtx.ExtContextAbility.loadShop(long,com.sankuai.athena.viewscene.framework.ActivityCxt,java.util.List)");
        ShopM ctxShopM = ParamsUtil.getValue(ctx, PmfConstants.Ctx.ctxShop, null);
        if (ctxShopM != null || dpPoiId <= 0 || !needFields.contains(CTX_SHOP)) {
            return CompletableFuture.completedFuture(ctxShopM);
        }
        return compositeAtomService.findShopsByDpShopIds(buildDpPoiRequest(dpPoiId)).thenApply(dpPoiList -> {
            if (CollectionUtils.isEmpty(dpPoiList)) {
                return null;
            }
            DpPoiDTO dpPoiDTO = dpPoiList.get(0);
            ShopM shopM = new ShopM();
            shopM.setLongShopId(Optional.ofNullable(dpPoiDTO.getShopId()).orElse(0L));
            shopM.setShopId(Long.valueOf(shopM.getLongShopId()).intValue());
            shopM.setShopUuid(Optional.ofNullable(dpPoiDTO.getUuid()).orElse(StringUtils.EMPTY));
            shopM.setShopName(Optional.ofNullable(dpPoiDTO.getShopName()).orElse(StringUtils.EMPTY));
            shopM.setShopType(Optional.ofNullable(dpPoiDTO.getShopType()).orElse(0));
            shopM.setCategory(Optional.ofNullable(dpPoiDTO.getMainCategoryId()).orElse(0));
            shopM.setLat(Optional.ofNullable(dpPoiDTO.getLat()).orElse(0d));
            shopM.setLng(Optional.ofNullable(dpPoiDTO.getLng()).orElse(0d));
            shopM.setCityId(Optional.ofNullable(dpPoiDTO.getCityId()).orElse(0));
            shopM.setStatus(Optional.ofNullable(dpPoiDTO.getPower()).orElse(0));
            return shopM;
        });
    }


}
