package com.sankuai.dzviewscene.product.dealstruct.vo.car;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.sankuai.dzviewscene.product.dealstruct.vo.CommonAttrsVO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/6 5:43 下午
 */
@MobileDo(id = 0xe2eb)
public class CarParamVO implements Serializable {
    /**
     * 参数列表
     */
    @MobileDo.MobileField(key = 0x3d2c)
    private List<CommonAttrsVO> params;

    /**
     * 标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    public List<CommonAttrsVO> getParams() {
        return params;
    }

    public void setParams(List<CommonAttrsVO> params) {
        this.params = params;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
