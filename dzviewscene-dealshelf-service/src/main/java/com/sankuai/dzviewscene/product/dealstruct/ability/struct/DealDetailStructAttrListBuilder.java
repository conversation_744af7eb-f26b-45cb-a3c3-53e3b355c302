package com.sankuai.dzviewscene.product.dealstruct.ability.struct;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.struct.vpoints.DealDetailStructAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.*;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/25 11:43 上午
 */
@Ability(code = DealDetailStructAttrListBuilder.CODE,
        name = "团购详情模块结构化属性卡片构造能力",
        description = "团购详情模块结构化属性卡片构造能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE
        }
)
public class DealDetailStructAttrListBuilder extends PmfAbility<List<DealDetailStructAttrModel>, DealDetailStructAttrListParam, DealDetailStructAttrListCfg> {

    public static final String CODE = "dealDetailStructAttrListBuilder";

    @Override
    public CompletableFuture<List<DealDetailStructAttrModel>> build(ActivityCxt activityCxt, DealDetailStructAttrListParam structAttrListParam, DealDetailStructAttrListCfg structAttrListCfg) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        if (CollectionUtils.isEmpty(dealDetailInfoModels)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        List<DealDetailStructAttrModel> detailStructAttrModels = dealDetailInfoModels.stream().map(detailModel -> {
            if(detailModel == null) {
                return null;
            }
            List<AttrM> dealAttrs = detailModel.getDealAttrs();
            DealDetailDtoModel dealDetailDtoModel = detailModel.getDealDetailDtoModel();
            List<ProductSkuCategoryModel> productCategories = detailModel.getProductCategories();
            DealDetailStructAttrListVP<?> dealDetailStructAttrListVP = findVPoint(activityCxt, DealDetailStructAttrListVP.CODE);
            List<StructAttrsModel> structAttrsModels = dealDetailStructAttrListVP.execute(activityCxt,
                    DealDetailStructAttrListVP.Param.builder().dealAttrs(dealAttrs).dealDetailDtoModel(dealDetailDtoModel).productCategories(productCategories).build());
            DealDetailStructAttrModel dealDetailStructAttrModel = new DealDetailStructAttrModel();
            dealDetailStructAttrModel.setDealId(detailModel.getDealId());
            dealDetailStructAttrModel.setStructAttrsModels(structAttrsModels);
            return dealDetailStructAttrModel;
        }).collect(Collectors.toList());

        return CompletableFuture.completedFuture(detailStructAttrModels);
    }
}
