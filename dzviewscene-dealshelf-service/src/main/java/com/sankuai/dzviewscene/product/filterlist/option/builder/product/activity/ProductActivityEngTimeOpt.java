package com.sankuai.dzviewscene.product.filterlist.option.builder.product.activity;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductActivityEndTimeVP;
import com.sankuai.dzviewscene.product.shelf.utils.JuHuaSuanUtils;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.shelf.utils.ShelfPromoUtils;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.CountdownLabelVO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/8 5:24 下午
 */

@VPointOption(name = "活动倒计时",
        description = "copy from DefItemActivityEndTimeOpt",
        code = "ProductActivityEngTimeOpt")
public class ProductActivityEngTimeOpt extends ProductActivityEndTimeVP<ProductActivityEngTimeOpt.Config> {

    @Override
    public CountdownLabelVO compute(ActivityCxt context, Param param, Config config) {
        if (config.isEnableExposureSecKill() && ProductMPromoInfoUtils.isExposureSecKillDeal(param.getProductM())) {
            return ShelfPromoUtils.buildDirectPromoCountdownLabel(param.getProductM(), config.enablePeriodReduction);
        }
        if (config.isEnableJuHuaSuan() && JuHuaSuanUtils.isJuHuaSuanDeal(param.getProductM())) {
            return JuHuaSuanUtils.buildJuHuaSuanEndTimeCountdownLabel(param.getProductM());
        }

        if (config.isEnableRainbowSecKill() && config.checkOnlyRainbowSecKill) {
            if (RainbowSecKillUtils.isRainbowSecKillDealFromRainbow(param.getProductM())) {
                return RainbowSecKillUtils.getSecKillCountdownLabelWithTimePreStr(param.getProductM(), "仅剩");
            }
        } else if (config.isEnableRainbowSecKill() && RainbowSecKillUtils.isRainbowSecKillDeal(param.getProductM())) {
            return RainbowSecKillUtils.getSecKillCountdownLabelWithTimePreStr(param.getProductM(), "仅剩");
        }
        return null;
    }

    @VPointCfg
    @Data
    public static class Config {
        private boolean enableJuHuaSuan = true;

        private boolean enableRainbowSecKill = false;

        /**
         * 是否仅获取彩虹秒杀信息作为倒计时
         */
        private boolean checkOnlyRainbowSecKill = false;

        private boolean enableExposureSecKill = false;
        /*
        是否开启分时段立减的倒计时计算，用于长期立减的每日立减结束时间计算
         */
        private boolean enablePeriodReduction = false;
    }
}
