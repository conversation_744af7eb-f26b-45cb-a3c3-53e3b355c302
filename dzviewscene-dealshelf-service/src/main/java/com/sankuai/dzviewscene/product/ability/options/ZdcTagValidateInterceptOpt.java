package com.sankuai.dzviewscene.product.ability.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.InterceptHandlerVP;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

@VPointOption(name = "ZDC标签校验拦截",
        description = "根据是否包含ZDC标签拦截货架",
        code = ZdcTagValidateInterceptOpt.CODE
)
public class ZdcTagValidateInterceptOpt extends InterceptHandlerVP<ZdcTagValidateInterceptOpt.Config> {

    public static final String CODE = "ZdcTagFilterValidateOpt";

    @Override
    public Boolean compute(ActivityCxt context, Param param, Config config) {
        // 依赖ZDC标签数据源
        List<Long> tagIds = context.getParam(ZdcTagIdFetcherOpt.CODE);
        if (CollectionUtils.isEmpty(tagIds)) {
            return false;
        }
        if (CollectionUtils.isNotEmpty(config.getAnyIncludeTagIds())) {
            return tagIds.stream().filter(Objects::nonNull).anyMatch(config.getAnyIncludeTagIds()::contains);
        }

        return false;
    }


    @Data
    @VPointCfg
    public static class Config {

        /**
         * 包含任一标签，通过校验
         */
        private List<Long> anyIncludeTagIds;
    }
}
