package com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.DealDetailSkuListsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/16 9:39 上午
 */
@VPoint(name = "sku列表后置处理", description = "sku列表后置处理",code = SkuListAfterProcessingVP.CODE, ability = DealDetailSkuListsBuilder.CODE)
public abstract class SkuListAfterProcessingVP<T> extends PmfVPoint<List<DealSkuVO>, SkuListAfterProcessingVP.Param, T> {

    public static final String CODE = "SkuListAfterProcessingVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private boolean isMustGroup;
        private List<DealSkuVO> dealSkuVOS;
        private List<SkuItemDto> skuItems;
        private List<ProductSkuCategoryModel> productCategories;
        private List<AttrM> dealAttrs;
    }
}
