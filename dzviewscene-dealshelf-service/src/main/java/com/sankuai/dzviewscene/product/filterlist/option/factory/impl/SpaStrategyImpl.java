package com.sankuai.dzviewscene.product.filterlist.option.factory.impl;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.filterlist.option.factory.AbstractMassageStrategy;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: created by hang.yu on 2024/4/9 14:53
 */
@Component
public class SpaStrategyImpl extends AbstractMassageStrategy {
    @Override
    public String getFilterListTitle(SkuItemDto skuItemDto, String serviceType) {
        String serviceDuration = getServiceDuration(skuItemDto);
        // 服务部位范围
        String bodyRegion = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, BODY_REGION);
        // 服务手法
        String serviceTechnique = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, SERVICE_TECHNIQUE);
        bodyRegion = "全身".equals(bodyRegion) ? bodyRegion : "";
        return String.format("%s%s%s", serviceDuration, bodyRegion, serviceTechnique);
    }

    @Override
    public List<Long> getProductCategorys() {
        return Lists.newArrayList(ProductCategoryEnum.MASSAGE.getProductCategoryId(),
                ProductCategoryEnum.ESSENTIAL_OIL_SPA.getProductCategoryId(),
                ProductCategoryEnum.ULNA.getProductCategoryId());
    }
}
