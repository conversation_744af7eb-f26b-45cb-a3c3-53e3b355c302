package com.sankuai.dzviewscene.product.filterlist.option.builder.product.saleprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePriceVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * <AUTHOR>
 * @date 2024-11-26
 * @desc 保洁自营团单售价
 */
@VPointOption(name = "保洁自营团单售价",
        description = "保洁自营团单售价",
        code = SelfOperatedCleaningDealSalePriceOpt.CODE)
public class SelfOperatedCleaningDealSalePriceOpt extends ProductSalePriceVP<SelfOperatedCleaningDealSalePriceOpt.Config> {
    public static final String CODE = "SelfOperatedCleaningDealSalePriceOpt";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (inValidParam(param)) {
            return null;
        }
        ProductM productM = param.getProductM();
        if (Objects.nonNull(productM.getSalePrice())) {
            return getSalePrice(productM.getSalePrice().toPlainString(), config);
        }
        return getSalePrice(productM.getBasePriceTag(), config);
    }

    private boolean inValidParam(Param param) {
        return Objects.isNull(param.getProductM())
                || (StringUtils.isBlank(param.getProductM().getBasePriceTag())
                    && Objects.isNull(param.getProductM().getSalePrice()));
    }

    private String getSalePrice(String salePriceStr, Config config) {
        if (StringUtils.isBlank(salePriceStr)) {
            return null;
        }
        if (salePriceStr.contains(config.getPriceSymbol())) {
            return salePriceStr;
        }
        BigDecimal salePrice = new BigDecimal(salePriceStr);
        BigDecimal finalPrice = salePrice.setScale(0, RoundingMode.HALF_UP);
        return String.format("%s%s", config.getPriceSymbol(), finalPrice.toPlainString());
    }

    @Data
    @VPointCfg
    public static class Config {
        /**
         * 价格符号
         */
        private String priceSymbol = "￥";
    }
}
