package com.sankuai.dzviewscene.product.filterlist.option.builder.product.pricebottomtag;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceBottomTagVP;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.strategy.promo.MagicalMemberPromoTagBuildStrategy;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/6 00:16
 */
@VPointOption(name = "优惠感知版本，支持神会员",
        description = "",
        code = "PromoPerceptionPriceBottomTagOpt")
public class PromoPerceptionPriceBottomTagOpt extends ProductPriceBottomTagVP<PromoPerceptionPriceBottomTagOpt.Config> {

    /**
     * 优惠标签前缀图片宽高比
     */
    private static final double MT_PROMO_TAG_PRE_PIC_RADIO = 3.25;

    private static final double DP_PROMO_TAG_PRE_PIC_RADIO = 3.25;

    @Autowired
    private MagicalMemberPromoTagBuildStrategy magicalMemberPromoTagBuildStrategy;

    @Override
    public List<DzTagVO> compute(ActivityCxt context, Param param, Config config) {
        // 多次卡商品不展示优惠共省标签
        if (param.getProductM().isTimesDeal()) {
            return null;
        }
        DzTagVO promoDzTagVO = this.buildPromoPerceptionTag(context, param, config);
        if (Objects.nonNull(promoDzTagVO)) {
            return Lists.newArrayList(promoDzTagVO);
        }
        return null;
    }

    /**
     * 神会员 > 预售 > 秒杀 > 美团补贴 > 新客 > 其他
     *
     *
     * @param context
     * @param param
     * @param config
     * @return
     */
    private DzTagVO buildPromoPerceptionTag(ActivityCxt context, Param param, Config config) {
        Map<Integer, ProductPromoPriceM> promoPriceMMap = ProductMPromoInfoUtils.getPromoTypeAndPriceMap(param.getProductM().getPromoPrices());
        if (MapUtils.isEmpty(promoPriceMMap)) {
            return null;
        }
        DzTagVO dzTagVO = null;
        // 神券
        if (config.getMagicalMemberTagConfig() != null) {
            dzTagVO = magicalMemberPromoTagBuildStrategy.buildTag(buildMagicalMemberReq(context, param, config));
            if (dzTagVO != null) {
                return dzTagVO;
            }
        }
        ProductPromoPriceM preSalePromoPrice = null;
        // 预售
        if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale_Member.getCode())) {
            preSalePromoPrice = promoPriceMMap.get(PromoTagTypeEnum.PreSale_Member.getCode());
        } else if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale_NewUser.getCode())) {
            preSalePromoPrice = promoPriceMMap.get(PromoTagTypeEnum.PreSale_NewUser.getCode());
        } else if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale.getCode())) {
            preSalePromoPrice = promoPriceMMap.get((PromoTagTypeEnum.PreSale.getCode()));
        }

        // 秒杀其次（非商品侧处理）
        if (Objects.isNull(preSalePromoPrice) && RainbowSecKillUtils.isRainbowSecKillDealFromRainbow(param.getProductM())) {
            DzTagVO secKillDzTag = ProductMPromoInfoUtils.getSecKillPromoPriceM(param.getProductM().getPromoPrices(), param.getProductM().getMarketPrice(), param.getSalePrice(), param.getPlatform(), 3);
            if (Objects.nonNull(secKillDzTag)) {
                BigDecimal promoPrice = new BigDecimal(param.getProductM().getMarketPrice()).subtract(ProductMPromoInfoUtils.getSalePrice(param.getSalePrice()));
                secKillDzTag.setName("秒杀共省¥" + promoPrice);
                secKillDzTag.setPromoDetail(null);
                return secKillDzTag;
            }
        }
        ProductPromoPriceM otherPromoPrice = this.getNoMemberPromoM(param.getProductM());
        if (Objects.isNull(otherPromoPrice)) {
            return null;
        }
        dzTagVO = this.buildPromoTagVo(param.getPlatform(), Objects.nonNull(preSalePromoPrice) ? preSalePromoPrice : otherPromoPrice);
        if (Objects.isNull(dzTagVO)) {
            return null;
        }
        if (config.showPromoDetail) {
            dzTagVO.setPrePic(new DzPictureComponentVO(otherPromoPrice.getIcon(), getPicRadio(param.getPlatform(), config.getMtPriceBottomTagPrePicAspectRadio(), config.getDpPriceBottomTagPrePicAspectRadio())));
            dzTagVO.setAfterPic(buildPromoAfterPic(param.getPlatform()));
            dzTagVO.setPromoDetail(DzPromoUtils.buildPromoDetail(otherPromoPrice, config.getPopType()));
            dzTagVO.setText(dzTagVO.getName());
        }
        dzTagVO.setHasBorder(config.isNeedBorder());
        dzTagVO.setBorderRadius(PlatformUtil.isMT(param.getPlatform()) ? 3 : 1);
        return dzTagVO;
    }

    private PriceBottomTagBuildReq buildMagicalMemberReq(ActivityCxt context, Param param, Config config){
        PriceBottomTagBuildReq req = new PriceBottomTagBuildReq();
        req.setProductM(param.getProductM());
        req.setCardM(param.getCardM());
        req.setPlatform(param.getPlatform());
        req.setSalePrice(param.getSalePrice());
        req.setPopType(config.getPopType());
        req.setCfg(config.getMagicalMemberTagConfig());
        // h5页面无版本概念
        req.setLowestShelfVersion(null);
        req.setContext(context);
        return req;
    }

    /**
     * 获取非会员的标签
     *
     * @param productM
     * @return
     */
    private ProductPromoPriceM getNoMemberPromoM(ProductM productM) {
        List<ProductPromoPriceM> noMemberPriceM = productM.getPromoPrices().stream()
                .filter(a -> Objects.nonNull(a.getPromoTagType())
                        // 过滤掉无效值
                        && !a.getPromoTagType().equals(0)
                        // 过滤掉会员
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Member.getCode())
                        //过滤商家会员
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Merchant_Member.getCode())
                        // 过滤掉没有标签
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Default.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noMemberPriceM)) {
            return null;
        }
        return noMemberPriceM.get(0);
    }

    /**
     * 构建优惠感知版本的priceBottomTag，有特别的样式逻辑
     *
     * @return
     */
    private DzTagVO buildPromoTagVo(int platform, ProductPromoPriceM productPromoPriceM) {
        DzTagVO basicTagVo = DzPromoUtils.buildBasicDzTagVOWithColorForBar(platform, productPromoPriceM.getPromoTag(),
                ColorUtils.colorFF4B10, ColorUtils.colorFF4B10, null, ColorUtils.colorFF6633, null);
        // 点评侧的「新客特惠」、「特惠促销」样式不一样
        if (Objects.nonNull(basicTagVo) && !PlatformUtil.isMT(platform) && Objects.nonNull(productPromoPriceM.getPromoTagType()) &&
                (productPromoPriceM.getPromoTagType().equals(PromoTagTypeEnum.NewUser.getCode()) || productPromoPriceM.getPromoTagType().equals(PromoTagTypeEnum.Other.getCode()))) {
//            basicTagVo.setHasBorder(true);
            basicTagVo.setBorderColor(ColorUtils.colorFFCFBF);
            basicTagVo.setBackground(null);
        }
        return basicTagVo;
    }

    private DzPictureComponentVO buildPromoAfterPic(int platform) {
        if (PlatformUtil.isMT(platform)) {
            return new DzPictureComponentVO("https://p0.meituan.net/travelcube/ac0b1b7e016f85fcf9b8784d34d1bc14439.png", 1);
        } else {
            return new DzPictureComponentVO("https://p1.meituan.net/travelcube/4b5a342d1d0c2ae89ea51d8a2a6f7d75459.png", 1);
        }
    }

    private double getPicRadio(int platform, Double mtRadio, Double dpRadio) {
        return getPicRadio(platform, mtRadio, dpRadio, MT_PROMO_TAG_PRE_PIC_RADIO, DP_PROMO_TAG_PRE_PIC_RADIO);
    }

    private double getPicRadio(int platform, Double mtRadio, Double dpRadio, double defaultMTRadio, double defaultDPRadio) {
        if (PlatformUtil.isMT(platform)) {
            return mtRadio != null && mtRadio > 0 ? mtRadio : defaultMTRadio;
        }
        return dpRadio != null && dpRadio > 0 ? dpRadio : defaultDPRadio;
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 美团侧 团购货架 优惠感知 PriceBottomTag PrePic 的宽高比
         */
        private Double mtPriceBottomTagPrePicAspectRadio = 3.25;
        /**
         * 点评侧 团购货架 优惠感知 PriceBottomTag PrePic 的宽高比
         */
        private Double dpPriceBottomTagPrePicAspectRadio = 3.25;

        /**
         * 神会员策略构造标签配置
         */
        private PriceBottomTagBuildCfg magicalMemberTagConfig;

        /**
         * 优惠浮层弹窗类型,默认为3
         */
        private int popType = 3;
        /**
         * 是否展示优惠详情
         */
        private boolean showPromoDetail = false;

        /**
         * 是否需要描边
         */
        private boolean needBorder = false;
    }
}
