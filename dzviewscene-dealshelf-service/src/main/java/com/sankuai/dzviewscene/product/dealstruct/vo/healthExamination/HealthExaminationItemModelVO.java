package com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/18 11:02 上午
 */
@MobileDo(id = 0x6fb5)
public class HealthExaminationItemModelVO implements Serializable {
    /**
     * 表头信息
     */
    @MobileDo.MobileField(key = 0x55b9)
    private HeaderVO header;

    /**
     * 子项目列表
     */
    @MobileDo.MobileField(key = 0xcc3a)
    private List<HealthExaminationSubitemVO> subitemList;

    /**
     * 项目名（如：一般检查（10项））
     */
    @MobileDo.MobileField(key = 0xee12)
    private String itemName;

    public HeaderVO getHeader() {
        return header;
    }

    public void setHeader(HeaderVO header) {
        this.header = header;
    }

    public List<HealthExaminationSubitemVO> getSubitemList() {
        return subitemList;
    }

    public void setSubitemList(List<HealthExaminationSubitemVO> subitemList) {
        this.subitemList = subitemList;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }
}