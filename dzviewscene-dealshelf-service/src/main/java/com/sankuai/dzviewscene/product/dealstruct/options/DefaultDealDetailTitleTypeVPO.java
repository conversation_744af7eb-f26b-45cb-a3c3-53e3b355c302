package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.title.vpoints.DealDetailTitleTypeVP;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/30 9:30 下午
 */
@VPointOption(name = "团购详情标题type默认变化点", description = "团购详情标题type默认变化点，支持配置", code = DefaultDealDetailTitleTypeVPO.CODE, isDefault = true)
public class DefaultDealDetailTitleTypeVPO extends DealDetailTitleTypeVP<DefaultDealDetailTitleTypeVPO.Config> {

    public static final String CODE = "DefaultDealDetailTitleTypeVPO";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        return config.getDealDetailTitleType();
    }

    @Data
    @VPointCfg
    public static class Config {
        private String dealDetailTitleType;
    }
}
