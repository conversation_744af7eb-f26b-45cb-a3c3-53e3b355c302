package com.sankuai.dzviewscene.product.filterlist.option.builder.product.attr;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductExtAttrVP;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/2/24.
 */
@VPointOption(name = "酒吧货架商品埋点",
        description = "是否翻单埋点",
        code = "BarOceanAttrOpt")
public class BarExtAttrOpt extends ProductExtAttrVP<Void> {
    @Override
    public String compute(ActivityCxt context, Param param, Void aVoid) {
        String barProductStandardAttrValue = param.getProductM().getAttr("barProductStandardAttr");
        Map<String, Object> oceanMap = new HashMap<>();
        oceanMap.put("deal_type", 0);
        if (StringUtils.isNotEmpty(barProductStandardAttrValue) && StringUtils.equals(Boolean.TRUE.toString(), barProductStandardAttrValue)) {
            //商品已翻单
            oceanMap.put("deal_type", 1);
        }
        return JsonCodec.encode(oceanMap);
    }

}
