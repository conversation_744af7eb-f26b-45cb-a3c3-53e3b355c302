package com.sankuai.dzviewscene.product.factory.impl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleItem;
import com.sankuai.dzviewscene.product.factory.AbstractDealCategoryStrategy;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: created by hang.yu on 2023/10/16 14:08
 */
@Service("massageCategoryStrategyImpl")
public class MassageCategoryStrategyImpl extends AbstractDealCategoryStrategy {

    @Override
    public List<ModuleItem> getModuleList(ActivityCxt activityCxt, DealDetailAssembleCfg assembleCfg) {
        if (CollectionUtils.isEmpty(assembleCfg.getNewModuleList())) {
            return assembleCfg.getModuleList();
        }
        // 足疗组合套餐 && 命中斗斛实验
        Boolean massageCombination = activityCxt.getParam(QueryFetcher.Params.massageCombination);
        if (massageCombination != null && massageCombination) {
            return assembleCfg.getNewModuleList();
        }
        // 足疗单品 餐食使用新数据 && 命中斗斛实验
        Boolean foodUseNewData = activityCxt.getParam(QueryFetcher.Params.foodUseNewData);
        if (foodUseNewData != null && foodUseNewData) {
            return assembleCfg.getNewModuleList();
        }
        return assembleCfg.getModuleList();
    }

}
