package com.sankuai.dzviewscene.product.filterlist.option.builder.product.title;

import com.dianping.lion.client.Lion;
import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.ability.options.LeCrossTitleFetcherOpt;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

@VPointOption(name = "Le行业cross类目品推荐商品名称展示",
            description = "推荐下单概率前三的其他科目",
            code = EduCrossCatDealTitleOpt.CODE)
public class EduCrossCatDealTitleOpt extends ProductTitleVP<EduCrossCatDealTitleOpt.Config> {

    public static final String CODE = "EduCrossCatDealTitleOpt";

    @Override
    public String compute(ActivityCxt context, Param param, EduCrossCatDealTitleOpt.Config config) {
        ProductM productM = param.getProductM();
        if (productM == null || StringUtils.isBlank(productM.getTitle())) {
            return null;
        }
        if (productM.getProductType() == ProductTypeEnum.GENERAL_SPU.getType()) {
            // 标品
            return getGeneralSpuTitle(context, productM, config);
        }
        // 团购
        return getDealTitle(productM);
    }

    private String getDealTitle(ProductM productM) {
        if (CollectionUtils.isEmpty(productM.getShopMs())) {
            return productM.getTitle();
        }
        String brandName = productM.getBrandName();
        if (StringUtils.isNotBlank(brandName)) {
            return String.format("%s | %s", brandName, productM.getTitle());
        }
        return productM.getTitle();
    }

    private String getGeneralSpuTitle(ActivityCxt context, ProductM productM, Config config) {
        String subject = getCrossSubjectTitle(context, config);
        String title = productM.getTitle();
        if (config.useFixedSpuTitle) {
            title = config.getFixedSpuTitle();
        }
        if (StringUtils.isNotBlank(subject)) {
            return String.format(config.spuFormat, title, subject);
        }
        return title;
    }

    private String getCrossSubjectTitle(ActivityCxt context, Config config) {
        if (!config.appendRecommendTitle) {
            return StringUtils.EMPTY;
        }
        return context.getParam(LeCrossTitleFetcherOpt.CODE);
    }

    @VPointCfg
    @Data
    public static class Config {
        private Boolean useFixedSpuTitle = false;
        private Boolean appendRecommendTitle = false;
        private String fixedSpuTitle = "";
        private String spuFormat = "%s【可选%s等】";
    }
}
