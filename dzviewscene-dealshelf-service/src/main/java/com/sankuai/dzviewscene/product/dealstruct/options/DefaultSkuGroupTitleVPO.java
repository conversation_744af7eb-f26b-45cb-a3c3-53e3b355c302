package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.SkuSequenceVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints.SkuGroupTitleVP;
import jodd.util.StringUtil;
import lombok.Data;
import org.codehaus.plexus.util.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/2/10 3:53 下午
 */
@VPointOption(name = "Sku一级分组总标题默认变化点", description = "Sku一级分组总标题默认变化点",code = DefaultSkuGroupTitleVPO.CODE, isDefault = true)
public class DefaultSkuGroupTitleVPO extends SkuGroupTitleVP<DefaultSkuGroupTitleVPO.Config> {

    public static final String CODE = "DefaultSkuGroupTitleVPO";

    private static final String DEFAULT_FORMAT = "以下%s选%s";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (param.isMust()) {
            return null;
        }
        String format = StringUtils.isEmpty(config.getFormat()) ? DEFAULT_FORMAT : config.getFormat();
        return String.format(format, param.getSum(), param.getOptionalCount());
    }

    @Data
    @VPointCfg
    public static class Config {
        private String format;
    }
}
