package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.GeneralProductThemeHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2024-07-09
 * @description: 泛商品填充器，直接复用老的泛商品主题填充
 */
@Component
public class GeneralProductThemePaddingHandler implements PaddingHandler {

    @Resource
    private GeneralProductThemeHandler generalProductThemeHandler;

    @Override
    public CompletableFuture<ProductGroupM> padding(ActivityCxt ctx, ProductGroupM productGroupM, Map<String, Object> params) {
        return generalProductThemeHandler.padding(ActivityCtxtUtils.toActivityContext(ctx), productGroupM, params);
    }
}
