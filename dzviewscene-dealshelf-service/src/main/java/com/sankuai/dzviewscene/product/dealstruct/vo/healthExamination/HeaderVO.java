package com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/18 11:03 上午
 */
@MobileDo(id = 0x2346)
public class HeaderVO implements Serializable {
    /**
     * 表头元素列表
     */
    @MobileDo.MobileField(key = 0xe06d)
    private List<HeaderItemVO> headerItems;

    public List<HeaderItemVO> getHeaderItems() {
        return headerItems;
    }

    public void setHeaderItems(List<HeaderItemVO> headerItems) {
        this.headerItems = headerItems;
    }
}
