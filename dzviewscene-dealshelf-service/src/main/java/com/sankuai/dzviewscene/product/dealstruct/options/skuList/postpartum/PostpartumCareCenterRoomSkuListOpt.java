package com.sankuai.dzviewscene.product.dealstruct.options.skuList.postpartum;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.dianping.cat.Cat;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.CommonPhotoDealAttrVOListOpt;
import com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.SkuPregnantPhotoDealAttrVOListOpt;
import com.sankuai.dzviewscene.product.dealstruct.vo.*;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@VPointOption(name = "月子中心房型选择", description = "月子中心房型选择(护理服务,提供用品)", code = PostpartumCareCenterRoomSkuListOpt.CODE)
public class PostpartumCareCenterRoomSkuListOpt extends SkuListModuleVP<PostpartumCareCenterRoomSkuListOpt.Config> {
    public static final String CODE = "PostpartumCareCenterRoomSkuListOpt";

    private static final String SUPPLIES = "提供用品";
    private static final String MOM_SUPPLIES = "妈妈用品";
    private static final String BABY_SUPPLIES = "宝宝用品";
    private static final String CARE_PROJECT = "护理服务";
    private static final String MOM_CARE_PROJECT = "妈妈护理";
    private static final String BABY_CARE_PROJECT = "宝宝护理";
    private static final String NO_USE_LIMIT = "仅限店内使用";
    private static final String ON_DEMAND = "按需";
    private static final String PROJECT_UNIT = "次";

    private static final Pattern PATTERN = Pattern.compile("\\{[^}]+\\}");

    @Override
    public List<DealDetailSkuListModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        if (dealDetailInfoModel == null || config == null || CollectionUtils.isEmpty(dealDetailInfoModel.getDealAttrs())) {
            return null;
        }
        List<AttrM> dealAttrs = dealDetailInfoModel.getDealAttrs();
        return buildModelList(dealAttrs, config);
    }


    /************************************************对象数据处理***************************************************/

    public List<DealDetailSkuListModuleGroupModel> buildModelList(List<AttrM> dealAttrs, Config config) {
        if (config == null || CollectionUtils.isEmpty(config.getSkuGroupModels())) {
            return null;
        }
        // 处理不同模块
        List<DealDetailSkuListModuleGroupModel> result = Lists.newArrayList();
        config.getSkuGroupModels().forEach(skuGroupModel -> {
            DealDetailSkuListModuleGroupModel groupModel = buildModel(dealAttrs, skuGroupModel);
            if (groupModel != null) {
                result.add(groupModel);
            }
        });
        // 合并相同groupName模块
        if (config.getMergeSameGroupNameFlag() != null && config.getMergeSameGroupNameFlag()) {
            return mergeSameGroupNameModule(result);
        }
        return result;
    }

    private List<DealDetailSkuListModuleGroupModel> mergeSameGroupNameModule(List<DealDetailSkuListModuleGroupModel> result) {
        if (CollectionUtils.isEmpty(result)) {
            return result;
        }
        // 获取groupNames排序集合
        List<String> groupNames = result.stream().map(DealDetailSkuListModuleGroupModel::getGroupName).distinct().collect(Collectors.toList());
        Map<String, DealDetailSkuListModuleGroupModel> groupName2ModelsMap = Maps.newHashMap();
        for (DealDetailSkuListModuleGroupModel model : result) {
            if (model != null) {
                // 合并相同groupName模块
                groupName2ModelsMap.merge(model.getGroupName(), model, (existingModel, newModel) -> {
                    if (CollectionUtils.isEmpty(existingModel.getDealSkuGroupModuleVOS())) {
                        existingModel.setDealSkuGroupModuleVOS(Lists.newArrayList());
                    }
                    existingModel.getDealSkuGroupModuleVOS().addAll(newModel.getDealSkuGroupModuleVOS());
                    return existingModel;
                });
            }
        }
        // 根据groupNames排序集合，获取合并后结果
        return groupNames.stream()
                .map(groupName -> groupName2ModelsMap.getOrDefault(groupName, null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    private DealDetailSkuListModuleGroupModel buildModel(List<AttrM> dealAttrs, SkuGroupModel skuGroupModel) {
        if (skuGroupModel == null) {
            return null;
        }
        DealDetailSkuListModuleGroupModel groupModel = new DealDetailSkuListModuleGroupModel();
        List<DealSkuVO> skuList = buildSkuVO(dealAttrs, skuGroupModel);
        if (skuList == null || CollectionUtils.isEmpty(skuList)) {
            return null;
        }
        List<DealSkuGroupModuleVO> dealSkuGroupList = buildSkuGroupList(skuList, skuGroupModel);
        groupModel.setGroupName(skuGroupModel.getGroupName());
        groupModel.setDealSkuGroupModuleVOS(dealSkuGroupList);
        return groupModel;
    }


    private List<DealSkuGroupModuleVO> buildSkuGroupList(List<DealSkuVO> skuList, SkuGroupModel skuGroupModel) {
        // 如果dealSku可以合并，则直接合并结果
        if (skuGroupModel.getMergeSkuListFlag() != null && skuGroupModel.getMergeSkuListFlag()) {
            DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
            dealSkuGroupModuleVO.setDealSkuList(skuList);
            return Lists.newArrayList(dealSkuGroupModuleVO);
        }
        // 否则拆分dealSku
        return skuList.stream().map(sku -> {
            DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
            dealSkuGroupModuleVO.setDealSkuList(Lists.newArrayList(sku));
            return dealSkuGroupModuleVO;
        }).collect(Collectors.toList());
    }

    private List<DealSkuVO> buildSkuVO(List<AttrM> dealAttrs, SkuGroupModel skuGroupModel) {
        List<AttrListGroupModel> attrListGroupModels = skuGroupModel.getAttrListGroupModels();
        if (CollectionUtils.isEmpty(attrListGroupModels)) {
            return null;
        }
        return attrListGroupModels.stream().map(attrListGroupModel -> buildDealSkuVO(dealAttrs, attrListGroupModel, skuGroupModel.getModuleType()))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    private DealSkuVO buildDealSkuVO(List<AttrM> dealAttrs, AttrListGroupModel attrListGroupModel, String moduleType) {
        if (attrListGroupModel == null) {
            return null;
        }
        List<DealSkuItemVO> skuItemList = buildDealSkuItemVo(dealAttrs, attrListGroupModel, moduleType);
        if (CollectionUtils.isEmpty(skuItemList)) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setItems(skuItemList);
        dealSkuVO.setType(attrListGroupModel.getSkuListType());
        dealSkuVO.setTitle(attrListGroupModel.getTitle());
        dealSkuVO.setIcon(attrListGroupModel.getIcon());
        return dealSkuVO;
    }

    public List<DealSkuItemVO> buildDealSkuItemVo(List<AttrM> dealAttrs, AttrListGroupModel attrListGroupModel, String moduleType) {
        if (attrListGroupModel == null) {
            return null;
        }
        List<DealSkuItemVO> skuItemList = Lists.newArrayList();
        if (attrListGroupModel.getDealStructAttrFlag() != null && attrListGroupModel.getDealStructAttrFlag()) {
            // 结构化属性设置
            skuItemList.addAll(buildStructAttrSkuItemList(dealAttrs, attrListGroupModel, moduleType));
        } else {
            //将护理服务以及提供用品数据进行拼装组合
            skuItemList.addAll(buildNonStructAttrSkuItemList(dealAttrs, attrListGroupModel));
        }
        return skuItemList.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }


    private List<DealSkuItemVO> buildStructAttrSkuItemList(List<AttrM> dealAttrs, AttrListGroupModel model, String moduleType) {
        if (CollectionUtils.isEmpty(model.getAttrModelList())) {
            return null;
        }
        return model.getAttrModelList().stream()
                .flatMap(attrModel -> Optional.ofNullable(buildDealStructAttrItem(dealAttrs, attrModel, moduleType)).orElse(Lists.newArrayList()).stream())
                .filter(Objects::nonNull).collect(Collectors.toList());
    }


    private List<DealSkuItemVO> buildDealStructAttrItem(List<AttrM> dealAttrs, AttrModelList attrModel, String moduleType) {
        // 必须返回空列表，返回null会出现NPE
        if (Objects.equals(moduleType, "standard_facility_v1_2layer")) {
            return setAttr2SkuItemName(dealAttrs, attrModel);
        } else if (Objects.equals(moduleType, "standard_facility_v1_3layer")) {
            return setAttr2SkuItemValueV3(dealAttrs, attrModel);
        }
        // 其他场景待扩展
        return Lists.newArrayList();
    }

    private List<DealSkuItemVO> setAttr2SkuItemName(List<AttrM> dealAttrs, AttrModelList attrModel) {
        if (attrModel == null) {
            return Lists.newArrayList();
        }
        // 获取属性值列表
        List<String> attrValues = getAttrValues(dealAttrs, attrModel);
        if (CollectionUtils.isEmpty(attrValues)) {
            return Lists.newArrayList();
        }
        // 将属性值列表设置成sku列表
        return attrValues.stream().map(value -> buildBaseSkuItem(value, null, attrModel.getIcon()))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<DealSkuItemVO> setAttr2SkuItemValueV3(List<AttrM> dealAttrs, AttrModelList attrModel) {
        if (attrModel == null) {
            return Lists.newArrayList();
        }
        // 获取属性值列表
        List<String> attrValues = getAttrValues(dealAttrs, attrModel);
        if (CollectionUtils.isEmpty(attrValues)) {
            return Lists.newArrayList();
        }
        String seperator = attrModel.getSeperator() != null ? attrModel.getSeperator() : "";
        String valuesStr = StringUtils.join(attrValues, seperator);
        DealSkuItemVO dealSkuItemVO = buildBaseSkuItem(attrModel.getDisplayName(), valuesStr, attrModel.getIcon());
        return Lists.newArrayList(dealSkuItemVO);
    }

    private List<String> getAttrValues(List<AttrM> dealAttrs, AttrModelList model) {
        //一个属性映射多属性formatModel和各属性独立配置formatModel的情况只能有1个生效
        // 1个属性映射多属性formatModel
        if (Objects.nonNull(model.getAttr2MultiSelectFormatModels())) {
            return getAttrValuesByBaseAttr2MultiFormat(model.getAttrNameList(), model.getAttr2MultiSelectFormatModels(), dealAttrs);
        }
        // 如果不存在一个属性映射多属性的情况，则依次处理各个属性
        return model.getAttrNameList().stream()
                .flatMap(attrName -> Optional.ofNullable(getValueWithFormat(dealAttrs, attrName, model.getAttrFormatModels())).orElse(Lists.newArrayList()).stream())
                .filter(value -> StringUtils.isNotEmpty(value)).collect(Collectors.toList());
    }

    private List<String> getValueWithFormat(List<AttrM> dealAttrs, String attrName, List<AttrFormatModel> attrFormatModels) {
        AttrFormatModel attrFormatModel = getAttrFormatMapModelByAttrValue(attrFormatModels, attrName);
        // 允许直接替换当前字符串，替换字符串不能为空
        if (attrFormatModel != null && StringUtils.isNotBlank(attrFormatModel.getReplaceFormat())) {
            return Lists.newArrayList(attrFormatModel.getReplaceFormat());
        }
        // 直接取团购属性值，后续可以扩展
        List<String> values = DealDetailUtils.getAttrValueByAttrName(dealAttrs, attrName);
        if (CollectionUtils.isEmpty(values)) {
            return Lists.newArrayList();
        }
        if (attrFormatModel == null) {
            return values;
        }

        // 属性值拼接
        values = joinAttrValue(values, attrFormatModel);

        List<String> result = Lists.newArrayList();
        for (String value : values) {
            // 将属性值format,format可能为""
            if (StringUtils.isNotEmpty(attrFormatModel.getDisplayFormat()) || Objects.equals("", attrFormatModel.getDisplayFormat())) {
                value = String.format(attrFormatModel.getDisplayFormat(), value);
            }
            result.add(value);
        }
        return result;
    }

    public List<String> joinAttrValue(List<String> attrValues, AttrFormatModel attrFormatModel) {
        if (attrFormatModel == null || attrFormatModel.getSeparator() == null || CollectionUtils.isEmpty(attrValues) || attrValues.size() <= 1) {
            return attrValues;
        }
        String joinAttrValue = StringUtils.join(attrValues, attrFormatModel.getSeparator());
        // 将拼接后属性值format,format可能为""
        if (StringUtils.isNotEmpty(attrFormatModel.getMultiDisplayFormat()) || Objects.equals("", attrFormatModel.getMultiDisplayFormat())) {
            joinAttrValue = String.format(attrFormatModel.getMultiDisplayFormat(), joinAttrValue);
        }
        return Lists.newArrayList(joinAttrValue);
    }

    private AttrFormatModel getAttrFormatMapModelByAttrValue(List<AttrFormatModel> attrFormatModels, String attrName) {
        if (CollectionUtils.isEmpty(attrFormatModels) || StringUtils.isEmpty(attrName)) {
            return null;
        }
        return attrFormatModels.stream().filter(model -> attrName.equals(model.getAttrName())).findFirst().orElse(null);
    }

    private List<String> getAttrValuesByBaseAttr2MultiFormat(List<String> attrNameList, Attr2MultiSelectFormatModels baseAttr2MultiSelectFormatModels, List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(attrNameList) || Objects.isNull(baseAttr2MultiSelectFormatModels) || CollectionUtils.isEmpty(dealAttrs)) {
            return Lists.newArrayList();
        }
        // 如果基础属性不存在则返回空
        if (!attrNameList.contains(baseAttr2MultiSelectFormatModels.getAttrName())) {
            return Lists.newArrayList();
        }
        // 匹配基础属性对应属性format列表
        AttrValue2MultiFormatModels attrValue2MultiFormatModels = null;
        // 取到第1个匹配的基础属性即返回
        for (String attrName : attrNameList) {
            String attrValue = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, attrName);
            attrValue2MultiFormatModels = getAttrFormatModelsByAttrValue(attrValue, baseAttr2MultiSelectFormatModels.getAttrValue2MultiFormatModels());
            if (Objects.nonNull(attrValue2MultiFormatModels)) {
                break;
            }
        }
        if (Objects.isNull(attrValue2MultiFormatModels)) {
            return Lists.newArrayList();
        }
        // 获取选定模型
        List<AttrFormatModel> attrFormatModels = attrValue2MultiFormatModels.getAttrFormatModels();
        return attrNameList.stream().filter(name -> filterBaseAttrName(name, baseAttr2MultiSelectFormatModels))
                .flatMap(name -> Optional.ofNullable(getValueWithFormat(dealAttrs, name, attrFormatModels)).orElse(Lists.newArrayList()).stream())
                .filter(value -> StringUtils.isNotEmpty(value)).collect(Collectors.toList());
    }

    private boolean filterBaseAttrName(String attrName, Attr2MultiSelectFormatModels baseAttr2MultiSelectFormatModels) {
        // 若属性名或模型为空，则需要过滤
        if (StringUtils.isEmpty(attrName) || Objects.isNull(baseAttr2MultiSelectFormatModels)) {
            return false;
        }
        // 若过滤属性为空或指定不需要过滤，则需要保留
        if (Objects.isNull(baseAttr2MultiSelectFormatModels.getFilterBaseAttr()) || !baseAttr2MultiSelectFormatModels.getFilterBaseAttr()) {
            return true;
        }
        // 指定过滤，则需要过滤名称相同的属性
        return !attrName.equals(baseAttr2MultiSelectFormatModels.getAttrName());
    }

    private AttrValue2MultiFormatModels getAttrFormatModelsByAttrValue(String attrValue, List<AttrValue2MultiFormatModels> attrValue2MultiFormatModelsList) {
        if (StringUtils.isEmpty(attrValue) || CollectionUtils.isEmpty(attrValue2MultiFormatModelsList)) {
            return null;
        }
        return attrValue2MultiFormatModelsList.stream()
                .filter(model -> Objects.equals(attrValue, model.getAttrValue()))
                .findFirst()
                .orElse(null);
    }

    private DealSkuItemVO buildBaseSkuItem(String name, String value, String icon) {
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setValue(value);
        dealSkuItemVO.setIcon(icon);
        return dealSkuItemVO;
    }


    /**
     * 将护理服务以及提供用品数据进行拼装组合
     *
     * @param dealAttrs
     * @param attrListGroupModel
     * @return
     */
    private List<DealSkuItemVO> buildNonStructAttrSkuItemList(List<AttrM> dealAttrs, AttrListGroupModel attrListGroupModel) {
        List<DealSkuItemVO> skuItemList = Lists.newArrayList();
        List<AttrModelList> attrModelList = attrListGroupModel.getAttrModelList();
        String title = attrListGroupModel.getTitle();
        if (CollectionUtils.isNotEmpty(attrModelList)) {
            if (StringUtils.isBlank(title)) {
                return skuItemList;
            }
            for (AttrModelList attrModel : attrModelList) {
                DealSkuItemVO dealSkuItemVO = buildSkuItemVo(dealAttrs, attrModel, title);
                skuItemList.add(dealSkuItemVO);
            }
        }
        return skuItemList;
    }

    /**
     * 返回护理服务以及提供用品对应的DealSkuItemVO
     *
     * @param dealAttrs
     * @param attrModelList
     * @param title
     * @return
     */
    public DealSkuItemVO buildSkuItemVo(List<AttrM> dealAttrs, AttrModelList attrModelList, String title) {
        List<SkuAttrAttrItemVO> valueAttrs = new ArrayList<>();
        String name = attrModelList.getDisplayName();
        if (SUPPLIES.equals(title)) {
            //用品
            if (MOM_SUPPLIES.equals(name)) {
                processGroup(dealAttrs, "mom_supplies", MomSupplies.class, this::buildMomSuppliesItemVo, valueAttrs);
            }
            if (BABY_SUPPLIES.equals(name)) {
                processGroup(dealAttrs, "baby_supplies", BabySupplies.class, this::buildBabySuppliesItemVo, valueAttrs);
            }
        } else if (CARE_PROJECT.equals(title)) {
            //服务
            if (MOM_CARE_PROJECT.equals(name)) {
                processGroup(dealAttrs, "mom_care_project", MomCareProject.class, this::buildMomCareProjectItemVo, valueAttrs);
            }
            if (BABY_CARE_PROJECT.equals(name)) {
                processGroup(dealAttrs, "baby_care_project", BabyCareProject.class, this::buildCareProjectsItemVo, valueAttrs);
            }
        }
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setValueAttrs(valueAttrs);
        if (CollectionUtils.isNotEmpty(valueAttrs)) {
            dealSkuItemVO.setShowAll(true);
            if (valueAttrs.size() > 3) {
                int num = valueAttrs.size() - 3;
                String suffix = name.substring(name.length() - 2);
                dealSkuItemVO.setCloseTitle("收起");
                dealSkuItemVO.setOpenTitle("查看全部" + num + "项" + suffix);
            }
        }
        return dealSkuItemVO;
    }


    /************************************************数据处理及辅助方法***************************************************/

    /**
     * 拼装护理服务以及提供用品的特殊逻辑
     *
     * @param name      名称
     * @param numberStr 数字字符串
     * @param userName  使用情况
     * @param isUse     是否使用
     * @return SkuAttrAttrItemVO 对象
     */
    public static SkuAttrAttrItemVO buildSkuAttrItem(String name, String numberStr, String userName, boolean isUse) {
        SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
        String value;
        if (isUse) {
            value = convertNumberString(numberStr, null);
            if (StringUtils.isNotEmpty(userName) && userName.equals(NO_USE_LIMIT)) {
                value = value + "," + NO_USE_LIMIT;
            }
        } else {
            value = convertNumberString(numberStr, PROJECT_UNIT);
        }
        skuAttrAttrItemVO.setName(name);
        skuAttrAttrItemVO.setInfo(Collections.singletonList(value));
        return skuAttrAttrItemVO;
    }


    /**
     * 判断数字是否需要转换成按需
     *
     * @param numberStr 数字字符串
     * @param unit      单位
     * @return 转换后的字符串
     */
    public static String convertNumberString(String numberStr, String unit) {
        if (numberStr == null) {
            return null;
        }
        try {
            long longValue = Long.parseLong(numberStr);
            if (longValue >= Integer.MIN_VALUE && longValue <= Integer.MAX_VALUE) {
                int number = (int) longValue;
                String convertedValue = (number >= 999) ? ON_DEMAND : numberStr;
                if (StringUtils.isNotEmpty(unit)) {
                    if (ON_DEMAND.equals(convertedValue)) {
                        return convertedValue;
                    } else {
                        return convertedValue + unit;
                    }
                } else {
                    return convertedValue;
                }
            }
        } catch (Exception e) {
            log.error("[PostpartumCareCenterRoomSkuListOpt] convertNumberString error", e);
        }
        return null;
    }


    /**
     * 根据传入的Name查找到Attr中对应的对象信息
     *
     * @param dealAttrs
     * @param name
     * @return
     */
    public static String findAttrValue(List<AttrM> dealAttrs, String name) {
        String str = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, name);
        return (StringUtils.isEmpty(str)) ? null : str;
    }


    /**
     * 判断接受到的Str并转换成指定的java对象
     *
     * @param dealAttrs
     * @param attrKey
     * @param clazz
     * @param buildFunction
     * @param valueAttrs
     * @param <T>
     */
    public <T> void processGroup(List<AttrM> dealAttrs, String attrKey, Class<T> clazz,
                                 Function<List<T>, List<SkuAttrAttrItemVO>> buildFunction,
                                 List<SkuAttrAttrItemVO> valueAttrs) {
        String attrValue = findAttrValue(dealAttrs, attrKey);
        try {
            if (StringUtils.isNotEmpty(attrValue)) {
                List<T> items = new ArrayList<>();
                if (attrValue.trim().startsWith("[")) {
                    items = JSON.parseArray(attrValue, clazz);
                } else {
                    items = JSON.parseArray(convertToJsonArray(attrValue), clazz);
                }
                List<SkuAttrAttrItemVO> itemVos = buildFunction.apply(items);
                valueAttrs.addAll(itemVos);
            }
        } catch (Exception e) {
            log.error("[PostpartumCareCenterRoomSkuListOpt] processGroup error", e);
        }
    }

    public static String convertToJsonArray(String str) {
        str = str.replaceAll("\\s+", "");
        Matcher matcher = PATTERN.matcher(str);
        JSONArray jsonArray = new JSONArray();
        while (matcher.find()) {
            String jsonObjectStr = matcher.group();
            JSONObject jsonObject = JSON.parseObject(jsonObjectStr);
            jsonArray.add(jsonObject);
        }
        return jsonArray.toJSONString();
    }


    /**
     * 用品
     *
     * @param projects
     * @param getNameFunc
     * @param getNumberFunc
     * @param getUseFunc
     * @param <T>
     * @return
     */
    public <T> List<SkuAttrAttrItemVO> buildSkuAttrAttrItemVOList(List<T> projects, Function<T, String> getNameFunc, Function<T, String> getNumberFunc, Function<T, String> getUseFunc) {
        return projects.stream()
                .map(project -> buildSkuAttrItem(getNameFunc.apply(project), getNumberFunc.apply(project), getUseFunc.apply(project), true))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 护理项目
     *
     * @param projects
     * @param getNameFunc
     * @param getNumberFunc
     * @param <T>
     * @return
     */
    public <T> List<SkuAttrAttrItemVO> buildSkuAttrAttrItemVOList(List<T> projects, Function<T, String> getNameFunc, Function<T, String> getNumberFunc) {
        return projects.stream()
                .map(project -> buildSkuAttrItem(getNameFunc.apply(project), getNumberFunc.apply(project), null, false))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    //妈妈用品
    public List<SkuAttrAttrItemVO> buildMomSuppliesItemVo(List<MomSupplies> momSupplies) {
        return buildSkuAttrAttrItemVOList(momSupplies, MomSupplies::getMomSuppliesName, MomSupplies::getMomSuppliesNumber, MomSupplies::getMomInstoreUse);
    }

    //宝宝用品
    public List<SkuAttrAttrItemVO> buildBabySuppliesItemVo(List<BabySupplies> babySupplies) {
        return buildSkuAttrAttrItemVOList(babySupplies, BabySupplies::getBabySuppliesName, BabySupplies::getBabySuppliesNumber, BabySupplies::getBabyInstoreUse);
    }

    //妈妈护理项目
    public List<SkuAttrAttrItemVO> buildMomCareProjectItemVo(List<MomCareProject> momCareProjects) {
        return buildSkuAttrAttrItemVOList(momCareProjects, MomCareProject::getMomCareProjectName, MomCareProject::getMomProjectNumber);
    }

    //宝宝护理项目
    public List<SkuAttrAttrItemVO> buildCareProjectsItemVo(List<BabyCareProject> babyCareProjects) {
        return buildSkuAttrAttrItemVOList(babyCareProjects, BabyCareProject::getBabyCareProjectName, BabyCareProject::getBabyProjectNumber);
    }

    /************************************************实体类以及接收到JSON数据格式对象***************************************************/

    @Data
    @VPointCfg
    public static class Config {
        private List<SkuGroupModel> skuGroupModels;
        // 合并相同groupName模块
        private Boolean mergeSameGroupNameFlag;
    }

    @Data
    public static class SkuGroupModel {
        private String groupName;
        private List<AttrListGroupModel> attrListGroupModels;
        // 合并sku列表
        private Boolean mergeSkuListFlag;
        // 模块样式
        private String moduleType;
    }

    // 组成dealSku
    @Data
    public static class AttrListGroupModel {
        private List<AttrModelList> attrModelList;
        private String title;
        private int skuListType;
        private String icon;
        //是否处理结构化属性
        private Boolean dealStructAttrFlag;
    }

    // 组成skuItem
    @Data
    public static class AttrModelList {
        //属性名列表
        private List<String> attrNameList;
        //属性展示名称
        private String displayName;
        //图标
        private String icon;
        //属性名 - 展示format映射
        private List<AttrFormatModel> attrFormatModels;
        // 某一属性的多种属性值 -> 多属性展示的format映射模型
        private Attr2MultiSelectFormatModels attr2MultiSelectFormatModels;
        //多个属性之间的拼接符
        private String seperator;
    }

    @Data
    public static class AttrFormatModel {
        private String attrName;
        private String displayFormat;
        private String replaceFormat;
        // 多个属性之间的拼接符
        private String separator;
        // 针对拼接后字符处理
        private String multiDisplayFormat;
    }

    /**
     * 单一属性的不同取值会影响其他属性的取值。例如"家人陪住"这一属性，若返回"支持家人陪住"，则"陪住人数"的属性会展示为"支持X人陪住"；若返回"不支持家人陪住"，则"陪住人数"的属性会展示为"不支持家人陪住"
     */
    @Data
    public static class Attr2MultiSelectFormatModels {
        // 属性名
        private String attrName;
        // 属性值与其他属性format模型的映射关系
        private List<AttrValue2MultiFormatModels> attrValue2MultiFormatModels;
        // 是否过滤基础属性
        private Boolean filterBaseAttr;
    }

    @Data
    public static class AttrValue2MultiFormatModels {
        // 属性值
        private String attrValue;
        // 属性值关联的多个属性format模型
        private List<AttrFormatModel> attrFormatModels;
    }


    @Data
    public static class MomSupplies {
        @JSONField(name = "mom_supplies_name")
        private String momSuppliesName;

        @JSONField(name = "mom_supplies_number")
        private String momSuppliesNumber;

        @JSONField(name = "mom_instore_use")
        private String momInstoreUse;
    }

    @Data
    public static class BabySupplies {
        @JSONField(name = "baby_supplies_name")
        private String babySuppliesName;

        @JSONField(name = "baby_supplies_number")
        private String babySuppliesNumber;

        @JSONField(name = "baby_instore_use")
        private String babyInstoreUse;
    }

    @Data
    public static class MomCareProject {
        @JSONField(name = "mom_care_project_name")
        private String momCareProjectName;

        @JSONField(name = "mom_project_number")
        private String momProjectNumber;
    }

    @Data
    public static class BabyCareProject {
        @JSONField(name = "baby_care_project_name")
        private String babyCareProjectName;

        @JSONField(name = "baby_project_number")
        private String babyProjectNumber;
    }
}
