package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.moreJumpUrl;

import com.dianping.cat.Cat;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListMoreJumpUrlVP;
import com.sankuai.dzviewscene.productshelf.vu.utils.SchemaUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

/**
 * <AUTHOR>
 **/
@VPointOption(name = "双平台poi页面链接", description = "poi页面链接", code = CurrentShopDetailMoreJumpUrlOpt.CODE)
public class CurrentShopDetailMoreJumpUrlOpt extends DealFilterListMoreJumpUrlVP<CurrentShopDetailMoreJumpUrlOpt.Config> {

    public static final String CODE = "CurrentShopDetailMoreJumpUrlOpt";

    @Override
    public String compute(ActivityCxt ctx, Param param, Config config) {
        return getCurrentShopDetailUrl(ctx, config);
    }

    private String getCurrentShopDetailUrl(ActivityCxt ctx, Config config) {
        int userAgent = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.userAgent);
        if (PlatformUtil.isApp(userAgent)) {
            return getCurrentShopDetailUrlForApp(ctx, config);
        }
        return getCurrentShopDetailUrlForH5(ctx, config);
    }

    private String getCurrentShopDetailUrlForH5(ActivityCxt ctx, Config config) {
        Cat.logEvent("INVALID_METHOD_3", "c.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.moreJumpUrl.CurrentShopDetailMoreJumpUrlOpt.getCurrentShopDetailUrlForH5(ActivityCxt,CurrentShopDetailMoreJumpUrlOpt$Config)");
        ShopM shopM = ctx.getParam(PmfConstants.Ctx.ctxShop);
        if (shopM == null) {
            return StringUtils.EMPTY;
        }
        int platform = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.platform);
        if (PlatformUtil.isMT(platform)) {
            long mtShopId = ParamsUtil.getLongSafely(ctx, PmfConstants.Params.mtPoiIdL);
            return String.format(config.getMtH5Schema(), SchemaUtils.getMTMDomain(), mtShopId);
        }
        return String.format(config.getDpH5Schema(), SchemaUtils.getDPMDomain(), shopM.getShopUuid());
    }

    private String getCurrentShopDetailUrlForApp(ActivityCxt ctx, Config config) {
        int platform = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.platform);
        if (PlatformUtil.isMT(platform)) {
            long mtShopId = ParamsUtil.getLongSafely(ctx, PmfConstants.Params.mtPoiIdL);
            return String.format(config.getMtAppSchema(), mtShopId);
        }
        ShopM shopM = Optional.ofNullable((ShopM)ctx.getParam(ProductDetailActivityConstants.Ctx.ctxShop)).orElse(null);
        return shopM == null ? StringUtils.EMPTY : String.format(config.dpAppSchema, shopM.getShopUuid(), shopM.getLongShopId());
    }

    @Data
    @VPointCfg
    public static class Config {
        private String mtAppSchema = "imeituan://www.meituan.com/merchant?id=%s";
        private String dpAppSchema = "dianping://shopinfo?shopuuid=%s&shopid=%s";

        private String dpH5Schema = "%s/shop/%s";
        private String mtH5Schema = "%s/poi/%s";
    }
}
