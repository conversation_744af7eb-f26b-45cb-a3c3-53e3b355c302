package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.struct.vpoints.DealDetailStructAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrItemModel;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrsModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import jodd.util.StringUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/03/11 9:17 下午
 */
@VPointOption(name = "爱车团购详情结构化属性卡片变化点", description = "爱车团购详情结构化属性卡片变化点",code = CarDealDetailStructAttrListVPO.CODE)
public class CarDealDetailStructAttrListVPO extends DealDetailStructAttrListVP<CarDealDetailStructAttrListVPO.Config> {

    public static final String CODE = "CarDealDetailStructAttrListVPO";

    private static final String STRUCT_ATTR_TYPE = "struct";

    @Override
    public List<StructAttrsModel> compute(ActivityCxt context, Param param, Config config) {
        if(param == null || config == null) {
            return Lists.newArrayList();
        }
        //1、从skuItem中获取属性信息
        List<AttrM> dealAttrs = param.getDealAttrs();
        SkuItemDto skuItemDto = getSkuItemDtoFromDealDetailModel(param.getDealDetailDtoModel(), config);
        //2、从属性中获取属性信息
        List<StructAttrItemModel> structAttrModels = buildStructAttrItemModels(skuItemDto, dealAttrs, config);
        //3、排序
        List<StructAttrItemModel> sortedStructAttrsModels = sortByPriority(structAttrModels);
        //4、封装
        return buildStructAttrsModels(sortedStructAttrsModels);
    }

    private SkuItemDto getSkuItemDtoFromDealDetailModel(DealDetailDtoModel dealDetailDtoModel, Config config) {
        if(dealDetailDtoModel == null ||  dealDetailDtoModel.getSkuUniStructuredDto() == null
                || CollectionUtils.isEmpty(dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups())) {
            return null;
        }
        return getSkuItemDtoFromFirstMust(dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups().get(0), config);
    }

    private SkuItemDto getSkuItemDtoFromFirstMust(MustSkuItemsGroupDto mustSkuItemsGroupDto, Config config) {
        if(mustSkuItemsGroupDto == null || CollectionUtils.isEmpty(mustSkuItemsGroupDto.getSkuItems())) {
            return null;
        }
        return mustSkuItemsGroupDto.getSkuItems().stream()
                .filter(skuItem -> checkProductCategory(skuItem, config)).findFirst().orElse(null);
    }

    private boolean checkProductCategory(SkuItemDto skuItem, Config config) {
        if(config == null || CollectionUtils.isEmpty(config.getProductCategoryList())) {
            return true;
        }
        if(skuItem == null || skuItem.getProductCategory() <= 0) {
            return false;
        }
        return config.getProductCategoryList().contains(String.valueOf(skuItem.getProductCategory()));
    }

    private List<StructAttrItemModel> buildStructAttrItemModels(SkuItemDto skuItemDto, List<AttrM> dealAttrs, Config config) {
        List<StructAttrItemModel> structAttrItemModels = buildStructAttrItemModelsBySkuItem(skuItemDto, config.getAttrModels());
        if(CollectionUtils.isEmpty(dealAttrs)) {
            return structAttrItemModels;
        }
        return addAttr(structAttrItemModels, dealAttrs, config.getAttrModels());
    }

    private List<StructAttrItemModel> addAttr(List<StructAttrItemModel> structAttrItemModels, List<AttrM> dealAttrs, List<AttrModel> attrModels) {
        List<StructAttrItemModel> structAttrItemModelsByAttrs = buildStructAttrItemModelFromAttr(dealAttrs, attrModels);
        if(CollectionUtils.isEmpty(structAttrItemModels)) {
            return structAttrItemModelsByAttrs;
        }
        structAttrItemModels.addAll(structAttrItemModelsByAttrs);
        return structAttrItemModels;
    }

    private List<StructAttrItemModel> buildStructAttrItemModelFromAttr(List<AttrM> dealAttrs, List<AttrModel> attrModels) {
        if(CollectionUtils.isEmpty(dealAttrs) || CollectionUtils.isEmpty(attrModels)) {
            return Lists.newArrayList();
        }
        return dealAttrs.stream().filter(Objects::nonNull)
                .map(dealAttr -> buildStructAttrItemModelByAttrType(dealAttr, attrModels))
                .filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
    }

    private StructAttrItemModel buildStructAttrItemModelByAttrType(AttrM dealAttr, List<AttrModel> attrModels) {
        if(dealAttr == null || CollectionUtils.isEmpty(attrModels)) {
            return null;
        }
        AttrModel attrModel = attrModels.stream()
                .filter(attr -> !STRUCT_ATTR_TYPE.equals(attr.getType()))
                .filter(attr -> attr.getMataKey().equals(dealAttr.getName()))
                .findFirst().orElse(null);
        if(attrModel == null) {
            return null;
        }
        return convertToStructAttrItemModel(attrModel.getPriority(), attrModel.getChnName(), attrModel.hideIfNoValue, dealAttr.getValue());
    }

    private List<StructAttrItemModel> buildStructAttrItemModelsBySkuItem(SkuItemDto skuItemDto, List<AttrModel> attrModels) {
        if(skuItemDto == null) {
            return Lists.newArrayList();
        }
        return skuItemDto.getAttrItems().stream()
                .filter(Objects::nonNull).map(attrItem -> buildStructAttrItemModelByStructType(attrItem, attrModels))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    private StructAttrItemModel buildStructAttrItemModelByStructType(SkuAttrItemDto attrItem, List<AttrModel> attrModels) {
        if(CollectionUtils.isEmpty(attrModels)) {
            return null;
        }
        AttrModel attrModel = attrModels.stream()
                .filter(attr -> STRUCT_ATTR_TYPE.equals(attr.getType()) && StringUtil.isNotEmpty(attr.getMataKey()))
                .filter(attr -> attr.getMataKey().equals(String.valueOf(attrItem.getMetaAttrId())))
                .findFirst().orElse(null);
        if(attrModel == null) {
            return null;
        }
        return convertToStructAttrItemModel(attrModel.getPriority(), attrModel.getChnName(), attrModel.hideIfNoValue, attrItem.getAttrValue());
    }

    private StructAttrItemModel convertToStructAttrItemModel(int priority, String attrName, boolean hideIfNoValue, String attrValue) {
        if (StringUtils.isEmpty(attrValue) && hideIfNoValue){
            return null;
        }
        StructAttrItemModel structAttrItemModel = new StructAttrItemModel();
        structAttrItemModel.setAttrName(attrName);
        structAttrItemModel.setAttrValues(Lists.newArrayList(attrValue));
        structAttrItemModel.setPriority(priority);
        return structAttrItemModel;
    }

    private List<StructAttrItemModel> sortByPriority(List<StructAttrItemModel> structAttrModels) {
        if(CollectionUtils.isEmpty(structAttrModels)) {
            return structAttrModels;
        }
        return structAttrModels.stream()
                .sorted(Comparator.comparingInt(StructAttrItemModel::getPriority)).collect(Collectors.toList());
    }

    private List<StructAttrsModel> buildStructAttrsModels(List<StructAttrItemModel> structAttrModels) {
        if(CollectionUtils.isEmpty(structAttrModels)) {
            return Lists.newArrayList();
        }
        StructAttrsModel structAttrsModel = new StructAttrsModel();
        structAttrsModel.setStructAttrModels(structAttrModels);
        return Lists.newArrayList(structAttrsModel);
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<String> productCategoryList;
        private List<AttrModel> attrModels;
    }

    @Data
    public static class AttrModel {
        private String mataKey;
        private String type;
        private String chnName;
        private int priority;
        private boolean hideIfNoValue;
    }
}
