package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2023/6/25
 */
@VPointOption(name = "境外医美DealAttrVO列表变化点",
        description = "境外医美DealAttrVO列表变化点",
        code = MedicalBeautyAbroadAttrVOListOpt.CODE)
public class MedicalBeautyAbroadAttrVOListOpt extends DealAttrVOListVP<MedicalBeautyAbroadAttrVOListOpt.Config> {
    public static final String CODE = "MedicalBeautyAbroadAttrVOListOpt";

    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt activityCxt, Param param, Config config) {
        if (param == null) {
            return null;
        }
        return buildDealDetailStructAttrModuleGroupModel(config, activityCxt.getParam("serviceType"));
    }

    private List<DealDetailStructAttrModuleGroupModel> buildDealDetailStructAttrModuleGroupModel(MedicalBeautyAbroadAttrVOListOpt.Config config, String serviceType) {
        if (StringUtils.isNotEmpty(serviceType) && CollectionUtils.isNotEmpty(config.getConfigServiceTypeList()) && config.getConfigServiceTypeList().contains(serviceType)) {
            DealDetailStructAttrModuleVO dealDetailStructAttrModuleVO = new DealDetailStructAttrModuleVO();
            dealDetailStructAttrModuleVO.setAttrName(Objects.isNull(config.getAttrName()) ? StringUtils.EMPTY : config.getAttrName());
            dealDetailStructAttrModuleVO.setAttrValues(Lists.newArrayList());
            dealDetailStructAttrModuleVO.setIcon(config.getIcon());
            DealDetailStructAttrModuleGroupModel dealDetailStructAttrModuleGroupModel = new DealDetailStructAttrModuleGroupModel();
            dealDetailStructAttrModuleGroupModel.setGroupName(Objects.isNull(config.getGroupName()) ? "服务流程" : config.getGroupName());
            dealDetailStructAttrModuleGroupModel.setDealDetailStructAttrModuleVOS(Lists.newArrayList(dealDetailStructAttrModuleVO));
            return Lists.newArrayList(dealDetailStructAttrModuleGroupModel);
        }

        return null;
    }

    @Data
    @VPointCfg
    public static class Config {
        /**
         * groupName
         */
        private String groupName;

        /**
         * 属性名
         */
        private String attrName;

        /**
         * 配置属性值
         */
        private List<String> configServiceTypeList;

        /**
         * 图片地址
         */
        private String icon;
    }
}
