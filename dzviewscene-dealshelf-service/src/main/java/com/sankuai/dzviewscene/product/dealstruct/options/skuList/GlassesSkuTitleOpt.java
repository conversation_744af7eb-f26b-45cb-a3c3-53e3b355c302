package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuTitleVP;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/9/8 5:29 下午
 */
@VPointOption(name = "配镜sku标题的默认变化点", description = "sku标题默认变化点", code = GlassesSkuTitleOpt.CODE, isDefault = false)
public class GlassesSkuTitleOpt extends SkuTitleVP<GlassesSkuTitleOpt.Config> {

    public static final String CODE = "GlassesSkuTitleOpt";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        return null;
    }

    @Data
    @VPointCfg
    public static class Config {
        //服务项目属性key
        private String skuAttrKey;
    }
}
