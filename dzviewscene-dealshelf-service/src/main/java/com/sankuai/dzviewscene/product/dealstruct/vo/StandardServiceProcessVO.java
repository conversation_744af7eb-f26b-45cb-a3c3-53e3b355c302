package com.sankuai.dzviewscene.product.dealstruct.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-05-07
 * @desc
 */
@Data
@MobileDo(id = 0x5cc5)
public class StandardServiceProcessVO implements Serializable {
    /**
     * 下一步icon
     */
    @MobileDo.MobileField(key = 0x3af1)
    private String arrowIcon;

    /**
     * 步骤icon
     */
    @MobileDo.MobileField(key = 0x2445)
    private String stepIcon;

    /**
     * 步骤名
     */
    @MobileDo.MobileField(key = 0xfa55)
    private String stepName;


}
