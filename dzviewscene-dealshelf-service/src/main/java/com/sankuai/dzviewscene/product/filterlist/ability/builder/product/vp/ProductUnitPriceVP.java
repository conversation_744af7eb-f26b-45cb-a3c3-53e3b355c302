package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.product.filterlist.vo.DzProductVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;


/**
 * <AUTHOR>
 */
@VPoint(name = "团单单位价格能力点", description = "团单单位价格能力点", code = ProductUnitPriceVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductUnitPriceVP<T> extends PmfVPoint<String, ProductUnitPriceVP.Param, T> {

    public static final String CODE = "ProductUnitPriceVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;
        private DzProductVO dzProductVO;
    }

}
