package com.sankuai.dzviewscene.product.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/2/2 5:44 PM
 * 从这里摘录的：https://km.sankuai.com/page/1349176286
 */
@Getter
public enum ServiceProjectEnum {
    INFECTIOUS_DISEASE_SCREENING(1159, "传染病筛查"),
    BLOOD_TEST(1160, "血液检查"),
    IMAGING_TEST(1161, "影像检查"),
    URINE_TEST(1162, "尿液检查"),
    FECAL_TEST(1163, "粪便检查"),
    SKIN_TEST(1164, "皮肤检查"),
    ANTIBODY_TEST(1165, "抗体检测"),
    HUMAN_PET_DISEASE_TEST(1166, "人宠共患病检测"),
    ALLERGY_TEST(1167, "过敏检查"),
    GENETIC_TEST(1168, "基因检测"),
    VACCINATION(1174, "疫苗接种"),
    INTERNAL_DEFORMING(1175, "体内驱虫"),
    EXTERNAL_DEFORMING(1176, "体外驱虫"),
    GENERAL_DEFORMING(1177, "体内外通用驱虫");

    private final int code;
    private final String desc;

    ServiceProjectEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}