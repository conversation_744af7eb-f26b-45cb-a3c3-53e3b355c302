package com.sankuai.dzviewscene.product.filterlist.option.builder.product.pricebottomtag;

import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceBottomTagVP;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.utils.CardPromoUtils;
import com.sankuai.dzviewscene.product.utils.MerchantMemberPromoUtils;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.MerchantMemberProductPromoData;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoDetailVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils.getSalePrice;

/**
 * <AUTHOR>
 * *
 * @since 2023/7/24 00:16
 */
@VPointOption(name = "优惠码货架优惠感知版本",
        description = "",
        code = "PromoCodePriceBottomTagOpt")
public class PromoCodePriceBottomTagOpt extends ProductPriceBottomTagVP<PromoCodePriceBottomTagOpt.Config> {
    //1.文案前图片
    // 会员专属
    private static final String MEMBER_PRE_PIC_URL = "https://p0.meituan.net/travelcube/7544063c6ab978264c008e06a859073f9599.png";
    // 超值预售
    private static final String DP_PRE_SALE_PRE_PIC_URL = "https://p0.meituan.net/travelcube/6ff59e4557b8038a62d82d16573d60a84610.png";
    private static final String MT_PRE_SALE_PRE_PIC_URL = "https://p0.meituan.net/travelcube/f2dcc593a98bf7a207a8edb580573e048590.png";
    // 秒杀
    public static final String MT_SEC_KILL_TAG_URL = "https://p0.meituan.net/travelcube/ecee1469af53422e3342afce68633de18626.png";
    public static final String DP_SEC_KILL_TAG_URL = "https://p0.meituan.net/travelcube/ebb7a1fbb7a7486a31aab13b2aaca02c4109.png";
    // 美团补贴
    public static final String MT_MTPROMO_TAG_URL = "https://p1.meituan.net/travelcube/18a5c4d90ed531b2a1cb974af50284297891.png";
    public static final String DP_MTPROMO_TAG_URL = "https://p0.meituan.net/travelcube/a88b3de9d16e9fe635679584edbe1dc53328.png";
    // 特惠促销
    public static final String MT_SPEC_PROMO_TAG_URL = "https://p0.meituan.net/travelcube/025c0b55251f2aa7c2deb4a34fc3bd424249.png";
    public static final String DP_SPEC_PROMO_TAG_URL = "https://p0.meituan.net/travelcube/86c9ee6fe286704dbc154142f1d1922b4180.png";
    // 新客特惠
    public static final String MT_NEW_PROMO_TAG_URL = "https://p0.meituan.net/travelcube/7925227f33cc3afa375ef9177645eb834330.png";
    public static final String DP_NEW_PROMO_TAG_URL = "https://p0.meituan.net/travelcube/f85309252edf59cfb208b1f32b5041b94280.png";
    private static final int POP_TYPE_PRE_SALE = 3;
    // 神会员
    private static String MAGICAL_MEMBER_ICON = "https://p0.meituan.net/ingee/82081249d39757ec88f4a744a16938e26122.png";

    //2.箭头图片
    private static final String DP_COMMON_AFTER_PIC_URL = "https://p0.meituan.net/travelcube/a702ca40c19a07c42e74e5dc987cb9fa463.png";
    // 应用于MT超值预售、新客、特惠促销
    private static final String MT_COMMON_A_AFTER_PIC_URL = "https://p0.meituan.net/travelcube/504afdd7ce5370f7dc82945f62ba102b472.png";
    // 应用于秒杀、美团补贴
    private static final String MT_COMMON_B_AFTER_PIC_URL = "https://p0.meituan.net/travelcube/05c6b1dd56017571511b16a95c493e02438.png";
    private static final String MEMBER_AFTER_PIC_URL = "https://p0.meituan.net/travelcube/7e4b06049cdc08d97bb86be1595501a7466.png";
    // 神会员后标签
    private static String MAGICAL_MEMBER_AFTER_ICON = "https://p0.meituan.net/ingee/b42e754af807e76311ca73b422b9ee92385.png";

    //商家会员标签
    private static final String MERCHANT_MEMBER_ICON = "https://p0.meituan.net/dzimage/a7237a3f9e6d5d72668efb125dc50a692715.png.webp";

    private static final String MERCHANT_MEMBER_AFTER_ICON = "https://p1.meituan.net/travelcube/1d0b829ffe59b0007565c9a2c711d9cd457.png";
    private static final double ICON_PIC_RADIO = 2.5;


    @Override
    public List<DzTagVO> compute(ActivityCxt context, Param param, Config config) {
        if (Objects.isNull(config)) {
            return new ArrayList<>();
        }
        List<DzTagVO> result = Lists.newArrayList();

        DzTagVO promoDzTagVO = buildPriceBottomTag(context, param, config);

        ProductPromoPriceM promoPriceM = PriceUtils.getUserHasPromoPrice(param.getProductM(), param.getCardM());
        // 最优优惠是商家会员
        if (MerchantMemberPromoUtils.hasMerchantMemberPromoByPromoDetailList(Lists.newArrayList(promoPriceM))) {
            MerchantMemberProductPromoData merchantMemberPromo = MerchantMemberPromoUtils.getMerchantMemberPromo(param.getProductM());
            if (merchantMemberPromo.getProductPromoPrice() != null) {
                DzTagVO dzTagVO = buildMerchantMemberPromoTag(param.getProductM(), param.getPlatform(), param.getSalePrice(), merchantMemberPromo, config);
                CollectionUtils.addIgnoreNull(result, dzTagVO);
                return result;
            }
        } else if (DzPromoUtils.promoCombinationWithMagicalMemberCoupon(promoPriceM)) { //优惠组合有神券
            return getMMCDzTagVOS(context, param, result, promoPriceM);
        }

//        DzTagStyleWrapUtils.overridePromoStyle(promoDzTagVO);
        CollectionUtils.addIgnoreNull(result, promoDzTagVO);

        DzTagVO priceTag = fillPriceTag(param, config);
        if (Objects.nonNull(priceTag)) {
            result.add(priceTag);
        }
        return result;
    }

    @NotNull
    private List<DzTagVO> getMMCDzTagVOS(ActivityCxt context, Param param, List<DzTagVO> result, ProductPromoPriceM promoPriceM) {
        PriceBottomTagBuildReq req = new PriceBottomTagBuildReq();
        req.setCardM(param.getCardM());
        req.setProductM(param.getProductM());
        req.setPlatform(param.getPlatform());
        req.setPopType(POP_TYPE_PRE_SALE);
        req.setContext(context);
        DzTagVO dzTagVO = buildPromoTagVo(req, promoPriceM);
        dzTagVO.setPrePic(buildPrePic(req));
        dzTagVO.setAfterPic(buildAfterPic());
        dzTagVO.setPromoDetail(buildPromoDetail(req.getContext(), promoPriceM, req.getPopType(), req.getCfg()));
        dzTagVO.setBorderRadius(PlatformUtil.isMT(req.getPlatform()) ? 3 : 1);
        dzTagVO.setNoGapBetweenPicText(PlatformUtil.isMT(req.getPlatform()));
        //前端展示的是text字段，改name方便后面监控上报
//        dzTagVO.setName(DzPromoUtils.MAGICAL_MEMBER_TAG_NAME);
        dzTagVO.setName(dzTagVO.getName());
        dzTagVO.setBorderColor(ColorUtils.colorFF2727);
        dzTagVO.setLabs(buildOceanLabs());
        CollectionUtils.addIgnoreNull(result, dzTagVO);
        if (Objects.nonNull(dzTagVO)) {
            result.add(dzTagVO);
        }
        return result;
    }


    private DzPromoDetailVO buildPromoDetail(ActivityCxt context, ProductPromoPriceM couponPromo, int popType, PriceBottomTagBuildCfg cfg) {
        DzPromoDetailVO dzPromoDetailVO = DzPromoUtils.buildPromoDetail(couponPromo, popType);
        dzPromoDetailVO.getPromoItems().forEach(v -> {
            if (v.getCouponPromoItem() == null) {
                return;
            }
            v.getCouponPromoItem().setNibBiz(cfg == null ? null : cfg.getNibBiz());
            v.getCouponPromoItem().setPosition(ParamsUtil.getStringSafely(context.getParameters(), ShelfActivityConstants.Params.position));
        });
        return dzPromoDetailVO;
    }


    private DzPictureComponentVO buildPrePic(PriceBottomTagBuildReq req) {
        DzPictureComponentVO dzPictureComponentVO = new DzPictureComponentVO(MAGICAL_MEMBER_ICON, getPicRadio(req.getPlatform(), req.getCfg(), ICON_PIC_RADIO, ICON_PIC_RADIO));
        dzPictureComponentVO.setPicHeight(16);
        return dzPictureComponentVO;
    }

    private DzPictureComponentVO buildAfterPic() {
        DzPictureComponentVO dzPictureComponentVO = new DzPictureComponentVO(MAGICAL_MEMBER_AFTER_ICON, 1.1);
        dzPictureComponentVO.setPicHeight(10);
        return dzPictureComponentVO;
    }

    private DzTagVO buildPromoTagVo(PriceBottomTagBuildReq req, ProductPromoPriceM productPromoPriceM) {
        String promoTag = buildPromoTag(req, productPromoPriceM);
        return DzPromoUtils.buildBasicDzTagVOWithColor(req.getPlatform(), promoTag,
                ColorUtils.colorFF4B10, ColorUtils.colorFFD6BE, null, ColorUtils.colorFF4B10, ColorUtils.colorFFF2EE);
    }

    public double getPicRadio(int platform, PriceBottomTagBuildCfg cfg, double defaultMTRadio, double defaultDPRadio) {
        if (PlatformUtil.isMT(platform)) {
            return cfg != null && cfg.getMtRadio() > 0 ? cfg.getMtRadio() : defaultMTRadio;
        }
        return cfg != null && cfg.getDpRadio() > 0 ? cfg.getDpRadio() : defaultDPRadio;
    }


    private String buildPromoTag(PriceBottomTagBuildReq req, ProductPromoPriceM productPromoPriceM) {
        //计算优惠金额
        BigDecimal promoPrice;
        if (StringUtils.isNotEmpty(req.getProductM().getMarketPrice()) && StringUtils.isNotEmpty(req.getSalePrice())) {
            promoPrice = new BigDecimal(req.getProductM().getMarketPrice()).subtract(new BigDecimal(req.getSalePrice()));
        } else {
            promoPrice = productPromoPriceM.getTotalPromoPrice();
        }
        return "共省" + promoPrice.stripTrailingZeros().toPlainString();
    }

    public String buildOceanLabs() {
        Map<String, Object> oceanMap = Maps.newHashMap();
        oceanMap.put("type", "1");
        return JsonCodec.encode(oceanMap);
    }

    // 判断和构建「比价标签」（包括全网低价标签、时间比价标签(可选)）
    private DzTagVO fillPriceTag(Param param, Config config) {
        // 开关 -> 有秒杀就不展示比价标签
        if (config.enableSecKillHidePriceTag && RainbowSecKillUtils.isRainbowSecKillDealFromRainbow(param.getProductM())) {
            return null;
        }
        // 默认生成
        DzTagVO priceDzTagVo = PriceDisplayUtils.buildPriceTag(param.getProductM(), param.getPlatform(), config.getEnableTimePriceTag());
        return Objects.nonNull(priceDzTagVo) ? priceDzTagVo : null;
    }

    public static DzTagVO buildMerchantMemberPromoTag(ProductM productM, int platform, String salePrice, MerchantMemberProductPromoData memberProductPromo, Config config) {
        ProductPromoPriceM promoPriceM = memberProductPromo.getProductPromoPrice();
        if(StringUtils.isEmpty(productM.getMarketPrice())){
            return null;
        }
        String text = "共省¥" + new BigDecimal(productM.getMarketPrice()).subtract(getSalePrice(salePrice));
        DzTagVO memberDzTagVO = new DzTagVO(
                config.getMerchantMemberConfig() != null && StringUtils.isNotBlank(config.getMerchantMemberConfig().getTextColor())
                        ? config.getMerchantMemberConfig().getTextColor() : "#8E3C12",
                config.getMerchantMemberConfig() != null && config.getMerchantMemberConfig().isHashBorder(),
                text);
        memberDzTagVO.setBackground(config.getMerchantMemberConfig() != null && StringUtils.isNotBlank(config.getMerchantMemberConfig().getBackground())
                ? config.getMerchantMemberConfig().getBackground() : "#FFEDDE");
        memberDzTagVO.setPrePic(new DzPictureComponentVO(MERCHANT_MEMBER_ICON,
                config.getMerchantMemberConfig() != null && config.getMerchantMemberConfig().getPreAspectRadio() != null
                        ? config.getMerchantMemberConfig().getPreAspectRadio() : 2.68));
        memberDzTagVO.setAfterPic(new DzPictureComponentVO(MERCHANT_MEMBER_AFTER_ICON,
                config.getMerchantMemberConfig() != null && config.getMerchantMemberConfig().getAfterAspectRadio() != null
                        ? config.getMerchantMemberConfig().getAfterAspectRadio() : 1.25));
        ProductPromoPriceM hasDefaultPromoPriceM = productM.getPromoPrices().stream().filter(item -> CollectionUtils.isNotEmpty(item.getPromoItemList())).findFirst().orElse(null);
        memberDzTagVO.setPromoDetail(Objects.nonNull(promoPriceM) ?
                DzPromoUtils.buildPromoDetail(promoPriceM, config.getPopType()) : DzPromoUtils.buildPromoDetail(hasDefaultPromoPriceM, config.getPopType()));

        memberDzTagVO.setNoGapBetweenPicText(PlatformUtil.isMT(platform));
        memberDzTagVO.setBorderRadius(PlatformUtil.isMT(platform) ? 3 : 1);
        return memberDzTagVO;
    }

    private DzTagVO buildPriceBottomTag(ActivityCxt context, Param param, Config config) {
        Map<Integer, ProductPromoPriceM> promoPriceMMap = ProductMPromoInfoUtils.getPromoTypeAndPriceMap(param.getProductM().getPromoPrices());
        if (CollectionUtils.isEmpty(param.getProductM().getPromoPrices()) || MapUtils.isEmpty(promoPriceMMap)) {
            return null;
        }

        // 预售优先级最高
        DzTagVO preSaleDzTag = buildPreSaleTag(param, promoPriceMMap, config);
        if (Objects.nonNull(preSaleDzTag)) {
            return preSaleDzTag;
        }

        // 会员
        ProductPromoPriceM memberPromoM = ProductMPromoInfoUtils.getMemberPromoPriceM(param.getProductM(), param.getCardM());
        // 非会员标签
        ProductPromoPriceM noMemberPromoM = this.getNoMemberPromoM(param);

        if (Objects.nonNull(memberPromoM) && (Objects.isNull(noMemberPromoM) || memberPromoM.getPromoPrice().compareTo(noMemberPromoM.getPromoPrice()) < 0)) {
            // 会员比其他标签便宜，返回 会员标签
            return buildMemberTag(param.getPlatform(), memberPromoM, config);
        } else if (Objects.nonNull(noMemberPromoM) && RainbowSecKillUtils.isRainbowSecKillDealFromRainbow(param.getProductM())) {
            // 有秒杀，返回秒杀标签
            return buildSecKillTag(param, noMemberPromoM, config);
        } else if (Objects.nonNull(noMemberPromoM)) {
            // 非会员、非秒杀
            return buildNoMemberTag(param.getPlatform(), noMemberPromoM, config);
        }
        return null;
    }

    /**
     * 判断并构建秒杀标签
     *
     * @param param
     * @param noMemberPromoM
     * @param config
     * @return
     */
    public DzTagVO buildSecKillTag(Param param, ProductPromoPriceM noMemberPromoM, Config config) {
        // 秒杀单独实现
        DzTagVO secKillDzTag = ProductMPromoInfoUtils.getSecKillPromoPriceM(Lists.newArrayList(noMemberPromoM), param.getProductM().getMarketPrice(), param.getSalePrice(), param.getPlatform(), config.getPopType());
        //
        if (Objects.isNull(secKillDzTag)) {
            return null;
        }
        secKillDzTag.setHasBorder(true);
        secKillDzTag.setTextColor(PlatformUtil.isMT(param.getPlatform()) ? ColorUtils.colorFF2727 : ColorUtils.colorFF6633);
        secKillDzTag.setBorderColor(PlatformUtil.isMT(param.getPlatform()) ? ColorUtils.colorFF2727 : ColorUtils.colorFF6633);
        secKillDzTag.setBackground(null);
        if (Objects.nonNull(secKillDzTag)) {
            secKillDzTag.setPrePic(new DzPictureComponentVO(
                    PlatformUtil.isMT(param.getPlatform()) ? MT_SEC_KILL_TAG_URL : DP_SEC_KILL_TAG_URL,
                    PlatformUtil.isMT(param.getPlatform()) ? config.getMtPriceBottomTagPrePicAspectRadio() : config.getDpPriceBottomTagPrePicAspectRadio()));
            secKillDzTag.setAfterPic(this.buildAfterPic(param.getPlatform(), false, false, true, false));
            secKillDzTag.setBorderRadius(PlatformUtil.isMT(param.getPlatform()) ? 3 : 1);
            return secKillDzTag;
        }
        return null;
    }

    /**
     * 构建预售标签
     *
     * @param param
     * @param preSalePromoPrice
     * @param config
     * @return
     */
    private DzTagVO buildPreSaleTag(Param param, ProductPromoPriceM preSalePromoPrice, Config config) {
        int platform = param.getPlatform();
        DzTagVO dzTagVO = DzPromoUtils.buildBasicDzTagVOWithColorAndBorder(param.getPlatform(), preSalePromoPrice.getPromoTag(),
                ColorUtils.colorFF4B10, ColorUtils.colorFF4B10, null, ColorUtils.colorFF6633, null, true, ColorUtils.colorFF6633);
        if (Objects.isNull(dzTagVO)) {
            return null;
        }
        dzTagVO.setPrePic(new DzPictureComponentVO(PlatformUtil.isMT(platform) ? MT_PRE_SALE_PRE_PIC_URL : DP_PRE_SALE_PRE_PIC_URL,
                PlatformUtil.isMT(platform) ? config.getMtPriceBottomTagPrePicAspectRadio() : config.getDpPriceBottomTagPrePicAspectRadio()));
        dzTagVO.setAfterPic(this.buildAfterPic(platform, false, true, false, false));
        dzTagVO.setPromoDetail(DzPromoUtils.buildPromoDetail(preSalePromoPrice, config.getPopType()));
        dzTagVO.setBorderRadius(PlatformUtil.isMT(platform) ? 3 : 1);
        return dzTagVO;
    }

    /**
     * 判断并构建预售标签
     *
     * @param param
     * @param promoPriceMMap
     * @param config
     * @return
     */
    private DzTagVO buildPreSaleTag(Param param, Map<Integer, ProductPromoPriceM> promoPriceMMap, Config config) {
        // 预售优先级最高
        ProductPromoPriceM preSalePromoPrice = null;
        if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale_Member.getCode())) {
            preSalePromoPrice = promoPriceMMap.get(PromoTagTypeEnum.PreSale_Member.getCode());
        } else if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale_NewUser.getCode())) {
            preSalePromoPrice = promoPriceMMap.get(PromoTagTypeEnum.PreSale_NewUser.getCode());
        } else if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale.getCode())) {
            preSalePromoPrice = promoPriceMMap.get((PromoTagTypeEnum.PreSale.getCode()));
        }
        if (Objects.nonNull(preSalePromoPrice)) {
            return buildPreSaleTag(param, preSalePromoPrice, config);
        }
        return null;
    }

    private ProductPromoPriceM getNoMemberPromoM(Param param) {
        List<ProductPromoPriceM> noMemberPriceM = param.getProductM().getPromoPrices().stream()
                .filter(a -> Objects.nonNull(a.getPromoTagType())
                        // 过滤掉会员
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Member.getCode())
                        // 过滤掉没有标签
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Default.getCode()))
                // 过滤掉 使用了会员优惠的Promo
                .filter(a -> !CardPromoUtils.CARD_PROMOS.contains(a.getPromoType()))
                .filter(a -> !a.getPromoTagType().equals(PromoTagTypeEnum.Merchant_Member.getCode())) // 过滤掉商家会员
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noMemberPriceM)) {
            return null;
        }
        return noMemberPriceM.get(0);
    }

    private DzTagVO buildNoMemberTag(int platform, ProductPromoPriceM productPromoPriceM, Config config) {
        boolean isMeituanPromo = productPromoPriceM.getPromoTagType().equals(PromoTagTypeEnum.Official_Subsidies.getCode())
                || productPromoPriceM.getPromoTagType().equals(PromoTagTypeEnum.Official_Subsidies_NewUser.getCode());
        DzTagVO dzTagVO = this.buildPromoTagVo(platform, productPromoPriceM, isMeituanPromo);
        if (Objects.isNull(dzTagVO)) {
            return null;
        }
        DzPictureComponentVO pictureComponentVO = getNoMemberTagPicVO(productPromoPriceM, isMeituanPromo, platform, config);
        dzTagVO.setPrePic(pictureComponentVO);
        dzTagVO.setAfterPic(this.buildAfterPic(platform, false, false, false, isMeituanPromo));
        dzTagVO.setPromoDetail(DzPromoUtils.buildPromoDetail(productPromoPriceM, config.getPopType()));
        dzTagVO.setBorderRadius(PlatformUtil.isMT(platform) ? 3 : 1);
        return dzTagVO;
    }

    private DzPictureComponentVO getNoMemberTagPicVO(ProductPromoPriceM productPromoPriceM, boolean isMeituanPromo, int platform, Config config) {
        if (isMeituanPromo) {
            // 美团补贴图片
            return new DzPictureComponentVO(PlatformUtil.isMT(platform) ? MT_MTPROMO_TAG_URL : DP_MTPROMO_TAG_URL,
                    PlatformUtil.isMT(platform) ? config.getMtPriceBottomTagPrePicAspectRadio() : config.getDpPriceBottomTagPrePicAspectRadio());
        } else if (productPromoPriceM.getPromoTagType().equals(PromoTagTypeEnum.NewUser.getCode())) {
            // 新客
            return new DzPictureComponentVO(PlatformUtil.isMT(platform) ? MT_NEW_PROMO_TAG_URL : DP_NEW_PROMO_TAG_URL,
                    PlatformUtil.isMT(platform) ? config.getMtPriceBottomTagPrePicAspectRadio() : config.getDpPriceBottomTagPrePicAspectRadio());
        } else if (productPromoPriceM.getPromoTagType().equals(PromoTagTypeEnum.Other.getCode())) {
            // 特惠促销 60
            return new DzPictureComponentVO(PlatformUtil.isMT(platform) ? MT_SPEC_PROMO_TAG_URL : DP_SPEC_PROMO_TAG_URL,
                    PlatformUtil.isMT(platform) ? config.getMtPriceBottomTagPrePicAspectRadio() : config.getDpPriceBottomTagPrePicAspectRadio());
        } else {
            return new DzPictureComponentVO(productPromoPriceM.getIcon(),
                    PlatformUtil.isMT(platform) ? config.getMtPriceBottomTagPrePicAspectRadio() : config.getDpPriceBottomTagPrePicAspectRadio());
        }
    }

    private DzTagVO buildMemberTag(int platform, ProductPromoPriceM memberPromoM, Config config) {
        DzTagVO memberDzTagVO = this.buildPromoTagVo(platform, memberPromoM, false);
        memberDzTagVO.setPrePic(new DzPictureComponentVO(MEMBER_PRE_PIC_URL,
                PlatformUtil.isMT(platform) ? config.getMtMemberPriceBottomTagPrePicAspectRadio() : config.getDpMemberPriceBottomTagPrePicAspectRadio()));
        memberDzTagVO.setAfterPic(this.buildAfterPic(platform, true, false, false, false));
        memberDzTagVO.setPromoDetail(DzPromoUtils.buildPromoDetail(memberPromoM, config.getPopType()));
        memberDzTagVO.setBorderRadius(PlatformUtil.isMT(platform) ? 3 : 1);
        return memberDzTagVO;
    }

    /**
     * 构建 AfterPic
     *
     * @param platform
     */
    private DzPictureComponentVO buildAfterPic(int platform, boolean isMember, boolean isPreSale, boolean isSecKill, boolean isMeituanPromo) {
        if (isPreSale) {
            // 预售
            return new DzPictureComponentVO(PlatformUtil.isMT(platform) ? MT_COMMON_A_AFTER_PIC_URL : DP_COMMON_AFTER_PIC_URL, 0.875);
        }
        if (isSecKill || isMeituanPromo) {
            // 限时秒杀、美团补贴
            return new DzPictureComponentVO(PlatformUtil.isMT(platform) ? MT_COMMON_B_AFTER_PIC_URL : DP_COMMON_AFTER_PIC_URL, 0.875);
        }
        if (isMember) {
            // 会员
            return new DzPictureComponentVO(MEMBER_AFTER_PIC_URL, 0.875);
        }
        if (PlatformUtil.isMT(platform)) {
            return new DzPictureComponentVO(MT_COMMON_A_AFTER_PIC_URL, 1);
        } else {
            return new DzPictureComponentVO(DP_COMMON_AFTER_PIC_URL, 1);
        }
    }

    /**
     * 构建优惠感知版本的priceBottomTag，有特别的样式逻辑
     *
     * @return
     */
    private DzTagVO buildPromoTagVo(int platform, ProductPromoPriceM productPromoPriceM, boolean isMeituanPromo) {
        DzTagVO basicTagVo;
        if (Objects.nonNull(productPromoPriceM.getPromoPriceTag()) && productPromoPriceM.getPromoTagType().equals(PromoTagTypeEnum.Member.getCode())) {
            // 会员样式不一样
            basicTagVo = DzPromoUtils.buildBasicDzTagVOWithColorAndBorder(VCPlatformEnum.MT.getType(), productPromoPriceM.getPromoTag(),
                    ColorUtils.color222222, ColorUtils.colorEAA37C, null, ColorUtils.color222222, null, true, ColorUtils.colorEAA37C);
        } else if (isMeituanPromo) {
            // 美团补贴
            basicTagVo = DzPromoUtils.buildBasicDzTagVOWithColorAndBorder(platform, productPromoPriceM.getPromoTag(),
                    ColorUtils.colorFF2727, ColorUtils.colorFF4B10, null, ColorUtils.colorFF6633, null, true, ColorUtils.colorFF6633);
        } else {
            // 新客特惠、特惠促销使用该逻辑代码
            basicTagVo = DzPromoUtils.buildBasicDzTagVOWithColorAndBorder(platform, productPromoPriceM.getPromoTag(),
                    ColorUtils.colorFF4B10, ColorUtils.colorFF4B10, null, ColorUtils.colorFF6633, null, true, ColorUtils.colorFFCFBF);
        }
        return basicTagVo;
    }

    @VPointCfg
    @Data
    public static class Config {

        private int popType = 3;

        /**
         * 美团侧 团购货架 优惠感知 PriceBottomTag PrePic 的宽高比
         */
        private Double mtPriceBottomTagPrePicAspectRadio = 3.25;
        /**
         * 点评侧 团购货架 优惠感知 PriceBottomTag PrePic 的宽高比
         */
        private Double dpPriceBottomTagPrePicAspectRadio = 3.25;

        /**
         * 美团侧 团购货架 会员 优惠感知 PriceBottomTag PrePic 的宽高比
         */
        private Double mtMemberPriceBottomTagPrePicAspectRadio = 2.435;
        /**
         * 点评侧 团购货架 会员 优惠感知 PriceBottomTag PrePic 的宽高比
         */
        private Double dpMemberPriceBottomTagPrePicAspectRadio = 2.435;

        /**
         * 有秒杀时，是否不展示比价标签
         */
        private boolean enableSecKillHidePriceTag = false;

        /**
         * 是否启用时间比价标签
         */
        private Boolean enableTimePriceTag = true;

        /**
         * 商家会员标签配置
         */
        private MerchantMemberConfig merchantMemberConfig;
    }

    @Data
    public static class MerchantMemberConfig {
        private Double afterAspectRadio;
        private Double preAspectRadio;
        private String textColor;
        private String background;
        private boolean hashBorder;
    }
}
