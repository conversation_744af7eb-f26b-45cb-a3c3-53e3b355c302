package com.sankuai.dzviewscene.product.dealstruct.options.desc;

import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.haima.entity.haima.HaimaConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.pigeon.util.MdpEnvUtils;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.dealstruct.ability.desc.vpoints.DealDetailDescVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.shelf.business.utils.ParamUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/28 5:04 下午
 */
@VPointOption(
        name = "团购详情服务质保可选项", description = "团购详情服务质保可选项", code = WarrantyDealDetailDescVPO.CODE
)
public class WarrantyDealDetailDescVPO extends DealDetailDescVP<WarrantyDealDetailDescVPO.Config> {

    public static final String CODE = "WarrantyDealDetailDescVPO";

    public static final String NO_SUPPORT_WARRANTY_SERVICE = "不提供质保服务";
    public static final String NO_SUPPORT_SERVICE_WARRANTY = "不提供服务质保";
    public static final String NO_SUPPORT_IMPLANT_WARRANTY = "不提供种植体质保";
    public static final String NO_SUPPORT_CROWN_WARRANTY = "不提供牙冠质保";
    public static final String  Long_TIME_WARRANTY = "终身质保";

    public static final String WARRANTY = "warranty";
    public static final String SERVICE_WARRANTY = "service_warranty";
    public static final String SERVICE_WARRANTY_DURATION = "service_warranty_duration";
    public static final String SERVICE_WARRANTY_CONTENT = "service_warranty_content";
    public static final String IMPLANT_WARRANTY = "implant_warranty";
    public static final String IMPLANT_WARRANTY_DURATION = "implant_warranty_duration";
    public static final String CROWN_WARRANTY = "crown_warranty";
    public static final String CROWN_WARRANTY_DURATION = "crown_warranty_duration";
    public static final String COMMA_SPLIT = "，";

    //安心补牙
    public static final Long SAFE_DENTURE_POI_TAGID = 21279L;
    //安心种植牙
    public static final Long SAFE_IMPLANT_POI_TAGID = 21278L;
    public static final Long SAFE_DENTURE_TAGID_POI_TEST = 7441L;
    public static final Long SAFE_IMPLANT_TAGID_POI_TEST = 7440L;
    private static final String MEDICAL_SAFETREAT_TAGNAME = "medicalSafeTreatTagName";
    private static final String GUARANTEETYPE = "guaranteeType";

    private static final String GUARANTEE_TAG_NAME_DENTURE = "安心医·补牙";
    private static final String GUARANTEE_TAG_NAME_IMPLANT = "安心医·种植";


    @Resource
    private CompositeAtomService compositeAtomService;


    Map<String, Boolean> notSupportWarrantyMap = new HashMap<>();
    {
        notSupportWarrantyMap.put(NO_SUPPORT_WARRANTY_SERVICE, true);
        notSupportWarrantyMap.put(NO_SUPPORT_SERVICE_WARRANTY, true);
        notSupportWarrantyMap.put(NO_SUPPORT_IMPLANT_WARRANTY, true);
        notSupportWarrantyMap.put(NO_SUPPORT_CROWN_WARRANTY, true);
    }

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (param == null) {
            return null;
        }
        if (ObjectUtils.isEmpty(param.getDealDetailInfoModel())) {
            return null;
        }
        Map<String, AttrM> attrMS = getAttrMap(param.getDealDetailInfoModel());
        String warranty = getAttr(attrMS, WARRANTY);
        if (!isSupportWarranty(warranty) || hasSafeTag(param,context)) {
            return null;
        }
        StringBuilder stringBuilder = new StringBuilder();
        String serviceWarranty = getAttr(attrMS, SERVICE_WARRANTY);
        if (isSupportWarranty(serviceWarranty)) {
            String serviceWarrantyDuration = getAttr(attrMS, SERVICE_WARRANTY_DURATION);
            if (!ObjectUtils.isEmpty(serviceWarrantyDuration)) {
                if(serviceWarrantyDuration.equals(Long_TIME_WARRANTY)) {
                    stringBuilder.append(config.getServiceWarrantyPrefix());
                    stringBuilder.append(serviceWarrantyDuration);
                }else {
                    stringBuilder.append(serviceWarrantyDuration);
                }
                stringBuilder.append(COMMA_SPLIT);
            }
            String serviceWarrantyContent = getAttr(attrMS, SERVICE_WARRANTY_CONTENT);
            if (!ObjectUtils.isEmpty(serviceWarrantyContent)) {
                stringBuilder.append(serviceWarrantyContent);
                stringBuilder.append(COMMA_SPLIT);
            }
        }
        String implantWarranty = getAttr(attrMS, IMPLANT_WARRANTY);
        if (isSupportWarranty(implantWarranty)) {
            String implantWarrantyDuration = getAttr(attrMS, IMPLANT_WARRANTY_DURATION);
            if (!ObjectUtils.isEmpty(implantWarrantyDuration)) {
                if(implantWarrantyDuration.equals(Long_TIME_WARRANTY)) {
                    stringBuilder.append(config.getImplantWarrantyPrefix());
                    stringBuilder.append(implantWarrantyDuration);
                }else {
                stringBuilder.append(implantWarrantyDuration);
                }
                stringBuilder.append(COMMA_SPLIT);
            }

        }
        String crownWarranty = getAttr(attrMS, CROWN_WARRANTY);
        if (isSupportWarranty(crownWarranty)) {
            String implantWarrantyDuration = getAttr(attrMS, CROWN_WARRANTY_DURATION);
            if (!ObjectUtils.isEmpty(implantWarrantyDuration)) {
                if(implantWarrantyDuration.equals(Long_TIME_WARRANTY)) {
                    stringBuilder.append(config.getCrownWarrantyPrefix());
                    stringBuilder.append(implantWarrantyDuration);
                }else {
                    stringBuilder.append(implantWarrantyDuration);
                }
                stringBuilder.append(COMMA_SPLIT);
            }
        }
        if(stringBuilder.length() > 0) {
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        }
        return stringBuilder.toString();
    }

    private boolean hasSafeTag(Param param,ActivityCxt context) {
        Long safeImplantTagForEnv = MdpEnvUtils.isTestEnv() ? SAFE_IMPLANT_TAGID_POI_TEST : SAFE_IMPLANT_POI_TAGID;
        Long safeDentureTagForEnv = MdpEnvUtils.isTestEnv() ? SAFE_DENTURE_TAGID_POI_TEST : SAFE_DENTURE_POI_TAGID;
        return (hasGuaranteeTagCode(param,GUARANTEE_TAG_NAME_IMPLANT) && hasDisplayPOITagId(context,safeImplantTagForEnv))||
        (hasGuaranteeTagCode(param,GUARANTEE_TAG_NAME_DENTURE) && hasDisplayPOITagId(context,safeDentureTagForEnv));
    }

    private boolean hasDisplayPOITagId(ActivityCxt context,Long id) {
        long dpPoiId = ParamUtil.getDpPoiId(context);
        return compositeAtomService.checkZdcTagByDpShopId(dpPoiId, id).join();
    }

    private boolean hasGuaranteeTagCode(Param param, String GuaranteeTagName) {
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        if (dealDetailInfoModel == null) {
            return false;
        }
        List<AttrM> dealAttrs = dealDetailInfoModel.getDealAttrs();
        String guaranteeType = getAttrValue(dealAttrs, GUARANTEETYPE);
        if(!("3").equals(guaranteeType)){
            return false;
        }
        String medicalSafeTreatTagName = getAttrValue(dealAttrs, MEDICAL_SAFETREAT_TAGNAME);
        return medicalSafeTreatTagName.equals(GuaranteeTagName);
    }

    private String getAttrValue(List<AttrM> dealAttrs, String attrName) {
        return dealAttrs.stream()
                .filter(attr -> attr.getName().equals(attrName))
                .findFirst()
                .map(AttrM::getValue)
                .orElse(StringUtils.EMPTY);
    }


    public boolean isSupportWarranty(String warranty) {
        return !ObjectUtils.isEmpty(warranty) && notInNoSupportWarrant(warranty);
    }

    private boolean notInNoSupportWarrant(String warranty) {
        Boolean notSupport = notSupportWarrantyMap.get(warranty);
        return ObjectUtils.isEmpty(notSupport);
    }

    @Nullable
    private String getAttr(Map<String, AttrM> attrMS, String attrName) {
        return Optional.ofNullable(attrMS.get(attrName)).map(AttrM::getValue).orElse(null);
    }

    private Map<String, AttrM> getAttrMap(DealDetailInfoModel dealDetailInfoModel) {
        return Optional.ofNullable(dealDetailInfoModel.getDealAttrs())
                .map(tt -> tt.stream().collect(Collectors.toMap(AttrM::getName, Function.identity(), (o, n) -> o)))
                .orElse(Maps.newHashMap());
    }

    @Data
    @VPointCfg
    public static class Config {
        private String serviceWarrantyPrefix="服务";
        private String implantWarrantyPrefix="种植体";
        private String crownWarrantyPrefix= "牙冠";

    }
}
