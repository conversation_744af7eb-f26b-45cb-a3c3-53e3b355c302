package com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.DealDetailSkuProductsGroupsBuilder;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/26 6:35 下午
 */
@Deprecated
@VPoint(name = "团购详情sku货份数变化点", description = "团购详情sku货份数变化点，支持配置文案Format", code = SkuCopiesVP.CODE, ability = DealDetailSkuProductsGroupsBuilder.CODE)
public abstract class SkuCopiesVP<T> extends PmfVPoint<String, SkuCopiesVP.Param, T>{

    public static final String CODE = "SkuCopiesVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private String copies;
    }

}
