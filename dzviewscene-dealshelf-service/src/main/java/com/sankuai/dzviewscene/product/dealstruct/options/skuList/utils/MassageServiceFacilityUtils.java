package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.enums.ServiceFacilityEnum;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.enums.ServiceMaterialAndToolEnum;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: created by hang.yu on 2023/9/13 10:56
 */
public class MassageServiceFacilityUtils {

    private static final String SPERATOR = "、";

    /**
     * 无
     */
    private static final String NOTHING = "无";

    /**
     * 服务设施icon
     */
    private static final String SERVICE_FACILITY_ICON = "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png";

    public static List<DealSkuVO> parseServiceFacility(SkuItemDto skuItemDto, boolean hitNewIcon) {
        // 材料工具
        DealSkuVO serviceMaterialAndTool = buildServiceMaterialAndTool(skuItemDto, hitNewIcon);
        // 玩乐设施
        DealSkuVO serviceFacility = buildPlayFacility(skuItemDto, hitNewIcon);
        // 其他服务
        DealSkuVO unclassifiedServices = buildUnclassifiedServices(skuItemDto, hitNewIcon);
        return Lists.newArrayList(serviceMaterialAndTool, serviceFacility, unclassifiedServices).stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private static DealSkuVO buildServiceMaterialAndTool(SkuItemDto skuItemDto, boolean hitNewIcon) {
        List<DealSkuItemVO> items = Lists.newArrayList();
        for (ServiceMaterialAndToolEnum toolEnum : ServiceMaterialAndToolEnum.values()) {
            String toolValue = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), toolEnum.getToolCode());
            if (StringUtils.isNotBlank(toolValue)) {
                items.add(buildToolItem(toolEnum, toolValue));
            }
        }
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(ServiceFacilityEnum.SERVICE_MATERIAL_AND_TOOL.getServiceFacility());
        dealSkuVO.setIcon(hitNewIcon ? ServiceFacilityEnum.SERVICE_MATERIAL_AND_TOOL.getNewIcon() : ServiceFacilityEnum.SERVICE_MATERIAL_AND_TOOL.getIcon());
        dealSkuVO.setItems(items);
        return dealSkuVO;
    }

    private static DealSkuItemVO buildToolItem(ServiceMaterialAndToolEnum toolEnum, String toolValue) {
        DealSkuItemVO tool = new DealSkuItemVO();
        if (toolEnum == ServiceMaterialAndToolEnum.FOOT_BATH_MATERIAL || toolEnum == ServiceMaterialAndToolEnum.FREE_ESSENTIAL_OIL) {
            // 泡脚包 或 免费精油
            tool.setName(getValue1(toolValue, toolEnum));
        } else if (toolEnum == ServiceMaterialAndToolEnum.EAR_PICKING_TOOL || toolEnum == ServiceMaterialAndToolEnum.DISPOSABLE_MATERIAL
                || toolEnum == ServiceMaterialAndToolEnum.DISINFECTION_SUPPLIES || toolEnum == ServiceMaterialAndToolEnum.FEMALE_SUPPLIES) {
            // 特色采耳工具、衣物布草、消毒及卫生用品）、女性用品
            tool.setName(getValue2(toolValue, toolEnum));
        } else {
            tool.setName(toolValue);
        }
        tool.setIcon(SERVICE_FACILITY_ICON);
        return tool;
    }

    private static DealSkuVO buildPlayFacility(SkuItemDto skuItemDto, boolean hitNewIcon) {
        String serviceFacility = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), "serviceFacility");
        if (StringUtils.isBlank(serviceFacility) || NOTHING.equals(serviceFacility)) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(ServiceFacilityEnum.PLAY_FACILITY.getServiceFacility());
        dealSkuVO.setIcon(hitNewIcon ? ServiceFacilityEnum.PLAY_FACILITY.getNewIcon() : ServiceFacilityEnum.PLAY_FACILITY.getIcon());
        dealSkuVO.setItems(buildDealSkuItem(serviceFacility));
        return dealSkuVO;
    }

    private static DealSkuVO buildUnclassifiedServices(SkuItemDto skuItemDto, boolean hitNewIcon) {
        String unclassifiedServices = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), "OtherStoreServices");
        if (StringUtils.isBlank(unclassifiedServices) || NOTHING.equals(unclassifiedServices)) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(ServiceFacilityEnum.UNCLASSIFIED_SERVICES.getServiceFacility());
        dealSkuVO.setIcon(hitNewIcon ? ServiceFacilityEnum.UNCLASSIFIED_SERVICES.getNewIcon() : ServiceFacilityEnum.UNCLASSIFIED_SERVICES.getIcon());
        dealSkuVO.setItems(buildDealSkuItem(unclassifiedServices));
        return dealSkuVO;
    }

    private static List<DealSkuItemVO> buildDealSkuItem(String serviceFacility) {
        List<DealSkuItemVO> items = Lists.newArrayList();
        for (String facilityValue : serviceFacility.split(SPERATOR)) {
            DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
            dealSkuItemVO.setName(facilityValue);
            dealSkuItemVO.setIcon(SERVICE_FACILITY_ICON);
            items.add(dealSkuItemVO);
        }
        return items;
    }

    private static String getValue1(String value, ServiceMaterialAndToolEnum serviceMaterialAndToolEnum) {
        // 需要对泡脚包的牛奶进行替换
        value = value.replace("牛奶", "牛奶包");
        if (!value.contains(SPERATOR)) {
            return value;
        }
        List<String> bags = Arrays.asList(value.split(SPERATOR));
        return String.format("%d种%s等任选（%s）", bags.size(), serviceMaterialAndToolEnum.getToolName(), value);
    }

    private static String getValue2(String value, ServiceMaterialAndToolEnum serviceMaterialAndToolEnum) {
        if (!value.contains(SPERATOR)) {
            return value;
        }
        return String.format("%s（%s）", serviceMaterialAndToolEnum.getToolName(), value);
    }

}
