package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.moreJumpUrl;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.dp.schema.Schemas;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListMoreJumpUrlVP;
import com.sankuai.dzviewscene.product.filterlist.utils.ContextParamBuildUtils;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.productdetail.util.UrlUtils;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 **/
@VPointOption(name = "剧本杀标品详情页团单筛选模块更多跳转url选项", description = "更多跳转url选项", code = ScriptKillConfigMoreJumpUrlOpt.CODE)
public class ScriptKillConfigMoreJumpUrlOpt extends DealFilterListMoreJumpUrlVP<ScriptKillConfigMoreJumpUrlOpt.Config> {

    public static final String CODE = "ScriptKillConfigMoreJumpUrlOpt";

    private static final String PAGE_TITLE = "pageTitle";

    @Override
    public String compute(ActivityCxt ctx, Param param, Config config) {
        if(ContextParamBuildUtils.lessThanDefaultCount(config.getDefaultDisplayCount(), ctx)) {
            return null;
        }
        int platform = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform);
        double lat = ParamsUtil.getDoubleSafely(ctx, ShelfActivityConstants.Params.lat);
        double lng = ParamsUtil.getDoubleSafely(ctx, ShelfActivityConstants.Params.lng);
        String scriptKillTitle = getScriptTitle(ctx);
        int cityId = getCityId(ctx, platform);
        String entityId = ParamsUtil.getStringSafely(ctx, ShelfActivityConstants.Params.entityId);
        if(StringUtils.isEmpty(entityId)) {
            return null;
        }
        return buildDealPageListJumpUrl(config, platform,  UrlUtils.urlEncode(scriptKillTitle), cityId, entityId, lat, lng);
    }

    private String buildDealPageListJumpUrl(Config config, int platform, String scriptTitle, int cityId, String entityId, double lat, double lng) {
        if(PlatformUtil.isMT(platform)) {
            return String.format(config.getMtMoreJumpUrl(), cityId, entityId, scriptTitle, lat, lng);
        }
        return String.format(config.getDpMoreJumpUrl(), cityId, entityId, scriptTitle, lat, lng);
    }

    private int getCityId(ActivityCxt ctx, int platform) {
        if (PlatformUtil.isMT(platform)) {
            return ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.mtCityId);
        }
        return ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.dpCityId);
    }

    private String getScriptTitle(ActivityCxt ctx) {
        Map<String, Object> extraMap = JsonCodec.decode((String)ctx.getParam(ShelfActivityConstants.Params.extra), new TypeReference<Map<String, Object>>() {});
        if(MapUtils.isEmpty(extraMap)) {
            return null;
        }
        return (String) extraMap.get(PAGE_TITLE);
    }

    @Data
    @VPointCfg
    public static class Config {
        private String mtMoreJumpUrl;
        private String dpMoreJumpUrl;
        private int defaultDisplayCount = 3;

    }
}
