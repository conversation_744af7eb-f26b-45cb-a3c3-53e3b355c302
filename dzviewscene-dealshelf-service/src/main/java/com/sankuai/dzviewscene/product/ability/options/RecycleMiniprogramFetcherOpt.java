package com.sankuai.dzviewscene.product.ability.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreAsyncHandlerVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

@VPointOption(name = "回收小程序查询", description = "回收小程序查询", code = RecycleMiniprogramFetcherOpt.CODE)
public class RecycleMiniprogramFetcherOpt extends PreAsyncHandlerVP<RecycleMiniprogramFetcherOpt.Config> {

    public static final String CODE = "RecycleMiniprogramFetcherOpt";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<Object> compute(ActivityCxt context, Param param, Config config) {
        int platform = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.platform);
        Long shopId = PlatformUtil.isMT(platform) ? PoiIdUtil.getMtPoiIdL(context, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId)
                : PoiIdUtil.getDpPoiIdL(context, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
        return compositeAtomService.queryIsMiniprogramShop(shopId, platform).thenApply(res -> res);
    }

    @Data
    @VPointCfg
    public static class Config{
    }
}
