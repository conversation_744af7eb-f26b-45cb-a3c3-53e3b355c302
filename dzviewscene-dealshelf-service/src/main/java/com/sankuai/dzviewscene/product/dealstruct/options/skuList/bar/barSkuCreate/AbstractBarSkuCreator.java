package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.vo.*;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/9 4:10 下午
 */
public abstract class AbstractBarSkuCreator {

    //sku头图属性名，1:1比例
    protected static final String HEAL_PIC_SKU_ATTR_NAME = "headpic";

    //sku详情图片属性名，3:2比例
    protected static final String DETAIL_PIC_SKU_ATTR_NAME = "detailpic";

    //sku详情图片属性别名，3:2比例
    protected static final String NEW_DETAIL_PIC_SKU_ATTR_NAME = "primarypic";

    /**
     * creator识别 抽象方法
     *@param
     *@return
     */
    public abstract boolean ideantify(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config);

    /**
     * 构造头图 抽象方法
     *@param
     *@return
     */
    protected abstract String buildIcon(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config, boolean isSameCategorySku);

    /**
     * 构造副标题文案属性名 to 属性format配置 抽象方法，用于构造副标题文案列表
     *@return
     * @param
     * @param skuItemDto
     * @param isHitDouhu
     */
    protected abstract Map<String, String> getSubtitlesAttrName2FormatMap(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config, boolean isHitDouhu);

    /**
     * 构造弹窗 抽象方法
     *@return
     * @param
     * @param isHitDouhu
     */
    protected abstract PopUpWindowVO buildPopUpWindowVO(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config, boolean isHitDouhu);

    /**
     * 提取份数
     * @param skuItemDto
     * @param config
     * @return
     */
    protected abstract String extractCopies(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config);

    /**
     * 构造sku
     *@return
     * @param
     * @param isHitDouhu
     */
    public DealSkuVO buildDealSkuVO(SkuItemDto skuItemDto, boolean isSameCategorySku, BarDealDetailSkuListModuleOpt.Config config, boolean isHitDouhu) {
        if (skuItemDto == null) {
            return null;
        }
        String price = skuItemDto.getMarketPrice() == null ? null : "¥".concat(skuItemDto.getMarketPrice().stripTrailingZeros().toPlainString());
        //构造icon
        String icon = buildIcon(skuItemDto, config, isSameCategorySku);
        //构造副标题
        List<DealSkuItemVO> dealSkuItemVOS = buildDealSkuItemVOs(skuItemDto, config, isHitDouhu);
        //构造弹窗
        PopUpWindowVO popUpWindowVO = buildPopUpWindowVO(skuItemDto, config, isHitDouhu);
        //提取份数
        String copies = extractCopies(skuItemDto, config);
        //构造结果
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setIcon(icon);
        dealSkuVO.setTitle(skuItemDto.getName());
        dealSkuVO.setItems(dealSkuItemVOS);
        dealSkuVO.setPopup(popUpWindowVO);
        dealSkuVO.setPrice(price);
        dealSkuVO.setCopies(copies);
        return dealSkuVO;
    }

    private List<String> getSkuSubtitles(SkuItemDto skuItemDto, Map<String, String> subtitlesAttrName2FormatMap, boolean drinkOrCola) {
        if (skuItemDto == null || MapUtils.isEmpty(subtitlesAttrName2FormatMap)) {
            return new ArrayList<>();
        }
        return subtitlesAttrName2FormatMap.entrySet()
                .stream()
                .map(entry -> buildSubtitle(entry.getKey(), entry.getValue(), skuItemDto.getAttrItems(), drinkOrCola))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private String buildSubtitle(String attrName, String attrFormat, List<SkuAttrItemDto> attrList, boolean drinkOrCola) {
        String value = DealDetailUtils.getSkuAttrValueBySkuAttrName(attrList, attrName);
        if (StringUtils.isEmpty(value) || StringUtils.isEmpty(attrFormat)) {
            return null;
        }
        //对于容量属性，要在属性值后面加上表示单位的quantityUnit属性
        if (ObjectUtils.equals(attrName, "quantityAvailable")) {
            String unit = DealDetailUtils.getSkuAttrValueBySkuAttrName(attrList, "quantityUnit");
            return StringUtils.isEmpty(unit) ? value : value + unit;
        }
        //对于是否畅饮属性，要将属性值"是"转换为"畅饮"，"否"则不展示
        if (ObjectUtils.equals(attrName, "serviceLimit")) {
            String serviceLimit = DealDetailUtils.getSkuAttrValueBySkuAttrName(attrList, "serviceLimit");
            return ObjectUtils.equals(serviceLimit, "是") ? "畅饮" : null;
        }
        if (ObjectUtils.equals(attrName, "volume") && drinkOrCola) {
            //应对商品bp的单位缓存不稳定问题, 非餐食的体积需要用rawValue && 在attrFormat自己加单位。
            value = DealDetailUtils.getRawSkuAttrValue(attrList, attrName);
        }
        return String.format(attrFormat, value);
    }

    /**
     * 构造副标题子项列表
     *
     * @param
     * @param isHitDouhu
     * @return
     */
    private List<DealSkuItemVO> buildDealSkuItemVOs(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config, boolean isHitDouhu) {
        boolean drinkOrCola = isDrinkOrCola(skuItemDto.getProductCategory(), config);
        Map<String, String> subtitlesAttrName2FormatMap = getSubtitlesAttrName2FormatMap(skuItemDto, config, isHitDouhu);
        List<String> subtitles = getSkuSubtitles(skuItemDto, subtitlesAttrName2FormatMap, drinkOrCola);
        if (CollectionUtils.isEmpty(subtitles)) {
            return null;
        }
        List<DealSkuItemVO> dealSkuItemVOS = subtitles.stream().map(this::buildDealSkuItemVO).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dealSkuItemVOS)) {
            return null;
        }
        return dealSkuItemVOS;
    }

    protected boolean isDrinkOrCola(long productCategory, BarDealDetailSkuListModuleOpt.Config config) {
        if (CollectionUtils.isEmpty(config.getColaSkuCateIds()) || (CollectionUtils.isEmpty(config.getDrinksSkuCateIds()))) {
            return false;
        }
        return config.getDrinksSkuCateIds().contains(productCategory) || config.getColaSkuCateIds().contains(productCategory);
    }


    /**
     * 构造副标题子项
     *
     * @param
     * @return
     */
    private DealSkuItemVO buildDealSkuItemVO(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setValue(name);
        return dealSkuItemVO;
    }

    /**
     * 构造酒水弹窗
     *
     * @param
     * @return
     */
    protected PopUpWindowVO buildPopUpWindowVOForDrinks(SkuItemDto skuItemDto, LinkedHashMap<Double, String> alcoholByVolumeRange2DocMap, List<BarDealDetailSkuListModuleOpt.AttrConfigModel> popupAttrConfigModels) {
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return null;
        }
        //获取弹窗主图
        String pic = StringUtils.isNotEmpty(DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), DETAIL_PIC_SKU_ATTR_NAME)) ? DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), DETAIL_PIC_SKU_ATTR_NAME) : DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), NEW_DETAIL_PIC_SKU_ATTR_NAME);
        List<CommonAttrVO> commonAttrVOS = new ArrayList<>();
        //获取酒水弹窗详细参数属性CommonAttrVO
        addDrinksPopupParamsInfoCommonAttrVO(skuItemDto, commonAttrVOS, popupAttrConfigModels);
        //获取常见酒精度酒水说明属性CommonAttrVO
        addGeneralAlcoholCommonAttrVO(skuItemDto, alcoholByVolumeRange2DocMap, commonAttrVOS);
        return buildPopUpWindowVO(pic, commonAttrVOS);
    }

    private PopUpWindowVO buildPopUpWindowVO(String pic, List<CommonAttrVO> commonAttrVOS) {
        PopUpWindowVO popUpWindowVO = new PopUpWindowVO();
        popUpWindowVO.setIcon(pic);
        popUpWindowVO.setInfos(commonAttrVOS);
        return popUpWindowVO;
    }

    /**
     * 添加常见酒精度酒水说明
     *
     * @param
     * @return
     */
    private void addGeneralAlcoholCommonAttrVO(SkuItemDto skuItemDto, LinkedHashMap<Double, String> alcoholByVolumeRange2DocMap, List<CommonAttrVO> commonAttrVOS) {
        if (skuItemDto == null || commonAttrVOS == null) {
            return;
        }
        double alcoholByVolume = NumberUtils.objToDouble(DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), "alcoholByVolume"));
        String generalAlcoholDesc = getAlcoholByVolumeRangeDoc(alcoholByVolume, alcoholByVolumeRange2DocMap);
        if (StringUtils.isEmpty(generalAlcoholDesc)) {
            return;
        }
        CommonAttrVO generalAlcoholCommonAttrVO = new CommonAttrVO();
        generalAlcoholCommonAttrVO.setName("常见酒水酒精度");
        generalAlcoholCommonAttrVO.setValue(generalAlcoholDesc);
        commonAttrVOS.add(generalAlcoholCommonAttrVO);
    }

    /**
     * 添加酒水弹窗详细参数模块CommonAttrVO
     *
     * @param
     * @return
     */
    private void addDrinksPopupParamsInfoCommonAttrVO(SkuItemDto skuItemDto, List<CommonAttrVO> commonAttrVOS, List<BarDealDetailSkuListModuleOpt.AttrConfigModel> popupAttrConfigModels) {
        if (commonAttrVOS == null) {
            return;
        }
        //获取该模块展示的属性列表
        List<CommonAttrsVO> commonAttrsVOs = getDrinksPopupParamsInfoCommonAttrsVOList(skuItemDto.getAttrItems(), popupAttrConfigModels);
        if (CollectionUtils.isEmpty(commonAttrsVOs)) {
            return;
        }
        //构造结果
        CommonAttrVO paramsCommonAttrVO = new CommonAttrVO();
        paramsCommonAttrVO.setCommonAttrs(commonAttrsVOs);
        paramsCommonAttrVO.setName(skuItemDto.getName());
        commonAttrVOS.add(paramsCommonAttrVO);
    }

    /**
     * 添加酒水弹窗详细参数模块属性列表
     *
     * @param
     * @return
     */
    private List<CommonAttrsVO> getDrinksPopupParamsInfoCommonAttrsVOList(List<SkuAttrItemDto> attrItems, List<BarDealDetailSkuListModuleOpt.AttrConfigModel> popupAttrConfigModels) {
        if (CollectionUtils.isEmpty(popupAttrConfigModels) || CollectionUtils.isEmpty(attrItems)) {
            return null;
        }
        return popupAttrConfigModels.stream().map(model -> {
            String value = DealDetailUtils.getSkuAttrValueBySkuAttrName(attrItems, model.getAttrName());
            if (StringUtils.isEmpty(value) || StringUtils.isEmpty(model.getAttrFormat())) {
                return null;
            }
            CommonAttrsVO commonAttrsVO = new CommonAttrsVO();
            commonAttrsVO.setName(model.getAttrTitle());
            commonAttrsVO.setValues(Lists.newArrayList(String.format(model.getAttrFormat(), value)));
            return commonAttrsVO;
        }).filter(attr -> attr != null).collect(Collectors.toList());
    }

    /**
     * 获取常见酒水酒精度说明文案
     * 1）说明：选取如下四款主流酒水度数作为参照标的，按照该款酒水位于的区间动态展示常见酒精度数文案
     * 2）标的：当前选择如下4个酒精度标的，支持后续配置更改。①青岛啤酒 3%Vol ②长城干红葡萄酒 12%Vol ③格兰菲迪12年威士忌，40%Vol ④飞天茅台 53%Vol
     * 3）文案：「常见酒水酒精度：{距离该款酒水酒精度最近的两个标的或相等的一个标的}」,例如真露的酒精度是13%Vol，则其度数说明文案为「常见酒水酒精度：青岛啤酒 3%Vol 、长城干红葡萄酒 12%Vol 」
     *
     * @param
     * @return
     */
    private String getAlcoholByVolumeRangeDoc(double alcoholByVolumeInfact, LinkedHashMap<Double, String> alcoholByVolumeRange2DocMap) {
        //如果酒精度数临近区间没有配置，则返回null
        if (MapUtils.isEmpty(alcoholByVolumeRange2DocMap)) {
            return null;
        }
        //如果实际酒精度数命中配置的某个酒精度数临近区间边界值，则返回该边界值对应的酒饮
        if (alcoholByVolumeRange2DocMap.containsKey(alcoholByVolumeInfact)) {
            return alcoholByVolumeRange2DocMap.get(alcoholByVolumeInfact);
        }
        //否则返回实际酒精度数命中的酒精度数区间边界两侧对应的酒饮
        String result = null;
        String lastDoc = StringUtils.EMPTY;
        int index = 0;
        for (Map.Entry entry : alcoholByVolumeRange2DocMap.entrySet()) {
            if (alcoholByVolumeInfact < (Double) entry.getKey()) {
                result = (index == 0) ? (String) entry.getValue() : (lastDoc + "、" + (String) entry.getValue());
                continue;
            }
            lastDoc = (String) entry.getValue();
            index++;
        }
        if (result == null) {
            return lastDoc;
        }
        return result;
    }

}
