package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.SkuAttrItemsVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.ExhibitsItemModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.PicItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/27 10:16 下午
 */
@VPointOption(name = "丽人- 美甲团购详情货attr列表变化点", description = "团购详情货attr列表默认变化点，支持配置", code = BeautyNailSkuAttrItemsVPO.CODE, isDefault = false)
public class BeautyNailSkuAttrItemsVPO extends SkuAttrItemsVP<BeautyNailSkuAttrItemsVPO.Config> {

    public static final String CODE = "BeautyNailSkuAttrItemsVPO";

    private static final String SERVICE_CONTENT = "服务内容";

    private static final String BRAND = "品牌";

    private static final String NAIL_TYPE = "甲片类型";

    private static final String NAIL_NUM = "甲片数量";

    private static final String EXTAND_NAIL_TYPE = "延长甲类型";

    private static final String EXTAND_NAIL_NUM = "延长甲数量";

    private static final String SECOND_CATEGORY = "二级分类";

    private static final String FENG_CENG = "封层";

    private static final String JIAYOUJIAO_BRAND = "甲油胶品牌";

    private static final String OPTIONAL_COLOR_RANGE = "可选颜色";

    private static final String SINGLE_COLOR_SECOND_CATEGORY = "纯色/跳色";

    private static final String GIVEN_DETION_CHN_NAMW = "赠送饰品";

    private static final String DETION_TYPE_CHN_NAME = "饰品类型";

    private static final String DETION_NAIL_NUM_CHN_NAME = "饰品赠送指数";

    private static final String OPTIONAL_DECORATION_TYPE_ATTR_NAME = "optionalDecorationsType";

    private static final String DECORATION_TYPE = "全部饰品任选（不包含满铺钻、异形钻）";

    private static final String DECORATION_FORMAT = "%s任意饰品（除满铺钻、异形钻外）";

    private static final String TOTAL_DECORATION_TYPE = "全部饰品任选";

    private static final String TOTAL_DECORATION_FORMAT = "%s任意饰品";

    private static final String NAIL_DECORATION = "美甲饰品";

    private static final String CHANGE_COLOR_TYPE = "含跳色";

    private static final String CHANGE_COLOR_FORMAT = "纯色，含%s跳色";

    private static final String WITHOUT_COLOR_FORMAT = "纯色，不含跳色";

    private static final String CONTAINS_COLOR_FORMAT = "纯色，含跳色";

    private static final String CHANGE_COLOR_NUM = "跳色指数";

    private static final String BEAUTY_NAIL_TYPE = "美甲类型";

    private static final String TYPE = "款式类型";

    private static final String ALL_TYPE = "全手款式任选";


    private static final String SPECIFIC_TYPE = "限定款式类型";

    private static final String OPTIONAL_STYLE_TYPE = "optionalStyleType";

    private static final String SPECIFIC_NUM_TYPE = "限定x指款式";

    private static final String TYPE_NAIL_NUM = "款式适用指数";


    private static final String ALL_NAIL = "全手";

    private static final String OPTIONAL_FORMAT = "%s选一";

    private static final String REFERENCE_TYPE = "参考款式";

    private static final String DEAL_RELATED_EXHIBITS_ATTR_NAME = "dealRelatedExhibitsAttr";
    private static final String DEAL_RELATED_CASE_ATTR_NAME = "dealRelatedCaseAttr";

    private static final String SEPERATOR = "/";

    private static final String NO = "否";

    private static final String COMMA = "、";

    private static final long COLOR_CATEGORY_ID = 4043;

    private static final String CUSTOMIZED_STYLE_CONTENT = "自定义款式内容";

    private static final String CUSTOMIZED_DECORATION_TYPE = "自定义饰品类型";

    private static final String STYLE_DESC_SKU_ATTR_NAME = "styledesc";

    private static final String NAIL_SUIT_PART_DEAL_ATTR_NAME = "nail_suit_part";

    private static final List<String> HAND_NAIL_SUIT_PART_DEAL_ATTRS = Lists.newArrayList("手部", "手部美甲");

    private static final String HAND_PART = "手";

    private static final String FOOT_PART = "足";

    private static final String OPTIONAL_TYPE = "optionalType";

    private static final String DECORATION_DESC_SKU_ATTR_NAME = "jewelryDesc";


    @Override
    public List<DealSkuItemVO> compute(ActivityCxt context, Param param, Config config) {
        SkuItemDto skuItemDto = param.getSkuItemDto();
        if (skuItemDto == null) {
            return null;
        }
        Map<Long, SkuShowAttrsModel> skuCategoryId2SkuShowAttrsModelMap = config.getCategoryId2SkuShowAttrsModelMap();
        long productCategoryId = skuItemDto.getProductCategory();
        List<AttrM> dealAttrs = param.getDealAttrs();
        if (DealDetailUtils.isWearableNail(dealAttrs)) {
            return getWearableNailDealSkuItemVO(skuItemDto, dealAttrs, config);
        }
        if (MapUtils.isNotEmpty(skuCategoryId2SkuShowAttrsModelMap) && skuCategoryId2SkuShowAttrsModelMap.containsKey(productCategoryId)) {
            List<Long> useStyleTypeStrCategory = config.getUseStyleTypeStrProductCategory();
            Map<String, String> styleType2ShowStrMap = config.getStyleType2ShowStrMap();
            List<DealSkuItemVO> dealSkuItemVO = getDealSkuItemVO(skuCategoryId2SkuShowAttrsModelMap, skuItemDto, productCategoryId, dealAttrs,
                    useStyleTypeStrCategory, styleType2ShowStrMap);
            return dealSkuItemVO;
        }
        if (productCategoryId == COLOR_CATEGORY_ID) {
            return getColorDealSkuItemVO(skuItemDto, dealAttrs, config);
        }
       return null;
    }

    private List<DealSkuItemVO> getWearableNailDealSkuItemVO(SkuItemDto skuItemDto, List<AttrM> dealAttrs, Config config) {
        if (Objects.isNull(skuItemDto)) {
            return null;
        }

        return skuItemDto.getAttrItems().stream()
                .map(this::convertToWearableNailAttrItem)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private DealSkuItemVO convertToWearableNailAttrItem(SkuAttrItemDto skuAttrItemDto) {
        if (Objects.isNull(skuAttrItemDto)) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(skuAttrItemDto.getChnName());
        dealSkuItemVO.setValue(skuAttrItemDto.getAttrValue());
        return dealSkuItemVO;
    }

    private List<DealSkuItemVO> getDealSkuItemVO(Map<Long, SkuShowAttrsModel> skuCategoryId2SkuShowAttrsModelMap,
                                                 SkuItemDto skuItemDto, long productCategoryId, List<AttrM> dealAttrs,
                                                 List<Long> useStyleTypeStrCategory,
                                                 Map<String, String> styleType2ShowStrMap) {
        SkuShowAttrsModel skuShowAttrsModel = skuCategoryId2SkuShowAttrsModelMap.get(productCategoryId);
        if (skuShowAttrsModel == null) {
            return null;
        }
        return getDealSkuItemVO(skuItemDto, skuShowAttrsModel.getAttrNames(), skuShowAttrsModel.getReplacedSeperator(),
                dealAttrs, useStyleTypeStrCategory, styleType2ShowStrMap);
    }

    private List<DealSkuItemVO> getColorDealSkuItemVO(SkuItemDto skuItemDto, List<AttrM> dealAttrs, Config config) {
        if (skuItemDto == null) {
            return null;
        }
        List<DealSkuItemVO> dealSkuItemVOS = new ArrayList<>();
        String category2 = DealDetailUtils.getSkuAttrValueBySkuAttrChnName(skuItemDto.getAttrItems(), SECOND_CATEGORY);
        DealSkuItemVO typeDealSkuItemVO = getTypeDealSkuItemVO(category2, skuItemDto, config, dealAttrs);
        dealSkuItemVOS.add(typeDealSkuItemVO);
        DealSkuItemVO decorationsDealSkuItemVO = getDecorationsDealSkuItemVO(category2, skuItemDto, config);
        dealSkuItemVOS.add(decorationsDealSkuItemVO);
        List<DealSkuItemVO> sealCoatArmourBrandDealSkuItemVOS = getDealSkuItemVO(skuItemDto,
                Lists.newArrayList(FENG_CENG, JIAYOUJIAO_BRAND, OPTIONAL_COLOR_RANGE), SEPERATOR,
                dealAttrs, config.getUseStyleTypeStrProductCategory(), config.getStyleType2ShowStrMap());
        if (CollectionUtils.isNotEmpty(sealCoatArmourBrandDealSkuItemVOS)) {
            dealSkuItemVOS.addAll(sealCoatArmourBrandDealSkuItemVOS);
        }
        DealSkuItemVO exhibitsSkuItemVO = getExhibitsSkuItemVO(dealAttrs);
        dealSkuItemVOS.add(exhibitsSkuItemVO);
        dealSkuItemVOS = dealSkuItemVOS.stream().filter(vo -> vo != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dealSkuItemVOS)) {
            return null;
        }
        return dealSkuItemVOS;
    }

    private DealSkuItemVO getDecorationsDealSkuItemVO(String category2, SkuItemDto skuItemDto, Config config) {
        Map<String, String> num2ShowStrMap = config.getNum2ShowStrMap();
        //当项目二级分类为“纯色/跳色”时，不展示美甲饰品字段
        if (skuItemDto == null || StringUtils.isEmpty(category2) || category2.equals(SINGLE_COLOR_SECOND_CATEGORY)) {
            return null;
        }
        String giveDecorationsValue = getStyleNailDecoration(skuItemDto.getAttrItems(), num2ShowStrMap);
        if (StringUtils.isEmpty(giveDecorationsValue)) {
            return null;
        }
        return buildDealSkuItemVO(NAIL_DECORATION, null, giveDecorationsValue, 0);
    }

    private String getStyleNailDecoration(List<SkuAttrItemDto> attrItems, Map<String, String> num2ShowStrMap) {
        if (CollectionUtils.isEmpty(attrItems)) {
            return null;
        }
        String serviceContent = DealDetailUtils.getSkuAttrValueBySkuAttrChnName(attrItems, SERVICE_CONTENT);
        if (CUSTOMIZED_STYLE_CONTENT.equals(serviceContent)) {
            return DealDetailUtils.getSkuAttrValueBySkuAttrName(attrItems, DECORATION_DESC_SKU_ATTR_NAME);
        }
        String giveDecorations = DealDetailUtils.getSkuAttrValueBySkuAttrChnName(attrItems, GIVEN_DETION_CHN_NAMW);
        if (StringUtils.isEmpty(giveDecorations) || giveDecorations.equals(NO)) {
            return null;
        }
        String decorationsType = DealDetailUtils.getSkuAttrValueBySkuAttrChnName(attrItems, DETION_TYPE_CHN_NAME);
        String optionalType = DealDetailUtils.getSkuAttrValueBySkuAttrName(attrItems, OPTIONAL_TYPE);
        if (TOTAL_DECORATION_TYPE.equals(decorationsType) || DECORATION_TYPE.equals(decorationsType)) {
            return decorationsType;
        }
        if (CUSTOMIZED_DECORATION_TYPE.equals(decorationsType)) {
            return optionalType;
        }
        String decorationsOptionalType = DealDetailUtils.getSkuAttrValueBySkuAttrName(attrItems, OPTIONAL_DECORATION_TYPE_ATTR_NAME);
        String decorationsNum = DealDetailUtils.getSkuAttrValueBySkuAttrChnName(attrItems, DETION_NAIL_NUM_CHN_NAME);
        return getDecorationsStr(decorationsOptionalType, decorationsNum, num2ShowStrMap);
    }

    private DealSkuItemVO getTypeDealSkuItemVO(String category2, SkuItemDto skuItemDto,Config config, List<AttrM> dealAttrs) {
        String value = getTypeDealSkuItemVOValue(category2, skuItemDto, config, dealAttrs);
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        return buildDealSkuItemVO(BEAUTY_NAIL_TYPE, null, value, 0);
    }

    private String getTypeDealSkuItemVOValue(String category2, SkuItemDto skuItemDto,Config config, List<AttrM> dealAttrs) {
        if (StringUtils.isEmpty(category2) || skuItemDto == null) {
            return null;
        }
        String content = DealDetailUtils.getSkuAttrValueBySkuAttrChnName(skuItemDto.getAttrItems(), SERVICE_CONTENT);
        if (category2.equals(SINGLE_COLOR_SECOND_CATEGORY)) {
            return getSingleColorSkuItemValue(content, skuItemDto);
        }
        return getMultiStyleNailSkuItemValue(skuItemDto, content, config, dealAttrs);
    }

    private String getMultiStyleNailSkuItemValue(SkuItemDto skuItemDto, String content, Config config, List<AttrM> dealAttrs) {
        //服务内容为"自定款式内容"
        if (CUSTOMIZED_STYLE_CONTENT.equals(content)) {
            return DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), STYLE_DESC_SKU_ATTR_NAME);
        }
        String docForHand = "";
        //服务内容为"全手款式任选"
        if (ALL_TYPE.equals(content)) {
            docForHand = getAllTypeSkuItemValue(skuItemDto, config);
        }
        //服务内容为"限定x指款式"
        if(SPECIFIC_NUM_TYPE.equals(content)){
            docForHand =  getSpecificNumSkuItemValue(skuItemDto, config);
        }
        String suitablePart = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, NAIL_SUIT_PART_DEAL_ATTR_NAME);
        //适用部位为"足部"，展示字段要做兼容
        if (StringUtils.isNotEmpty(docForHand) && !HAND_NAIL_SUIT_PART_DEAL_ATTRS.contains(suitablePart)) {
            return docForHand.replaceAll(HAND_PART, FOOT_PART);
        }
        return docForHand;
    }

    private String getSingleColorSkuItemValue(String content, SkuItemDto skuItemDto) {
        String toningIndex = DealDetailUtils.getSkuAttrValueBySkuAttrChnName(skuItemDto.getAttrItems(), CHANGE_COLOR_NUM);
        if (!Objects.equals(content, CHANGE_COLOR_TYPE)) {
            return WITHOUT_COLOR_FORMAT;
        }
        if (StringUtils.isEmpty(toningIndex)) {
            return CONTAINS_COLOR_FORMAT;
        }
        return String.format(CHANGE_COLOR_FORMAT, toningIndex);
    }

    private String getAllTypeSkuItemValue(SkuItemDto skuItemDto, Config config) {
        String nailType = DealDetailUtils.getSkuAttrValueBySkuAttrChnName(skuItemDto.getAttrItems(), TYPE);
        if (SPECIFIC_TYPE.equals(nailType)) {
            String optionalType = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), OPTIONAL_STYLE_TYPE);
            return getAllTypeStr(optionalType, config);
        }
        if(MapUtils.isNotEmpty(config.getNailType2AllShowStrMap()) && config.getNailType2AllShowStrMap().containsKey(nailType)){
            return config.getNailType2AllShowStrMap().get(nailType);
        }
        return null;
    }

    private String getSpecificNumSkuItemValue(SkuItemDto skuItemDto, Config config) {
        String nailType = DealDetailUtils.getSkuAttrValueBySkuAttrChnName(skuItemDto.getAttrItems(), TYPE);
        String nailNum = DealDetailUtils.getSkuAttrValueBySkuAttrChnName(skuItemDto.getAttrItems(), TYPE_NAIL_NUM);
        Map<String, String> num2ShowStrMap = config.getNum2ShowStrMap();
        if (SPECIFIC_TYPE.equals(nailType)) {
            String optionalType = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), OPTIONAL_STYLE_TYPE);
            return getDecorationsStr(optionalType, nailNum, num2ShowStrMap);
        }
        if(MapUtils.isNotEmpty(config.getNailType2LimitedShowStrMap()) && config.getNailType2LimitedShowStrMap().containsKey(nailType)){
            return String.format(config.getNailType2LimitedShowStrMap().get(nailType), nailNum);
        }
        return null;
    }


    private String getAllTypeStr(String optionalType, Config config) {
        Map<String, String> num2ShowStrMap = config.getNum2ShowStrMap();
        List<String> types = getOptionalTypes(optionalType);
        if (CollectionUtils.isEmpty(types)) {
            return null;
        }
        if(MapUtils.isEmpty(num2ShowStrMap)){
            return StringUtils.join(types, SEPERATOR);
        }
        String showNum = num2ShowStrMap.get(String.valueOf(types.size()));
        if (types.size() <= 1 || StringUtils.isEmpty(showNum)) {
            return StringUtils.join(types, SEPERATOR);
        }
        return StringUtils.join(types, SEPERATOR) + String.format(OPTIONAL_FORMAT, showNum);
    }

    private List<String> getOptionalTypes(String optionalType) {
        if (StringUtils.isEmpty(optionalType)) {
            return null;
        }
        List<String> optionalTypes = Lists.newArrayList(optionalType.split(COMMA));
        if (CollectionUtils.isEmpty(optionalTypes)) {
            return null;
        }
        return optionalTypes.stream().map(type -> ALL_NAIL + type).collect(Collectors.toList());
    }

    private String getDecorationsStr(String decorationsOptionalType, String decorationsNum, Map<String, String> num2ShowStrMap) {
        List<String> decorationsStrs = getDecorationsStrs(decorationsOptionalType, decorationsNum);
        if (CollectionUtils.isEmpty(decorationsStrs)) {
            return null;
        }
        if(MapUtils.isEmpty(num2ShowStrMap)){
            return StringUtils.join(decorationsStrs, SEPERATOR);
        }
        String showNum = num2ShowStrMap.get(String.valueOf(decorationsStrs.size()));
        if (decorationsStrs.size() <= 1 || StringUtils.isEmpty(showNum)) {
            return StringUtils.join(decorationsStrs, SEPERATOR);
        }
        return StringUtils.join(decorationsStrs, SEPERATOR) + String.format(OPTIONAL_FORMAT, showNum);
    }

    private List<String> getDecorationsStrs(String decorationsOptionalType, String decorationsNum) {
        if (StringUtils.isEmpty(decorationsOptionalType) || StringUtils.isEmpty(decorationsNum)) {
            return null;
        }
        List<String> decorationsOptionalTypes = Lists.newArrayList(decorationsOptionalType.split(COMMA));
        if (CollectionUtils.isEmpty(decorationsOptionalTypes)) {
            return null;
        }
        return decorationsOptionalTypes.stream().map(type -> decorationsNum + type).collect(Collectors.toList());
    }

    private List<DealSkuItemVO> getDealSkuItemVO(SkuItemDto skuItemDto, List<String> attrNameList,
                                                 String replacedSeperator, List<AttrM> dealAttrs,
                                                 List<Long> useStyleTypeStrCategory,
                                                 Map<String, String> styleType2ShowStrMap) {
        if (skuItemDto == null || CollectionUtils.isEmpty(attrNameList)) {
            return null;
        }
        return attrNameList.stream().map(attrName -> {
            String attrValue = DealDetailUtils.getSkuAttrValueBySkuAttrChnName(skuItemDto.getAttrItems(), attrName);
            if ("适用妆容".equals(attrName)){
                return buildDealSkuItemVOByDealAttr(attrName, null, dealAttrs);
            }
            if (StringUtils.isEmpty(attrValue)) {
                return null;
            }
            attrValue = attrValue.replaceAll(COMMA, replacedSeperator);
            if (CollectionUtils.isNotEmpty(useStyleTypeStrCategory) && useStyleTypeStrCategory.contains(skuItemDto.getProductCategory()) && MapUtils.isNotEmpty(styleType2ShowStrMap)) {
                attrValue = styleType2ShowStrMap.getOrDefault(attrValue, attrValue);
            }
            return buildDealSkuItemVO(attrName, null, attrValue, 0);
        }).filter(vo -> vo != null).collect(Collectors.toList());
    }
    public DealSkuItemVO buildDealSkuItemVOByDealAttr(String attrName, String attrValue, List<AttrM> dealAttrs) {
        List<PicItemVO> picItemVOList = getPicItemVOFromCases(dealAttrs);
        if (CollectionUtils.isEmpty(picItemVOList)){
            return null;
        }
        return buildDealSkuItemVO(attrName, picItemVOList, attrValue, 3);

    }

    private List<PicItemVO> getPicItemVOFromCases(List<AttrM> dealAttrs) {
        String exhibitsStr = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, DEAL_RELATED_CASE_ATTR_NAME);
        if (StringUtils.isEmpty(exhibitsStr)) {
            return null;
        }
        List<ExhibitsItemModel> exhibitsItemModels = JsonCodec.converseList(exhibitsStr, ExhibitsItemModel.class);
        if (CollectionUtils.isEmpty(exhibitsItemModels)) {
            return null;
        }
        return exhibitsItemModels.stream().map(model -> buildPicItemVO(model)).filter(model -> model != null).collect(Collectors.toList());
    }

    private DealSkuItemVO getExhibitsSkuItemVO(List<AttrM> dealAttrs) {
        String exhibitsStr = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, DEAL_RELATED_EXHIBITS_ATTR_NAME);
        if (StringUtils.isEmpty(exhibitsStr)) {
            return null;
        }
        List<ExhibitsItemModel> exhibitsItemModels = JsonCodec.converseList(exhibitsStr, ExhibitsItemModel.class);
        if (CollectionUtils.isEmpty(exhibitsItemModels)) {
            return null;
        }
        List<PicItemVO> picValues = exhibitsItemModels.stream().map(model -> buildPicItemVO(model)).filter(model -> model != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(picValues)) {
            return null;
        }
        return buildDealSkuItemVO(REFERENCE_TYPE, picValues, null, 3);
    }

    private PicItemVO buildPicItemVO(ExhibitsItemModel model) {
        if (model == null) {
            return null;
        }
        PicItemVO picItemVO = new PicItemVO();
        picItemVO.setTitle(model.getName());
        picItemVO.setUrl(model.getPic());
        return picItemVO;
    }

    private DealSkuItemVO buildDealSkuItemVO(String name, List<PicItemVO> picValues, String value, int type) {
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setType(type);
        dealSkuItemVO.setPicValues(picValues);
        dealSkuItemVO.setValue(value);
        return dealSkuItemVO;
    }

    @Data
    @VPointCfg
    public static class Config {
        private Map<String, String> num2ShowStrMap;
        private Map<Long, SkuShowAttrsModel> categoryId2SkuShowAttrsModelMap;
        //服务内容为"全手款式任选"时，款式类型和展示字段的映射关系
        private Map<String, String> nailType2AllShowStrMap;
        //服务内容为"限定x指款式"时，款式类型和展示字段的映射关系
        private Map<String, String> nailType2LimitedShowStrMap;
        // 服务项目分类为穿戴甲时，进行款式类型和展示字段的映射
        private List<Long> useStyleTypeStrProductCategory;
        // 服务项目分类为穿戴甲时，款式类型和展示字段的映射关系
        private Map<String, String> styleType2ShowStrMap;
    }

    @Data
    public static class SkuShowAttrsModel {
        List<String> attrNames;
        String replacedSeperator;
    }
}
