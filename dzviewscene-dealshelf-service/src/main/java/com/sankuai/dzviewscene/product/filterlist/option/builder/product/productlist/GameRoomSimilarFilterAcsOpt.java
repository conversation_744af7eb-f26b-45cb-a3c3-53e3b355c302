package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/06
 */
@VPointOption(name = "亲子游乐游戏厅相似团购排序",
        description = "亲子游乐游戏厅相似团购排序",
        code = "GameRoomSimilarFilterAcsOpt")
public class GameRoomSimilarFilterAcsOpt extends ProductListVP<GameRoomSimilarFilterAcsOpt.Config> {

    private static final String ATTR_SERVICE_TYPE = "serviceType";
    private static final String ATTR_PRODUCT_CATEGORY_IDS = "attr_totalProductCategoryIds";
    private static final String ATTR_SEARCH_HIDDEN_STATUS = "attr_search_hidden_status";

    private static final String ATTR_GAME_CURRENCY_COUNT = "dealGameCurrencyCountAttr";

    private static final String SERVICE_TYPE = "游戏币";
    //游戏厅二级类目
    private static final Integer PUBLISH_CATEGORY_ID = 322;
    //游戏币项目分类
    private String PRODUCT_CATEGORY_IDS = "[2054]";

    @Override
    public List<ProductM> compute(ActivityCxt context, Param param, Config config) {
        List<ProductM> productMS = param.getProductMS();
        if (CollectionUtils.isEmpty(productMS)) {
            return productMS;
        }
        //获取当前团单信息
        ProductM currentProduct = productMS.stream()
                .filter(productM -> productM.getProductId() == ParamsUtil.getIntSafely(context, PmfConstants.Params.entityId))
                .findFirst().orElse(null);
        // 判断是否有游戏币的团单
        if (null == currentProduct || !isHasGameCurrency(currentProduct)) {
            return null;
        }
        // 当前团单游戏币个数
        Integer gameCurrencyCount = getGameCurrencyCount(currentProduct);
        productMS = productMS.stream()
                .filter(e -> isValid(e, currentProduct))
                .sorted(this::comparing)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productMS)) {
            return null;
        }
        productMS.add(0, currentProduct);
        return productMS;
    }

    private Integer getGameCurrencyCount(ProductM productM) {
        return Integer.valueOf(productM.getAttr(ATTR_GAME_CURRENCY_COUNT));
    }

    private Integer getSale(ProductM productM) {
        if (productM.getSale() != null) {
            return productM.getSale().getSale();
        }
        return 0;
    }

    private boolean isValid(ProductM productM, ProductM currentProduct) {
        return !productM.equals(currentProduct) //过滤当前团单
                && "false".equals(productM.getAttr(ATTR_SEARCH_HIDDEN_STATUS))// 过滤隐藏单
                && isHasGameCurrency(productM);
    }


    private boolean isHasGameCurrency(ProductM productM) {
        // 过滤游戏币数为null
        String gameCurrencyCount = productM.getAttr(ATTR_GAME_CURRENCY_COUNT);
        if (null == gameCurrencyCount || !StringUtils.isNumeric(gameCurrencyCount)) {
            return false;
        }
        if (StringUtils.isBlank(productM.getTitle())) {
            return false;
        }
        // 过滤团购三级分类=游戏币（service_type=游戏币）
        String serviceType = productM.getAttr(ATTR_SERVICE_TYPE);
        if (StringUtils.isNotBlank(serviceType) && SERVICE_TYPE.contains(serviceType)) {
            return true;
        }
        // 团购二级分类=游戏厅（publishCategoryId=322）、项目分类=游戏币（product.sku.productcategorys=2054）
        String productCategoryIds = productM.getAttr(ATTR_PRODUCT_CATEGORY_IDS);
        int categoryId = productM.getCategoryId();
        if (categoryId == PUBLISH_CATEGORY_ID && PRODUCT_CATEGORY_IDS.equals(productCategoryIds)) {
            return true;
        }
        return false;
    }

    /**
     * 排序规则：
     * 游戏币数大于当前商品，正序排列；
     * 游戏币小于当前商品，倒序排列；
     * 币数一致优先展示销量高的团单。
     *
     * @param p1
     * @param p2
     * @param gameCurrencyCount 当前商品游戏币数
     */
    private Integer comparing(ProductM p1, ProductM p2, int gameCurrencyCount) {
        Integer gameCurrencyCountP1 = getGameCurrencyCount(p1);
        Integer gameCurrencyCountP2 = getGameCurrencyCount(p2);
        int saleP1 = getSale(p1);
        int saleP2 = getSale(p1);
        if (gameCurrencyCountP1.equals(gameCurrencyCountP2)) {
            return saleP1 == saleP2 ? 0 : (saleP1 > saleP2 ? -1 : 1);
        } else if (gameCurrencyCountP1 >= gameCurrencyCount && gameCurrencyCountP2 >= gameCurrencyCount) {
            return gameCurrencyCountP1 > gameCurrencyCountP2 ? 1 : -1;
        } else if (gameCurrencyCountP1 < gameCurrencyCount && gameCurrencyCountP2 < gameCurrencyCount) {
            return gameCurrencyCountP1 > gameCurrencyCountP2 ? -1 : 1;
        } else {
            return gameCurrencyCountP1 >= gameCurrencyCount ? -1 : 1;
        }
    }

    /**
     * 排序规则，当前团单展示在第一个
     * 剩下团单按照游戏币数量从小到大正序排序
     * 币数一致展示销量高的团单
     * @param p1
     * @param p2
     * @return
     */
    private Integer comparing(ProductM p1, ProductM p2){
        Integer gameCurrencyCountP1 = getGameCurrencyCount(p1);
        Integer gameCurrencyCountP2 = getGameCurrencyCount(p2);
        int saleP1 = getSale(p1);
        int saleP2 = getSale(p2);
        if (gameCurrencyCountP1.equals(gameCurrencyCountP2)) {
            return saleP1 == saleP2 ? 0 : (saleP1 > saleP2 ? -1 : 1);
        }else {
            return gameCurrencyCountP1 > gameCurrencyCountP2 ? 1 : -1;
        }
    }

    @VPointCfg
    @Data
    public static class Config {

    }
}
