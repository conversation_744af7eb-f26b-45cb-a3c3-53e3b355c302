package com.sankuai.dzviewscene.product.dealstruct.model;

import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.JumpUrlVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.VideoModuleVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/22 8:35 下午
 */
@Data
public class DealDetailStructAttrModuleGroupModel {

    /**
     * 结构化属性列表组名
     */
    private String groupName;

    /**
     * 结构化属性列表副标题
     */
    private String groupSubtitle;

    /**
     * 结构化属性列表
     */
    private List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOS;

    /**
     * 跳转
     */
    private JumpUrlVO jumpUrl;

    /**
     * 列表形式，默认0，1代表原点，2代表数字
     */
    private int dotType;

    /**
     * 最小显示几个，超过后会折叠
     */
    private int showNum;

    /**
     * 折叠文案的前缀
     * 查看更多9件
     */
    private String foldStr;

    /**
     * 团详嵌套层
     */
    private List<DealDetailStructAttrModuleGroupModel> dealDetailModuleList2;

    /**
     * 团详标题附加字段
     */
    private List<DealDetailStructAttrModuleVO> subTitleItems;

    /**
     * 模块内嵌视频/图文组件
     */
    private VideoModuleVO videoModuleVO;

    /**
     * groupName同名情况下控制展示优先级（越大优先级越高）
     */
    private Integer order=0;

}
