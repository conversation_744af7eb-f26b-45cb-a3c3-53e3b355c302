package com.sankuai.dzviewscene.product.dealstruct.ability.sku;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.struct.query.api.entity.dto.*;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.*;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealSkuGroupSequenceModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealSkuSequenceModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/25 3:53 下午
 */
@Deprecated
@Ability(code = DealDetailSkuProductsGroupsBuilder.CODE,
        name = "团购详情模块sku货列表组组件构造能力",
        description = "团购详情模块sku货列表组组件构造能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE
        }
)
public class DealDetailSkuProductsGroupsBuilder extends PmfAbility<List<List<DealSkuGroupModuleVO>>, DealDetailSkuProductsGroupsParam, DealDetailSkuProductsGroupsCfg> {

    public static final String CODE = "dealDetailSkuProductsGroupsBuilder";

    @Override
    public CompletableFuture<List<List<DealSkuGroupModuleVO>>> build(ActivityCxt activityCxt, DealDetailSkuProductsGroupsParam skuProductsGroupsParam, DealDetailSkuProductsGroupsCfg skuProductsGroupsCfg) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        if (CollectionUtils.isEmpty(dealDetailInfoModels)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        List<List<DealSkuGroupModuleVO>> dealSkusGroupsList = dealDetailInfoModels.stream().map(detailModel -> {
            //对应一整个团单
            //有效性校验
            if(!isValidDealDetail(detailModel)) {
                return null;
            }
            //团单属性信息
            List<AttrM> dealAttrs = detailModel.getDealAttrs();
            // 穿戴甲删除了服务项目，这里要手动从属性中添加
            boolean wearableNail = DealDetailUtils.isWearableNail(dealAttrs);
            if (wearableNail) {
                DealDetailDtoModel wearableDealDetailDtoModel = handleWearableNailAttr(
                        detailModel.getDealId(), dealAttrs, skuProductsGroupsCfg);
                detailModel.setDealDetailDtoModel(wearableDealDetailDtoModel);
            }
            //构造sku列表组列表
            List<DealSkuGroupSequenceModel> dealSkuGroupSequenceModels = new ArrayList<>();
            DealDetailSkuUniStructuredDto dealDetailSkuUniStructuredDto = detailModel.getDealDetailDtoModel().getSkuUniStructuredDto();
            //构造sku列表组
            //1.构造必须信息sku列表组列表
            List<MustSkuItemsGroupDto> mustGroups = dealDetailSkuUniStructuredDto.getMustGroups();
            List<DealSkuGroupSequenceModel> dealSkuGroupModuleVOSFromMustGroups = buildSkuGroupsFromMustGroups(mustGroups, detailModel.getProductCategories(), activityCxt, dealAttrs);
            if(CollectionUtils.isNotEmpty(dealSkuGroupModuleVOSFromMustGroups)) {
                dealSkuGroupSequenceModels.addAll(dealSkuGroupModuleVOSFromMustGroups);
            }
            //2.构造可选信息sku列表组列表
            List<OptionalSkuItemsGroupDto> optionalGroups = dealDetailSkuUniStructuredDto.getOptionalGroups();
            List<DealSkuGroupSequenceModel> dealSkuGroupModuleVOSFromOptionalGroups = buildSkuGroupsFromOptionalGroups(optionalGroups, detailModel.getProductCategories(), activityCxt, dealAttrs);
            if(CollectionUtils.isNotEmpty(optionalGroups)) {
                dealSkuGroupSequenceModels.addAll(dealSkuGroupModuleVOSFromOptionalGroups);
            }
            //对sku列表组排序
            if(CollectionUtils.isEmpty(dealSkuGroupSequenceModels)) {
                return null;
            }
            List<DealSkuGroupModuleVO> skuGroups = dealSkuGroupSequenceModels.stream().sorted(Comparator.comparingInt(DealSkuGroupSequenceModel::getPriority))
                    .map(sequenceModel -> sequenceModel.getDealSkuGroupModuleVO()).filter(vo -> vo != null).collect(Collectors.toList());
/*            //获取展示样式
            DealDetailSkusGroupsType<?> dealDetailSkusGroupsType = findVPoint(activityCxt, DealDetailSkusGroupsType.CODE);
            String type = dealDetailSkusGroupsType.execute(activityCxt, DealDetailSkusGroupsType.Param.builder().build());*/
            //对sku列表组进行后置处理
            DealDetailSkusGroupListAfterProcessingVP<?> dealDetailSkusGroupListAfterProcessingVP = findVPoint(activityCxt, DealDetailSkusGroupListAfterProcessingVP.CODE);
            if (dealDetailSkusGroupListAfterProcessingVP != null) {
                skuGroups = dealDetailSkusGroupListAfterProcessingVP.execute(activityCxt, DealDetailSkusGroupListAfterProcessingVP.Param.builder().dealAttrs(dealAttrs).skuGroups(skuGroups).dealTitle(detailModel.getDealTitle()).marketPrice(detailModel.getMarketPrice()).build());
            }
            //构造sku列表组列表module
            return skuGroups;
        }).filter(vo -> vo != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dealSkusGroupsList) && skuProductsGroupsCfg.isEnableAdaptNoStructDeal()) {
            return CompletableFuture.completedFuture(getNoStructDealSkuGroupModule(dealDetailInfoModels, activityCxt));
        }
        return CompletableFuture.completedFuture(dealSkusGroupsList);
    }

    private DealDetailDtoModel handleWearableNailAttr(int dealGroupId,List<AttrM> dealAttrs, DealDetailSkuProductsGroupsCfg skuProductsGroupsCfg) {
        if (CollectionUtils.isEmpty(dealAttrs) || Objects.isNull(skuProductsGroupsCfg) ||
                MapUtils.isEmpty(skuProductsGroupsCfg.getSkuAttrGroups())) {
            return new DealDetailDtoModel();
        }
        boolean wearableNail = DealDetailUtils.isWearableNail(dealAttrs);
        if (!wearableNail) {
            return new DealDetailDtoModel();
        }
        Map<String, DealDetailSkuProductsGroupsCfg.SkuAttrGroupByItem> skuAttrGroups = skuProductsGroupsCfg.getSkuAttrGroups();
        DealDetailSkuProductsGroupsCfg.SkuAttrGroupByItem groupByItem = skuAttrGroups.getOrDefault("502.穿戴甲",
                new DealDetailSkuProductsGroupsCfg.SkuAttrGroupByItem());
        List<DealDetailSkuProductsGroupsCfg.SkuAttrGroup> groupBy = groupByItem.getGroupBy();
        List<SkuItemDto> skuItemDtos = groupBy.stream().filter(Objects::nonNull)
                .map(group -> convertWearableNailSkuItemDto(group, dealAttrs))
                .collect(Collectors.toList());

        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        dealDetailDtoModel.setDealGroupId(dealGroupId);
        dealDetailDtoModel.setTitle("团购详情");
        DealDetailSkuUniStructuredDto dealDetailSkuUniStructuredDto = new DealDetailSkuUniStructuredDto();
        MustSkuItemsGroupDto mustGroup = new MustSkuItemsGroupDto();
        mustGroup.setSkuItems(skuItemDtos);
        dealDetailSkuUniStructuredDto.setMustGroups(Collections.singletonList(mustGroup));
        dealDetailDtoModel.setSkuUniStructuredDto(dealDetailSkuUniStructuredDto);
        dealDetailDtoModel.setStructType("uniform-structure-table");
        return dealDetailDtoModel;
    }

    public SkuItemDto convertWearableNailSkuItemDto(DealDetailSkuProductsGroupsCfg.SkuAttrGroup group,
                                                    List<AttrM> dealAttrs) {
        if (Objects.isNull(group) || CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        List<DealDetailSkuProductsGroupsCfg.SkuAttrDTO> includeAttrNames = group.getIncludeAttrName();
        SkuItemDto skuItemDto = new SkuItemDto();
        List<SkuAttrItemDto> skuAttrItemDtos = new ArrayList<>();
        for (DealDetailSkuProductsGroupsCfg.SkuAttrDTO includeAttr : includeAttrNames) {
            String attrValueStr = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, includeAttr.getAttrName());
            if (StringUtils.isBlank(attrValueStr)) {
                continue;
            }
            SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
            skuAttrItemDto.setAttrName(includeAttr.getAttrName());
            if (StringUtils.isNotBlank(includeAttr.getCnName())) {
                skuAttrItemDto.setChnName(includeAttr.getCnName());
            }
            // 需要解析分隔符
            List<String> attrValues = Lists.newArrayList();
            if (StringUtils.isNotBlank(includeAttr.getSeparator())) {
                attrValues = JSON.parseArray(attrValueStr, String.class);
            }
            String attrValueDesc = null;
            if (StringUtils.isNotBlank(includeAttr.getDesc()) && !attrValues.isEmpty()) {
                String attrValueDesc1 = attrValues.stream().collect(Collectors.joining(includeAttr.getSeparator()));
                String attrValueDesc2 = String.format(includeAttr.getDesc(), attrValues.size());
                attrValueDesc = attrValueDesc1 + " " + attrValueDesc2;
            }
            skuAttrItemDto.setAttrValue(Objects.isNull(attrValueDesc) ? attrValueStr : attrValueDesc);
            skuAttrItemDtos.add(skuAttrItemDto);
        }
        skuItemDto.setAttrItems(skuAttrItemDtos);
        String itemName = group.getItemName();
        skuItemDto.setName(itemName);
        return skuItemDto;
    }

    /**
     * 获取旧版非结构化 SKU 属性
     * @param dealDetailInfoModels
     * @param activityCxt
     * @return
     */
    private List<List<DealSkuGroupModuleVO>> getNoStructDealSkuGroupModule(List<DealDetailInfoModel> dealDetailInfoModels, ActivityCxt activityCxt) {
        NoStructDealSkuGroupBuildVP<?> vPoint = findVPoint(activityCxt, NoStructDealSkuGroupBuildVP.CODE);
        List<List<DealSkuGroupModuleVO>> resultList = new ArrayList<>(dealDetailInfoModels.size());
        for (DealDetailInfoModel detailInfoModel : dealDetailInfoModels) {
            List<DealSkuGroupModuleVO> result = vPoint.execute(activityCxt, NoStructDealSkuGroupBuildVP.Param.builder().dealDetailInfo(detailInfoModel).build());
            if (CollectionUtils.isNotEmpty(result)) {
                resultList.add(result);
            }
        }
        return resultList;
    }

    private List<DealSkuGroupSequenceModel> buildSkuGroupsFromMustGroups(List<MustSkuItemsGroupDto> mustGroups, List<ProductSkuCategoryModel> productCategories, ActivityCxt activityCxt, List<AttrM> dealAttrs) {
        if(CollectionUtils.isEmpty(mustGroups)) {
            return null;
        }
        return mustGroups.stream().map(mustGroup -> {
            if (mustGroup == null) {
                return null;
            }
            return buildSkuGroups(mustGroup.getSkuItems(), productCategories, activityCxt, true, CollectionUtils.size(mustGroup.getSkuItems()), dealAttrs);
        }).filter(model -> model != null).collect(Collectors.toList());
    }

    private DealSkuGroupSequenceModel buildSkuGroups(List<SkuItemDto> skuItems, List<ProductSkuCategoryModel> productCategories, ActivityCxt activityCxt, boolean isMustGroup, int optionalNum, List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(skuItems)) {
            return null;
        }
        DealSkuGroupSequenceModel dealSkuGroupSequenceModel = new DealSkuGroupSequenceModel();
        //构造sku列表组顺序号
        DealDetailSkusGroupSequenceIdVP<?> dealDetailSkusGroupSequenceIdVP = findVPoint(activityCxt, DealDetailSkusGroupSequenceIdVP.CODE);
        Integer sequenceId = dealDetailSkusGroupSequenceIdVP.execute(activityCxt, DealDetailSkusGroupSequenceIdVP.Param.builder().skuItems(skuItems).isMustGroup(true).productCategories(productCategories).build());
        if(sequenceId != null) {
            dealSkuGroupSequenceModel.setPriority(sequenceId == null ? Integer.MAX_VALUE : sequenceId.intValue());
        }

        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        //构造sku列表组标题
        DealDetailSkusGroupTitleVP<?> dealDetailSkusGroupTitleVP = findVPoint(activityCxt, DealDetailSkusGroupTitleVP.CODE);
        String groupTitle = dealDetailSkusGroupTitleVP.execute(activityCxt, DealDetailSkusGroupTitleVP.Param.builder().isMustGroup(isMustGroup).totalNum(CollectionUtils.size(skuItems)).optionalNum(optionalNum).build());
        dealSkuGroupModuleVO.setTitle(groupTitle);

        //构造sku列表
        List<DealSkuVO> dealSkuList = skuItems.stream().map(sku -> {
            //构造sku
            return convertSkuItemDto2DealSkuVO(sku, activityCxt, productCategories, dealAttrs);
        }).sorted(Comparator.comparingInt(DealSkuSequenceModel::getSequenceIndex))
                .filter(sku -> sku != null).map(model -> model.getSkuVO()).collect(Collectors.toList());
        //sku列表后置处理
        DealDetailSkuListAfterProcessingVP<?> dealDetailSkuListAfterProcessingVP = findVPoint(activityCxt, DealDetailSkuListAfterProcessingVP.CODE);
        if (dealDetailSkuListAfterProcessingVP != null) {
            dealSkuList = dealDetailSkuListAfterProcessingVP.execute(activityCxt, DealDetailSkuListAfterProcessingVP.Param.builder().isMustGroup(isMustGroup).dealSkuVOS(dealSkuList).productCategories(productCategories).skuItems(skuItems).build());
        }
        dealSkuGroupModuleVO.setDealSkuList(dealSkuList);
        dealSkuGroupSequenceModel.setDealSkuGroupModuleVO(dealSkuGroupModuleVO);
        return dealSkuGroupSequenceModel;
    }

    private DealSkuSequenceModel convertSkuItemDto2DealSkuVO(SkuItemDto skuItemDto, ActivityCxt activityCxt, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs) {
        if(skuItemDto == null) {
            return null;
        }
        // 如果是穿戴甲，则不查价格和份数
        if (DealDetailUtils.isWearableNail(dealAttrs)) {
            return convertWearableNailSkuItemDto2DealSkuVO(skuItemDto, activityCxt, productCategories, dealAttrs);
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        //构造sku price 提供默认变化点，支持文案format配置
        SkuPriceVP<?> skuPriceVP = findVPoint(activityCxt, SkuPriceVP.CODE);
        String skuPrice = skuItemDto.getMarketPrice() != null ? skuItemDto.getMarketPrice().stripTrailingZeros().toPlainString() : null;
        String price = skuPriceVP.execute(activityCxt, SkuPriceVP.Param.builder().price(skuPrice).build());
        dealSkuVO.setPrice(price);
        //构造sku copies 提供默认变化点，支持文案format配置
        SkuCopiesVP<?> skuCopiesVP = findVPoint(activityCxt, SkuCopiesVP.CODE);
        String skuCopies = String.valueOf(skuItemDto.getCopies());
        String copies = skuCopiesVP.execute(activityCxt, SkuCopiesVP.Param.builder().copies(skuCopies).build());
        dealSkuVO.setCopies(copies);
        //构造sku title 提供默认变化点
        SkuTitleVP<?> skuTitleVP = findVPoint(activityCxt, SkuTitleVP.CODE);
        String skuTitle = skuItemDto.getName();
        String title = skuTitleVP.execute(activityCxt, SkuTitleVP.Param.builder().skuTitle(skuTitle).skuItemDto(skuItemDto).productCategories(productCategories).build());
        dealSkuVO.setTitle(title);
        //构造sku attr item 列表  非常定制化，这块提供的默认变化点只用作降级，一般不建议使用
        SkuAttrItemsVP<?> skuAttrItemsVP = findVPoint(activityCxt, SkuAttrItemsVP.CODE);
        List<DealSkuItemVO> skuItemVOS = skuAttrItemsVP.execute(activityCxt, SkuAttrItemsVP.Param.builder().skuItemDto(skuItemDto).productCategories(productCategories).dealAttrs(dealAttrs).build());
        dealSkuVO.setItems(skuItemVOS);
        DealSkuSequenceModel dealSkuSequenceModel = new DealSkuSequenceModel();
        dealSkuSequenceModel.setSkuVO(dealSkuVO);
        //构造sku列表组顺序号
        SkuSequenceVP<?> skuSequenceVP = findVPoint(activityCxt, SkuSequenceVP.CODE);
        Integer sequenceId = skuSequenceVP.execute(activityCxt, SkuSequenceVP.Param.builder().skuItemDto(skuItemDto).productCategories(productCategories).build());
        dealSkuSequenceModel.setSequenceIndex(sequenceId == null ? Integer.MAX_VALUE : sequenceId.intValue());
        return dealSkuSequenceModel;
    }

    private DealSkuSequenceModel convertWearableNailSkuItemDto2DealSkuVO(SkuItemDto skuItemDto, ActivityCxt activityCxt, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs) {
        DealSkuVO dealSkuVO = new DealSkuVO();
        //构造sku title 提供默认变化点
        dealSkuVO.setTitle(skuItemDto.getName());
        //构造sku attr item 列表  非常定制化，这块提供的默认变化点只用作降级，一般不建议使用
        SkuAttrItemsVP<?> skuAttrItemsVP = findVPoint(activityCxt, SkuAttrItemsVP.CODE);
        List<DealSkuItemVO> skuItemVOS = skuAttrItemsVP.execute(activityCxt, SkuAttrItemsVP.Param.builder().skuItemDto(skuItemDto).productCategories(productCategories).dealAttrs(dealAttrs).build());
        dealSkuVO.setItems(skuItemVOS);
        DealSkuSequenceModel dealSkuSequenceModel = new DealSkuSequenceModel();
        dealSkuSequenceModel.setSkuVO(dealSkuVO);
        //构造sku列表组顺序号
        SkuSequenceVP<?> skuSequenceVP = findVPoint(activityCxt, SkuSequenceVP.CODE);
        Integer sequenceId = skuSequenceVP.execute(activityCxt, SkuSequenceVP.Param.builder().skuItemDto(skuItemDto).productCategories(productCategories).build());
        dealSkuSequenceModel.setSequenceIndex(sequenceId == null ? Integer.MAX_VALUE : sequenceId.intValue());
        return dealSkuSequenceModel;
    }

    private List<DealSkuGroupSequenceModel> buildSkuGroupsFromOptionalGroups(List<OptionalSkuItemsGroupDto> optionalGroups, List<ProductSkuCategoryModel> productCategories, ActivityCxt activityCxt, List<AttrM> dealAttrs) {
        if(CollectionUtils.isEmpty(optionalGroups)) {
            return null;
        }
        return optionalGroups.stream().map(optionalGroup -> {
            if (optionalGroup == null) {
                return null;
            }
            return buildSkuGroups(optionalGroup.getSkuItems(), productCategories, activityCxt, false, optionalGroup.getOptionalCount(), dealAttrs);
        }).filter(model -> model != null).collect(Collectors.toList());
    }

    private boolean isValidDealDetail(DealDetailInfoModel detailModel) {
        if (Objects.isNull(detailModel)) {
            return false;
        }
        // 穿戴甲没有服务流程，不做检验
        boolean wearableNail = DealDetailUtils.isWearableNail(detailModel.getDealAttrs());
        if (wearableNail) {
            return true;
        }
        if(detailModel.getDealDetailDtoModel() == null || detailModel.getDealDetailDtoModel().getSkuUniStructuredDto() == null) {
            return false;
        }
        return isStructDealDetail(detailModel.getDealDetailDtoModel().getStructType());
    }

    private boolean isStructDealDetail(String structType) {
        return Lists.newArrayList("uniform-structure-table").contains(structType);//todo lmd 写成配置化
    }
}
