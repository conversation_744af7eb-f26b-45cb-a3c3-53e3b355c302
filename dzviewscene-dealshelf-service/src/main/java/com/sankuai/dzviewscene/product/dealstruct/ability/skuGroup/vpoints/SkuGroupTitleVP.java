package com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.DealDetailSkuGroupsBuilder;
import lombok.Builder;
import lombok.Data;

/**
 * created by zhangzhiyuan04 in 2021/12/31
 */
@VPoint(name = "Sku一级分组总标题", description = "Sku分组的一级标题，一般必选组为空，可选组为X选X", code = SkuGroupTitleVP.CODE, ability = DealDetailSkuGroupsBuilder.CODE)
public abstract class SkuGroupTitleVP<T> extends PmfVPoint<String, SkuGroupTitleVP.Param, T> {

    public static final String CODE = "SkuGroupTitleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {

        /**
         * 是否必选
         */
        private boolean isMust;

        /**
         * 可选数量
         */
        private int optionalCount;

        /**
         * 总数
         */
        private int sum;
    }
}
