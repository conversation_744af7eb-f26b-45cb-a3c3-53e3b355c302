package com.sankuai.dzviewscene.product.filterlist.option.builder.product.sale;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePriceDescVP;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/28
 */
@VPointOption(
        name = "默认-价格描述标签",
        description = "价格描述标签",
        code = "DefaultProductSalePriceDescOpt",
        isDefault = true)
public class DefaultProductSalePriceDescOpt extends ProductSalePriceDescVP<DefaultProductSalePriceDescOpt.Config> {

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Config config) {
        return param.getProductM().getBasePriceDesc();
    }

    @VPointCfg
    @Data
    public static class Config {
    }

}
