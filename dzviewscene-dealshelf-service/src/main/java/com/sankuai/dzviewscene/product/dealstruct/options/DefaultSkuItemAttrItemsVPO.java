package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.SkuAttrItemsVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints.SkuItemAttrVP;
import com.sankuai.dzviewscene.product.dealstruct.model.PicItemModel;
import com.sankuai.dzviewscene.product.dealstruct.model.SkuAttrModel;
import com.sankuai.dzviewscene.product.dealstruct.model.SkuAttrValueModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/27 10:16 下午
 */
@VPointOption(name = "团购详情货attr列表默认变化点", description = "团购详情货attr列表默认变化点", code = DefaultSkuItemAttrItemsVPO.CODE, isDefault = true)
public class DefaultSkuItemAttrItemsVPO extends SkuItemAttrVP<DefaultSkuItemAttrItemsVPO.Config> {

    public static final String CODE = "DefaultSkuItemAttrItemsVPO";

    @Override
    public List<SkuAttrModel> compute(ActivityCxt context, Param param, Config config) {
        SkuItemDto skuItemDto = param.getSkuItemDto();
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return null;
        }
        List<SkuAttrItemDto> skuAttrItemDtos = skuItemDto.getAttrItems();
        return skuAttrItemDtos.stream().map(attr -> {
            if (attr == null) {
                return null;
            }
            return buildSkuAttrModel(attr.getChnName(), attr.getAttrName(), attr.getAttrValue());
        }).filter(dto -> dto != null).collect(Collectors.toList());
    }

    private SkuAttrModel buildSkuAttrModel(String name, String attrName,  String value) {
        SkuAttrModel skuAttrModel = new SkuAttrModel();
        skuAttrModel.setName(name);
        skuAttrModel.setAttrName(attrName);
        skuAttrModel.setValue(buildSkuAttrValueModel(value, null));
        return skuAttrModel;
    }

    private SkuAttrValueModel buildSkuAttrValueModel(String doc, List<PicItemModel> pics) {
        SkuAttrValueModel skuAttrValueModel = new SkuAttrValueModel();
        skuAttrValueModel.setDoc(doc);
        skuAttrValueModel.setPics(pics);
        return skuAttrValueModel;
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
