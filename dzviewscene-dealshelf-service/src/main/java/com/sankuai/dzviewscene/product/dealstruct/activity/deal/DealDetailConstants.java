package com.sankuai.dzviewscene.product.dealstruct.activity.deal;

import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfSubScene;
import com.sankuai.dzviewscene.shelf.business.utils.ParamUtil;

/**
 * Created by float on 2020/8/22.
 */
public interface DealDetailConstants {

    /**
     * 参数名定义列表
     */
    interface Params {

        /**
         * 1. 平台
         */
        String platform = "platform";

        /**
         * 2. 城市ID
         */
        String dpCityId = "dpCityId";

        /**
         * 3. 点评城市
         */
        String mtCityId = "mtCityId";

        /**
         * 4. 点评侧门店ID
         */
        String dpPoiId = "dpPoiId";

        /**
         * 5. 美团侧门店ID
         */
        String mtPoiId = "mtPoiId";

        /**
         * 6. 客户端类型
         */
        String clientType = "clientType";

        /**
         * 7. 用户ID, 不同平台不同值
         */
        String dpUserId = "dpUserId";

        /**
         * 7. 用户ID, 不同平台不同值
         */
        String mtUserId = "mtUserId";

        /**
         * 8. 当前选中的筛选标签ID
         */
        String selectedFilterId = "selectedFilterId";

        /**
         * 9. 设备ID
         */
        String deviceId = "deviceId";

        /**
         * 10. unionID
         */
        String unionId = "unionId";

        /**
         * 11. 搜索关键词
         */
        String keyword = "keyword";

        /**
         * 12. 页大小
         */
        String pageSize = "pageSize";

        /**
         * 13. 第几页
         */
        String pageNo = "pageNo";

        /**
         * 14. shopuuid
         */
        String shopUuid = "shopuuid";

        /*
         * 15. 场景ID
         */
        String sceneCode = "sceneCode";

        /**
         * 16. UA
         */
        String userAgent = "userAgent";

        /**
         * 17. trace参数
         */
        String traceMark = "_activity_trace";

        /**
         * 18. trace参数
         */
        String appVersion = "appVersion";

        /**
         * 19. 经纬度
         */
        String lat = "lat";
        String lng = "lng";

        /**
         * 20. 请求来源参数字段, 用于区分是筛选接口还是商品列表接口
         */
        String channel = "channel";

        /**
         * 21. 密室sku型货架，需要传递spuId
         */
        String productId = "productId";

        /**
         * 22. 传递productItemId
         */
        String productItemId = "productItemId";

        /**
         * 22. 密室sku型货架，需要传递日期
         */
        String selectDate = "selectDate";

        /**
         * 23. 模块名
         */
        String moduleName = "moduleName";

        /**
         * 24.【召回参数，可选】扩展信息
         */
        String extra = "extra";

        /**
         * 立减的商品类型
         */
        String promoProductType = "promoProductType";

        /**
         * 3.通用销量统计平台 1-点评单平台，2-美团单平台，其它默认双平台
         */
        String salePlatforms = "salePlatforms";
        /**
         * 点评指定策略ID名
         */
        String DP_EXP_ID = "DP_EXP_ID";

        /**
         * 美团指定策略ID名
         */
        String MT_EXP_ID = "MT_EXP_ID";

        /**
         * 标品ID，String，逗号分隔
         */
        String spuIds = "spuIds";

        /**
         * 货架类型列表
         */
        String shelfType = "shelfType";

        /**
         * 实体ID，比如：检测报告ID
         */
        String entityId = "entityId";

        /**
         * 案例id
         */
        String nailexhibitid = "nailexhibitid";

        /**
         * 算法召回业务标识
         */
        String algorithmQueryBizId = "algorithmQueryBizId";

        /**
         * 购买日期
         */
        String purchaseDate = "purchasedate";

        /**
         * 门店美团城市id
         */
        String shopMtCityId = "shopMtCityId";

        /**
         * 置顶商品ID列表，格式：id列表，逗号分隔
         */
        String topProductIds = "topProductIds";

        /**
         * 下挂商品Id 【新】
         * Ex：{"deal":"1,2,3","spu":"4,5,6"}
         * deal - 团单， spu - 泛商品
         * 解析工具如下：
         * {@link ParamUtil#getSummaryDealIds(String, String)}
         */
        String summaryProductIds = "summarypids";

        /**
         * tab锚定关键字
         */
        String searchKeyword = "searchkeyword";

        /**
         * 能力Code-class 映射
         * key - code
         * value - class
         */
        String abilityClassMap = "abilityClassMap";

        /**
         * key - code
         * value - class
         * 扩展点 Code-Class 映射
         */
        String extPointClassMap = "extPointClassMap";

        /**
         * 斗斛实验结果
         * value：List<DouHuM>
         */
        String douHus = "douHus";

        /**
         * 斗斛请求构造（无实验id）用于给新框架场景识别使用
         * value：DouHuRequest 对象
         */
        String douHuRequest = "douHuRequest";

        /**
         * 子场景{@link DealShelfSubScene}
         */
        String subScene = "subScene";
    }

    interface Style {

        /**
         * 1. 货架样式
         */
        String showType = "showType";

        /**
         * 1. 货架交互类型
         */
        String interactType = "interactType";
    }

    interface Ctx {
        /**
         * 上下文中门店
         */
        String ctxShop = "ctxShop";
    }

    /**
     * 暂存结果key定义列表
     */
    interface Attachments {
        /**
         * 召回结果暂存KEY
         */
        String productGroups = "groupNames";
    }

    /**
     * 来源类型
     */
    interface ChannelType {
        /**
         * 预订筛选来源
         */
        String bookingFilter = "bookingFilter";

        /**
         * 预订商品列表来源
         */
        String bookingProducts = "bookingProducts";

        /**
         * 泛商品筛选来源
         */
        String productFilter = "productFilter";

        /**
         * 泛商品列表来源
         */
        String productProducts = "productProducts";
        /**
         * 泛商品列表来源
         */
        String filterListRPC = "filterListRPC";

        /**
         * 团购货架首次加载
         */
        String dealShelfFirstLoad = "dealShelfFirstLoad";

        /**
         * 团购货架点击Tab获取List
         */
        String dealShelfListForTab = "dealShelfListForTab";
    }
}
