package com.sankuai.dzviewscene.product.dealstruct.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/1/28 3:57 下午
 */
@MobileDo(id = 0x2121)
public class TableHeaderCellVO implements Serializable {
    /**
     * 表头元素关联表数据key
     */
    @MobileDo.MobileField(key = 0x9e5e)
    private String key;

    /**
     * 表头元素名
     */
    @MobileDo.MobileField(key = 0xef84)
    private String data;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }
}