package com.sankuai.dzviewscene.product.filterlist.option.builder.product.button;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.enums.WarmUpStageEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductButtonVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;

/**
 * <AUTHOR>
 * @date 2023/6/19
 */
@VPointOption(
        name = "预热样式的按钮",
        description = "根据商品的预热秒杀状态展示对应的抢购按钮",
        code = "ShoppingMallMembershipWarmUpButtonOpt")
public class ShoppingMallMembershipWarmUpButtonOpt extends ProductButtonVP<Void> {

    @Override
    public DzSimpleButtonVO compute(ActivityCxt context, Param param, Void unused) {
        ProductM productM = param.getProductM();
        WarmUpStageEnum.WarmUpStageResult warmUpStageResult = WarmUpStageEnum.getWarmUpStageResult(productM);
        if (!warmUpStageResult.isValidWarmUpStage()) {
            return buildButton(productM.getOrderUrl(), "抢购");
        }
        switch (warmUpStageResult.getStage()) {
            default:
            case ONLY_WARM_UP_PRESALE:
            case NOT_STARTED_TIME_STOCK:
            case ONLY_TIME_STOCK_PRESALE:
                return buildButton(productM.getJumpUrl(), "即将开抢");
            case ONLY_WARM_UP_SALE:
            case IN_PROCESS_TIME_STOCK:
                return buildButton(productM.getOrderUrl(), "立即抢");
            case CURRENT_TIME_END_HAVE_NEXT:
            case CURRENT_STOCK_END_HAVE_NEXT:
                return buildButton(productM.getJumpUrl(), "已抢光");
            case NO_NEXT_TIME_STOCK:
            case NO_STOCK:
                return buildButton(productM.getJumpUrl(), "抢购结束");
        }
    }

    private DzSimpleButtonVO buildButton(String jumpUrl, String buttonName) {
        DzSimpleButtonVO button = new DzSimpleButtonVO();
        button.setJumpUrl(jumpUrl);
        button.setName(buttonName);
        button.setType(1);
        button.setShow(true);
        return button;
    }
}
