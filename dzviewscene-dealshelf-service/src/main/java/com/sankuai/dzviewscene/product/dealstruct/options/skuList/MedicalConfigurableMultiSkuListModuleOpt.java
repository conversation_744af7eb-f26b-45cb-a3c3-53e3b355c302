package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.mdp.boot.starter.pigeon.util.MdpEnvUtils;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.dealstruct.ability.desc.vpoints.DealDetailDescVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.MedicalDealAttrUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.SkuGroupModuleConfig;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.SkuItemValueConfig;
import com.sankuai.dzviewscene.shelf.business.utils.ParamUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@VPointOption(name = "医疗可配置的多SKU列表", description = "医疗可配置的多SKU列表", code = MedicalConfigurableMultiSkuListModuleOpt.CODE)

public class MedicalConfigurableMultiSkuListModuleOpt extends SkuListModuleVP<MedicalConfigurableMultiSkuListModuleOpt.Config> {

    public static final String CODE = "MedicalConfigurableMultiSkuListModuleOpt";

    private static final String SERVICE_TYPE_KEY = "service_type";

    // 补充信息key
    private static final String DESC_KEY = "medical_desc";

    private static final String MEDICAL_SAFETREAT_TAGNAME = "medicalSafeTreatTagName";
    private static final String GUARANTEETYPE = "guaranteeType";
    //安心补牙
    public static final Long SAFE_DENTURE_POI_TAGID = 21279L;
    //安心种植牙
    public static final Long SAFE_IMPLANT_POI_TAGID = 21278L;
    public static final Long SAFE_DENTURE_TAGID_POI_TEST = 7441L;
    public static final Long SAFE_IMPLANT_TAGID_POI_TEST = 7440L;

    private static final String GUARANTEE_TAG_NAME_DENTURE = "安心医·补牙";
    private static final String GUARANTEE_TAG_NAME_IMPLANT = "安心医·种植";
    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public List<DealDetailSkuListModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        Map<String, String> name2ValueMap = MedicalDealAttrUtils.convertName2ValueMap(param.getDealDetailInfoModel());
        fillDefaultValue(name2ValueMap, param.getDealDetailInfoModel());
        detailInfoParse(config, name2ValueMap);
        if (validateRequest(name2ValueMap, config)) {
            return null;
        }

        name2ValueMapPostProcess(context,param,name2ValueMap);

        return config.getGroupConfigsMap().get(name2ValueMap.get(SERVICE_TYPE_KEY)).stream().map(groupConfig -> {
            String descModel = MedicalDealAttrUtils.buildCommonAttrValue(groupConfig.getDescConfig(), name2ValueMap, null);
            List<DealSkuGroupModuleVO> groupModuleVOs = MedicalDealAttrUtils.buildDealSkusByAttrs(name2ValueMap, groupConfig.getSkuGroupModuleConfigs());
            if ((ObjectUtils.isEmpty(groupConfig.getGroupName())) ||
                    (!ObjectUtils.isEmpty(groupConfig.getDescConfig()) && ObjectUtils.isEmpty(descModel))
                    || (!ObjectUtils.isEmpty(groupConfig.getSkuGroupModuleConfigs()) && ObjectUtils.isEmpty(groupModuleVOs))) {
                return null;
            }
            return buildGroupModels(descModel, groupModuleVOs, groupConfig.getGroupName());
        }).filter(v -> !ObjectUtils.isEmpty(v)).collect(Collectors.toList());
    }

    private void name2ValueMapPostProcess(ActivityCxt context, Param param ,Map<String, String> name2ValueMap) {
        Long safeImplantTagForEnv = MdpEnvUtils.isTestEnv() ? SAFE_IMPLANT_TAGID_POI_TEST : SAFE_IMPLANT_POI_TAGID;
        Long safeDentureTagForEnv = MdpEnvUtils.isTestEnv() ? SAFE_DENTURE_TAGID_POI_TEST : SAFE_DENTURE_POI_TAGID;
        //符合安心补牙，安心种植打标标签
       if((hasGuaranteeTagCode(param,GUARANTEE_TAG_NAME_IMPLANT) && hasDisplayPOITagId(context,safeImplantTagForEnv))||
                (hasGuaranteeTagCode(param,GUARANTEE_TAG_NAME_DENTURE) && hasDisplayPOITagId(context,safeDentureTagForEnv))){
           //对质保服务做处理：
           name2ValueMap.remove("WarrantyPeriod");
       }
    }

    private boolean hasDisplayPOITagId(ActivityCxt context,Long id) {
        long dpPoiId = ParamUtil.getDpPoiId(context);
        return compositeAtomService.checkZdcTagByDpShopId(dpPoiId, id).join();
    }


    private boolean hasGuaranteeTagCode(Param param, String GuaranteeTagName) {
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        if (dealDetailInfoModel == null) {
            return false;
        }
        List<AttrM> dealAttrs = dealDetailInfoModel.getDealAttrs();
        String guaranteeType = getAttrValue(dealAttrs, GUARANTEETYPE);
        if(!("3").equals(guaranteeType)){
            return false;
        }
        String medicalSafeTreatTagName = getAttrValue(dealAttrs, MEDICAL_SAFETREAT_TAGNAME);
        return medicalSafeTreatTagName.equals(GuaranteeTagName);
    }

    private String getAttrValue(List<AttrM> dealAttrs, String attrName) {
        return dealAttrs.stream()
                .filter(attr -> attr.getName().equals(attrName))
                .findFirst()
                .map(AttrM::getValue)
                .orElse(StringUtils.EMPTY);
    }

    private void detailInfoParse(Config config, Map<String, String> name2ValueMap) {
        if (config == null || config.getIsDetailInfoParsed() == null || !config.getIsDetailInfoParsed()) {
            return;
        }
        String detailInfo = name2ValueMap.get("detailInfo");
        if (ObjectUtils.isEmpty(detailInfo)) {
            return;
        }
        Map<String, JSONArray> serviceProjectMap = new HashMap<>();
        Optional.ofNullable(JSON.parseObject(detailInfo))
                .map(obj -> obj.getJSONArray("content"))
                .filter(arr -> !arr.isEmpty())
                .map(arr -> getForType(arr, "uniform-structure-table"))
                .map(obj -> obj.getJSONObject("data"))
                .map(obj -> obj.getJSONArray("groups"))
                .filter(arr -> !arr.isEmpty())
                .orElse(new JSONArray()).stream()
                .map(JSONObject.class::cast)
                .filter(obj -> obj.containsKey("units"))
                .map(obj -> obj.getJSONArray("units"))
                .flatMap(Collection::stream)
                .map(JSONObject.class::cast)
                .forEach(obj -> {
                    if (obj.containsKey("attrValues")) {
                        JSONObject attrValues = obj.getJSONObject("attrValues");
                        for (String key : attrValues.keySet()) {
                            name2ValueMap.put(key, attrValues.getString(key));
                        }
                    }
                    if (obj.containsKey("skuCateId")) {
                        String skuCateId = obj.getString("skuCateId");
                        if (!serviceProjectMap.containsKey(skuCateId)) {
                            serviceProjectMap.put(skuCateId, new JSONArray());
                        }
                        serviceProjectMap.get(skuCateId).add(obj);

                    }
                });
        if (!serviceProjectMap.isEmpty()) {
            for (Map.Entry<String, JSONArray> entry : serviceProjectMap.entrySet()) {
                name2ValueMap.put(entry.getKey(), entry.getValue().toJSONString());
            }
        }
    }

    private static JSONObject getForType(JSONArray array, String type) {
        Optional<JSONObject> opt = array.stream()
                .map(JSONObject.class::cast)
                .filter(obj -> type.equals(obj.getString("type")))
                .findFirst();
        return opt.orElse(null);
    }

    private void fillDefaultValue(Map<String, String> name2ValueMap, DealDetailInfoModel dealDetailInfoModel) {
        Optional.ofNullable(dealDetailInfoModel).map(DealDetailInfoModel::getDesc)
                .ifPresent(desc -> name2ValueMap.put(DESC_KEY, desc));
    }

    private DealDetailSkuListModuleGroupModel buildGroupModels(String descModel, List<DealSkuGroupModuleVO> groupModuleVOs, String groupName) {
        DealDetailSkuListModuleGroupModel groupModel = new DealDetailSkuListModuleGroupModel();
        groupModel.setGroupName(groupName);
        groupModel.setDescModel(descModel);
        groupModel.setDealSkuGroupModuleVOS(groupModuleVOs);
        return groupModel;
    }

    private boolean validateRequest(Map<String, String> name2ValueMap, Config config) {
        return ObjectUtils.isEmpty(config) || ObjectUtils.isEmpty(config.getGroupConfigsMap())
                || ObjectUtils.isEmpty(name2ValueMap) || ObjectUtils.isEmpty(name2ValueMap.get(SERVICE_TYPE_KEY))
                || ObjectUtils.isEmpty(config.getGroupConfigsMap().get(name2ValueMap.get(SERVICE_TYPE_KEY)));
    }

    @Data
    @VPointCfg
    public static class Config {
        private Map<String, List<GroupConfig>> groupConfigsMap;
        private Boolean isDetailInfoParsed;
    }

    @Data
    public static class GroupConfig {
        private String groupName;
        private SkuItemValueConfig descConfig;
        private List<SkuGroupModuleConfig> skuGroupModuleConfigs;
    }
}
