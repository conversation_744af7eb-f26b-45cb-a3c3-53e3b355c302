package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.shop;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.common.helper.LionConfigHelper;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.shoppadding.ContextHandlerAbility;
import com.sankuai.dzviewscene.shelf.platform.common.model.ContextHandlerResult;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.dto.BNPLExposureDTO;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.dto.DeviceInfoDTO;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.dto.ExposureScenarioDTO;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.dto.ProductSignDTO;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.enums.BizIdFieldMeaning;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.enums.ExposureEnum;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.request.BNPLExposureRequest;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.response.BNPLExposureResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Component
@Slf4j
public class UserAfterPayPaddingHandler implements ContextPaddingHandler {

    @Resource
    private CompositeAtomService compositeAtomService;
    private static final String USE_CASE = "OPEN";

    private static final String USER_CREDIT_PAY_CONFIG = "deal.shelf.user.credit.pay.config";

    // 业务ID值 金服提供 线上线下不一致,需配置lion
    private static final String BIZ_ID = "bizId";

    // 先用后付模版ID 金服提供 线上线下不一致,需配置lion
    private static final String PLAN_ID = "planId";

    // 签约商户号 金服提供 线上线下不一致,需配置lion
    private static final String SIGN_IPH_PAY_MERCHANT_NO = "signIphPayMerchantNo";

    @Override
    public CompletableFuture<ContextHandlerResult> padding(ActivityCxt ctx, ContextHandlerResult contextHandlerResult, Map<String, Object> params) {
        if (MapUtils.isEmpty(params)) {
            return CompletableFuture.completedFuture(contextHandlerResult);
        }
        if (!LionConfigHelper.timesDealCreditPaySwitch()) {
            return CompletableFuture.completedFuture(contextHandlerResult);
        }
        String clientType = ParamsUtil.getStringSafely(ctx.getParameters(), ShelfActivityConstants.Params.userAgent);
        if(!params.containsKey(clientType)){
            return CompletableFuture.completedFuture(contextHandlerResult);
        }
        BNPLExposureRequest request = getBNPLExposureRequest(ctx);
        if (request.getUserId() <= 0) {
            return CompletableFuture.completedFuture(contextHandlerResult);
        }
        CompletableFuture<BNPLExposureResponse> queryResultSyncCompletableFuture = compositeAtomService.queryIsAfterPay(request);
        if (queryResultSyncCompletableFuture == null) {
            return CompletableFuture.completedFuture(contextHandlerResult);
        }
        return queryResultSyncCompletableFuture.thenApply(queryResultSync -> {
            if (Objects.isNull(queryResultSync) || !queryResultSync.isSuccess()) {
                return contextHandlerResult;
            }
            BNPLExposureDTO data = queryResultSync.getData();
            if (Objects.isNull(data)) {
                return contextHandlerResult;
            }
            contextHandlerResult.setExposure(data.getExposure());
            return contextHandlerResult;
        });
    }

    private UserCreditPayConfig getUserCreditPayConfig() {
        try {
            Map<String, String> configMap = Lion.getMap(Environment.getAppName(), USER_CREDIT_PAY_CONFIG, String.class,
                    Maps.newHashMap());
            String bizIdStr = configMap.getOrDefault(BIZ_ID, StringUtils.EMPTY);
            String planIdStr = configMap.getOrDefault(PLAN_ID, StringUtils.EMPTY);
            String signPayStr = configMap.getOrDefault(SIGN_IPH_PAY_MERCHANT_NO, StringUtils.EMPTY);
            UserCreditPayConfig config = new UserCreditPayConfig();
            config.setBizId(bizIdStr);
            config.setPlanId(NumberUtils.toLong(planIdStr, 0));
            config.setSignPayMerchantNo(NumberUtils.toLong(signPayStr, 0));
            return config;
        } catch (Exception e) {
            log.error("getUserCreditPayConfig error", e);
            return null;
        }
    }

    @Data
    static class UserCreditPayConfig {
        private String bizId;
        private Long planId;
        private Long signPayMerchantNo;
    }

    private BNPLExposureRequest getBNPLExposureRequest(ActivityCxt ctx) {
        BNPLExposureRequest request = new BNPLExposureRequest();
        ExposureScenarioDTO exposureScenarioDTO = new ExposureScenarioDTO();
        // 业务ID字段含义
        exposureScenarioDTO.setBizIdFieldMeaning(BizIdFieldMeaning.PLAN_ID.getValue());

        // 用例 OPEN：预览准入； OPEN_ACCOUNT：开通准入； COMMIT_ORDER：提单页准入
        exposureScenarioDTO.setUseCase(USE_CASE);
        // 丽人次卡：BeauCard 教育次卡：EduCard 按摩足疗次卡:SpaCard
        exposureScenarioDTO.setSubBizScenario(getSubBizScenario(ctx));
        request.setExposureScenario(exposureScenarioDTO);
        request.setUserId(ParamsUtil.getUserId(ctx));
        ProductSignDTO productSignDTO = new ProductSignDTO();
        UserCreditPayConfig userCreditPayConfig = getUserCreditPayConfig();
        if (userCreditPayConfig != null) {
            // 业务ID值 金服提供
            exposureScenarioDTO.setBizId(userCreditPayConfig.getBizId());
            // 先用后付模版ID 金服提供
            productSignDTO.setPlanId(userCreditPayConfig.getPlanId());
            // 签约商户号 金服提供
            productSignDTO.setSignIphPayMerchantNo(userCreditPayConfig.getSignPayMerchantNo());
        }
        request.setProductSignInfo(productSignDTO);
        DeviceInfoDTO deviceInfoDTO = new DeviceInfoDTO();
        String deviceId = ParamsUtil.getStringSafely(ctx.getParameters(), ShelfActivityConstants.Params.deviceId);
        deviceInfoDTO.setUuid(deviceId);
        String clientType = ParamsUtil.getStringSafely(ctx.getParameters(), ShelfActivityConstants.Params.clientType);
        // 平台标识
        deviceInfoDTO.setPlatform("android".equalsIgnoreCase(clientType)?"android" : "iphone");
        // 客户端标识
        deviceInfoDTO.setApp(getApp(ctx));
        deviceInfoDTO.setLocation(getLocation(ctx));
        int platform = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform);
        int cityId = PlatformUtil.isMT(platform) ? ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.mtCityId) : ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.dpCityId);
        deviceInfoDTO.setCityId((long) cityId);
        String appVersion = ParamsUtil.getStringSafely(ctx.getParameters(), ShelfActivityConstants.Params.appVersion);
        deviceInfoDTO.setAppVersion(appVersion);
        request.setDeviceInfo(deviceInfoDTO);
        return request;
    }

    private String getSubBizScenario(ActivityCxt ctx) {
        ShopM shopM = ctx.getParam(ShelfActivityConstants.Ctx.ctxShop);
        if (shopM == null) {
            return null;
        }
        int shopType = shopM.getShopType();
        if(shopType == 50) {
            return "BeauCard";
        } else if(shopType == 30){
            return "SpaCard";
        } else if(shopType == 75){
            return "EduCard";
        }
        return null;
    }

    private String getLocation(ActivityCxt ctx) {
        double lat = ParamsUtil.getDoubleSafely(ctx.getParameters(), ShelfActivityConstants.Params.lat);
        double lng = ParamsUtil.getDoubleSafely(ctx.getParameters(), ShelfActivityConstants.Params.lng);
        return lat+"_"+lng;
    }

    private String getApp(ActivityCxt ctx) {
        int clientType = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.userAgent);
        if(VCClientTypeEnum.MT_APP.getCode() == clientType){
            return "group";
        } else if(VCClientTypeEnum.MT_XCX.getCode() == clientType){
            return "weixin";
        } else if(VCClientTypeEnum.DP_APP.getCode() == clientType){
            return "dianping-nova";
        }
        return null;
    }

    /**
     * 用户是否否支持先用后付
     * @param ctx
     * @return
     */
    public static boolean getUserExposure(ActivityCxt ctx)  {
        ContextHandlerResult contextHandlerResult = ctx.getSource(ContextHandlerAbility.CODE);
        if(contextHandlerResult == null){
            return false;
        }
        return ExposureEnum.EXPOSED.getCode().equals(contextHandlerResult.getExposure());
    }
}
