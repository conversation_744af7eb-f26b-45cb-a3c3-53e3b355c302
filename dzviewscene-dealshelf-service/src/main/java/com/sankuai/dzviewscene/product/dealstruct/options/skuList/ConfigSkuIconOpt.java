package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuIconVP;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

@VPointOption(name = "取配置的项目icon变化点", description = "取配置的项目icon变化点，每种类型的项目对应一个icon", code = ConfigSkuIconOpt.CODE)
public class ConfigSkuIconOpt extends SkuIconVP<ConfigSkuIconOpt.Config> {

    public static final String CODE = "ConfigSkuIconOpt";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (validateParams(param, config)) {
            return config.getDefaultIcon();
        }
        String icon = config.getIcons().get(param.getSkuItemDto().getProductCategory());
        return StringUtils.isEmpty(icon) ? config.getDefaultIcon() : icon;
    }

    private boolean validateParams(Param param, Config config) {
        return config == null ||
                MapUtils.isEmpty(config.getIcons()) ||
                param.getSkuItemDto() == null ||
                !config.getIcons().containsKey(param.getSkuItemDto().getProductCategory());
    }

    @Data
    @VPointCfg
    public static class Config {
        /**
         * 项目分类2Icon
         */
        private Map<Long, String> icons;
        /**
         * 默认Icon
         */
        private String defaultIcon;
    }
}
