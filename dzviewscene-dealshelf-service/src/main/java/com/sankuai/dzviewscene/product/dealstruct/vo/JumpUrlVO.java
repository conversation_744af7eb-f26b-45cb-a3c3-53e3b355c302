package com.sankuai.dzviewscene.product.dealstruct.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/18 1:18 下午
 */
@Data
@MobileDo(id = 0x4700)
public class JumpUrlVO implements Serializable {

    /**
     * 跳转链接展示icon
     */
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    /**
     * 跳转链接
     */
    @MobileDo.MobileField(key = 0xc56e)
    private String url;

    /**
     * 跳转模块展示内容
     */
    @MobileDo.MobileField(key = 0xcce)
    private String content;

    /**
     * 跳转模块名称
     */
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;
}
