package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/1 3:11 下午
 */
@VPointOption(name = "教育-运动培训-球类运动课程内容构造DealAttrVO列表变化点", description = "教育-运动培训-球类运动课程内容构造DealAttrVO列表变化点",code = EduSportBallCourseContentAttrVOListOpt.CODE, isDefault = false)
public class EduSportBallCourseContentAttrVOListOpt extends DealAttrVOListVP<EduSportBallCourseContentAttrVOListOpt.Config> {

    public static final String CODE = "EduSportBallCourseContentAttrVOListOpt";

    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        //获取课程内容
        List<String> courseContents = getCourseContents(param);
        //构造DealDetailStructAttrModuleGroupModel列表
        return buildDealDetailStructAttrModuleGroupModels("课程内容", courseContents);
    }

    private DealDetailStructAttrModuleVO buildDealDetailStructAttrModuleVO(List<String> courseContents) {
        if (CollectionUtils.isEmpty(courseContents)) {
            return null;
        }
        DealDetailStructAttrModuleVO dealDetailStructAttrModuleVO = new DealDetailStructAttrModuleVO();
        dealDetailStructAttrModuleVO.setAttrValues(courseContents);
        return dealDetailStructAttrModuleVO;
    }

    private List<DealDetailStructAttrModuleGroupModel> buildDealDetailStructAttrModuleGroupModels(String groupName, List<String> courseContents) {
        DealDetailStructAttrModuleVO dealDetailStructAttrModuleVOS = buildDealDetailStructAttrModuleVO(courseContents);
        if (dealDetailStructAttrModuleVOS == null) {
            return null;
        }
        DealDetailStructAttrModuleGroupModel dealDetailStructAttrModuleGroupModel = new DealDetailStructAttrModuleGroupModel();
        dealDetailStructAttrModuleGroupModel.setGroupName(groupName);
        dealDetailStructAttrModuleGroupModel.setDealDetailStructAttrModuleVOS(Lists.newArrayList(dealDetailStructAttrModuleVOS));
        return Lists.newArrayList(dealDetailStructAttrModuleGroupModel);
    }

    /**
     * 获取课程内容列表
     *@param
     *@return
     */
    private List<String> getCourseContents(Param param) {
        String courseContentAttrStr = getSkuCourseContentAttrStr(param);
        if (StringUtils.isEmpty(courseContentAttrStr)) {
            return new ArrayList<>();
        }
        List<CourseContentModel> courseContentModels = JsonCodec.converseList(courseContentAttrStr, CourseContentModel.class);
        if (CollectionUtils.isEmpty(courseContentModels)) {
            return new ArrayList<>();
        }
        return courseContentModels.stream().map(model -> model.getEducontent()).collect(Collectors.toList());
    }

    /**
     * 获取该sku课程内容属性值
     *@param
     *@return
     */
    private String getSkuCourseContentAttrStr(Param param) {
        List<SkuAttrItemDto> skuAttrItemDtos = DealDetailUtils.getFirstMustGroupFirstSkuAttrList(param.getDealDetailDtoModel());
        return DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItemDtos, "eduserviceitemtable");
    }

    @Data
    private static class CourseContentModel {
        private String educontent;
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
