package com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.DealDetailSkuGroupsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.product.dealstruct.model.SkuAttrModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * created by z<PERSON><PERSON>yuan04 in 2022/1/4
 */
@VPoint(name = "Sku元素属性构造能力", description = "构造Sku元素属性信息", code = SkuItemAttrVP.CODE, ability = DealDetailSkuGroupsBuilder.CODE)
public abstract class SkuItemAttrVP<C> extends PmfVPoint<List<SkuAttrModel>, SkuItemAttrVP.Param, C> {

    public static final String CODE = "SkuItemAttrVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {

        /**
         * 是否必选
         */
        private boolean isMust;

        /**
         * 分组标题
         */
        private String setTitle;

        /**
         * Sku信息
         */
        private SkuItemDto skuItemDto;

        /**
         * Sku category信息
         */
        private List<ProductSkuCategoryModel> productCategories;

        /**
         * 团单属性信息
         */
        private List<AttrM> dealAttrs;

        /**
         * 斗斛命中信息
         */
        private List<DouhuResultModel> douhuResultModels;
    }
}
