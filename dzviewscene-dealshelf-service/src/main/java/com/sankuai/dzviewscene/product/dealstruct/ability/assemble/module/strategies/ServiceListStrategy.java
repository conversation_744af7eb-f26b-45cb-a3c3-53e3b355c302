package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.DealDetailSkuProductsGroupsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * created by z<PERSON><PERSON>yuan04 in 2021/12/14
 */
@Deprecated
@Component("serviceList")
public class ServiceListStrategy implements ModuleStrategy {

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        String abilityCode = DealDetailSkuProductsGroupsBuilder.CODE;
        List<List<DealSkuGroupModuleVO>> dealSkusGroupsList = activityCxt.getSource(abilityCode);
        List<DealSkuGroupModuleVO> dealSkusGroups = CollectUtils.firstValue(dealSkusGroupsList);
        if (CollectionUtils.isEmpty(dealSkusGroups)) {
            return null;
        }
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        dealDetailModuleVO.setSkuGroupsModel1(dealSkusGroups);
        return dealDetailModuleVO;
    }

    @Data
    @VPointCfg
    public static class Config {
        private String styleType;
    }
}
