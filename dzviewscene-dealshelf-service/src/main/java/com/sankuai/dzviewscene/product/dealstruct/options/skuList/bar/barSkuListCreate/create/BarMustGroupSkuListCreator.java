package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.AbstractBarSkuListCreator;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 酒吧必选服务项目组sku列表构造器
 * <AUTHOR>
 * @date 2023/2/9 2:05 下午
 */
public class BarMustGroupSkuListCreator extends AbstractBarSkuListCreator {

    @Override
    public boolean ideantify(boolean isMustGroupSku, List<SkuItemDto> skuList, BarDealDetailSkuListModuleOpt.Config config) {
        return isMustGroupSku;
    }

    @Override
    public List<DealSkuGroupModuleVO> buildSkuListModules(List<SkuItemDto> skuItemDtos, BarDealDetailSkuListModuleOpt.Config config, int optionalnum, boolean hitDouHu) {
        //获取必享酒水sku源数据
        List<SkuItemDto> drinksCateSkus = extractSpecialCatesSkus(skuItemDtos, config.getDrinksSkuCateIds());
        //获取必享餐食sku源数据
        List<SkuItemDto> mealsCateSkus = extractSpecialCatesSkus(skuItemDtos, config.getMealsSkuCateIds());
        //获取必享入场券sku源数据
        List<SkuItemDto> entranceTicket = extractSpecialCatesSkus(skuItemDtos, Lists.newArrayList(4050L));
        List<DealSkuGroupModuleVO> result = new ArrayList<>();
        //添加必享酒水sku模型
        addSkuListModule(drinksCateSkus, hitDouHu ? null : String.format(config.getMustDrinksSkusGroupNameFormat(), getDrinksQuantities(drinksCateSkus)), result, config, true, hitDouHu);
        //添加必享餐食sku模型
        addSkuListModule(mealsCateSkus, hitDouHu ? null : String.format(config.getMustMealsSkusGroupNameFormat(), getMealsQuantities(mealsCateSkus)), result, config, true, hitDouHu);
        //添加入场券
        addSkuListModule(entranceTicket, null, result, config, true, hitDouHu);
        return result;
    }

    /**
     * 获取酒水餐食数量
     *@param
     *@return
     */
    private String getMealsQuantities(List<SkuItemDto> mealsCateSkus) {
        if (CollectionUtils.isEmpty(mealsCateSkus)) {
            return StringUtils.EMPTY;
        }
        int allNumSum = mealsCateSkus.stream()
                .map(sku -> getNumGivenNumStrAndCopies(DealDetailUtils.getSkuAttrValueBySkuAttrName(sku.getAttrItems(), "unit"), sku.getCopies()))
                .reduce(Integer::sum)
                .orElse(0);
        if (allNumSum == 0) {
            return StringUtils.EMPTY;
        }
        return String.valueOf(allNumSum);
    }

    /**
     * 获取酒水展示数量
     *@param
     *@return
     */
    private String getDrinksQuantities(List<SkuItemDto> drinksCateSkus) {
        if (CollectionUtils.isEmpty(drinksCateSkus)) {
            return StringUtils.EMPTY;
        }
        //获取数量值：所有  sku的数量属性quantityAvailable值 和 份数 乘积  之和
        int quantitiesSum = drinksCateSkus.stream()
                .map(sku -> getNumGivenNumStrAndCopies(DealDetailUtils.getSkuAttrValueBySkuAttrName(sku.getAttrItems(), "quantityAvailable"), sku.getCopies()))
                .reduce(Integer::sum)
                .orElse(0);
        if (quantitiesSum == 0) {
            return StringUtils.EMPTY;
        }
        //获取单位：所有sku的单位属性quantityUnit值一样时取该值作为单位，不一样时单位为 "份"，当所有sku的单位都为null时展示畅饮
        List<String> units = drinksCateSkus.stream()
                .map(sku -> DealDetailUtils.getSkuAttrValueBySkuAttrName(sku.getAttrItems(), "quantityUnit"))
                .filter(unit -> StringUtils.isNotEmpty(unit))
                .distinct()
                .collect(Collectors.toList());
        if (units.size() == 0) {
            return "畅饮";
        }
        if (units.size() == 1) {
            return String.valueOf(quantitiesSum) + CollectUtils.firstValue(units);
        }
        return String.valueOf(quantitiesSum) + "份";
    }

    private int getNumGivenNumStrAndCopies(String numStr, int copies) {
        int num = NumberUtils.objToInt(numStr);
        if (num == -1) {
            num = 1;
        }
        return num * copies;
    }

    /**
     * 提取指定品类的sku列表
     *@param skuCateIds 所需的sku品类id列表
     *@return
     */
    private List<SkuItemDto> extractSpecialCatesSkus(List<SkuItemDto> skuItemDtos, List<Long> skuCateIds) {
        if (CollectionUtils.isEmpty(skuItemDtos)) {
            return new ArrayList<>();
        }
        return skuItemDtos.stream().filter(skuItemDto -> skuCateIds.contains(skuItemDto.getProductCategory())).collect(Collectors.toList());
    }

}
