package com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.DealDetailSkuListsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Builder;
import lombok.Data;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/6/8 6:02 下午
 */
@VPoint(name = "sku列表组列表后置处理", description = "sku列表组列表后置处理",code = SkuListGroupsAfterProcessingVP.CODE, ability = DealDetailSkuListsBuilder.CODE)
public abstract class SkuListGroupsAfterProcessingVP<T> extends PmfVPoint<List<DealSkuGroupModuleVO>, SkuListGroupsAfterProcessingVP.Param, T> {

    public static final String CODE = "SkuListGroupsAfterProcessingVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private List<DealSkuGroupModuleVO> skuGroups;
        private List<AttrM> dealAttrs;
        private List<ProductSkuCategoryModel> productCategories;
    }
}
