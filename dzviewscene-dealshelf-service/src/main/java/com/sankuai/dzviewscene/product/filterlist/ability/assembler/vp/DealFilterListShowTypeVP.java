package com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.DealListResponseAssembler;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/11 19:21
 */
@VPoint(name = "生成showType", description = "生成showType", code = DealFilterListShowTypeVP.CODE, ability = DealListResponseAssembler.CODE)
public abstract class DealFilterListShowTypeVP<T> extends PmfVPoint<Integer, DealFilterListShowTypeVP.Param, T> {
    public static final String CODE = "DealFilterListShowTypeVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private List<DouHuM> douHuList;
    }
}
