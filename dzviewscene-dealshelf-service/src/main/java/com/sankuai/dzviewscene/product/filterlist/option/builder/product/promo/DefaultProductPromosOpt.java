package com.sankuai.dzviewscene.product.filterlist.option.builder.product.promo;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPromosVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzPromoVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/14
 */
@VPointOption(name = "默认-返回立减优惠详情",
        description = "",
        code = "DefaultProductPromosOpt",
        isDefault = true)
public class DefaultProductPromosOpt extends ProductPromosVP<Void> {
    @Override
    public List<DzPromoVO> compute(ActivityCxt context, Param param, Void unused) {
        return getDefPromo(param);
    }
}
