package com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.DealDetailSkuGroupsBuilder;
import lombok.Builder;
import lombok.Data;

/**
 * created by zhangzhiyuan04 in 2021/12/31
 */
@VPoint(name = "Sku总标题", description = "Sku模块的整体标题，一般为空", code = SkuUniTitleVP.CODE, ability = DealDetailSkuGroupsBuilder.CODE)
public abstract class SkuUniTitleVP<T> extends PmfVPoint<String, SkuUniTitleVP.Param, T> {

    public static final String CODE = "SkuUniTitleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {

    }
}
