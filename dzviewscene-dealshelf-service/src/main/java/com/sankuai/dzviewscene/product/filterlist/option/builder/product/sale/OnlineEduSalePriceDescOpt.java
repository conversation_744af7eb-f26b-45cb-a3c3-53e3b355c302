package com.sankuai.dzviewscene.product.filterlist.option.builder.product.sale;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePriceDescVP;
import lombok.Data;

@VPointOption(
        name = "在线教育团购价格描述标签",
        description = "在线教育团购",
        code = "OnlineEduSalePriceDescOpt")
public class OnlineEduSalePriceDescOpt extends ProductSalePriceDescVP<OnlineEduSalePriceDescOpt.Config> {

    public static final String EDU_SALE_PRICE_REMARK = "dealEduSalePriceRemarkAttr";

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Config config) {
        return param.getProductM().getAttr(EDU_SALE_PRICE_REMARK);
    }

    @VPointCfg
    @Data
    public static class Config {
    }

}
