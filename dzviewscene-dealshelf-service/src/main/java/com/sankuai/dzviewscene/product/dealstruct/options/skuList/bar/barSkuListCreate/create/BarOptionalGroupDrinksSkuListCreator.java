package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.AbstractBarSkuListCreator;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 酒吧可选项目组酒水sku列表构造器
 * <AUTHOR>
 * @date 2023/2/9 2:08 下午
 */
public class BarOptionalGroupDrinksSkuListCreator extends AbstractBarSkuListCreator {

    @Override
    public boolean ideantify(boolean isMustGroupSku, List<SkuItemDto> skuList, BarDealDetailSkuListModuleOpt.Config config) {
        if (isMustGroupSku || CollectionUtils.isEmpty(skuList) || CollectionUtils.isEmpty(config.getDrinksSkuCateIds())) {
            return false;
        }
        return skuList.stream().filter(sku -> !config.getDrinksSkuCateIds().contains(sku.getProductCategory())).collect(Collectors.toList()).size() == 0;
    }

    @Override
    public List<DealSkuGroupModuleVO> buildSkuListModules(List<SkuItemDto> skuItemDtos, BarDealDetailSkuListModuleOpt.Config config, int optionalNum, boolean hitDouHu) {
        List<DealSkuGroupModuleVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuItemDtos)) {
            return result;
        }
        String groupName = hitDouHu && StringUtils.isNotEmpty(config.getOptionalFormat()) ? String.format(config.getOptionalFormat(), skuItemDtos.size(), optionalNum) : getDrinkGroupName(skuItemDtos, optionalNum, config);
        addSkuListModule(skuItemDtos, groupName, result, config, true, hitDouHu);
        return result;
    }

    private String getDrinkGroupName(List<SkuItemDto> skuItemDtos, int optionalNum, BarDealDetailSkuListModuleOpt.Config config) {
        if (CollectionUtils.isEmpty(skuItemDtos)) {
            return null;
        }
        //提取所有服务项目的单位列表
        List<String> allUnitList = skuItemDtos.stream()
                .map(sku -> DealDetailUtils.getSkuAttrValueBySkuAttrName(sku.getAttrItems(), "quantityUnit"))
                .filter(unit -> StringUtils.isNotEmpty(unit))
                .distinct()
                .collect(Collectors.toList());
        //提取所有服务项目的数量
        List<Integer> allNumList = skuItemDtos.stream()
                .map(sku -> {
                    int num = NumberUtils.objToInt(DealDetailUtils.getSkuAttrValueBySkuAttrName(sku.getAttrItems(), "quantityAvailable"));
                    if (num == -1) {
                        num = 1;
                    }
                    return sku.getCopies() * num;
                })
                .distinct()
                .collect(Collectors.toList());
        if (allUnitList.size() == 0) {
            return String.format(config.getOptionalDrinksSkusWithSameNumGroupNameFormat(), "畅饮", StringUtils.EMPTY, skuItemDtos.size(), optionalNum);
        }
        if (allUnitList.size() == 1 && allNumList.size() == 1) {
            return String.format(config.getOptionalDrinksSkusWithSameNumGroupNameFormat(), CollectUtils.firstValue(allNumList),  CollectUtils.firstValue(allUnitList), skuItemDtos.size(), optionalNum);
        }
        return String.format(config.getOptionalDrinksSkusGroupNameFormat(), skuItemDtos.size(), optionalNum);
    }
}
