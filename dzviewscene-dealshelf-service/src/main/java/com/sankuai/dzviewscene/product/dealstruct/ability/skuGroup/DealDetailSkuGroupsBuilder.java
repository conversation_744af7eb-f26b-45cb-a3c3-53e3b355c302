package com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup;

import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.OptionalSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.douhu.DealDetailDouhuFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints.*;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.*;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.apache.commons.collections.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * created by zhangzhiyuan04 in 2021/12/30
 */
@Ability(code = DealDetailSkuGroupsBuilder.CODE,
        name = "团购详情模块sku货组构造能力",
        description = "团购详情模块sku货组构造能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE,
                DealDetailDouhuFetcher.CODE
        }
)
public class DealDetailSkuGroupsBuilder extends PmfAbility<List<DealDetailSkuUniModel>, DealDetailSkuGroupsParam, DealDetailSkuGroupsCfg> {

    public static final String CODE = "dealDetailSkuGroupsBuilder";

    @Override
    public CompletableFuture<List<DealDetailSkuUniModel>> build(ActivityCxt activityCxt, DealDetailSkuGroupsParam dealDetailSkuGroupsParam, DealDetailSkuGroupsCfg dealDetailSkuGroupsCfg) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        List<DouhuResultModel> douhuResultModels = activityCxt.getSource(DealDetailDouhuFetcher.CODE);
        if (CollectionUtils.isEmpty(dealDetailInfoModels)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        List<DealDetailSkuUniModel> collect = dealDetailInfoModels.stream().map(detailModel -> {
            //有效性校验
            if (!isValidDealDetail(detailModel)) {
                return null;
            }
            return buildSkuUniModel(activityCxt, detailModel.getDealDetailDtoModel().getSkuUniStructuredDto(), detailModel.getProductCategories(), detailModel.getDealAttrs(), douhuResultModels);
        }).filter(model -> model != null).collect(Collectors.toList());
        return CompletableFuture.completedFuture(collect);
    }

    private DealDetailSkuUniModel buildSkuUniModel(ActivityCxt activityCxt, DealDetailSkuUniStructuredDto structuredDto, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs, List<DouhuResultModel> douhuResultModels) {
        DealDetailSkuUniModel dealDetailSkuUniModel = new DealDetailSkuUniModel();
        SkuUniTitleVP<?> skuUniTitleVP = findVPoint(activityCxt, SkuUniTitleVP.CODE);
        //Sku模块标题
        String skuUniTitle = skuUniTitleVP.execute(activityCxt, SkuUniTitleVP.Param.builder().build());
        dealDetailSkuUniModel.setTitle(skuUniTitle);
        List<MustSkuItemsGroupDto> mustGroups = structuredDto.getMustGroups();
        if (CollectionUtils.isNotEmpty(mustGroups)) {
            List<DealDetailSkuGroupModel> mustItems = mustGroups.stream().map(mustSkuItemsGroupDto -> buildMustSkuGroupModel(activityCxt, mustSkuItemsGroupDto, productCategories, dealAttrs, douhuResultModels)).filter(item -> item != null).collect(Collectors.toList());
            dealDetailSkuUniModel.setMustGroups(mustItems);
        }
        List<OptionalSkuItemsGroupDto> optionalGroups = structuredDto.getOptionalGroups();
        if (CollectionUtils.isNotEmpty(optionalGroups)) {
            List<DealDetailSkuGroupModel> optionalItems = optionalGroups.stream().map(optionalSkuItemsGroupDto -> buildOptionalSkuGroupModel(activityCxt, optionalSkuItemsGroupDto, productCategories, dealAttrs, douhuResultModels)).filter(item -> item != null).collect(Collectors.toList());
            dealDetailSkuUniModel.setOptionGroups(optionalItems);
        }
        return dealDetailSkuUniModel;
    }

    private DealDetailSkuGroupModel buildOptionalSkuGroupModel(ActivityCxt activityCxt,
                                                               OptionalSkuItemsGroupDto optionalSkuItemsGroupDto, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs, List<DouhuResultModel> douhuResultModels) {
        int sum = 0;
        List<SkuItemDto> skuItems = optionalSkuItemsGroupDto.getSkuItems();
        if (CollectionUtils.isNotEmpty(skuItems)) {
            sum = skuItems.size();
        }
        SkuGroupTitleVP<?> skuGroupTitleVP = findVPoint(activityCxt, SkuGroupTitleVP.CODE);
        String title = skuGroupTitleVP.execute(activityCxt,
                SkuGroupTitleVP.Param.builder().
                        isMust(false)
                        .sum(sum)
                        .optionalCount(optionalSkuItemsGroupDto.getOptionalCount())
                        .build());
        List<DealDetailSkuSetModel> dealDetailSkuSets = buildSkuSets(activityCxt, false, skuItems, productCategories, dealAttrs, douhuResultModels);
        DealDetailSkuGroupModel dealDetailSkuGroupModel = new DealDetailSkuGroupModel();
        dealDetailSkuGroupModel.setTitle(title);
        dealDetailSkuGroupModel.setSkuSetModels(dealDetailSkuSets);
        return dealDetailSkuGroupModel;
    }

    private DealDetailSkuGroupModel buildMustSkuGroupModel(ActivityCxt activityCxt,
                                                           MustSkuItemsGroupDto mustSkuItemsGroupDto, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs, List<DouhuResultModel> douhuResultModels) {
        int sum = 0;
        List<SkuItemDto> skuItems = mustSkuItemsGroupDto.getSkuItems();
        if (CollectionUtils.isNotEmpty(skuItems)) {
            sum = skuItems.size();
        }
        SkuGroupTitleVP<?> skuGroupTitleVP = findVPoint(activityCxt, SkuGroupTitleVP.CODE);
        String title = skuGroupTitleVP.execute(activityCxt,
                SkuGroupTitleVP.Param.builder().
                        isMust(true)
                        .sum(sum)
                        .optionalCount(sum)
                        .build());
        List<DealDetailSkuSetModel> dealDetailSkuSets = buildSkuSets(activityCxt, true, skuItems, productCategories, dealAttrs, douhuResultModels);
        DealDetailSkuGroupModel dealDetailSkuGroupModel = new DealDetailSkuGroupModel();
        dealDetailSkuGroupModel.setTitle(title);
        dealDetailSkuGroupModel.setSkuSetModels(dealDetailSkuSets);
        return dealDetailSkuGroupModel;
    }

    private List<DealDetailSkuSetModel> buildSkuSets(ActivityCxt activityCxt,
                                                     boolean isMust,
                                                     List<SkuItemDto> skuItems, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs, List<DouhuResultModel> douhuResultModels) {
        SkuSetVP<?> skuSetVP = findVPoint(activityCxt, SkuSetVP.CODE);
        List<DealDetailSkuSetModel> skuSets = skuSetVP.execute(activityCxt, SkuSetVP.Param.builder().isMust(isMust).skuItems(skuItems).productCategories(productCategories).build());
        if (CollectionUtils.isEmpty(skuSets)) {
            return skuSets;
        }
        for (DealDetailSkuSetModel setModel : skuSets) {
            if (CollectionUtils.isEmpty(setModel.getSkuItems())) {
                continue;
            }
            List<SkuItemModel> skuItemList =
                    processAndBuildSkuItems(activityCxt, isMust, setModel.getTitle(), setModel.getSkuItems(), productCategories, dealAttrs, douhuResultModels);
            setModel.setSkuItems(skuItemList);
        }
        //todo 补充skuSets的优先级
        return skuSets;
    }

    private List<SkuItemModel> processAndBuildSkuItems(ActivityCxt activityCxt,
                                                       boolean isMust,
                                                       String title,
                                                       List<SkuItemModel> skuItemList,
                                                       List<ProductSkuCategoryModel> productCategories,
                                                       List<AttrM> dealAttrs,
                                                       List<DouhuResultModel> douhuResultModels) {
        if (CollectionUtils.isEmpty(skuItemList)) {
            return Lists.newArrayList();
        }
        List<SkuItemModel> collect = skuItemList.stream()
                .map(skuItem -> processAndBuildSkuItem(activityCxt, skuItem, isMust, title, productCategories, dealAttrs, douhuResultModels)) //加工
                .filter(item -> item != null) // 过滤
                .sorted(Comparator.comparingInt(SkuItemModel::getPriority)) // 排序
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(collect)) {
            return Lists.newArrayList();
        }
        return collect;
    }

    private SkuItemModel processAndBuildSkuItem(ActivityCxt activityCxt,
                                                SkuItemModel skuItemModel,
                                                boolean isMust,
                                                String setTitle,
                                                List<ProductSkuCategoryModel> productCategories,
                                                List<AttrM> dealAttrs,
                                                List<DouhuResultModel> douhuResultModels) {
        if (skuItemModel == null) {
            return null;
        }
        //标题
        SkuItemTitleVP<?> skuItemTitleVP = findVPoint(activityCxt, SkuItemTitleVP.CODE);
        String title = skuItemTitleVP.execute(activityCxt, SkuItemTitleVP.Param.builder().isMust(isMust).setTitle(setTitle).skuItemDto(skuItemModel.getSkuItemDto()).productCategories(productCategories).build());
        skuItemModel.setName(title);

        //价格
        SkuItemPriceVP<?> skuItemPriceVP = findVPoint(activityCxt, SkuItemPriceVP.CODE);
        String price = skuItemPriceVP.execute(activityCxt, SkuItemPriceVP.Param.builder().isMust(isMust).setTitle(setTitle).skuItemDto(skuItemModel.getSkuItemDto()).build());
        skuItemModel.setPrice(price);

        //份数
        SkuItemCopiesVP<?> skuItemCopiesVP = findVPoint(activityCxt, SkuItemCopiesVP.CODE);
        String copies = skuItemCopiesVP.execute(activityCxt, SkuItemCopiesVP.Param.builder().isMust(isMust).setTitle(setTitle).skuItemDto(skuItemModel.getSkuItemDto()).build());
        skuItemModel.setCopies(copies);

        //优先级
        SkuItemPriorityVP<?> skuItemPriorityVP = findVPoint(activityCxt, SkuItemPriorityVP.CODE);
        Integer priority = skuItemPriorityVP.execute(activityCxt, SkuItemPriorityVP.Param.builder().isMust(isMust).setTitle(setTitle).skuItemDto(skuItemModel.getSkuItemDto()).productCategories(productCategories).build());
        skuItemModel.setPriority(priority == null ? Integer.MAX_VALUE : priority.intValue());

        //属性
        SkuItemAttrVP<?> skuItemAttrVP = findVPoint(activityCxt, SkuItemAttrVP.CODE);
        List<SkuAttrModel> skuAttrs = skuItemAttrVP.execute(activityCxt, SkuItemAttrVP.Param.builder().isMust(isMust).setTitle(setTitle).skuItemDto(skuItemModel.getSkuItemDto()).productCategories(productCategories).dealAttrs(dealAttrs).douhuResultModels(douhuResultModels).build());
        skuItemModel.setAttrItems(skuAttrs);

        //过滤
        SkuItemFilterVP<?> skuItemFilterVP = findVPoint(activityCxt, SkuItemFilterVP.CODE);
        Boolean filter = skuItemFilterVP.execute(activityCxt, SkuItemFilterVP.Param.builder().isMust(isMust).setTitle(setTitle).skuItemModel(skuItemModel).productCategories(productCategories).build());
        if (filter != null && !filter) {
            return null;
        }
        return skuItemModel;
    }

    private boolean isValidDealDetail(DealDetailInfoModel detailModel) {
        if(detailModel == null || detailModel.getDealDetailDtoModel() == null || detailModel.getDealDetailDtoModel().getSkuUniStructuredDto() == null) {
            return false;
        }
        return true;
    }

}
