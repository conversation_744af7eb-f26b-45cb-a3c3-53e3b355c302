package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bath.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/5/28 11:48 AM
 */
@Getter
@AllArgsConstructor
public enum TeamBuildFacilitiesEnum {

    SERVICE0("正餐服务", "diningServiceCheck", "https://p0.meituan.net/ingee/09c075a5a5dbed1cbd582c36adea36551189.png"),
    SERVICE1("饮品小食", "beverageSnacksCheck", "https://p0.meituan.net/ingee/9df1f4dd42bd78ab872be4dbdff34af1755.png"),
    SERVICE2("摄影摄像", "photographyVideographyCheck", "https://p0.meituan.net/ingee/1265175aee931d434e6dd3c1490b1948952.png"),
    SERVICE3("主题策划", "themePlanningCheck", "https://p0.meituan.net/ingee/97fd4e43f0db50607e3fea99c8d2b188590.png"),
    SERVICE4("服务设施", "serviceFacilitiesCheck", "https://p0.meituan.net/ingee/81b90c0b8ba0110be4e6b8c8be90b9471601.png"),
    SERVICE5("服务人员", "serviceStaffCheck", "https://p0.meituan.net/ingee/8826534486ae790beecf3d229382682a837.png"),
    SERVICE6("妆造", "makeupCheck", "https://p0.meituan.net/ingee/c8e1c0c401010b82e08118b85a2bacfb882.png"),
    SERVICE7("独立空间", "independentSpaceCheck", "https://p0.meituan.net/ingee/2ad865d8dfc40dda07109f760bdf9e3d517.png"),
    SERVICE8("便利服务", "convenienceServiceCheck", "https://p0.meituan.net/ingee/ffe09979469a66dc591d3e69b58a7555777.png"),
    SERVICE9("装备", "equipmentCheck", "https://p0.meituan.net/ingee/54cc30cd454c1eb3e7a4048969a33132679.png")
    ;

    public static TeamBuildFacilitiesEnum getEnumByServiceName(String name) {
        return Arrays.stream(TeamBuildFacilitiesEnum.values()).filter(value -> value.getName().equals(name)).findFirst().orElse(null);
    }
    /**
     * 服务分类中文名
     */
    private final String name;

    /**
     * 查询关联的具体服务标签（多个）
     */
    private final String detailKey;

    /**
     * 免费设施icon
     */
    private final String icon;

}
