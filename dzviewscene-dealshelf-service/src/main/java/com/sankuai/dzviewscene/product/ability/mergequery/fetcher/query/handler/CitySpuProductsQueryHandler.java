package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.meituan.mpproduct.general.trade.api.request.CitySpuIdQueryRequest;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.QueryConstants;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductIdM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 标品城市维度召回
 */
@Component
public class CitySpuProductsQueryHandler implements QueryHandler {

    /**
     * 一口价类标品的id类型（资源类型）
     */
    private static final int COMMON_SPU_ID_TYPE = 16;

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<ProductGroupM> query(ActivityCxt ctx, String groupName, Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler.CitySpuProductsQueryHandler.query(com.sankuai.athena.viewscene.framework.ActivityCxt,java.lang.String,java.util.Map)");
        List<Long> productIdBlackList = JsonCodec.converseList(ParamsUtil.getStringSafely(params, QueryConstants.Params.productIdBlackList), Long.class);
        return compositeAtomService.querySpuIdsByCity(buildRequest(ctx, params)).thenApply(spuIds -> {
            ProductGroupM productGroupM = new ProductGroupM();
            productGroupM.setProducts(buildProducts(groupName, spuIds, productIdBlackList));
            return productGroupM;
        });
    }

    private List<ProductM> buildProducts(String groupName, List<Long> spuIds, List<Long> productIdBlackList) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler.CitySpuProductsQueryHandler.buildProducts(java.lang.String,java.util.List,java.util.List)");
        if (CollectionUtils.isEmpty(spuIds)) {
            return Lists.newArrayList();
        }
        return spuIds.stream().distinct()
                .filter(id -> CollectionUtils.isEmpty(productIdBlackList) || !productIdBlackList.contains(id))
                .map(spuId -> {
                    ProductM productM = new ProductM();
                    productM.setGroupName(groupName);
                    productM.setId(new ProductIdM(spuId, COMMON_SPU_ID_TYPE));
                    return productM;
                }).collect(Collectors.toList());
    }

    private CitySpuIdQueryRequest buildRequest(ActivityCxt ctx, Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler.CitySpuProductsQueryHandler.buildRequest(com.sankuai.athena.viewscene.framework.ActivityCxt,java.util.Map)");
        CitySpuIdQueryRequest citySpuIdQueryRequest = new CitySpuIdQueryRequest();
        citySpuIdQueryRequest.setCityId(ParamsUtil.getIntSafely(ctx, PmfConstants.Params.mtCityId));
        citySpuIdQueryRequest.setCategoryId(ParamsUtil.getLongSafely(params, QueryConstants.Params.secondCategoryId));
        return citySpuIdQueryRequest;
    }
}
