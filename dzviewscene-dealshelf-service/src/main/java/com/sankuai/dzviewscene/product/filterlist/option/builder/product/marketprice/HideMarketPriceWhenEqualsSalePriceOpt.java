package com.sankuai.dzviewscene.product.filterlist.option.builder.product.marketprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductMarketPriceVP;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExtAdapter;

@VPointOption(name = "划线价=售价时隐藏划线价",
        description = "",
        code = "HideMarketPriceWhenEqualsSalePriceOpt")
public class HideMarketPriceWhenEqualsSalePriceOpt extends ProductMarketPriceVP<Void> {
    @Override
    public String compute(ActivityCxt activityCxt, Param param, Void unused) {
        // 没有立减
        if (param.getProductM().getPromo(PromoTypeEnum.DIRECT_PROMO.getType()) == null) {
            return FloorsBuilderExtAdapter.EMPTY_VALUE;
        }
        return param.getProductM().getMarketPrice();
    }
}
