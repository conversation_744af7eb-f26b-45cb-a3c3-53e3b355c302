package com.sankuai.dzviewscene.product.dealstruct.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/14 8:32 下午
 */
@MobileDo(id = 0xc871)
public class CommonAttrVO implements Serializable {
    /**
     * 属性列表
     */
    @MobileDo.MobileField(key = 0x4ee8)
    private List<CommonAttrsVO> commonAttrs;

    /**
     * 属性值
     */
    @MobileDo.MobileField(key = 0x97dd)
    private String value;

    /**
     * 属性名称
     */
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;

    /**
     * item附带图片
     */
    @MobileDo.MobileField(key = 0xb18b)
    private String pic;

    public List<CommonAttrsVO> getCommonAttrs() {
        return commonAttrs;
    }

    public void setCommonAttrs(List<CommonAttrsVO> commonAttrs) {
        this.commonAttrs = commonAttrs;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }
}
