package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuPriorityVP;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/8 5:29 下午
 */
@VPointOption(name = "sku优先级默认变化点", description = "sku优先级默认变化点，支持按照sku类别配置优先级",code = DefaultSkuPriorityOpt.CODE, isDefault = true)
public class DefaultSkuPriorityOpt extends SkuPriorityVP<DefaultSkuPriorityOpt.Config> {

    public static final String CODE = "DefaultSkuPriorityOpt";

    @Override
    public Integer compute(ActivityCxt context, Param param, Config config) {
        if (MapUtils.isEmpty(config.getSkuCategory2PriorityMap()) || CollectionUtils.isEmpty(param.getProductCategories()) || param.getSkuItemDto() == null) {
            return Integer.MAX_VALUE;
        }
        long skuCategoryId = param.getSkuItemDto().getProductCategory();
        ProductSkuCategoryModel skuCategory = param.getProductCategories().stream().filter(category -> category.getProductCategoryId() != null && category.getProductCategoryId() == skuCategoryId).findFirst().orElse(null);
        if (skuCategory == null || !config.getSkuCategory2PriorityMap().containsKey(skuCategory.getCnName())) {
            return Integer.MAX_VALUE;
        }
        return config.getSkuCategory2PriorityMap().get(skuCategory.getCnName());
    }

    @Data
    @VPointCfg
    public static class Config {
        private Map<String, Integer> skuCategory2PriorityMap;
    }
}
