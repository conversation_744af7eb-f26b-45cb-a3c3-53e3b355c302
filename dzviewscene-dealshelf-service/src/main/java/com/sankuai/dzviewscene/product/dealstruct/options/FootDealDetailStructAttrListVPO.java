package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dp.arts.utils.recycler.Recycler;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.struct.vpoints.DealDetailStructAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrItemModel;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrsModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/27 9:17 下午
 */
@VPointOption(name = "足疗团购详情结构化属性卡片变化点", description = "足疗团购详情结构化属性卡片变化点",code = FootDealDetailStructAttrListVPO.CODE, isDefault = false)
public class FootDealDetailStructAttrListVPO extends DealDetailStructAttrListVP<FootDealDetailStructAttrListVPO.Config> {

    public static final String CODE = "FootDealDetailStructAttrListVPO";

    private static final String SERVICE_EFFICACY_ATTR_NAME = "service_efficacy";

    private static final String FOOD_CONTENT_ARRAY_SKU_ATTR_NAME = "foodContentArray";

    @Override
    public List<StructAttrsModel> compute(ActivityCxt context, Param param, Config config) {
        StructAttrsModel extraService = getExtraService(param.getDealDetailDtoModel(), config);
        StructAttrsModel serviceMethod = getServiceMethodAttr(param.getDealAttrs());
        List<StructAttrsModel> structAttrsModels = new ArrayList<>();
        if (extraService != null) {
            structAttrsModels.add(extraService);
        }
        if (serviceMethod != null) {
            structAttrsModels.add(serviceMethod);
        }
        if (CollectionUtils.isEmpty(structAttrsModels)) {
            return null;
        }
        return structAttrsModels;
     }

    private StructAttrsModel getExtraService(DealDetailDtoModel dealDetailDtoModel, Config config) {
        List<SkuItemDto> skuItemDtos = getSkuItemDtosFromMustGroup(dealDetailDtoModel);
        if (CollectionUtils.isEmpty(skuItemDtos)) {
            //在策略部分是按照配置的数组index从StructAttrsModel数组中获取某个StructAttrsModel转换成前端VO的，如果某个index位置的StructAttrsModel为空，要填充空实例，以免按照index获取的实例错位
            return new StructAttrsModel();
        }
        SkuItemDto skuItemDto = skuItemDtos.stream().filter(item -> CollectionUtils.isNotEmpty(config.getExtraServiceSkuCategoryIds()) && config.getExtraServiceSkuCategoryIds().contains(item.getProductCategory())).findFirst().orElse(null);
        return convertSkuItemDto2StructAttrsModel(skuItemDto, config);
    }

    private List<SkuItemDto> getSkuItemDtosFromMustGroup(DealDetailDtoModel dealDetailDtoModel) {
        if (dealDetailDtoModel == null || dealDetailDtoModel.getSkuUniStructuredDto() == null) {
            return null;
        }
        List<MustSkuItemsGroupDto> mustGroups = dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups();
        if (CollectionUtils.isEmpty(mustGroups)) {
            return null;
        }
        return mustGroups.stream().flatMap(group -> group.getSkuItems().stream()).collect(Collectors.toList());
    }

     private StructAttrsModel getServiceMethodAttr(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
         List<String> serviceEfficacyAttrValues = DealDetailUtils.getAttrValueByAttrName(dealAttrs, SERVICE_EFFICACY_ATTR_NAME);
        if (CollectionUtils.isEmpty(serviceEfficacyAttrValues)) {
            return null;
        }
        StructAttrItemModel structAttrItemModel = new StructAttrItemModel();
        //structAttrItemModel.setAttrName(SERVICE_EFFICACY_ATTR_TITLE);
        structAttrItemModel.setAttrValues(serviceEfficacyAttrValues);
        StructAttrsModel structAttrsModel = new StructAttrsModel();
        structAttrsModel.setStructAttrModels(Lists.newArrayList(structAttrItemModel));
        return structAttrsModel;
     }

     private StructAttrsModel convertSkuItemDto2StructAttrsModel(SkuItemDto skuItemDto, Config config) {
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            //在策略部分是按照配置的数组index从StructAttrsModel数组中获取某个StructAttrsModel转换成前端VO的，如果某个index位置的StructAttrsModel为空，要填充空实例，以免按照index获取的实例错位
            return new StructAttrsModel();
        }
        List<StructAttrItemModel> structAttrItemModels = convertSkuAttrItem2StructAttrItemModels(skuItemDto.getAttrItems(), config);
        StructAttrsModel structAttrsModel = new StructAttrsModel();
        structAttrsModel.setStructAttrModels(structAttrItemModels);
        return structAttrsModel;
     }

     private List<StructAttrItemModel> convertSkuAttrItem2StructAttrItemModels(List<SkuAttrItemDto> attrItems, Config config) {
         String foodContent = DealDetailUtils.getSkuAttrValueBySkuAttrName( attrItems, FOOD_CONTENT_ARRAY_SKU_ATTR_NAME);
         List<MealContent> mealContents = JsonCodec.converseList(foodContent, MealContent.class);
         if (CollectionUtils.isEmpty(mealContents)) {
             return null;
         }
         return mealContents.stream().map(content -> convertMealContent2StructAttrItemModel(content, config)).collect(Collectors.toList());
     }

     private StructAttrItemModel convertMealContent2StructAttrItemModel(MealContent mealContent, Config config) {
        if (mealContent == null || CollectionUtils.isEmpty(mealContent.getMealListArray())) {
            return null;
        }
        StructAttrItemModel structAttrItemModel = new StructAttrItemModel();
        structAttrItemModel.setIcon(getExtraServiceAttrIcon(mealContent, config));
        structAttrItemModel.setAttrName(getExtraServiceAttrName(mealContent, config));
        List<String> values = getExtraServiceAttrValue(mealContent, config);
        structAttrItemModel.setAttrValues(values);
        return structAttrItemModel;
     }

    private String getExtraServiceAttrIcon(MealContent mealContent, Config config) {
        if (config == null || MapUtils.isEmpty(config.getFoodType2IconMap())) {
            return null;
        }
        return config.getFoodType2IconMap().get(mealContent.getFoodType());
    }

     private List<String> getExtraServiceAttrValue(MealContent mealContent, Config config) {
         if (mealContent == null || CollectionUtils.isEmpty(mealContent.getMealListArray()) || config == null) {
             return null;
         }
         return mealContent.getMealListArray().stream().map(meal -> String.format(config.getMealNameCountJoinFormat(), meal.getMealName(), meal.getMealCount())).collect(Collectors.toList());
     }

     private String getExtraServiceAttrName(MealContent mealContent, Config config) {
         if (mealContent == null || StringUtils.isEmpty(mealContent.getUsageRules()) || config == null) {
             return null;
         }
         if (mealContent.getUsageRules().equals(config.getProvideAllUsageRules())) {
             return String.format(config.getProvideAllUsageRulesFormat(), mealContent.getFoodType());
         }
         if (CollectionUtils.isEmpty(mealContent.getMealListArray())) {
             return null;
         }
         if (mealContent.getUsageRules().equals(config.getProvideOneUsageRules())) {
             String mealChineseNum = getMealChineseNum(mealContent.getMealListArray().size(), config);
             return String.format(config.getProvideOneUsageRulesFormat(), mealContent.getFoodType(), mealChineseNum);
         }
         return null;
     }

     private String getMealChineseNum(int mealNum, Config config) {
        if (config == null || MapUtils.isEmpty(config.getNunber2ChineseNumberMap())) {
            return Integer.toString(mealNum);
        }
        String mealChineseNum = config.getNunber2ChineseNumberMap().get(mealNum);
        if (StringUtils.isEmpty(mealChineseNum)) {
            return Integer.toString(mealNum);
        }
        return mealChineseNum;
     }

    @Data
    @VPointCfg
    public static class Config {
        private Map<String, String> foodType2IconMap;
        private Map<Integer, String> nunber2ChineseNumberMap;
        private List<Long> extraServiceSkuCategoryIds;
        private String provideAllUsageRules;
        private String provideAllUsageRulesFormat;
        private String provideOneUsageRules;
        private String provideOneUsageRulesFormat;
        private String mealNameCountJoinFormat;
    }

    @Data
    private static class MealContent {
        private String usageRules;
        private String foodType;
        private List<Meal> mealListArray;
    }

    @Data
    private static class Meal {
        private String mealCount;
        private String mealName;
    }
}
