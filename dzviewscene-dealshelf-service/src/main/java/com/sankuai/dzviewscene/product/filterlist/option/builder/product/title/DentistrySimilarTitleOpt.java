package com.sankuai.dzviewscene.product.filterlist.option.builder.product.title;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTitleVP;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealDetailSkuUniStructuredModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealDetailStructuredModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealSkuAttrModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealStructModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.TagM;
import joptsimple.internal.Strings;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@VPointOption(name = "医疗齿科相似团购名称展示",
        description = "医疗齿科相似团购名称展示",
        code = "DentistrySimilarTitleOpt")
public class DentistrySimilarTitleOpt extends ProductTitleVP<DentistrySimilarTitleOpt.Config> {

    private static final String SERVICE_TYPE = "service_type";

    private static final String DEAL_STRUCT_CONTENT_ATTR_NAME = "dealStructContent";

    private static final String CATEGORY = "category";

    private static final String SANDBLASTING = "sandblasting";

    private static final String SUIT_CROWD = "suitCrowds";

    private static final String QUANTITY_UNIT = "quantityUnit";

    private static final String BRAND_NAME = "brandName";


    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        ProductM productM = param.getProductM();
        if (ObjectUtils.isEmpty(productM)) {
            return Strings.EMPTY;
        }
        switch (productM.getAttr(SERVICE_TYPE)) {
            case "洗牙":
                return getWashTitle(productM);
            case "涂氟":
                return getFluorideTitle(productM);
            case "窝沟封闭":
                return getVaultTitle(productM);
            case "种植牙":
                return getImplantTitle(productM);
            default:
                return Strings.EMPTY;
        }
    }

    private String getImplantTitle(ProductM productM) {
        List<String> titleItems = new ArrayList<>();
        titleItems.add(getSkuAttrValue(productM, BRAND_NAME));
        titleItems.add("种植牙");
        return titleItems.stream().filter(v -> !ObjectUtils.isEmpty(v))
                .collect(Collectors.joining(""));
    }

    private String getVaultTitle(ProductM productM) {
        List<String> titleItems = new ArrayList<>();
        titleItems.add(getSkuAttrValue(productM, QUANTITY_UNIT));
        titleItems.add("窝沟封闭");
        return titleItems.stream().filter(v -> !ObjectUtils.isEmpty(v))
                .collect(Collectors.joining(""));
    }

    private String getFluorideTitle(ProductM productM) {
        List<String> titleItems = new ArrayList<>();
        titleItems.add(getAvailableTime(productM));
        titleItems.add(getSkuAttrValue(productM, SUIT_CROWD));
        titleItems.add("涂氟");
        return titleItems.stream().filter(v -> !ObjectUtils.isEmpty(v))
                .collect(Collectors.joining(""));
    }

    private String getWashTitle(ProductM productM) {
        String suitCrowds = getSkuAttrValue(productM, SUIT_CROWD);
        if (!ObjectUtils.isEmpty(suitCrowds) && suitCrowds.contains("成人")) {
            List<String> titleItems = new ArrayList<>();
            titleItems.add(getAvailableTime(productM));
            titleItems.add(getCategory(productM));
            titleItems.add(getSkuAttrValue(productM, SANDBLASTING));
            return titleItems.stream().filter(v -> !ObjectUtils.isEmpty(v))
                    .collect(Collectors.joining(""));
        }
        if (!ObjectUtils.isEmpty(suitCrowds) && suitCrowds.contains("儿童")) {
            List<String> titleItems = new ArrayList<>();
            titleItems.add(getAvailableTime(productM));
            titleItems.add("儿童洗牙");
            return titleItems.stream().filter(v -> !ObjectUtils.isEmpty(v))
                    .collect(Collectors.joining(""));
        }
        return Strings.EMPTY;
    }

    private String getCategory(ProductM productM) {
        String category = getSkuAttrValue(productM, CATEGORY);
        if (ObjectUtils.isEmpty(category)) {
            return Strings.EMPTY;
        }
        return category.replaceAll("洗牙", "");
    }

    private String getSkuAttrValue(ProductM productM, String attrName) {
        DealStructModel dealStructModel = getDealStructModel(productM.getAttr(DEAL_STRUCT_CONTENT_ATTR_NAME));
        return Optional.ofNullable(dealStructModel).map(DealStructModel::getDealDetailStructuredData)
                .map(DealDetailStructuredModel::getDealDetailSkuUniStructuredModel)
                .map(DealDetailSkuUniStructuredModel::getMustGroups)
                .orElse(org.apache.commons.compress.utils.Lists.newArrayList())
                .stream()
                .filter(group -> !ObjectUtils.isEmpty(group) && !ObjectUtils.isEmpty(group.getSkus()))
                .flatMap(mustSkus -> mustSkus.getSkus().stream())
                .filter(sku -> !ObjectUtils.isEmpty(sku) && !ObjectUtils.isEmpty(sku.getSkuAttrs()))
                .flatMap(sku -> sku.getSkuAttrs().stream())
                .filter(skuAttr -> !ObjectUtils.isEmpty(skuAttr) && Objects.equals(attrName, skuAttr.getAttrName()))
                .map(DealSkuAttrModel::getAttrValue)
                .findFirst()
                .orElse(Strings.EMPTY);
    }

    private DealStructModel getDealStructModel(String dealDetail) {
        if (ObjectUtils.isEmpty(dealDetail)) {
            return null;
        }
        DealStructModel dealStructModel = JsonCodec.decode(dealDetail, DealStructModel.class);
        if (dealStructModel == null || StringUtils.isEmpty(dealStructModel.getStract())) {
            return null;
        }
        return dealStructModel;
    }

    private String getAvailableTime(ProductM productM) {
        List<String> tagIdList = Optional.ofNullable(productM.getProductTagList()).orElse(Lists.newArrayList())
                .stream().filter(tag -> !ObjectUtils.isEmpty(tag) && !ObjectUtils.isEmpty(tag.getId()))
                .map(TagM::getId).collect(Collectors.toList());
        if (tagIdList.contains("100212529")) {
            return "周末";
        }
        if (tagIdList.contains("100234243")) {
            return "工作日";
        }
        if (tagIdList.contains("100213506")) {
            return "通用";
        }
        return Strings.EMPTY;
    }

    @VPointCfg
    @Data
    public static class Config {

    }
}
