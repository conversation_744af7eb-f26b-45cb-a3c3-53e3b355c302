package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.edu.OpenClassTime;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 教育的SKU相关工具类
 *
 * @auther: liweilong06
 * @date: 2024/5/14 11:31 上午
 */
public class EduSkuUtils {

    public static final String ATTR_OPEN_CLASS_TYPE = "class_start_time";
    public static final String ATTR_OPEN_CLASS_TIME = "class_start_time_detail";
    public static final String OPEN_CLASS_TYPE_FROM_JSON = "指定日期";
    public static final String OPEN_CLASS_MULTI_APPEND = "，多期可选";
    public static final String OPEN_CLASS_SPLIT = "、";
    public static final String OPEN_CLASS_ITEM_TITLE = "开班时间";
    public static final String KE_CHENG_KAI_KE_SHI_JIAN_2 = "kechengkaikeshijian2";
    public static final String TARGET_CERTIFICATE = "目标证书";
    public static final String ATTR_SUBJECT1 = "subject1";
    public static final String ATTR_SUBJECT2 = "subject2";
    public static final String ATTR_SUBJECT3 = "subject3";
    public static final String STUDY_SUBJECT = "学习科目";
    public static final String ATTR_COURSE_EFFECT = "goal";
    public static final String COURSE_EFFECT = "课程效果";
    public static final String ATTR_COURSE_TIME_DETAIL = "course_time_detail";
    public static final String ATTR_CLASS_ARRANGEMENT = "class_arrangement";
    public static final String ATTR_CLASS_ARRANGEMENT_DETAIL = "class_arrangement_detail";
    public static final String CLASS_SCHEDULE = "课程安排";
    public static final String CLASS_TIME_TEMPLATE = "每课时%s分";
    public static final String CLASS_SCHEDULE_TEMPLATE = "每周%s节课";
    private static final String CERTIFICATION = "证书";
    private static final String COMMA = "，";


    /**
     * 以dealDetailInfoModel构建开班时间SKU
     *
     * @param dealDetailInfoModel
     * @return
     */
    public static DealSkuItemVO buildOpenClassTimeSkuItem(DealDetailInfoModel dealDetailInfoModel) {
        if (dealDetailInfoModel == null) {
            return null;
        }
        String openClassTime = readOpenClassTime(dealDetailInfoModel);
        if (StringUtils.isBlank(openClassTime)) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(OPEN_CLASS_ITEM_TITLE);
        dealSkuItemVO.setValue(openClassTime);
        return dealSkuItemVO;
    }

    private static String readOpenClassTime(DealDetailInfoModel dealDetailInfoModel) {
        String openClassType = DealDetailUtils.getAttrSingleValueByAttrName(dealDetailInfoModel.getDealAttrs(), ATTR_OPEN_CLASS_TYPE);
        if (StringUtils.isBlank(openClassType)) {
            return readOpenClassTimeForNightSchool(dealDetailInfoModel);
        }
        if (!OPEN_CLASS_TYPE_FROM_JSON.equals(openClassType)) {
            return openClassType;
        }
        String openClassTimeJson = DealDetailUtils.getAttrSingleValueByAttrName(dealDetailInfoModel.getDealAttrs(), ATTR_OPEN_CLASS_TIME);
        if (StringUtils.isBlank(openClassTimeJson)) {
            return null;
        }
        List<String> timeList = JsonCodec.decode(openClassTimeJson, new TypeReference<List<String>>() {
        });
        if (CollectionUtils.isEmpty(timeList)) {
            return null;
        }
        String appendStr = timeList.size() > 1 ? OPEN_CLASS_MULTI_APPEND : "";
        return String.join(OPEN_CLASS_SPLIT, timeList) + appendStr;
    }

    private static String readOpenClassTimeForNightSchool(DealDetailInfoModel dealDetailInfoModel) {
        String openClassTimeJson = DealDetailUtils.getFirstSkuAttrModelValueByAttrName(dealDetailInfoModel.getStandardServiceProjectDTO(),
                dealDetailInfoModel.getDealDetailDtoModel(), KE_CHENG_KAI_KE_SHI_JIAN_2);
        if (StringUtils.isBlank(openClassTimeJson)) {
            return null;
        }
        List<OpenClassTime> openTimes = JsonCodec.converseList(openClassTimeJson, OpenClassTime.class);
        if (CollectionUtils.isEmpty(openTimes)) {
            return null;
        }
        List<String> openTimeStrList = new ArrayList<>();
        for (OpenClassTime openTime : openTimes) {
            if (CollectionUtils.isEmpty(openTime.getWeek())) {
                continue;
            }
            for (String week : openTime.getWeek()) {
                openTimeStrList.add(week + openTime.getTime());
            }
        }
        return String.join(OPEN_CLASS_SPLIT, openTimeStrList);
    }

    public static DealSkuItemVO buildTargetCertificate(List<AttrM> dealAttrs) {
        //当subject1=“证书”展示，否则不展示
        String subject1 = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, ATTR_SUBJECT1);
        if (!CERTIFICATION.equals(subject1)) {
            return null;
        }
        DealSkuItemVO skuItemVO = new DealSkuItemVO();
        skuItemVO.setName(TARGET_CERTIFICATE);
        skuItemVO.setValue(DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, ATTR_SUBJECT2));
        return skuItemVO;
    }


    public static DealSkuItemVO buildSubject(List<AttrM> dealAttrs) {
        List<String> subject3 = DealDetailUtils.parseAttrValueByAttrName(dealAttrs, ATTR_SUBJECT3);
        if (CollectionUtils.isEmpty(subject3)) {
            return null;
        }
        DealSkuItemVO skuItemVO = new DealSkuItemVO();
        skuItemVO.setName(STUDY_SUBJECT);
        skuItemVO.setValue(String.join(",", subject3));
        return skuItemVO;
    }

    public static DealSkuItemVO buildCourseEffect(List<AttrM> dealAttrs) {
        String value = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, ATTR_COURSE_EFFECT);
        if (StringUtils.isBlank(value)) {
            return null;
        }
        DealSkuItemVO skuItemVO = new DealSkuItemVO();
        skuItemVO.setName(COURSE_EFFECT);
        skuItemVO.setValue(value);
        return skuItemVO;
    }

    public static DealSkuItemVO buildClassSchedule(List<AttrM> dealAttrs) {
        String classSchedule = doBuildClassSchedule(dealAttrs);
        if (StringUtils.isBlank(classSchedule)) {
            return null;
        }
        DealSkuItemVO skuItemVO = new DealSkuItemVO();
        skuItemVO.setName(CLASS_SCHEDULE);
        skuItemVO.setValue(classSchedule);
        return skuItemVO;
    }

    private static String doBuildClassSchedule(List<AttrM> dealAttrs) {
        String courseTimeDetail = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, ATTR_COURSE_TIME_DETAIL);
        String classArrangementDetail = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, ATTR_CLASS_ARRANGEMENT_DETAIL);
        String classArrangement = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, ATTR_CLASS_ARRANGEMENT);

        return Stream.of(
                        buildClassSchedulePart(courseTimeDetail, CLASS_TIME_TEMPLATE),
                        buildClassSchedulePart(classArrangementDetail, CLASS_SCHEDULE_TEMPLATE),
                        classArrangementDetail == null ? classArrangement : null
                ).filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(COMMA));


    }

    private static String buildClassSchedulePart(String detail, String template) {
        return StringUtils.isNotBlank(detail) ? String.format(template, detail) : null;
    }
}
