package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.OptionalSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/8/9 01:21
 */
public class DealSkuListModuleUtils {

    /**
     * 构建过夜服务模块
     *
     * @param dealAttrs
     * @return
     */
    public static DealDetailSkuListModuleGroupModel buildOverNightSkuListModule(List<AttrM> dealAttrs) {
        try {
            String dealOverNightRule = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "dealOverNightRule");
            if (StringUtils.isEmpty(dealOverNightRule)) {
                return null;
            }
            Map<String, String> overNightRuleMap = JSON.parseObject(dealOverNightRule, new TypeReference<HashMap<String, String>>() {
            });
            if (!overNightRuleMap.containsKey("identityKey") || !overNightRuleMap.get("identityKey").equals("overNightService")) {
                return null;
            }
            if (!overNightRuleMap.containsKey("amount") || !overNightRuleMap.containsKey("serviceTitle") || !overNightRuleMap.containsKey("originalPrice")
                    || !overNightRuleMap.containsKey("free") || !overNightRuleMap.containsKey("rule")) {
                return null;
            }
            String price = Boolean.parseBoolean(overNightRuleMap.get("free")) ? "¥0" : "¥" + overNightRuleMap.get("amount");
            return buildDealDetailSkuListModuleGroupModel("付费服务(可选)", null, Lists.newArrayList(
                    buildDealSkuVO("过夜服务", price,
                            Objects.nonNull(buildDealSkuItemVO(overNightRuleMap.get("rule"))) ? Lists.newArrayList(buildDealSkuItemVO(overNightRuleMap.get("rule"))) : null,
                            null, overNightRuleMap.get("originalPrice"), null)));
        } catch (Exception e) {
            Cat.logError(e);
        }
        return null;
    }

    public static DealSkuItemVO buildDealSkuItemVO(String itemName) {
        if(StringUtils.isBlank(itemName)){
            return null;
        }
        DealSkuItemVO itemVO = new DealSkuItemVO();
        itemVO.setName(itemName);
        return itemVO;
    }

    public static DealDetailSkuListModuleGroupModel buildDealDetailSkuListModuleGroupModel(String groupName, String title, List<DealSkuVO> dealSkuList) {
        if (CollectionUtils.isEmpty(dealSkuList)) {
            return null;
        }
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(dealSkuList);
        dealSkuGroupModuleVO.setTitle(title);
        DealDetailSkuListModuleGroupModel dealDetailSkuListModuleGroupModel = new DealDetailSkuListModuleGroupModel();
        dealDetailSkuListModuleGroupModel.setGroupName(groupName);
        dealDetailSkuListModuleGroupModel.setDealSkuGroupModuleVOS(Lists.newArrayList(dealSkuGroupModuleVO));
        return dealDetailSkuListModuleGroupModel;
    }

    public static DealSkuVO buildDealSkuVO(String title, String price, List<DealSkuItemVO> dealSkuItemVO, String icon, String originalPrice, String desc) {
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(title);
        dealSkuVO.setPrice(price);
        dealSkuVO.setIcon(icon);
        dealSkuVO.setItems(dealSkuItemVO);
        dealSkuVO.setOriginalPrice(originalPrice);
        dealSkuVO.setDesc(desc);
        return dealSkuVO;
    }

    /**
     * 取出全部可享的服务项目
     */
    @Deprecated
    public static List<SkuItemDto> extractMustSkuItemList(DealDetailInfoModel dealDetailInfoModel) {
        if (dealDetailInfoModel == null || dealDetailInfoModel.getDealDetailDtoModel() == null || dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto() == null
                || CollectionUtils.isEmpty(dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto().getMustGroups())) {
            return null;
        }
        List<MustSkuItemsGroupDto> mustGroups = dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto().getMustGroups();
        List<SkuItemDto> result = Lists.newArrayList();
        for (MustSkuItemsGroupDto mustGroup : mustGroups) {
            if (mustGroup != null && CollectionUtils.isNotEmpty(mustGroup.getSkuItems())) {
                result.addAll(mustGroup.getSkuItems());
            }
        }
        return result;
    }

    /**
     * 取出全部可享的服务项目，使用新查询中心接口（太极）
     */
    public static List<StandardServiceProjectItemDTO> standardExtractMustSkuItemList(DealDetailInfoModel dealDetailInfoModel) {
        if (dealDetailInfoModel == null || dealDetailInfoModel.getStandardServiceProjectDTO() == null
                || CollectionUtils.isEmpty(dealDetailInfoModel.getStandardServiceProjectDTO().getMustGroups())) {
            return null;
        }
        List<StandardServiceProjectGroupDTO> mustGroups = dealDetailInfoModel.getStandardServiceProjectDTO().getMustGroups();
        List<StandardServiceProjectItemDTO> result = Lists.newArrayList();
        for (StandardServiceProjectGroupDTO mustGroup : mustGroups) {
            if (mustGroup != null && CollectionUtils.isNotEmpty(mustGroup.getServiceProjectItems())) {
                result.addAll(mustGroup.getServiceProjectItems());
            }
        }
        return result;
    }

    /**
     * 取出m选n的可选服务项目集合
     */
    @Deprecated
    public static List<OptionalSkuItemsGroupDto> extractOptionalSkuItemsGroupList(DealDetailInfoModel dealDetailInfoModel) {
        if (dealDetailInfoModel == null || dealDetailInfoModel.getDealDetailDtoModel() == null || dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto() == null) {
            return null;
        }
        List<OptionalSkuItemsGroupDto> optionalSkuGroupList = dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto().getOptionalGroups();
        if (CollectionUtils.isEmpty(optionalSkuGroupList)) {
            return null;
        }

        return optionalSkuGroupList;
    }

    public static List<StandardServiceProjectGroupDTO> standardExtractOptionalSkuItemsGroupList(DealDetailInfoModel dealDetailInfoModel) {
        return Optional.ofNullable(dealDetailInfoModel)
                .map(DealDetailInfoModel::getStandardServiceProjectDTO)
                .map(StandardServiceProjectDTO::getOptionalGroups)
                .filter(CollectionUtils::isNotEmpty)
                .orElse(null);
    }
}
