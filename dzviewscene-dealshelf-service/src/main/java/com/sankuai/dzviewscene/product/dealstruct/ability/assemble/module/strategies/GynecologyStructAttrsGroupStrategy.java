package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component("gynecologyStructAttrsGroupStrategy")
public class GynecologyStructAttrsGroupStrategy implements ModuleStrategy {


    private static final String INSPECTION_INSTRUCTIONS = "inspectionInstructionsArray";

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);

        List<AttrM> dealAttrs = Optional.ofNullable(dealDetailInfoModels)
                .flatMap(list -> list.stream().findFirst())
                .map(DealDetailInfoModel::getDealAttrs)
                .orElse(Collections.emptyList());

        String attrValue = dealAttrs.stream()
                .filter(dealAttr -> dealAttr != null && config.equals(dealAttr.getName()))
                .map(AttrM::getValue)
                .findFirst()
                .orElse(null);
        if (StringUtils.isBlank(attrValue)) {
            return null;
        }
        List<String> list = buildContentLists(config, attrValue);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        String result = String.join("\n", list);
        return buildDealDetailModuleVO(result);
    }


    private List<String> buildContentLists(String config, String attrValue) {
        if (INSPECTION_INSTRUCTIONS.equals(config)) {
            List<Map> contentValueList = JsonCodec.converseList(attrValue, Map.class);
            return contentValueList.stream()
                    .map(map -> (String) map.get("content"))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } else {
            return Arrays.asList(attrValue.split("[,、\\-.，]"));
        }
    }

    private DealDetailModuleVO buildDealDetailModuleVO(String result) {
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();

        dealDetailModuleVO.setDescModel(result);

        return dealDetailModuleVO;
    }

}
