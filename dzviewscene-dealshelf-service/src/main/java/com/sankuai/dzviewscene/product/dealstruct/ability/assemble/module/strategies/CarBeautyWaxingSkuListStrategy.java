package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.DealDetailSkuGroupsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuSetModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuUniModel;
import com.sankuai.dzviewscene.product.dealstruct.model.SkuItemModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/14 10:14 下午
 */
@Component("carBeautyWaxingSkuListStrategy")
public class CarBeautyWaxingSkuListStrategy implements ModuleStrategy  {

    private static final String SEPERATOR = "、";

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        //1. 获取需要的依赖数据
        List<DealDetailSkuUniModel> dealSkusGroupsList = activityCxt.getSource(DealDetailSkuGroupsBuilder.CODE);
            //1.1 依赖数据 - 基础服务内容
        List<String>  baseServiceContents = extractBaseServiceContents(dealSkusGroupsList);
            //1.2 依赖数据 - 相同的份数
        String identicalCopies = extractIdenticalCopies(dealSkusGroupsList);
        //2. 组装VOList
        List<DealSkuVO> dealSkuVOS = convertBaseServiceContents2DealSkuVOs(baseServiceContents, identicalCopies, config);
        //3. 组装为结果VO
        return buildDealDetailModuleVO(dealSkuVOS);
    }

    private DealDetailModuleVO buildDealDetailModuleVO(List<DealSkuVO> dealSkuVOS) {
        DealSkuGroupModuleVO dealSkuGroupModuleVO = buildDealSkuGroupModuleVO(dealSkuVOS);
        if (dealSkuGroupModuleVO == null) {
            return null;
        }
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        dealDetailModuleVO.setSkuGroupsModel1(Lists.newArrayList(dealSkuGroupModuleVO));
        return dealDetailModuleVO;
    }

    private DealSkuGroupModuleVO buildDealSkuGroupModuleVO(List<DealSkuVO> dealSkuVOS) {
        if (CollectionUtils.isEmpty(dealSkuVOS)) {
            return null;
        }
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(dealSkuVOS);
        return dealSkuGroupModuleVO;
    }

    private List<DealSkuVO> convertBaseServiceContents2DealSkuVOs(List<String>  baseServiceContents, String copies, String config) {
        if (CollectionUtils.isEmpty(baseServiceContents)) {
            return null;
        }
        ConfigModel configModel = JsonCodec.decode(config, ConfigModel.class);
        Map<String, String> baseServiceContent2IconMap = JsonCodec.converseMap(config, String.class, String.class);
        return baseServiceContents.stream().map(title -> buildDealSkuVO(title, copies, configModel)).collect(Collectors.toList());
    }

    private DealSkuVO buildDealSkuVO(String title, String copies, ConfigModel configModel) {
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(title);
        dealSkuVO.setCopies(copies);
        if (configModel == null) {
            return dealSkuVO;
        }
        if (MapUtils.isEmpty(configModel.getBaseServiceContent2IconMap()) || !configModel.getBaseServiceContent2IconMap().containsKey(title)) {
            dealSkuVO.setIcon(configModel.getDefaultIcon());
            return dealSkuVO;
        }
        dealSkuVO.setIcon(configModel.getBaseServiceContent2IconMap().get(title));
        return dealSkuVO;
    }

    private String extractIdenticalCopies(List<DealDetailSkuUniModel> dealSkusGroupsList) {
        SkuItemModel skuItemModel = getFirstSkuItemModel(dealSkusGroupsList);
        if (skuItemModel == null || StringUtils.isEmpty(skuItemModel.getName())) {
            return null;
        }
        return skuItemModel.getCopies();
    }

    private List<String> extractBaseServiceContents(List<DealDetailSkuUniModel> dealSkusGroupsList) {
        SkuItemModel skuItemModel = getFirstSkuItemModel(dealSkusGroupsList);
        if (skuItemModel == null || StringUtils.isEmpty(skuItemModel.getName())) {
            return null;
        }
        return Lists.newArrayList(skuItemModel.getName().split(SEPERATOR));
    }

    private SkuItemModel getFirstSkuItemModel(List<DealDetailSkuUniModel> dealSkusGroupsList) {
        DealDetailSkuUniModel dealDetailSkuUniModel = CollectUtils.firstValue(dealSkusGroupsList);
        if (dealDetailSkuUniModel == null) {
            return null;
        }
        DealDetailSkuGroupModel dealDetailSkuGroupModel = CollectUtils.firstValue(dealDetailSkuUniModel.getMustGroups());
        if (dealDetailSkuGroupModel == null) {
            return null;
        }
        DealDetailSkuSetModel dealDetailSkuSetModel = CollectUtils.firstValue(dealDetailSkuGroupModel.getSkuSetModels());
        if (dealDetailSkuSetModel == null) {
            return null;
        }
        return CollectUtils.firstValue(dealDetailSkuSetModel.getSkuItems());
    }

    @Data
    private static class ConfigModel {
        private Map<String, String> baseServiceContent2IconMap;
        private String defaultIcon;
    }
}
