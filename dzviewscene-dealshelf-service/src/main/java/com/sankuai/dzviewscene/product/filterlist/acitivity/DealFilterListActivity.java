package com.sankuai.dzviewscene.product.filterlist.acitivity;

import com.sankuai.athena.viewscene.framework.annotation.Activity;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfActivity;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterProductListVO;

/**
 * <AUTHOR>
 * @date 2022/4/29
 */
@Activity(name = "团购筛选列表活动",
        code = DealFilterListActivity.CODE,
        description = "团购筛选列表活动",
        basePackage = "com.sankuai.dzviewscene.product")
public class DealFilterListActivity extends PmfActivity<DzFilterProductListVO> {

    public static final String CODE = "deal_filter_list_activity";
    /**
     * 展位列表
     */
    public interface SpaceKey {

        /**
         * 团详页列表模块
         */
        String DEAL_DETAIL_LIST = "deal_detail_list";

        /**
         * 商详团购货架落地页
         */
        String SHOP_DEAL_SHELF_LANDING = "shop_deal_shelf_landing";

        /**
         * 默认团购筛选列表页
         */
        String DEFAULT_DEAL_FILTER_LIST = "default_deal_filter_list";

        /**
         * 旗舰店货架
         */
        String FLAGSHIP_STORE_SHELF = "flagship_store_shelf";
    }
}
