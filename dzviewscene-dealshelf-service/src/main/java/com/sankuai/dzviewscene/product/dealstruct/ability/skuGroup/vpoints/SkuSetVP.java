package com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.DealDetailSkuGroupsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuSetModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * created by z<PERSON><PERSON><PERSON>04 in 2022/1/4
 */
@VPoint(name = "Sku二级分组", description = "主要负责构造二级分组的分类逻辑，并构造各组的标题", code = SkuSetVP.CODE, ability = DealDetailSkuGroupsBuilder.CODE)
public abstract class SkuSetVP<C> extends PmfVPoint<List<DealDetailSkuSetModel>, SkuSetVP.Param, C> {

    public static final String CODE = "SkuSetVP";

    @Override
    public List<DealDetailSkuSetModel> compute(ActivityCxt activityCxt, Param param, C c) {
        return null;
    }

    @Data
    @Builder
    @VPointParam
    public static class Param {
        /**
         * 是否必选
         */
        private boolean isMust;

        /**
         * Sku信息
         */
        private List<SkuItemDto> skuItems;

        /**
         * Sku category信息
         */
        private List<ProductSkuCategoryModel> productCategories;
    }
}
