package com.sankuai.dzviewscene.product.dealstruct.options.desc;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.desc.vpoints.DealDetailDescVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@VPointOption(name = "配置化的团购详情补充描述组件，直接取attr里的值",
        description = "配置化的团购详情补充描述组件，直接取attr里的值",
        code = DealDetailDescFromAttrOpt.CODE)
public class DealDetailDescFromAttrOpt extends DealDetailDescVP<DealDetailDescFromAttrOpt.Config> {

    public static final String CODE = "dealDetailDescFromAttrOpt";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (param == null || param.getDealDetailInfoModel() == null || StringUtils.isEmpty(config.getAttrKey())) {
            return null;
        }
        return param.getDealDetailInfoModel().getAttrValueFromAllAttrs(config.getAttrKey());
    }

    @Data
    @VPointCfg
    public static class Config {
        private String attrKey;
    }
}
