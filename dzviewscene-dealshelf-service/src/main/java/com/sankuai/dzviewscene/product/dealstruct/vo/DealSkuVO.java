package com.sankuai.dzviewscene.product.dealstruct.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/25 4:22 下午
 */
@MobileDo(id = 0x2d3a)
public class DealSkuVO implements Serializable {

    /**
     * 副标题
     */
    @MobileDo.MobileField(key = 0xd894)
    private String subTitle;

    /**
     * 弹窗
     */
    @MobileDo.MobileField(key = 0x76f2)
    private PopUpWindowVO popup;

    /**
     * 描述信息
     */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
     * sku属性列表
     */
    @MobileDo.MobileField(key = 0xe23d)
    private List<DealSkuItemVO> items;

    /**
     * sku份数
     */
    @MobileDo.MobileField(key = 0xd993)
    private String copies;

    /**
     * sku价格
     */
    @MobileDo.MobileField(key = 0xb716)
    private String price;

    /**
     *
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * icon
     */
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    /**
     * 商品原始价格
     */
    @MobileDo.MobileField(key = 0xdfc4)
    private String originalPrice;

    /**
     * 商品标签
     */
    @MobileDo.MobileField(key = 0xbf9b)
    private DealItemTag tag;

    /**
     * 跳转链接
     */
    @MobileDo.MobileField(key = 0x774e)
    private JumpUrlVO jumpUrl;

    @MobileDo.MobileField(key = 0x8f0c)
    private Integer type;

    /**
     * 下发字段展示在原价格处，如果此处不为空，优先展示此处，否则展示price字段
     */
    @MobileDo.MobileField(key = 0xaf10)
    private String rightText;

    /**
     * 副标题是否占满宽度
     */
    @MobileDo.MobileField(key = 0xb824)
    private Boolean fullOccupy;

    public Boolean getFullOccupy() {
        return fullOccupy;
    }

    public void setFullOccupy(Boolean fullOccupy) {
        this.fullOccupy = fullOccupy;
    }

    public String getRightText() {
        return rightText;
    }

    public void setRightText(String rightText) {
        this.rightText = rightText;
    }

    public PopUpWindowVO getPopup() {
        return popup;
    }

    public void setPopup(PopUpWindowVO popup) {
        this.popup = popup;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<DealSkuItemVO> getItems() {
        return items;
    }

    public void setItems(List<DealSkuItemVO> items) {
        this.items = items;
    }

    public String getCopies() {
        return copies;
    }

    public void setCopies(String copies) {
        this.copies = copies;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(String originalPrice) {
        this.originalPrice = originalPrice;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public DealItemTag getTag() {
        return tag;
    }

    public void setTag(DealItemTag tag) {
        this.tag = tag;
    }

    public JumpUrlVO getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(JumpUrlVO jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
