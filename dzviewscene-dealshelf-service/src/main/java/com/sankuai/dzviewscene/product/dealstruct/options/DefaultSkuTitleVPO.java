package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.SkuTitleVP;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/27 9:17 下午
 */
@VPointOption(name = "团购详情sku货标题默认变化点", description = "团购详情sku货标题默认变化点",code = DefaultSkuTitleVPO.CODE, isDefault = true)
public class DefaultSkuTitleVPO extends SkuTitleVP<DefaultSkuTitleVPO.Config> {

    public static final String CODE = "DefaultSkuTitleVPO";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        return param.getSkuTitle();
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
