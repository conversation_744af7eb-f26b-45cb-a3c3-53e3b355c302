package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: created by hang.yu on 2024/4/19 14:34
 */
@VPointOption(name = "第一个sku属性属性列表变化点", description = "第一个sku属性属性列表变化点", code = FirstSkuAttrVOListOpt.CODE)
public class FirstSkuAttrVOListOpt extends DealAttrVOListVP<FirstSkuAttrVOListOpt.Config> {

    public static final String CODE = "FirstSkuAttrVOListOpt";

    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        List<SkuAttrItemDto> list = DealDetailUtils.getFirstMustGroupFirstSkuAttrList(param.getDealDetailDtoModel());
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }

        DealDetailStructAttrModuleGroupModel result = new DealDetailStructAttrModuleGroupModel();
        result.setGroupName(config.getGroupName());
        result.setDealDetailStructAttrModuleVOS(buildAttrList(list, config.getAttrConfigs()));
        result.setShowNum(config.getShowNum());
        result.setFoldStr(config.getFoldStr());
        return Lists.newArrayList(result);
    }

    List<DealDetailStructAttrModuleVO> buildAttrList(List<SkuAttrItemDto> list, List<AttrConfig> attrConfigs) {
        if (CollectionUtils.isEmpty(attrConfigs) || CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return attrConfigs.stream().filter(
                        attrConfig -> attrConfig.isShowSwitch() && CollectionUtils.isNotEmpty(attrConfig.getAttrNames()))
                .map(attrConfig -> {
                    DealDetailStructAttrModuleVO dealDetailStructAttr = new DealDetailStructAttrModuleVO();
                    dealDetailStructAttr.setAttrName(attrConfig.getShowName());
                    String attrValue = attrConfig.getAttrNames().stream().map(attrName -> list.stream()
                            .filter(skuAttrItemDto -> StringUtils.equals(attrName, skuAttrItemDto.getAttrName()))
                            .findFirst().map(skuAttrItemDto -> getAttrValue(skuAttrItemDto.getAttrValue(), attrConfig))
                            .orElse(null)).filter(Objects::nonNull).findFirst().orElse(null);
                    if (StringUtils.isBlank(attrValue)) {
                        return null;
                    }
                    dealDetailStructAttr.setAttrValues(Lists.newArrayList(attrValue));
                    return dealDetailStructAttr;
                }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    String getAttrValue(String attrValue, AttrConfig attrConfig) {
        if (StringUtils.isBlank(attrValue)) {
            return null;
        }
        if (StringUtils.isBlank(attrConfig.getFormatTemplate())) {
            return attrValue;
        }
        if (StringUtils.isBlank(attrConfig.getSpliceStr())) {
            return String.format(attrConfig.getFormatTemplate(), attrValue);
        }
        if (attrValue.contains(attrConfig.getSpliceStr())) {
            return attrValue;
        }
        return String.format(attrConfig.getFormatTemplate(), attrValue);
    }

    @Data
    @VPointCfg
    public static class Config {

        /**
         * 分组名
         */
        private String groupName;

        /**
         * 展示个数
         */
        private Integer showNum;

        /**
         * 折叠文案
         */
        private String foldStr;

        /**
         * 属性配置列表
         */
        private List<AttrConfig> attrConfigs;

    }

    @Data
    static class AttrConfig {

        private String showName;

        private List<String> attrNames;

        private String formatTemplate;

        /**
         * 拼接符
         */
        private String spliceStr;

        /**
         * 展示开关。默认展示。
         */
        private boolean showSwitch = true;

    }
}
