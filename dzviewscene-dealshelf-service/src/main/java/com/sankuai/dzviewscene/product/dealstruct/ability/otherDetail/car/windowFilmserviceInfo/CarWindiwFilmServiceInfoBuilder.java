package com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.car.windowFilmserviceInfo;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.CommonAttrsVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.PicItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.car.CarDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.car.CarDetailPopupItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.car.CarItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.car.CarPopupDetailVO;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.uniform.UniformStructContentModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/8 2:01 下午
 */
@Ability(code = CarWindiwFilmServiceInfoBuilder.CODE,
        name = "车窗玻璃贴膜服务信息模块构造能力",
        description = "车窗玻璃贴膜服务信息模块构造能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE
        }
)
public class CarWindiwFilmServiceInfoBuilder extends PmfAbility<CarDetailModuleVO, CarWindiwFilmServiceInfoParam, CarWindiwFilmServiceInfoCfg> {

    public static final String CODE = "CarWindiwFilmServiceInfoBuilder";

    @Override
    public CompletableFuture<CarDetailModuleVO> build(ActivityCxt ctx, CarWindiwFilmServiceInfoParam carWindiwFilmServiceInfoParam, CarWindiwFilmServiceInfoCfg cfg) {
        //1.获取数据源
            //1.2 获取属性信息
        Map<String, String> attrsMap = getAttrsMap(ctx);
            //1.2 获取服务流程列表
        List<String> serviceFlow = getFlowStepList(ctx);
        //2.构造服务信息列表
        List<CarItemVO> carItemVOS = buildCarItemVOList(attrsMap, cfg);
        //3.构造弹窗
        CarDetailPopupItemVO carDetailPopupItemVO = buildCarDetailPopupItemVO(attrsMap, serviceFlow, cfg);
        //4.组装结果
        return CompletableFuture.completedFuture(buildCarDetailModuleVO(carItemVOS, carDetailPopupItemVO, cfg));
    }

    private CarDetailModuleVO buildCarDetailModuleVO(List<CarItemVO> carItemVOS, CarDetailPopupItemVO carDetailPopupItemVO, CarWindiwFilmServiceInfoCfg cfg) {
        if (CollectionUtils.isEmpty(carItemVOS) && carDetailPopupItemVO == null) {
            return null;
        }
        CarDetailModuleVO carDetailModuleVO = new CarDetailModuleVO();
        carDetailModuleVO.setItemList(carItemVOS);
        carDetailModuleVO.setPopup(carDetailPopupItemVO);
        carDetailModuleVO.setTitle(cfg.getCarWindowFilmServiceInfoModuleTitle());
        return carDetailModuleVO;
    }

    /**
     * 构造弹窗模块
     *@param
     *@return
     */
    private CarDetailPopupItemVO buildCarDetailPopupItemVO(Map<String, String> attrsMap, List<String> serviceFlow, CarWindiwFilmServiceInfoCfg cfg) {
        CarPopupDetailVO carPopupDetailVO = buildCarPopupDetailVO(attrsMap, serviceFlow, cfg);
        if (carPopupDetailVO == null) {
            return null;
        }
        CarDetailPopupItemVO carDetailPopupItemVO = new CarDetailPopupItemVO();
        carDetailPopupItemVO.setPopupDetail(carPopupDetailVO);
        carDetailPopupItemVO.setName(cfg.getCarWindowFilmServiceInfoModulePopupDoc());
        return carDetailPopupItemVO;
    }

    /**
     * 构造弹窗页面
     *@param
     *@return
     */
    private CarPopupDetailVO buildCarPopupDetailVO(Map<String, String> attrsMap, List<String> serviceFlow, CarWindiwFilmServiceInfoCfg cfg) {
        List<CommonAttrsVO> commonAttrsVOS = getServiceInfoCommonAttrsVOs(attrsMap, cfg);
        CommonAttrsVO flow = buildServiceFlow(serviceFlow);
        if (CollectionUtils.isEmpty(commonAttrsVOS) && flow == null) {
            return null;
        }
        CarPopupDetailVO carPopupDetailVO = new CarPopupDetailVO();
        carPopupDetailVO.setAttrs(commonAttrsVOS);
        carPopupDetailVO.setFlow(flow);
        carPopupDetailVO.setTitle(cfg.getCarWindowFilmServiceInfoModulePopupTitle());
        return carPopupDetailVO;
    }

    /**
     * 获取弹窗服务流程
     *@param
     *@return
     */
    private CommonAttrsVO buildServiceFlow(List<String> serviceFlow) {
        if (CollectionUtils.isEmpty(serviceFlow)) {
            return null;
        }
        CommonAttrsVO commonAttrsVO = new CommonAttrsVO();
        commonAttrsVO.setName("流程");
        commonAttrsVO.setValues(serviceFlow);
        return commonAttrsVO;
    }

    /**
     * 获取弹窗服务信息属性列表
     *@param
     *@return
     */
    private List<CommonAttrsVO> getServiceInfoCommonAttrsVOs(Map<String, String> attrsMap, CarWindiwFilmServiceInfoCfg cfg) {
        if (MapUtils.isEmpty(attrsMap) || MapUtils.isEmpty(cfg.getAttrName2AttrTitleMapInPopup())) {
            return null;
        }
        return cfg.getAttrName2AttrTitleMapInPopup().entrySet().stream().map(attrCfg -> buildCommonAttrsVO(attrCfg.getValue(), attrsMap.get(attrCfg.getKey()))).filter(vo -> vo != null).collect(Collectors.toList());
    }

    private CommonAttrsVO buildCommonAttrsVO(String title, String value) {
        if (title == null || value == null) {
            return null;
        }
        CommonAttrsVO commonAttrsVO = new CommonAttrsVO();
        commonAttrsVO.setName(title);
        commonAttrsVO.setValues(Lists.newArrayList(value));
        return commonAttrsVO;
    }

    /**
     * 构造服务信息列表
     *@param
     *@return
     */
    private List<CarItemVO> buildCarItemVOList(Map<String, String> attrsMap, CarWindiwFilmServiceInfoCfg cfg) {
        if (MapUtils.isEmpty(attrsMap) || MapUtils.isEmpty(cfg.getAttrName2AttrTitleMapInServiceInfo())) {
            return null;
        }
        return cfg.getAttrName2AttrTitleMapInServiceInfo().entrySet().stream().map(attrCfg -> buildCarItemVO(attrsMap.get(attrCfg.getKey()), attrCfg.getValue())).filter(vo -> vo != null).collect(Collectors.toList());
    }

    private CarItemVO buildCarItemVO(String attrValue, String attrTitle) {
        if (attrValue == null || attrTitle == null) {
            return null;
        }
        CarItemVO carItemVO = new CarItemVO();
        carItemVO.setTitle(getTitle(attrTitle, null));
        carItemVO.setValue(attrValue);
        return carItemVO;
    }

    private PicItemVO getTitle(String part, String partDescUrl) {
        PicItemVO title = new PicItemVO();
        title.setTitle(part);
        title.setUrl(partDescUrl);
        return title;
    }

    private Map<String, String> getAttrsMap(ActivityCxt ctx) {
        Map<String, String> attrsMap = new HashMap<>();
        Map<String, String> attrsMapFromDealAttrList = extractAttrsMapFromDealAttrList(ctx);
        if (MapUtils.isNotEmpty(attrsMapFromDealAttrList)) {
            attrsMap.putAll(attrsMapFromDealAttrList);
        }
        Map<String, String> attrsMapFromSkuAttrItemDtos = extractAttrsMapFromSkuAttrItemDtos(ctx);
        if (MapUtils.isNotEmpty(attrsMapFromSkuAttrItemDtos)) {
            attrsMap.putAll(attrsMapFromSkuAttrItemDtos);
        }
        return attrsMap;
    }

    /**
     * 获取流程步骤列表
     *@param
     *@return
     */
    private List<String> getFlowStepList(ActivityCxt ctx) {
        DealDetailInfoModel dealDetailInfoModel = getDealDetailInfoModel(ctx);
        if (dealDetailInfoModel == null || CollectionUtils.isEmpty(dealDetailInfoModel.getDealModuleAttrs())) {
            return null;
        }
        UniformStructContentModel flowModel = dealDetailInfoModel.getDealModuleAttrs().stream().filter(moduleAttr -> ObjectUtils.equals("physicalExaminationServiceNote", moduleAttr.getType())).findFirst().orElse(null);
        if (flowModel == null || flowModel.getData() == null) {
            return null;
        }
        FlowModel flow = JsonCodec.decode(JsonCodec.encodeWithUTF8(flowModel.getData()), FlowModel.class);
        if (flow == null || CollectionUtils.isEmpty(flow.getGroups())) {
            return null;
        }
        return flow.getGroups().stream().map(step -> step.getContent()).collect(Collectors.toList());
    }

    /**
     * 从团单属性列表中提取属性名到属性值Map
     *@param
     *@return
     */
    private Map<String, String> extractAttrsMapFromDealAttrList(ActivityCxt ctx) {
        DealDetailInfoModel dealDetailInfoModel = getDealDetailInfoModel(ctx);
        if (dealDetailInfoModel == null) {
            return new HashMap<>();
        }
        List<AttrM> dealAttrs = dealDetailInfoModel.getDealAttrs();
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return new HashMap<>();
        }
        return dealAttrs.stream().filter(attr -> attr != null).collect(HashMap::new, (map, attr) -> map.put(attr.getName(), attr.getValue()), HashMap::putAll);
    }

    /**
     * 从第一个must组的第一个sku的属性列表中提取属性名到属性值Map
     *@param
     *@return
     */
    private Map<String, String> extractAttrsMapFromSkuAttrItemDtos(ActivityCxt ctx) {
        DealDetailInfoModel dealDetailInfoModel = getDealDetailInfoModel(ctx);
        List<SkuAttrItemDto> skuAttrItemDtos = DealDetailUtils.extractFirstMustSkuAttrListFromDealDetailInfoModel(dealDetailInfoModel);
        if (CollectionUtils.isEmpty(skuAttrItemDtos)) {
            return new HashMap<>();
        }
        return skuAttrItemDtos.stream().filter(skuAttr -> skuAttr != null).collect(HashMap::new, (map, skuAttr) -> map.put(skuAttr.getAttrName(), skuAttr.getAttrValue()), HashMap::putAll);
    }

    private DealDetailInfoModel getDealDetailInfoModel(ActivityCxt ctx) {
        List<DealDetailInfoModel> dealDetailInfoModels = ctx.getSource(DealDetailFetcher.CODE);
        return CollectUtils.firstValue(dealDetailInfoModels);
    }

    @Data
    private static class FlowModel {
        private List<FlowStepModel> groups;
    }

    @Data
    private static class FlowStepModel {
        private String content;
    }

}
