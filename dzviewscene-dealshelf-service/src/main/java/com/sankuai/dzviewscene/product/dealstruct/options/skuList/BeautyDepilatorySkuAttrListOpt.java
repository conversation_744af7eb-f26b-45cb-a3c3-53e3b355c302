package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DataUtils;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.func.Func;
import com.sankuai.dzviewscene.product.dealstruct.vo.CommonAttrVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.sankuai.dzviewscene.product.dealstruct.ability.utils.Constants.DEFAULT_STEP_TYPE;
import static com.sankuai.dzviewscene.product.dealstruct.ability.utils.Constants.SPC_SPLITTER;

/**
 * <AUTHOR>
 * 美容美体-脱毛sku属性列表变化点（503-脱毛）
 * 适用于当团单内的服务项目为【脱毛-2104709】
 * 根据配置取货属性值
 * 需求：https://km.sankuai.com/collabpage/1581105098
 */
@VPointOption(name = "美容美体-脱毛sku属性列表变化点", description = "美容美体-脱毛sku属性列表变化点，展示配置的sku属性", code = BeautyDepilatorySkuAttrListOpt.CODE)
public class BeautyDepilatorySkuAttrListOpt extends SkuAttrListVP<BeautyDepilatorySkuAttrListOpt.Config> {

    public static final String CODE = "BeautyDepilatorySkuAttrListOpt";

    private static final String APPLICABLE_PARTS = "适用部位";

    private static final String USE_QTY = "使用次数";

    private static final String USE_PERIOD = "使用期限";

    private static final String EQUIPMENT_PRODUCT = "仪器产品";

    private static final String STEP_DESC = "步骤说明";

    private static final String SERVICE_STEP = "服务流程";

    private static final String ZERO_STEP_TIME_SHOW_DOC = "不计入总时长";

    private static final String STEP_TIME_FORMAT = "%s分钟";

    /**
     * 【脱毛】服务项目Id
     */
    private static final long DEPILATION_CATEGORY_ID = 2104709L;

    @Override
    public List<DealSkuItemVO> compute(ActivityCxt context, Param param, BeautyDepilatorySkuAttrListOpt.Config config) {
        // 团单属性
        SkuItemDto skuItemDto = param.getSkuItemDto();
        if (skuItemDto == null
                || CollectionUtils.isEmpty(skuItemDto.getAttrItems())
                || !Objects.equals(skuItemDto.getProductCategory(), DEPILATION_CATEGORY_ID)) {
            return null;
        }
        List<SkuAttrItemDto> skuAttrItems = skuItemDto.getAttrItems();
        List<DealSkuItemVO> list = Lists.newArrayList();
        addIfValiad(list, () -> buildBodyName(skuAttrItems));// 适用部位
        addIfValiad(list, () -> buildUseQty(skuAttrItems));// 使用次数
        addIfValiad(list, () -> buildPeriodOfUse(skuAttrItems));// 使用期限
        addIfValiad(list, () -> buildServiceStep(skuAttrItems, config));// 服务步骤
        return list;
    }

    private void addIfValiad(List<DealSkuItemVO> list, Func<DealSkuItemVO> function) {
        DealSkuItemVO invoke = function.invoke();
        if (invoke != null) {
            list.add(invoke);
        }
    }

    // 适用部位
    private DealSkuItemVO buildBodyName(List<SkuAttrItemDto> skuAttrItems) {
        String bodyName = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "bodyname");
        if (StringUtils.isEmpty(bodyName)) {
            return null;
        }
        int bodyOptionQty = DealDetailUtils.getSkuAttrIntVal(skuAttrItems, "bodyOptionCount");
        int bodyQty = DataUtils.toList(bodyName, SPC_SPLITTER).size();

        String val = bodyName;
        if (bodyOptionQty < bodyQty) {
            val = String.format("%s%s选%s", bodyName, bodyQty, bodyOptionQty);
        }
        return buildDealSkuItemVO(APPLICABLE_PARTS, val, null, 0);
    }

    // 使用次数
    private DealSkuItemVO buildUseQty(List<SkuAttrItemDto> skuAttrItems) {
        String numberOfUsage = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "numberOfUsage");
        if (StringUtils.isEmpty(numberOfUsage)) {
            return null;
        }
        return buildDealSkuItemVO(USE_QTY, numberOfUsage, null, 0);
    }

    // 使用期限
    private DealSkuItemVO buildPeriodOfUse(List<SkuAttrItemDto> skuAttrItems) {
        String periodOfUse = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "periodOfUse");
        if (StringUtils.isEmpty(periodOfUse)) {
            return null;
        }
        String numberOfUsage = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "numberOfUsage");
        if (Objects.equals(numberOfUsage, "1次")) {
            return null;
        }
        return buildDealSkuItemVO(USE_PERIOD, periodOfUse, null, 0);
    }

    private DealSkuItemVO buildServiceStep(List<SkuAttrItemDto> skuAttrItems, Config config) {
        List<ServiceStepModel> serviceStepModels = DealDetailUtils.getSkuAttrVal(skuAttrItems, "servicestep", new TypeReference<List<ServiceStepModel>>() {
        });
        if (CollectionUtils.isEmpty(serviceStepModels)) {
            return null;
        }
        List<SkuAttrAttrItemVO> serviceInfoList = serviceStepModels
                .stream()
                .map(this::buildSkuAttrAttrItemVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(serviceInfoList)) {
            int stepType = config.getStepType() > 0 ? config.getStepType() : DEFAULT_STEP_TYPE;
            return buildDealSkuItemVO(SERVICE_STEP, null, serviceInfoList, stepType);
        }
        return null;
    }

    private SkuAttrAttrItemVO buildSkuAttrAttrItemVO(ServiceStepModel serviceStepModel) {
        if (serviceStepModel == null) {
            return null;
        }
        // 服务项目，SKU 名称-左侧
        SkuAttrAttrItemVO itemVO = new SkuAttrAttrItemVO();
        itemVO.setName(serviceStepModel.getStepType());

        // 服务项目，SKU 时间-右侧
        itemVO.setInfo(Lists.newArrayList(getNormalizedStepTime(serviceStepModel.getStepTime())));

        // 服务项目，SKU 名称下部
        List<CommonAttrVO> commonAttrs = new ArrayList<>();
        addToListIfNotNull(commonAttrs, buildCommonAttrVO(STEP_DESC, serviceStepModel.getStepName()));
        addToListIfNotNull(commonAttrs, buildCommonAttrVO(EQUIPMENT_PRODUCT, serviceStepModel.getEquipmentProduct()));
        itemVO.setValues(commonAttrs);
        return itemVO;
    }

    private <T> void addToListIfNotNull(List<T> list, T item) {
        if (list == null || item == null) {
            return;
        }
        list.add(item);
    }

    private CommonAttrVO buildCommonAttrVO(String name, String value) {
        if (StringUtils.isEmpty(name) || StringUtils.isEmpty(value)) {
            return null;
        }
        CommonAttrVO commonAttrVO = new CommonAttrVO();
        commonAttrVO.setName(name);
        commonAttrVO.setValue(value);
        return commonAttrVO;
    }

    /**
     * 如果stepTime为0则展示"不计入总时长"，否则展示"%s分钟"
     */
    private String getNormalizedStepTime(String stepTime) {
        if (StringUtils.isEmpty(stepTime)) {
            return null;
        }
        if (stepTime.contains("分钟")) {
            return stepTime;
        }
        if (Integer.toString(0).equals(stepTime)) {
            return ZERO_STEP_TIME_SHOW_DOC;
        }
        return String.format(STEP_TIME_FORMAT, stepTime);
    }

    private DealSkuItemVO buildDealSkuItemVO(String name, String value, List<SkuAttrAttrItemVO> valueAttrs, int type) {
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setType(type);
        dealSkuItemVO.setValue(value);
        dealSkuItemVO.setValueAttrs(valueAttrs);
        return dealSkuItemVO;
    }

    @Data
    @VPointCfg
    public static class Config {
        // 服务步骤或服务流程的展示类型
        private int stepType;
    }

    @Data
    private static class ServiceStepModel {
        // 使用仪器产品
        private String equipmentProduct;

        // 步骤说明
        private String stepName;

        // 步骤耗时
        private String stepTime;

        // 步骤名称
        private String stepType;

        // 步骤说明
        private String stepDesc;
    }
}