package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.shop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.platform.common.model.ContextHandlerResult;
import com.sankuai.dzviewscene.shelf.platform.other.OtherActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.feitianplus.data.onedata.api.thrift.domain.KeyQueryParamSync;
import com.sankuai.feitianplus.data.onedata.api.thrift.domain.QueryDataTitle;
import com.sankuai.feitianplus.data.onedata.api.thrift.domain.QueryResultSync;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

@Component
public class LifeWashShopTimelinessPaddingHandler implements ContextPaddingHandler {

    @Resource
    private CompositeAtomService compositeAtomService;

    //服务所属业务线ID
    private static final int BIZ_TYPE_ID = 2;
    ////服务标识，可通过服务列表页面查看
    private static final String QUERY_KEY = "2_dpapp_shop_reserve_accept_order_effect_d";
    //授权集群名称
    private static final String CELL_NAME = "gray-release-feitianplus-zong-set";

    @Override
    public CompletableFuture<ContextHandlerResult> padding(ActivityCxt ctx, ContextHandlerResult contextHandlerResult, Map<String, Object> params) {
        KeyQueryParamSync keyQueryParamSync = getKeyQueryParamSync(ctx);
        CompletableFuture<QueryResultSync> queryResultSyncCompletableFuture = compositeAtomService.queryByKey(keyQueryParamSync);
        if (queryResultSyncCompletableFuture == null) {
            return CompletableFuture.completedFuture(null);
        }
        return queryResultSyncCompletableFuture.thenApply(queryResultSync -> {
            if (Objects.isNull(queryResultSync) || !queryResultSync.isIfSuccess()) {
                return contextHandlerResult;
            }
            List<List<String>> dataList = new ArrayList<>();
            String data = queryResultSync.getData();
            if (StringUtils.isNotEmpty(data)) {
                dataList = JSON.parseObject(data, new TypeReference<List<List<String>>>() {
                });
            }
            if (CollectionUtils.isEmpty(dataList) || CollectionUtils.isEmpty(dataList.get(0))) {
                return contextHandlerResult;
            }
            List<QueryDataTitle> queryResultTitles = queryResultSync.getQueryResultTitles();
            if(CollectionUtils.isEmpty(queryResultTitles)){
                return contextHandlerResult;
            }
            if (dataList.get(0).size() == queryResultTitles.size()) {
                Map<String, String> dataMaps = new HashMap<>();
                for (int i = 0; i < queryResultTitles.size(); i++) {
                    QueryDataTitle queryDataTitle = queryResultTitles.get(i);
                    String columnName = queryDataTitle.getColumnName();
                    String value = dataList.get(0).get(i);
                    dataMaps.put(columnName, value);
                }
                contextHandlerResult.setAvgAcceptOderTime(NumberUtils.toLong(dataMaps.get("avg_accept_oder_time")));
            }
            return contextHandlerResult;
        });
    }

    private static KeyQueryParamSync getKeyQueryParamSync(ActivityCxt ctx) {
        KeyQueryParamSync keyQueryParamSync = new KeyQueryParamSync();
        keyQueryParamSync.setBizTypeId(BIZ_TYPE_ID);
        keyQueryParamSync.setQueryKey(QUERY_KEY);
        //keyQueryParamSync.setTestEnvDebugQueryVersionKey("${queryVersionKey}");//服务版本标识,该参数只在测试环境有效(供联调使用)
        keyQueryParamSync.setCellName(CELL_NAME);

        Map<String, String> param = new HashMap<>();
        int platform = ParamsUtil.getIntSafely(ctx, OtherActivityConstants.Params.platform);
        long shopId = !PlatformUtil.isMT(platform) ? PoiIdUtil.getDpPoiIdL(ctx, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId)
                : PoiIdUtil.getMtPoiIdL(ctx, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
        ShopInfo shopInfo = new ShopInfo();
        if (PlatformUtil.isMT(platform)) {
            shopInfo.setMt_shop_id(shopId+"");
            shopInfo.setDp_shop_id("null");
            param.put("params", JSON.toJSONString(shopInfo));
        } else {
            shopInfo.setDp_shop_id(shopId+"");
            shopInfo.setMt_shop_id("null");
            param.put("params", JSON.toJSONString(shopInfo));
        }
        keyQueryParamSync.setParams(param);
        return keyQueryParamSync;
    }

    @Data
    public static class ShopInfo {
        private String mt_shop_id;
        private String dp_shop_id;
    }
}
