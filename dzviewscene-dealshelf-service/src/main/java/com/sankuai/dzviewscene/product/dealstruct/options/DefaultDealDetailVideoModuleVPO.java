package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.videoModule.vpoints.DealDetailVideoModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.VideoModuleVO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/27 9:17 下午
 */
@VPointOption(name = "团购详情模块视频组件默认变化点", description = "团购详情模块视频组件默认变化点",code = DefaultDealDetailVideoModuleVPO.CODE, isDefault = true)
public class DefaultDealDetailVideoModuleVPO extends DealDetailVideoModuleVP<DefaultDealDetailVideoModuleVPO.Config> {

    public static final String CODE = "defaultDealDetailVideoModuleVPO";

    @Override
    public VideoModuleVO compute(ActivityCxt context, Param param, Config config) {
        if (config == null) {
            return null;
        }
        return config.getVideoModuleVO();
     }


    @Data
    @VPointCfg
    public static class Config {
        VideoModuleVO videoModuleVO;
    }
}
