package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RegexValueProcessor extends AbstractValueProcessor {
    @Override
    public List<String> convertDisplayValues(String value, ValueConfig valueConfig, Map<String, String> name2ValueMap) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor.RegexValueProcessor.convertDisplayValues(String,ValueConfig,Map)");
        if (validate(value, valueConfig)) {
            return Lists.newArrayList();
        }
        value = matchRegex(value, valueConfig.getRegex());
        if (ObjectUtils.isEmpty(value)) {
            return Lists.newArrayList();
        }
        if (!ObjectUtils.isEmpty(valueConfig.getFormat())) {
            value = String.format(valueConfig.getFormat(), value);
        }
        if (ObjectUtils.isEmpty(value)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(value);
    }

    private String matchRegex(String value, String regex) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor.RegexValueProcessor.matchRegex(java.lang.String,java.lang.String)");
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(value);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    private boolean validate(String value, ValueConfig valueConfig) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor.RegexValueProcessor.validate(java.lang.String,com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig)");
        return ObjectUtils.isEmpty(value) || ObjectUtils.isEmpty(valueConfig)
                || ObjectUtils.isEmpty(valueConfig.getRegex());
    }
}
