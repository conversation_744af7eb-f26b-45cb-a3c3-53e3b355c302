package com.sankuai.dzviewscene.product.dealstruct.options.skuList.beautyyoga;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.beautyyoga.enums.BeautyYogaEnum;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.BeautyYogaUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/6/6 19:13
 */
@VPointOption(name = "舞蹈瑜伽团详sku列表组变化点V2", description = "舞蹈瑜伽团详sku列表组变化点V2", code = BeautyYogaDealModuleOpt.CODE)
public class BeautyYogaDealModuleOpt extends AbstractBeautyYogaModuleOpt<BeautyYogaDealModuleOpt.Config> {

    public static final String CODE = "BeautyYogaDealModuleOpt";

    @Override
    public List<DealDetailSkuListModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
       if (Objects.isNull(dealDetailInfoModel)) {
            return Lists.newArrayList();
        }
        List<AttrM> dealAttrs = dealDetailInfoModel.getDealAttrs();
        // 获取服务类型
        String serviceType = getServiceType(dealAttrs);
        // 构造服务内容模块
        return buildServiceContentModule(serviceType, dealAttrs, config);
    }

    private String getServiceType(List<AttrM> dealAttrs){
        String serviceType = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "serviceType");
        if (StringUtils.isBlank(serviceType)){
            serviceType = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "service_type");
        }
        return serviceType;
    }

    private List<DealDetailSkuListModuleGroupModel> buildServiceContentModule(String serviceType, List<AttrM> dealAttrs, BeautyYogaDealModuleOpt.Config config) {
        try{
            BeautyYogaEnum serviceTypeEnum = BeautyYogaEnum.getEnumByServiceType(serviceType);
            if (serviceTypeEnum == null) {
                return null;
            }
            List<DealDetailSkuListModuleGroupModel> result = Lists.newArrayList();
            switch (serviceTypeEnum) {
                case SINGLE_TIME_CARD:
                case MULTI_CYCLE_TIMES_CARD:
                case MULTI_CYCLE_TIMES_CARD_:
                case PERSONAL_TIMES_CARD:
                case PERSONAL_MULTI_CYCLE_TIMES_CARD:
                case PERSONAL_MULTI_CYCLE_TIMES_CARD_:
                case COACH_TRAINING:
                case CLASSROOM_RENTAL:
                case CHOREOGRAPHY:
                    result.add(buildServiceContentModuleByDealAttrs(serviceType, dealAttrs, config));
                    return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
                case COMBINATION_LESSON_PACK:
                    Map<String, String> attr2ValueMap = dealAttrs.stream().collect(Collectors.toMap(e->e.getName(), e->e.getValue(), ((e1, e2)->e2)));
                    String dealInfoJson = attr2ValueMap.get("detailInfo");
                    JSONArray groupsJSONArray = BeautyYogaUtils.parseDetailInfo(dealInfoJson);
                    AtomicInteger count = new AtomicInteger();
                    groupsJSONArray.forEach(units->{
                        JSONArray unitsArray = BeautyYogaUtils.parseUnitsJson((JSONObject) units);
                        int optionalCount = (int) ((JSONObject) units).get("optionalCount");
                        int unitSize = unitsArray.size();
                        String optionStr = getOptionalStr(optionalCount, unitSize);
                        unitsArray.forEach(unit->{
                            count.getAndIncrement();
                            result.add(buildServiceContentModuleByUnit(serviceType, dealAttrs, (JSONObject) unit, optionStr, count.get(),  config));
                        });
                    });
                    return result;
                case PHYSICAL_GOODS:
                    return null;
                default:
                    return null;
            }
        }catch (Exception e){
            Cat.logError(e);
            return null;
        }
    }

    private String getOptionalStr(int optionalCount, int unitSize){
        if (optionalCount > 0 && unitSize > 0){
            return unitSize +"选" + optionalCount;
        }
        return null;
    }


    /**
     * 构建服务内容模块
     */
    private DealDetailSkuListModuleGroupModel buildServiceContentModuleByDealAttrs(String serviceType, List<AttrM> dealAttrs, BeautyYogaDealModuleOpt.Config config) {
        List<DealSkuVO> dealSkuVOList = buildServiceItemsByServiceType(serviceType, dealAttrs, null, null, config);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(dealSkuVOList)) {
            return null;
        }
        return buildDealDetailSkuListModuleGroupModel(serviceType, null, dealSkuVOList);
    }


    private DealDetailSkuListModuleGroupModel buildServiceContentModuleByUnit(String serviceType, List<AttrM> dealAttrs, JSONObject unitJson, String optionStr, int index, BeautyYogaDealModuleOpt.Config config) {
        List<DealSkuVO> dealSkuVOList = buildServiceItemsByServiceType(serviceType,dealAttrs, unitJson, optionStr, config);
        if (index>1){
            serviceType = serviceType + index;
        }
        return buildDealDetailSkuListModuleGroupModel(serviceType, null, dealSkuVOList);
    }

    @Data
    @VPointCfg
    public static class Config {
        // sku需要展示的属性
        private Map<String, List<String>> skuName2Attr = new HashMap<>();

        // 属性需要展示的图标
        private Map<String, String> skuName2IconMap = new HashMap<>();

        // 属性的布局类型，0-双列展示，1-单列展示
        private Map<String, Integer> attr2LayerType = new HashMap<>();
    }

}
