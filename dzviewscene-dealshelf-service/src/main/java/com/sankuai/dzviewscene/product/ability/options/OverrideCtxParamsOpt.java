package com.sankuai.dzviewscene.product.ability.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreSyncHandlerVP;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/19 3:52 下午
 *
 */
@VPointOption(name = "覆盖上下文", description = "按需覆盖", code = OverrideCtxParamsOpt.CODE)
public class OverrideCtxParamsOpt extends PreSyncHandlerVP<OverrideCtxParamsOpt.Config> {

    public static final String CODE = "OverrideCtxParamsOpt";

    @Override
    public Map<String, Object> compute(ActivityCxt ctx, Param param, Config config) {
        if (MapUtils.isEmpty(config.getOverrideMap())) {
            return null;
        }
        return config.getOverrideMap();
    }

    @Data
    @VPointCfg
    public static class Config {
        private Map<String, Object> overrideMap;
    }
}
