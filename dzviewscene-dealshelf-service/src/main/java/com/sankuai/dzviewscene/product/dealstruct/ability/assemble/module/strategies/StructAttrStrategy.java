package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.struct.DealDetailStructAttrListBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModel;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrItemModel;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrsModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailAttrDescVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * created by zhangzhiyuan04 in 2021/12/14
 */
@Component("structAttr")
public class StructAttrStrategy implements ModuleStrategy {

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        String abilityCode = DealDetailStructAttrListBuilder.CODE;
        List<DealDetailStructAttrModel> structAttrModels = activityCxt.getSource(abilityCode);
        //拿到第一个团单（默认只有一个团单）
        DealDetailStructAttrModel dealDetailStructAttrModel = CollectUtils.firstValue(structAttrModels);
        if (dealDetailStructAttrModel == null) {
            return null;
        }
        List<StructAttrsModel> structAttrsModels = dealDetailStructAttrModel.getStructAttrsModels();
        //拿第一个属性列表组
        StructAttrsModel structAttrsModel = CollectUtils.firstValue(structAttrsModels);
        if (structAttrsModel == null || CollectionUtils.isEmpty(structAttrsModel.getStructAttrModels())) {
            return null;
        }
        //填充主属性
        Map<String, List<DealDetailStructAttrModuleVO>> mainAttrMap = getAttrModuleWithMain(structAttrsModel);
        if (MapUtils.isEmpty(mainAttrMap)) {
            return null;
        }
        List<DealDetailStructAttrModuleVO> structAttrModuleVOS = paddingDescAttrAndGetValueList(mainAttrMap, structAttrsModel);

        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        dealDetailModuleVO.setDealStructAttrsModel1(structAttrModuleVOS);
        return dealDetailModuleVO;
    }

    /**
     * key - attrName
     *
     * @param structAttrsModel
     * @return 返回仅由主属性构造而成的 module map，用于后续填充描述属性
     */
    private Map<String, List<DealDetailStructAttrModuleVO>> getAttrModuleWithMain(StructAttrsModel structAttrsModel) {
        Map<String, List<DealDetailStructAttrModuleVO>> mainAttrMap = new LinkedHashMap<>();
        for (StructAttrItemModel attrModel : structAttrsModel.getStructAttrModels()) {
            if (Objects.isNull(attrModel) || StringUtils.isNotEmpty(attrModel.getRelateMainAttrName())) {
                continue;
            }
            List<DealDetailStructAttrModuleVO> modules = mainAttrMap.get(attrModel.getAttrName());
            if (CollectionUtils.isEmpty(modules)) {
                modules = new ArrayList<>();
                mainAttrMap.put(attrModel.getAttrName(), modules);
            }
            modules.add(buildDealDetailStructAttrModuleVO(attrModel));
        }
        return mainAttrMap;
    }

    /**
     * 构造主属性
     *
     * @param innerModel
     * @return
     */
    private DealDetailStructAttrModuleVO buildDealDetailStructAttrModuleVO(StructAttrItemModel innerModel) {
        DealDetailStructAttrModuleVO dealDetailStructAttrModuleVO = new DealDetailStructAttrModuleVO();
        dealDetailStructAttrModuleVO.setAttrValues(innerModel.getAttrValues());
        dealDetailStructAttrModuleVO.setIcon(innerModel.getIcon());
        dealDetailStructAttrModuleVO.setAttrName(innerModel.getAttrName());
        return dealDetailStructAttrModuleVO;
    }

    /**
     * 为主属性填充描述属性，并返回 List 结构
     *
     * @param mainAttrMap
     * @param structAttrsModel
     * @return
     */
    private List<DealDetailStructAttrModuleVO> paddingDescAttrAndGetValueList(Map<String, List<DealDetailStructAttrModuleVO>> mainAttrMap, StructAttrsModel structAttrsModel) {
        //填充描述属性
        for (StructAttrItemModel structAttrModel : structAttrsModel.getStructAttrModels()) {
            if (Objects.isNull(structAttrModel) || StringUtils.isEmpty(structAttrModel.getRelateMainAttrName()) || CollectionUtils.isEmpty(structAttrModel.getAttrValues())) {
                continue;
            }
            String relateMainAttrName = structAttrModel.getRelateMainAttrName();
            //要填充的主属性
            List<DealDetailStructAttrModuleVO> mainAttr = mainAttrMap.get(relateMainAttrName);
            for (DealDetailStructAttrModuleVO moduleVO : mainAttr) {
                paddingDescAttr(moduleVO, structAttrModel);
            }
        }
        return mainAttrMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
    }

    /**
     * 将描述属性填充至 DealDetailStructAttrModuleVO
     *
     * @param moduleVO
     * @param structAttrModel
     */
    private void paddingDescAttr(DealDetailStructAttrModuleVO moduleVO, StructAttrItemModel structAttrModel) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies.StructAttrStrategy.paddingDescAttr(DealDetailStructAttrModuleVO,StructAttrItemModel)");
        if (CollectionUtils.isEmpty(moduleVO.getDesc())) {
            moduleVO.setDesc(Lists.newArrayList());
        }
        List<DealDetailAttrDescVO> descVOList = structAttrModel.getAttrValues().stream().map(o -> {
            DealDetailAttrDescVO attrDescVO = new DealDetailAttrDescVO();
            attrDescVO.setTitle(o);
            return attrDescVO;
        }).collect(Collectors.toList());
        moduleVO.getDesc().addAll(descVOList);
    }

}