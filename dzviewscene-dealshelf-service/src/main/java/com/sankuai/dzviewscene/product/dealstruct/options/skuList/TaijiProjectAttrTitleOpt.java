package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.TaiJiProjectTitleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.TaiJiProjectProcessAttrModel;
import com.sankuai.dzviewscene.product.dealstruct.model.TaiJiProjectProcessAttrValueModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2024/9/7
 */
@VPointOption(name = "获取太极团单属性信息作为服务项目标题", description = "获取太极团单属性信息作为服务项目标题",code = TaijiProjectAttrTitleOpt.CODE)
public class TaijiProjectAttrTitleOpt extends TaiJiProjectTitleVP<TaijiProjectAttrTitleOpt.Config> {
    public static final String CODE = "TaijiProjectAttrTitleOpt";
    private static final String TUINA_SKU_CATE_ID = "2200283";
    private static final String COMPRESS_SKU_CATE_ID = "2200284";

    @Override
    public String compute(ActivityCxt activityCxt, Param param, TaijiProjectAttrTitleOpt.Config config) {
        /**
         * 1. serviceType匹配
         * 2. attrKey匹配
         */
        List<AttrM> attrs = param.getDealAttrs();
        StandardServiceProjectItemDTO itemDTO = param.getTaiJiProjectItem();
        if (CollectionUtils.isEmpty(attrs) || CollectionUtils.isEmpty(config.getTitleConfigs())) {
            return null;
        }

        List<TaijiProjectAttrTitleOpt.TitleConfig> titleConfigs = config.getTitleConfigs();
        Map<String, TaijiProjectAttrTitleOpt.TitleConfig> serviceTypeAttrKeyMap = titleConfigs.stream().collect(Collectors.toMap(TitleConfig::getServiceType, Function.identity(), (o1, o2) -> o1));
        AttrM attrM = attrs.stream().filter(attr -> serviceTypeAttrKeyMap.containsKey(attr.getValue())).findFirst().orElse(null);
        if (attrM == null) {
            return null;
        }
        //对综合特殊处理
        if ("综合".equals(attrM.getValue())) {
            return titleConfigs.stream()
                    .map(titleConfig -> getTitle(itemDTO, attrs, titleConfig))
                    .filter(StringUtils::isNotBlank)
                    .findFirst()
                    .orElse(getTiTleBySkuCateId(config,itemDTO));
        }

        String title = getTitle(itemDTO, attrs, serviceTypeAttrKeyMap.get(attrM.getValue()));

        // 使用cateId兜底
        if (StringUtils.isBlank(title)) {
            return getTiTleBySkuCateId(config, itemDTO);
        }
        return title;
    }

    private String getTiTleBySkuCateId(Config config, StandardServiceProjectItemDTO itemDTO) {
        StandardAttributeDTO standardAttribute = itemDTO.getStandardAttribute();
        List<StandardAttributeItemDTO> attrs = standardAttribute.getAttrs();
        String skuCateId = getAttrSimpleValuesByKey(attrs, "skuCateId");
        if (StringUtils.isBlank(skuCateId)) {
            return null;
        }
        Map<String, String> cateIdToShowNameMap = config.getCateIdToShowNameMap();
        if (MapUtils.isEmpty(cateIdToShowNameMap)) {
            return null;
        }
        return cateIdToShowNameMap.get(skuCateId);
    }

    private String getAttrSimpleValuesByKey(List<StandardAttributeItemDTO> attrs, String key) {
        return attrs.stream()
                .filter(attr -> key.equals(attr.getAttrName()))
                .findFirst()
                .map(StandardAttributeItemDTO::getAttrValues)
                .filter(CollectionUtils::isNotEmpty)
                .map(attrValues -> attrValues.get(0))
                .map(StandardAttributeValueDTO::getSimpleValues)
                .filter(CollectionUtils::isNotEmpty)
                .map(simpleValues -> simpleValues.get(0))
                .orElse(null);
    }


    private String getTitle(StandardServiceProjectItemDTO itemDTO,List<AttrM> attrs, TaijiProjectAttrTitleOpt.TitleConfig titleConfig) {
        String title = StringUtils.EMPTY;

        //综合下的 ， 推拿 敷贴 单独拿标题
       if (isGeneralServiceType(attrs)){
           StandardAttributeDTO standardAttribute = itemDTO.getStandardAttribute();
           if (standardAttribute == null || CollectionUtils.isEmpty(standardAttribute.getAttrs())) {
               return title;
           }
           List<StandardAttributeItemDTO> ats = standardAttribute.getAttrs();
           String skuCateId = getAttrSimpleValuesByKey(ats, "skuCateId");
           if (StringUtils.isBlank(skuCateId)) {
               return title;
           }
           if (TUINA_SKU_CATE_ID.equals(skuCateId)) {// 推拿
               return getAttrSimpleValuesByKey(ats, "Tuina_suitable_people");
           } else if (COMPRESS_SKU_CATE_ID.equals(skuCateId)) {// 敷贴
               return getAttrSimpleValuesByKey(ats, "compress_suita_population");
           }
       }

        if (titleConfig.getFetchSource() == 1) {
            title = DealDetailUtils.getAttrSingleValueByAttrName(attrs, titleConfig.getTitleAttrKey());
        } else {
            title = getTitleFromStandardProject(itemDTO, titleConfig.getTitleAttrKey());
        }
        return title;
    }

    private boolean isGeneralServiceType(List<AttrM> attrs) {
        AttrM attrM = attrs.stream().filter(attr -> "综合".equals(attr.getValue()))
                .findFirst()
                .orElse(null);
        return attrM != null;
    }

    private String getTitleFromStandardProject(StandardServiceProjectItemDTO itemDTO, String titleAttrKey) {
        if (Objects.isNull(itemDTO) || Objects.isNull(itemDTO.getStandardAttribute()) || CollectionUtils.isEmpty(itemDTO.getStandardAttribute().getAttrs())) {
            return null;
        }
        return itemDTO.getStandardAttribute().getAttrs().stream()
                .filter(attr -> titleAttrKey.equals(attr.getAttrName()))
                .findFirst()
                .map(StandardAttributeItemDTO::getAttrValues)
                .filter(CollectionUtils::isNotEmpty)
                .map(attrValues -> attrValues.get(0))
                .map(StandardAttributeValueDTO::getSimpleValues)
                .filter(CollectionUtils::isNotEmpty)
                .map(simpleValues -> simpleValues.get(0))
                .orElse(null);
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<TitleConfig> titleConfigs;
        
        private Map<String, String>  cateIdToShowNameMap;
    }

    @Data
    public static class TitleConfig {
        private String serviceType;
        private String titleAttrKey;
        /**
         * 获取来源 1-行业属性 2-服务项目
         */
        private int fetchSource;
    }
}
