package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.struct.DealDetailStructAttrListBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModel;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrsModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.CommonAttrsVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrGroupVO;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import jodd.util.StringUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * created by leimengdan in 2021/12/15
 */
@Component("drivingReceiveModestructAttrsGroup")
public class DrivingReceiveModeStructAttrsGroupStrategy implements ModuleStrategy {

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.driving.deal.detail.config", defaultValue = "{}")
    private DrivingDealDetailConfig drivingDealDetailConfig;

    private static final String RECEIVING_METHOD_ATTR_NAME = "接送服务";

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        String abilityCode = DealDetailStructAttrListBuilder.CODE;
        List<DealDetailStructAttrModel> structAttrModels = activityCxt.getSource(abilityCode);
        if (CollectionUtils.isEmpty(structAttrModels)) {
            return null;
        }
        //拿到第一个团单（默认只有一个团单）
        DealDetailStructAttrModel dealDetailStructAttrModel = CollectUtils.firstValue(structAttrModels);
        if (dealDetailStructAttrModel == null) {
            return null;
        }
        List<StructAttrsModel> structAttrsModels = dealDetailStructAttrModel.getStructAttrsModels();
        //拿第一个属性列表组
        StructAttrsModel structAttrsModel = CollectUtils.firstValue(structAttrsModels);
        if (structAttrsModel == null || CollectionUtils.isEmpty(structAttrsModel.getStructAttrModels())) {
            return null;
        }
        String receivingMethod = structAttrsModel.getStructAttrModels().stream().filter(item -> item != null && RECEIVING_METHOD_ATTR_NAME.equals(item.getAttrName())).map(item -> CollectUtils.firstValue(item.getAttrValues()).toString()).findFirst().orElse(null);
        if (drivingDealDetailConfig == null || MapUtils.isEmpty(drivingDealDetailConfig.getReceivingMethod2ShowDocMap())) {
            return null;
        }
        String receivingMethodDoc = drivingDealDetailConfig.getReceivingMethod2ShowDocMap().get(receivingMethod);
        if (StringUtil.isEmpty(receivingMethodDoc)) {
            return null;
        }
        DealDetailStructAttrGroupVO dealDetailStructAttrGroupVO = buildDealDetailStructAttrGroupVO(receivingMethodDoc);
        return buildDealDetailModuleVO(dealDetailStructAttrGroupVO);
    }

    private DealDetailModuleVO buildDealDetailModuleVO(DealDetailStructAttrGroupVO dealDetailStructAttrGroupVO) {
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        dealDetailModuleVO.setDealStructAttrsModel2(dealDetailStructAttrGroupVO);
        return dealDetailModuleVO;
    }

    private DealDetailStructAttrGroupVO buildDealDetailStructAttrGroupVO(String content) {
        DealDetailStructAttrGroupVO dealDetailStructAttrGroupVO = new DealDetailStructAttrGroupVO();
        dealDetailStructAttrGroupVO.setContent(content);
        return dealDetailStructAttrGroupVO;
    }

    @Data
    private static class DrivingDealDetailConfig {
        private Map<String, String> receivingMethod2ShowDocMap;
    }
}
