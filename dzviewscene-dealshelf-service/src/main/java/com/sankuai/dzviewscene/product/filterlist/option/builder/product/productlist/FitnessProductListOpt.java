package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.filterlist.utils.FitnessCrossUtils;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@VPointOption(name = "运健行业相似团购",
        description = "运健行业相似团购",
        code = "FitnessProductListOpt")
public class FitnessProductListOpt extends ProductListVP<FitnessProductListOpt.Config> {

    private static final String ATTR_SEARCH_HIDDEN_STATUS = "attr_search_hidden_status";

    private static final String DEAL_STATUS_ATTR = "dealStatusAttr";

    private static final String PRODUCT_TYPE = "productType";

    @Override
    public List<ProductM> compute(ActivityCxt context, Param param, Config config) {
        if (CollectionUtils.isEmpty(param.getProductMS())) {
            return Collections.emptyList();
        }
        List<ProductM> filterProductList = getFilterList(param.getProductMS());
        int currentDealId = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.entityId);
        ProductM currentProduct = getCurrentProductM(filterProductList, currentDealId);
        if (currentProduct == null) {
            return Collections.emptyList();
        }
        // 健身通不展示相似团购
        if (FitnessCrossUtils.isFitnessCrossDeal(currentProduct)) {
            return Collections.emptyList();
        }
        // 走多次卡逻辑
        return executeForTimes(currentProduct, filterProductList, currentDealId);
    }

    /**
     * 多次卡逻辑
     */
    protected List<ProductM> executeForTimes(ProductM currentProduct, List<ProductM> filterProductList, int currentDealId) {
        List<ProductM> result = Lists.newArrayList();
        // 当前商品置顶
        result.add(currentProduct);
        // 多次卡团单
        result.addAll(getTimesDealList(filterProductList, currentDealId));

        if (result.size() <= 1) {
            return Lists.newArrayList();
        }
        return result;
    }

    /**
     * 多次卡团单按照团购价从低到高排序
     */
    protected List<ProductM> getTimesDealList(List<ProductM> list, int currentDealId) {
        List<ProductM> timesDealQueryList = list.stream().filter(ProductM::isTimesDealQueryFlag).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(timesDealQueryList)) {
            return Collections.emptyList();
        }
        // 列表中必须有多次卡团单
        if (timesDealQueryList.stream().noneMatch(ProductM::isTimesDeal)) {
            return Collections.emptyList();
        }
        // 列表中必须有单次卡团单
        if (timesDealQueryList.stream().allMatch(ProductM::isTimesDeal)) {
            return Collections.emptyList();
        }
        List<ProductM> result = timesDealQueryList.stream()
                .filter(productM -> productM.getProductId() != currentDealId)
                .sorted(Comparator.comparing(ProductM::getSalePrice))
                .collect(Collectors.toList());

        return result == null ? Collections.emptyList() : result;
    }

    protected ProductM getCurrentProductM(List<ProductM> list, int currentDealId) {
        return list.stream().filter(productM -> productM.getProductId() == currentDealId).findAny().orElse(null);
    }

    protected List<ProductM> getFilterList(List<ProductM> list) {
        return list.stream().filter(this::isValid).collect(Collectors.toList());
    }

    protected boolean isValid(ProductM productM) {
        return productM != null && productM.getSalePrice() != null
                && "deal".equals(productM.getAttr(PRODUCT_TYPE))
                && "true".equals(productM.getAttr(DEAL_STATUS_ATTR))
                && "false".equals(productM.getAttr(ATTR_SEARCH_HIDDEN_STATUS))
                && productM.getSale() != null;
    }

    @Data
    @VPointCfg
    public static class Config {
    }

}
