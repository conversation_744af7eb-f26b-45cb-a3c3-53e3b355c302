package com.sankuai.dzviewscene.product.filterlist.option.builder.product.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.DealProductMaterialM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Optional;


@VPointOption(name = "穿戴甲列表标题",
        description = "穿戴甲列表标题",
        code = "PressOnNailProductTitleOpt")
public class PressOnNailListTitleOpt extends ProductTitleVP<Void> {

    @Override
    public String compute(ActivityCxt context, Param param, Void unused) {
        ProductM productM = param.getProductM();
        String productTitle = productM.getTitle();
        List<DealProductMaterialM> materialList = productM.getMaterialList();
        if (CollectionUtils.isNotEmpty(materialList)) {
            Optional<DealProductMaterialM> materialMOptional = materialList.stream().filter(t -> StringUtils.isNotBlank(t.getName())).findAny();
            if (materialMOptional.isPresent()) {
                productTitle =  materialMOptional.get().getName() + " | " + productTitle;
            }
        }
        return productTitle;
    }

}
