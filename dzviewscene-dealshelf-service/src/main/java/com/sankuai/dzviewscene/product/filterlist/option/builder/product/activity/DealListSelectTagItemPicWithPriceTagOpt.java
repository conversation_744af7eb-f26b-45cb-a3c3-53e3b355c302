package com.sankuai.dzviewscene.product.filterlist.option.builder.product.activity;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductActivityTagsVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzActivityTagVO;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildReq;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildStrategyFactory;
import com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.ConstantUtils;
import com.sankuai.dzviewscene.productshelf.vu.enums.FloatTagPositionEnums;
import com.sankuai.dzviewscene.productshelf.vu.enums.RichLabelFrontWeightEnums;
import com.sankuai.dzviewscene.productshelf.vu.enums.RichLabelPositionEnums;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfShowTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.FrontSizeUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/12 21:06
 */
@VPointOption(name = "自主配置图片标签+比价标签",
        description = "",
        code = "DealListSelectTagItemPicWithPriceTagOpt")
public class DealListSelectTagItemPicWithPriceTagOpt extends ProductActivityTagsVP<DealListSelectTagItemPicWithPriceTagOpt.Config> {

    @Autowired
    private FloatTagBuildStrategyFactory tagBuildStrategyFactory;

    @Override
    public List<DzActivityTagVO> compute(ActivityCxt context, ProductActivityTagsVP.Param param, DealListSelectTagItemPicWithPriceTagOpt.Config config) {
        if (StringUtils.isEmpty(param.getProductM().getPicUrl())) {
            return null;
        }
        List<FloatTagVO> result = new ArrayList<>();
        // 营销标签
        List<FloatTagVO> marketingFloatTagList = this.getMarketingFloatTag(param, config);
        if (CollectionUtils.isNotEmpty(marketingFloatTagList)) {
            marketingFloatTagList.forEach(a -> {
                a.setPosition(FloatTagPositionEnums.LEFT_TOP.getPosition());
            });
            result.addAll(marketingFloatTagList);
        }
        // 比价标签
        FloatTagVO priceDisplayTag = this.getPriceDisplayTag(param);
        if (Objects.nonNull(priceDisplayTag)) {
            result.add(priceDisplayTag);
        }

        return result.stream().filter(Objects::nonNull).map(this::convertDzActivityTagVO).collect(Collectors.toList());
    }

    private DzActivityTagVO convertDzActivityTagVO(FloatTagVO floatTagVO) {
        if (Objects.isNull(floatTagVO)) {
            return null;
        }
        DzActivityTagVO dzActivityTagVO = new DzActivityTagVO();
        dzActivityTagVO.setLabel(Objects.nonNull(floatTagVO.getLabel()) ? floatTagVO.getLabel().getText() : null);
        dzActivityTagVO.setBackgroundImg(floatTagVO.getBackgroundImg());
        dzActivityTagVO.setImgUrl(Objects.nonNull(floatTagVO.getIcon()) ? floatTagVO.getIcon().getPicUrl() : null);
        dzActivityTagVO.setPosition(floatTagVO.getPosition());
        return dzActivityTagVO;
    }

    /**
     * 获取比价标签
     *
     * @return
     */
    private FloatTagVO getPriceDisplayTag(Param param) {
        // 判断和构建「比价标签」（包括全网低价标签、时间比价标签(可选)）
        DzTagVO priceDzTagVo = PriceDisplayUtils.buildPriceTagContainsProtectionAndGuarantee(param.getProductM(), param.getPlatform(),
                false, true, true, true);
        if (Objects.isNull(priceDzTagVo)) {
            return null;
        }
        return convert(priceDzTagVo);
    }

    private FloatTagVO convert(DzTagVO priceDzTagVo) {
        FloatTagVO floatTag = new FloatTagVO();
        floatTag.setPosition(FloatTagPositionEnums.LEFT_TOP.getPosition());
        floatTag.setLabel(new RichLabelVO(RichLabelPositionEnums.MIDDLE.getType(), RichLabelFrontWeightEnums.NORMAL.getType(), FrontSizeUtils.front10, ColorUtils.colorFFFFFF, priceDzTagVo.getName()));
        floatTag.setBackgroundImg(ConstantUtils.GRAY_BACKGROUND);
        return floatTag;
    }

    /**
     * 获取营销标签
     *
     * @param param
     * @param config
     * @return
     */
    private List<FloatTagVO> getMarketingFloatTag(Param param, Config config) {
        if (CollectionUtils.isEmpty(config.getTagStrategies())) {
            return new ArrayList<>();
        }
        FloatTagBuildReq req = new FloatTagBuildReq(param.getProductM());
        req.setShelfShowType(config.getShelfShowType());
        FloatTagVO floatTagVO = tagBuildStrategyFactory.getFirstValidTag(req, getOrderedFloatTagBuildMap(config, param.getFilterId()));
        return floatTagVO != null ? Lists.newArrayList(floatTagVO) : null;
    }

    private LinkedHashMap<String, FloatTagBuildCfg> getOrderedFloatTagBuildMap(Config config, long selectedFilterId) {
        List<String> orderedTagStrategies = config.getTagStrategies();
        String filterMatchStrategy = MapUtils.isNotEmpty(config.getFilterId2TagStrategyMap()) ? config.getFilterId2TagStrategyMap().get(selectedFilterId) : null;
        if (StringUtils.isNotEmpty(filterMatchStrategy)) {
            //修改静态的排序顺序(指定筛选优先展示指定标签)
            orderedTagStrategies = config.getTagStrategies().stream().sorted((element1, element2) -> {
                if (Objects.equals(element1, filterMatchStrategy)) {
                    return -1;
                } else if (Objects.equals(element2, filterMatchStrategy)) {
                    return 1;
                } else {
                    return 0;
                }
            }).collect(Collectors.toList());
        }
        //进行结果组装
        LinkedHashMap<String, FloatTagBuildCfg> cfgMap = new LinkedHashMap<>(orderedTagStrategies.size());
        for (String strategy : orderedTagStrategies) {
            FloatTagBuildCfg floatTagBuildCfg = MapUtils.isNotEmpty(config.getTagStrategyName2TagCfgMap()) ? config.getTagStrategyName2TagCfgMap().get(strategy) : null;
            cfgMap.put(strategy, floatTagBuildCfg);
        }
        return cfgMap;
    }


    @VPointCfg
    @Data
    public static class Config {
        private double headPicAspectRadio = 1;

        private int picWidth = 300;

        private int picHeight = 300;

        /**
         * 标签拼接的策略，有序。值为策略对应的类名
         */
        private List<String> tagStrategies;

        /**
         * 允许自定义标签拼接策略的配置
         * key - 策略名
         * value - 策略对应的配置
         */
        private Map<String, FloatTagBuildCfg> tagStrategyName2TagCfgMap;

        /**
         * 指定 filterId 下优先展示对应策略的标签
         * 如美团补贴筛选下，同时有美团补贴和新人特惠标签时，展示美团补贴
         * key - filterId
         * value - 策略名
         */
        private Map<Long, String> filterId2TagStrategyMap;

        /**
         * {@link ShelfShowTypeEnum}
         */
        private int shelfShowType;
    }
}
