package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.AbstractBarSkuListCreator;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 酒吧可选项目组参食sku列表构造器
 * <AUTHOR>
 * @date 2023/2/9 2:08 下午
 */
public class BarOptionalGroupDrinksAndMealsSkuListCreator extends AbstractBarSkuListCreator {

    @Override
    public boolean ideantify(boolean isMustGroupSku, List<SkuItemDto> skuList, BarDealDetailSkuListModuleOpt.Config config) {
        if (isMustGroupSku || CollectionUtils.isEmpty(skuList) || CollectionUtils.isEmpty(config.getDrinksSkuCateIds()) || CollectionUtils.isEmpty(config.getMealsSkuCateIds())) {
            return false;
        }
        List<Long> skuCategoryIds = skuList.stream().map(SkuItemDto::getProductCategory).collect(Collectors.toList());
        return CollectionUtils.containsAny(config.getDrinksSkuCateIds(), skuCategoryIds) && CollectionUtils.containsAny(config.getMealsSkuCateIds(), skuCategoryIds);
    }

    @Override
    public List<DealSkuGroupModuleVO> buildSkuListModules(List<SkuItemDto> skuItemDtos, BarDealDetailSkuListModuleOpt.Config config, int optionalNum, boolean hitDouHu) {
        List<DealSkuGroupModuleVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuItemDtos)) {
            return result;
        }
        String groupName = hitDouHu && StringUtils.isNotEmpty(config.getOptionalFormat()) ? String.format(config.getOptionalFormat(), skuItemDtos.size(), optionalNum) : String.format(config.getOptionalDrinksMealsSkusGroupNameFormat(), skuItemDtos.size(), optionalNum);
        addSkuListModule(skuItemDtos, groupName, result, config, false, hitDouHu);
        return result;
    }
}
