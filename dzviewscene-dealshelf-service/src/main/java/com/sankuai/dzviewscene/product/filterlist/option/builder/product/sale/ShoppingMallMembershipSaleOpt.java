package com.sankuai.dzviewscene.product.filterlist.option.builder.product.sale;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.enums.WarmUpStageEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSaleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/7/12
 */
@VPointOption(
        name = "商场会员卡详情页销量文案，销量区间化处理",
        description = "预热单不展示销量，普通团单正常展示，并且将销量进行区间化处理",
        code = "ShoppingMallMembershipSaleOpt")
public class ShoppingMallMembershipSaleOpt extends ProductSaleVP<String> {

    @Override
    public String compute(ActivityCxt context, Param param, String s) {
        ProductM productM = param.getProductM();
        if (productM.getSale() == null || StringUtils.isBlank(productM.getSale().getSaleTag())) {
            return StringUtils.EMPTY;
        }

        //预热单不展示销量
        WarmUpStageEnum.WarmUpStageResult warmUpStageResult = WarmUpStageEnum.getWarmUpStageResult(productM);
        if (warmUpStageResult.isValidWarmUpStage()) {
            return StringUtils.EMPTY;
        }

        // 销量区间化
        return buildSectionSaleTag(productM.getSale().getSale(), "已售");
    }

    public static String buildSectionSaleTag(int sales, String preSaleTemplate) {
        if (sales < 0) {
            return StringUtils.EMPTY;
        }
        if (sales >= 0 && sales <= 49) {
            return preSaleTemplate + String.valueOf(sales);
        }
        if (sales >= 50 && sales <= 99) {
            return preSaleTemplate + getIntervalSales(sales, 10) + "+";
        }
        if (sales >= 100 && sales <= 999) {
            return preSaleTemplate + getIntervalSales(sales, 100) + "+";
        }
        if (sales >= 1000) {
            int intervalSales = getIntervalSales(sales, 1000);
            if (intervalSales >= 10000) {
                return preSaleTemplate + String.format("%.1f万+", intervalSales / 10000.0);
            } else {
                return preSaleTemplate + intervalSales + "+";
            }
        }
        return StringUtils.EMPTY;
    }

    private static int getIntervalSales(int sales, int step) {
        return (sales / step) * step;
    }
}
