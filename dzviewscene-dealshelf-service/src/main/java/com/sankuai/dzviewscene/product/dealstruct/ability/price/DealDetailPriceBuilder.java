package com.sankuai.dzviewscene.product.dealstruct.ability.price;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.douhu.DealDetailDouhuFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.price.vpoints.DealDetailPriceVP;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailPriceModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/25 11:43 上午
 */
@Ability(code = DealDetailPriceBuilder.CODE,
        name = "团购详情模块价格组件构造能力",
        description = "团购详情模块标题构造能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE,
                DealDetailDouhuFetcher.CODE
        }
)
public class DealDetailPriceBuilder extends PmfAbility<List<DealDetailPriceModel>, DealDetailPriceParam, DealDetailPriceCfg> {

    public static final String CODE = "dealDetailPriceBuilder";

    private static final String RETAIL_PRICE_STYLE = "retailPriceStyle";

    @Override
    public CompletableFuture<List<DealDetailPriceModel>> build(ActivityCxt activityCxt, DealDetailPriceParam priceParam, DealDetailPriceCfg priceCfg) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        if (CollectionUtils.isEmpty(dealDetailInfoModels)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        List<DealDetailPriceModel> detailPriceModuleList = dealDetailInfoModels.stream().map(detailModel -> {
            if(detailModel == null) {
                return null;
            }
            String salePrice = detailModel.getSalePrice();
            String marketPrice = detailModel.getMarketPrice();
            String retailPriceStyle = getRetailPriceStyle(detailModel);
            DealDetailPriceVP<?> dealDetailPriceVP = findVPoint(activityCxt, DealDetailPriceVP.CODE);
            DealDetailPriceModel dealDetailPriceModel =
                    dealDetailPriceVP.execute(activityCxt, DealDetailPriceVP.Param.builder()
                            .salePrice(salePrice)
                            .marketPrice(marketPrice)
                            .retailPriceStyle(retailPriceStyle)
                            .build());
            return dealDetailPriceModel;
        }).filter(price -> price != null).collect(Collectors.toList());
        return CompletableFuture.completedFuture(detailPriceModuleList);
    }

    private String getRetailPriceStyle(DealDetailInfoModel detailModel) {
        if (detailModel == null || CollectionUtils.isEmpty(detailModel.getDealAttrs())) {
            return null;
        }
        return detailModel.getDealAttrs().stream().filter(attr -> attr != null && attr.getName() != null && attr.getName().equals(RETAIL_PRICE_STYLE))
                .findFirst()
                .map(AttrM::getValue)
                .orElse(null);
    }
}
