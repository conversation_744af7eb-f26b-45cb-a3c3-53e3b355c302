package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.mppack.product.base.enums.PackCategoryEnum;
import com.sankuai.mppack.product.client.query.dto.ProductRelationDTO;
import com.sankuai.mppack.product.client.query.enums.QueryIdDimension;
import com.sankuai.mppack.product.client.query.request.PackProductByIdRequest;
import com.sankuai.mppack.product.client.query.request.SelectAndFilterParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 获取门店全量在线打包商品
 */

@Component
@Slf4j
public class PoiPackProductQueryHandler implements QueryHandler {

    @Resource
    private CompositeAtomService compositeAtomService;

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.poi.pack.select.category.name", defaultValue = "[]")
    private static List<String> selectCategoryList;

    @Override
    public CompletableFuture<ProductGroupM> query(ActivityCxt ctx, String groupName, Map<String, Object> groupParams) {
        return compositeAtomService.queryPackProductIdByShop(buildPackProductRequestByShopId(ctx, groupParams)).thenApply(dealIds -> {
            ProductGroupM productGroupM = new ProductGroupM();
            productGroupM.setProducts(buildProducts(dealIds));
            productGroupM.setTotal(CollectionUtils.isEmpty(dealIds) ? 0 : dealIds.size());
            return productGroupM;
        });
    }

    // 查询打包商品维度（有区分）：普通团购货架为POI（门店维度），商场货架为MALL_ID（商场ID），这里暂时只做POI
    private PackProductByIdRequest buildPackProductRequestByShopId(ActivityCxt ctx, Map<String, Object> params) {
        long mtPoiId = ParamsUtil.getLongSafely(ctx, ShelfActivityConstants.Params.mtPoiIdL);
        SelectAndFilterParam selectAndFilterParam = buildSelectAndFilterParam();

        PackProductByIdRequest request = new PackProductByIdRequest();
        request.setQueryIds(Lists.newArrayList(mtPoiId));
        request.setQueryIdDimension(QueryIdDimension.POI);
        request.setSelectAndFilterParam(selectAndFilterParam);
        return request;

    }

    private SelectAndFilterParam buildSelectAndFilterParam() {
        SelectAndFilterParam selectAndFilterParam = new SelectAndFilterParam();
        Set<PackCategoryEnum> selectCategoryEnumSet = new HashSet<>();
        // 综这边目前都是GENERAL_GROUP、GENERAL_MALL_CART
        if (CollectionUtils.isEmpty(selectCategoryList)) {
            return selectAndFilterParam;
        }
        selectCategoryEnumSet.addAll(selectCategoryList.stream()
                .map(category -> {
                    try {
                        return PackCategoryEnum.valueOf(category);
                    } catch (Exception e) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));
        selectAndFilterParam.setSelectCategoryEnumSet(selectCategoryEnumSet);
        return selectAndFilterParam;
    }

    private List<ProductM> buildProducts(List<ProductRelationDTO> products) {
        if (CollectionUtils.isEmpty(products)) {
            return Lists.newArrayList();
        }
        return products.stream().map(dto -> {
            ProductM productM = new ProductM();
            productM.setProductId(dto.getProductId().intValue());
            productM.setProductType(ProductTypeEnum.PACK.getType());
            return productM;
        }).collect(Collectors.toList());
    }

}
