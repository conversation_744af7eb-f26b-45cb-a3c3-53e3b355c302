package com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.DealDetailSkuListsBuilder;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/27 10:43 下午
 */

@VPoint(name = "团购详情sku展示优先级变化点", description = "团购详情sku展示优先级变化点", code = SkuPriorityVP.CODE, ability = DealDetailSkuListsBuilder.CODE)
public abstract class SkuPriorityVP<T> extends PmfVPoint<Integer, SkuPriorityVP.Param, T> {

    public static final String CODE = "SkuPriorityVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private SkuItemDto skuItemDto;
        private List<ProductSkuCategoryModel> productCategories;
    }
}
