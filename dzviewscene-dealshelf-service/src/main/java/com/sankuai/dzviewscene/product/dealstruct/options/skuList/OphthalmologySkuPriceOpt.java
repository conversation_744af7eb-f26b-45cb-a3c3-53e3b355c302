package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuPriceVP;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/9/8 5:29 下午
 */
@VPointOption(name = "眼科sku价格变化点"
        , description = "眼科sku价格变化点"
        ,code = OphthalmologySkuPriceOpt.CODE, isDefault = false)
public class OphthalmologySkuPriceOpt extends SkuPriceVP<OphthalmologySkuPriceOpt.Config> {

    public static final String CODE = "OphthalmologySkuPriceOpt";

    private static final String DEFAULT_FORMAT = "%s元";

    private static final String NO_SHOW_PRICE_FLAG = "不展示价格标识";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        return StringUtils.EMPTY;
    }

    @Data
    @VPointCfg
    public static class Config {
        private String format;
    }
}
