package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.productshelf.vu.vo.CountdownLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/8 5:23 下午
 */
@VPoint(name = "商品活动结束时间", description = "item-ActivityEndTime", code = ProductActivityEndTimeVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductActivityEndTimeVP<T> extends PmfVPoint<CountdownLabelVO, ProductActivityEndTimeVP.Param, T> {
    public static final String CODE = "ProductActivityEndTimeVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;
    }
}
