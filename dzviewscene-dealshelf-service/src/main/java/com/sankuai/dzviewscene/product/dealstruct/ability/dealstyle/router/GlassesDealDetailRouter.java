package com.sankuai.dzviewscene.product.dealstruct.ability.dealstyle.router;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealstyle.DealStyleSwitchCfg;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.detailSwitch.SwitchModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.detailSwitch.SwitchModuleModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/15 4:07 下午
 */
@Component
public class GlassesDealDetailRouter implements DealCategoryRouter {

    @Override
    public boolean identify(DealStyleSwitchCfg cfg, int dealCategory) {
        return Lists.newArrayList(406).contains(dealCategory);
    }

    @Override
    public SwitchModel compute(ActivityCxt context, Param param, DealStyleSwitchCfg config) {
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        // 1.service_type= “光学框架镜、离焦镜”
        // 2.取镜时长
        DealStyleSwitchCfg.MedicalHealthCondition condition = config.getMedicalHealthCondition();
        if (condition == null || !condition.openMatch) {
            return null;
        }
        // 为空时->不满足条件false
        boolean isMatch = matchItems(condition, dealDetailInfoModel);
        if (!isMatch) {
            return null;
        }
        SwitchModuleModel switchModuleModel = new SwitchModuleModel();
        switchModuleModel.setModuleValue(CUSTOM_STRUCTURE_MODULE);
        switchModuleModel.setModuleKey(param.getModuleKey());
        return buildSwitchModel(switchModuleModel, null);
    }

    private boolean matchItems(DealStyleSwitchCfg.MedicalHealthCondition condition, DealDetailInfoModel dealDetailInfoModel) {
        DealDetailDtoModel model = dealDetailInfoModel.getDealDetailDtoModel();
        List<AttrM> dealAttrs = dealDetailInfoModel.getDealAttrs();
        boolean matchOr = CollectionUtils.isEmpty(condition.getOrItems())
                || condition.getOrItems().stream().anyMatch(item -> match(dealAttrs, model, item));
        boolean matchAnd = CollectionUtils.isEmpty(condition.getAndItems())
                || condition.getAndItems().stream().allMatch(item -> match(dealAttrs, model, item));
        return matchOr && matchAnd;
    }

    // 条件全部满足 -> true 空条件判断为满足
    private boolean match(List<AttrM> attrMS, DealDetailDtoModel model, DealStyleSwitchCfg.ConditionItem item) {
        if (MapUtils.isNotEmpty(item.getMatchDealAttrValue())) {
            boolean matchDeal = item.getMatchDealAttrValue().entrySet().stream()
                    .allMatch(r -> r.getValue().contains(getAttrValue(attrMS, r.getKey())));
            if (!matchDeal) {
                return false;
            }
        }
        if (CollectionUtils.isNotEmpty(item.getHasDealAttr())) {
            boolean hasDealAttr = item.getHasDealAttr().stream()
                    .allMatch(r -> StringUtils.isNotEmpty(getAttrValue(attrMS, r)));
            if (!hasDealAttr) {
                return false;
            }
        }
        if (CollectionUtils.isNotEmpty(item.getHasSkuAttr())) {
            boolean hasSkuAttr = false;
            if (model != null && model.getSkuUniStructuredDto() != null
                    && CollectionUtils.isNotEmpty(model.getSkuUniStructuredDto().getMustGroups()) && CollectionUtils
                            .isNotEmpty(model.getSkuUniStructuredDto().getMustGroups().get(0).getSkuItems())) {
                List<String> skuAttrList = model.getSkuUniStructuredDto().getMustGroups().get(0).getSkuItems().get(0)
                        .getAttrItems().stream().map(SkuAttrItemDto::getAttrName).collect(Collectors.toList());
                hasSkuAttr = skuAttrList.containsAll(item.getHasSkuAttr());
            }
            if (!hasSkuAttr) {
                return false;
            }
        }
        return true;
    }

    private String getAttrValue(List<AttrM> attrMS, String attrName) {
        if (CollectionUtils.isEmpty(attrMS)) {
            return null;
        }
        AttrM attrM = attrMS.stream()
                .filter(Objects::nonNull)
                .filter(attr -> attr.getName().equals(attrName))
                .findFirst()
                .orElse(null);
        return attrM == null ? null: attrM.getValue();
    }

}
