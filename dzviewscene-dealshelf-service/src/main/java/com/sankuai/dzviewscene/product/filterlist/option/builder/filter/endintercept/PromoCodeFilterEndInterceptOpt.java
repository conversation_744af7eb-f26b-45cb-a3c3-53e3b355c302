package com.sankuai.dzviewscene.product.filterlist.option.builder.filter.endintercept;

import com.dianping.frog.sdk.util.CollectionUtils;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.filter.vp.FilterEndInterceptVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterBtnVO;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterVO;
import lombok.Data;

import java.util.Iterator;
import java.util.List;

/**
 * @description : 优惠码货架Tab栏后置处理，过滤掉神券]、[秒杀]、[特团]TAB
 * @date : 2025/4/15
 */
@VPointOption(name = "优惠码货架Tab栏后置处理", description = "优惠码货架Tab栏后置处理，过滤掉神券]、[秒杀]、[特团]TAB", code = "PromoCodeFilterEndInterceptOpt")
public class PromoCodeFilterEndInterceptOpt extends FilterEndInterceptVP<PromoCodeFilterEndInterceptOpt.Config> {


    @Override
    public Void compute(ActivityCxt context, Param param, Config config) {
        if (config == null || CollectionUtils.isEmpty(config.getFilterTabNames()) || CollectionUtils.isEmpty(param.getFilterList())) {
            return null;
        }
        DzFilterVO dzFilterVO = param.getFilterList().get(0);
        if (dzFilterVO == null || CollectionUtils.isEmpty(dzFilterVO.getChildren())) {
            return null;
        }
        List<DzFilterBtnVO> children = dzFilterVO.getChildren();
        Iterator<DzFilterBtnVO> iterator = children.iterator();
        while (iterator.hasNext()) {
            DzFilterBtnVO btnVO = iterator.next();
            if (config.getFilterTabNames().contains(btnVO.getName())) {
                iterator.remove();
                continue;
            }
            if (!CollectionUtils.isEmpty(btnVO.getChildren())) {
                removeFilterTabs(btnVO.getChildren(), config.getFilterTabNames());
            }
        }
        return null;
    }

    private void removeFilterTabs(List<DzFilterBtnVO> children, List<String> filterTabNames) {
        Iterator<DzFilterBtnVO> iterator = children.iterator();
        while (iterator.hasNext()) {
            DzFilterBtnVO btnVO = iterator.next();
            if (filterTabNames.contains(btnVO.getName())) {
                iterator.remove();
                continue;
            }
            if (!CollectionUtils.isEmpty(btnVO.getChildren())) {
                removeFilterTabs(btnVO.getChildren(), filterTabNames);
            }
        }
    }


    @VPointCfg
    @Data
    public static class Config {
        private List<String> filterTabNames;
    }
}
