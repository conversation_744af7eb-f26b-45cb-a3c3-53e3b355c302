package com.sankuai.dzviewscene.product.filterlist.option.builder.product.producttags;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONValidator;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DataUtils;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTagsVP;
import com.sankuai.dzviewscene.product.shelf.utils.PressOnNailUtils;
import com.sankuai.dzviewscene.shelf.business.shelf.medical.builder.AbstractFloorsBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@VPointOption(name = "穿戴甲", description = "穿戴甲", code = "PressOnNailListProductTagsOpt")
public class PressOnNailListProductTagsOpt extends ProductTagsVP<PressOnNailListProductTagsOpt.Config> {

    private static final String DEFAULT_COLOR = "#777777";
    private static final String HIGHLIGHT_COLOR = "#FF6633";
    private static final int DEFAULT_FONT = 11;

    @Override
    public List<String> compute(ActivityCxt context, Param param, Config config) {
        if (param.getProductM() == null || CollectionUtils.isEmpty(param.getProductM().getExtAttrs()) || PressOnNailUtils.checkExclusive(context)) {
            return Collections.emptyList();
        }
        List<String> tags = new ArrayList<>();
        List<AttrM> attrs = param.getProductM().getExtAttrs();
        if (DealDetailUtils.isCanWearAtStore(attrs)) {
            tags.add(richText("到店免费佩戴", HIGHLIGHT_COLOR));
        }
        String wearingNailsType = DealDetailUtils.getAttrSingleValueByAttrName(attrs, "wearingNailsType");
        if (StringUtils.isNotBlank(wearingNailsType)) {
            tags.add(richText(wearingNailsType, DEFAULT_COLOR));
        }
        String nailAdditionalItem = DealDetailUtils.getAttrSingleValueByAttrName(attrs, "nail_additional_item");
        if (StringUtils.isNotBlank(nailAdditionalItem)) {
            List<String> additionalItemList;
            if (JSONValidator.from(nailAdditionalItem).validate()) {
                additionalItemList = JSON.parseArray(nailAdditionalItem, String.class);
            }else {
                additionalItemList = DataUtils.toList(nailAdditionalItem, ",");
            }
            if(CollectionUtils.isNotEmpty(additionalItemList)) {
                tags.add(richText(additionalItemList.get(0), DEFAULT_COLOR));
            }
        }
        return tags;
    }

    private String richText(String str, String color) {
        AbstractFloorsBuilderExtAdapter.RichLabel richLabel = new AbstractFloorsBuilderExtAdapter.RichLabel(str, color, DEFAULT_FONT);
        return JsonCodec.encode(richLabel);
    }

    @Data
    @VPointCfg
    public static class Config {

    }
}
