package com.sankuai.dzviewscene.product.filterlist.option.builder.product.picpadding;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPicActivityTagVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzActivityTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * @description : 优惠码的图片填充处理, 支持买过图片标签
 * @date : 2025/4/17
 */
@VPointOption(name = "优惠码的图片填充处理类",
        description = "优优惠码的图片填充处理类，用于展示对应的浮层标签，支持买过图片标签",
        code = "PromoCodePicActivityTagOpt")
public class PromoCodePicActivityTagOpt extends ProductPicActivityTagVP<PromoCodePicActivityTagOpt.Config> {
    @Override
    public List<DzActivityTagVO> compute(ActivityCxt context, Param param, PromoCodePicActivityTagOpt.Config config) {
        ProductM productM = param.getProductM();
        if (productM == null || CollectionUtils.isEmpty(productM.getExtAttrs())) {
            return null;
        }
        TagCfg attrsTagCfg = findFirstMatchAttrTagCfg(config.getProductExtAttrs2TagCfg(), productM.getExtAttrs());
        return buildFloatTag(attrsTagCfg);
    }

    private TagCfg findFirstMatchAttrTagCfg(Map<String, TagCfg> productAttrs2TagCfg, List<AttrM> ExtAttrs) {
        return ExtAttrs.stream().filter(attr -> productAttrs2TagCfg.containsKey(attr.getName())
                        && Integer.parseInt(attr.getValue()) >= productAttrs2TagCfg.get(attr.getName()).getTargetNums())
                .map(attr -> productAttrs2TagCfg.get(attr.getName())).findFirst().orElse(null);
    }

    private List<DzActivityTagVO> buildFloatTag(TagCfg tagCfg) {
        if (tagCfg == null) {
            return null;
        }
        DzActivityTagVO tagVO = new DzActivityTagVO();
        tagVO.setImgUrl(tagCfg.getPicUrl());
        tagVO.setLabel(tagCfg.getLabel());
        tagVO.setBackgroundImg(tagCfg.getBackgroundImg());
        tagVO.setPosition(tagCfg.getPosition() != null ? tagCfg.getPosition() : 1);// 默认设置在左上角
        return Lists.newArrayList(tagVO);
    }

    @Data
    @VPointCfg
    public static class Config {
        /**
         * 商品扩展属性 -> 标签配置的映射
         */
        private Map<String, TagCfg> productExtAttrs2TagCfg;
    }

    @Data
    public static class TagCfg {
        /**
         * 图片链接
         */
        private String picUrl;
        /**
         * 标签
         */
        private String label;

        /**
         * 背景图，仅label才有效
         */
        private String backgroundImg;
        /**
         * 位置：位置（1左上单个，2右上单个，3右下单个，4左下单个，5底部平铺）
         */
        private Integer position;
        /**
         * 触发数量，只有同时大于或者等于目标数量才展示
         */
        private Integer targetNums = 1;
    }
}
