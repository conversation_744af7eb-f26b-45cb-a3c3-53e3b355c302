package com.sankuai.dzviewscene.product.filterlist.option.builder.product.activity;

import com.dianping.cat.Cat;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductActivityTagsVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzActivityTagVO;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.enums.FloatTagPositionEnums;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils.buildLowestPriceFloatTag;
import static com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils.buildPlatformPromoTag;

/**
 * <AUTHOR>
 * @date 2023/02/23
 */
@VPointOption(name = "酒吧货架活动可选项",
        description = "",
        code = "BarProductActivityTagsOpt")
public class BarProductActivityTagsOpt extends ProductActivityTagsVP<BarProductActivityTagsOpt.Config> {

    /**
     * 上方-商家推荐标签
     */
    private static final FloatTagVO SHOP_RECOMMEND_TOP_TAG = buildShopRecommendTag();

    private static FloatTagVO buildShopRecommendTag() {
        DzPictureComponentVO floatTagPic = new DzPictureComponentVO();
        floatTagPic.setPicUrl("https://p1.meituan.net/dprainbow/6b8ad53cc2d030a93b85d0c1d34bc8bd8626.png");
        floatTagPic.setAspectRadio(2.77778);
        FloatTagVO floatTagVO = new FloatTagVO();
        floatTagVO.setIcon(floatTagPic);
        floatTagVO.setPosition(FloatTagPositionEnums.LEFT_TOP.getPosition());
        return floatTagVO;
    }

    @Override
    public List<DzActivityTagVO> compute(ActivityCxt context, Param param, Config config) {
        if (StringUtils.isEmpty(param.getProductM().getPicUrl())) {
            return null;
        }
        List<FloatTagVO> floatTagVOList = new ArrayList<>();
        // 活动角标
        FloatTagVO floatTag = getActivityFloatTag(param.getProductM(), param.getFilterId(), config);
        if (Objects.nonNull(floatTag)) {
            floatTagVOList.add(floatTag);
        }
        // 全网低价角标
        FloatTagVO lowestPriceTag = this.getLowestPriceFloatTag(param, Objects.nonNull(floatTag));
        if (Objects.nonNull(lowestPriceTag)) {
            floatTagVOList.add(lowestPriceTag);
        }

        return floatTagVOList.stream().filter(Objects::nonNull).map(this::convertDzActivityTagVO).collect(Collectors.toList());
    }

    private DzActivityTagVO convertDzActivityTagVO(FloatTagVO floatTagVO) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.filterlist.option.builder.product.activity.BarProductActivityTagsOpt.convertDzActivityTagVO(com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO)");
        if (Objects.isNull(floatTagVO) || Objects.isNull(floatTagVO.getIcon())) {
            return null;
        }
        DzActivityTagVO dzActivityTagVO = new DzActivityTagVO();
        dzActivityTagVO.setImgUrl(floatTagVO.getIcon().getPicUrl());
        return dzActivityTagVO;
    }

    /**
     * 获取「全网低价」标签
     *
     * @param param
     * @param existActivityPic 是否已经有一个活动标签
     * @return
     */
    private FloatTagVO getLowestPriceFloatTag(Param param, boolean existActivityPic) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.filterlist.option.builder.product.activity.BarProductActivityTagsOpt.getLowestPriceFloatTag(ProductActivityTagsVP$Param,boolean)");
        return buildLowestPriceFloatTag(param.getProductM(), existActivityPic);
    }

    public FloatTagVO getActivityFloatTag(ProductM productM, long filterId, Config config) {
        // 价格力相关 -> 「美团补贴」角标
        FloatTagVO platformPromoTag = buildPlatformPromoTag(productM);
        if (platformPromoTag != null) {
            return platformPromoTag;
        }

        //玩美季
        FloatTagVO perfectTag = getPerfectBottomTagWithFilter(productM);
        if (perfectTag != null) {
            return perfectTag;
        }
        //普通活动
        FloatTagVO normalActivityTag = getNormalActivityTagWithFilter(productM, config);
        if (normalActivityTag != null) {
            return normalActivityTag;
        }
        //商家推荐
        return getShopRecommendTopTagWithFilter(productM, filterId);
    }

    private FloatTagVO getNormalActivityTagWithFilter(ProductM productM, Config config) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.filterlist.option.builder.product.activity.BarProductActivityTagsOpt.getNormalActivityTagWithFilter(ProductM,BarProductActivityTagsOpt$Config)");
        ProductActivityM firstNormalActivity = CollectUtils.firstValue(PerfectActivityBuildUtils.getNormalActivityList(productM.getActivities()));
        if (firstNormalActivity == null || StringUtils.isEmpty(firstNormalActivity.getUrl())) {
            return null;
        }
        DzPictureComponentVO floatTagPic = new DzPictureComponentVO();
        floatTagPic.setPicUrl(firstNormalActivity.getUrl());
        floatTagPic.setAspectRadio(getAspectRadio(config.activityAspectRadio, 2.77778));
        FloatTagVO floatTagVO = new FloatTagVO();
        floatTagVO.setIcon(floatTagPic);
        floatTagVO.setPosition(FloatTagPositionEnums.LEFT_TOP.getPosition());
        return floatTagVO;
    }

    private double getAspectRadio(double aspectRadio, double defaultAspectRadio) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.filterlist.option.builder.product.activity.BarProductActivityTagsOpt.getAspectRadio(double,double)");
        if (aspectRadio <= 0.0) {
            return defaultAspectRadio;
        }
        return aspectRadio;
    }

    protected FloatTagVO getShopRecommendTopTagWithFilter(ProductM productM, long filterId) {
        if (ProductMAttrUtils.isShopRecommend(productM, filterId)) {
            return SHOP_RECOMMEND_TOP_TAG;
        }
        return null;
    }

    protected FloatTagVO getPerfectBottomTagWithFilter(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.filterlist.option.builder.product.activity.BarProductActivityTagsOpt.getPerfectBottomTagWithFilter(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        List<FloatTagVO> tags = PerfectActivityBuildUtils.buildHeadPicFloatTagVO(productM);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(tags)) {
            return null;
        }
        return tags.get(0);
    }


    @VPointCfg
    @Data
    public static class Config {
        /**
         * 活动宽高比
         */
        private double activityAspectRadio;

    }
}
