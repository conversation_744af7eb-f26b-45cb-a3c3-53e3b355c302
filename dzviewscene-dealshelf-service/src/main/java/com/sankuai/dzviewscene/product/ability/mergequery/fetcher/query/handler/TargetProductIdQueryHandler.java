package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.QueryConstants;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductIdM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 通过参数中的商品id来构造召回结果
 */
@Component
public class TargetProductIdQueryHandler implements QueryHandler {

    @Override
    public CompletableFuture<ProductGroupM> query(ActivityCxt ctx, String groupName, Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler.TargetProductIdQueryHandler.query(com.sankuai.athena.viewscene.framework.ActivityCxt,java.lang.String,java.util.Map)");
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setTotal(1);
        productGroupM.setHasNext(false);
        productGroupM.setProducts(buildProducts(groupName, ctx.getParameters(), params));
        return CompletableFuture.completedFuture(productGroupM);
    }

    private List<ProductM> buildProducts(String groupName,
                                         Map<String, Object> params,
                                         Map<String, Object> groupParams) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler.TargetProductIdQueryHandler.buildProducts(java.lang.String,java.util.Map,java.util.Map)");
        ProductM productM = new ProductM();
        productM.setGroupName(groupName);
        productM.setId(buildProductId(params, groupParams));
        return Lists.newArrayList(productM);
    }

    private ProductIdM buildProductId(Map<String, Object> params, Map<String, Object> groupParams) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler.TargetProductIdQueryHandler.buildProductId(java.util.Map,java.util.Map)");
        ProductIdM productIdM = new ProductIdM();
        productIdM.setId(ParamsUtil.getLongSafely(params, PmfConstants.Params.productIdL));
        productIdM.setType(ParamsUtil.getIntSafely(groupParams, QueryConstants.Params.productType));
        return productIdM;
    }
}
