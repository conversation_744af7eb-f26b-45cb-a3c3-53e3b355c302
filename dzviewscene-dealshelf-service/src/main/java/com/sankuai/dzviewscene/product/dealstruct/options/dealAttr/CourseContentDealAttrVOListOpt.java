package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/6/20 5:22 下午
 */
@VPointOption(name = "课程内容构造DealAttrVO列表变化点", description = "课程内容构造DealAttrVO列表变化点",code = CourseContentDealAttrVOListOpt.CODE, isDefault = false)
public class CourseContentDealAttrVOListOpt extends DealAttrVOListVP<CourseContentDealAttrVOListOpt.Config> {

    public static final String CODE = "CourseContentDealAttrVOListOpt";

    private static final String COURSE_CONTENT_ATTR_NAME = "courseContent";

    private static final String COURSE_CONTENT_GROUP_NAME = "课程内容";

    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        if (config == null || param == null || CollectionUtils.isEmpty(param.getDealAttrs())) {
            return null;
        }
        DealDetailStructAttrModuleVO dealDetailStructAttrModuleVO = buildCourseContentStateDealDetailStructAttrModuleVO(param.getDealAttrs());
        if (dealDetailStructAttrModuleVO == null) {
            return null;
        }
        List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOS = Lists.newArrayList(dealDetailStructAttrModuleVO);
        DealDetailStructAttrModuleGroupModel dealDetailStructAttrModuleGroupModel = buildDealDetailStructAttrModuleGroupModel(COURSE_CONTENT_GROUP_NAME, dealDetailStructAttrModuleVOS);
        if (dealDetailStructAttrModuleGroupModel == null) {
            return null;
        }
        return Lists.newArrayList(dealDetailStructAttrModuleGroupModel);
    }

    private DealDetailStructAttrModuleGroupModel buildDealDetailStructAttrModuleGroupModel(String groupName, List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOS) {
        if (CollectionUtils.isEmpty(dealDetailStructAttrModuleVOS)) {
            return null;
        }
        DealDetailStructAttrModuleGroupModel dealDetailStructAttrModuleGroupModel = new DealDetailStructAttrModuleGroupModel();
        dealDetailStructAttrModuleGroupModel.setGroupName(groupName);
        dealDetailStructAttrModuleGroupModel.setDealDetailStructAttrModuleVOS(dealDetailStructAttrModuleVOS);
        return dealDetailStructAttrModuleGroupModel;
    }

    private DealDetailStructAttrModuleVO buildCourseContentStateDealDetailStructAttrModuleVO(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        String courseContentStr = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, COURSE_CONTENT_ATTR_NAME);
        CourseContentModel courseContentModel = getCourseContentModel(courseContentStr);
        if (courseContentModel == null || CollectionUtils.isEmpty(courseContentModel.getContentList())) {
            return null;
        }
        DealDetailStructAttrModuleVO dealDetailStructAttrModuleVO = new DealDetailStructAttrModuleVO();
        dealDetailStructAttrModuleVO.setAttrValues(courseContentModel.getContentList());
        return dealDetailStructAttrModuleVO;
    }

    private CourseContentModel getCourseContentModel(String courseContentStr) {
        if(StringUtils.isEmpty(courseContentStr)) {
            return null;
        }
        return JsonCodec.decode(courseContentStr, CourseContentModel.class);
    }


    @Data
    @VPointCfg
    public static class Config {

    }

    @Data
    static private class CourseContentModel {
        private List<String> contentList;
        private String title;
    }

}
