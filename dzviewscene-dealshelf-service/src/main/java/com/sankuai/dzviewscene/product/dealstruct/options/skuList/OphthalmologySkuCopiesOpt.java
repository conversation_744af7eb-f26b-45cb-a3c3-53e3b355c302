package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuCopiesVP;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * @author: wuweizhen
 * @Date: 2023/3/22 16:32
 * @Description:
 */
@VPointOption(name = "眼科sku份数变化点"
        , description = "眼科sku份数变化点",
        code = OphthalmologySkuCopiesOpt.CODE, isDefault = false)

public class OphthalmologySkuCopiesOpt extends SkuCopiesVP<DefaultSkuCopiesOpt.Config> {

    public static final String CODE = "OphthalmologySkuCopiesOpt";

    private static final String DEFAULT_FORMAT = "%s份";

    @Override
    public String compute(ActivityCxt context, SkuCopiesVP.Param param, DefaultSkuCopiesOpt.Config config) {
        return StringUtils.EMPTY;
    }

}