package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;


/**
 * <AUTHOR>
 * @since 2023/4/9 13:26
 */
@VPoint(name = "商品按钮", description = "商品按钮", code = ProductButtonVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductButtonVP<T> extends PmfVPoint<DzSimpleButtonVO, ProductButtonVP.Param, T> {
    public static final String CODE = "ProductButtonVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;
    }
}
