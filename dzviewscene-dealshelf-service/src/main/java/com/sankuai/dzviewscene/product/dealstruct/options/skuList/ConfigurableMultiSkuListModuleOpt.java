package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Data;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@VPointOption(name = "可配置的多SKU列表", description = "可配置的多SKU列表", code = ConfigurableMultiSkuListModuleOpt.CODE)
public class ConfigurableMultiSkuListModuleOpt extends SkuListModuleVP<ConfigurableMultiSkuListModuleOpt.Config> {

    public static final String CODE = "ConfigurableMultiSkuListModuleOpt";

    @Override
    public List<DealDetailSkuListModuleGroupModel> compute(ActivityCxt activityCxt, Param param, Config config) {
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        if (dealDetailInfoModel == null || dealDetailInfoModel.getDealDetailDtoModel() == null
                || dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto() == null
                || org.apache.commons.collections4.CollectionUtils.isEmpty(
                        dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto().getMustGroups())) {
            return null;
        }
        List<MustSkuItemsGroupDto> mustSkuItemsGroupDtoList =dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto().getMustGroups();
        if (ObjectUtils.isEmpty(mustSkuItemsGroupDtoList)) {
            return null;
        }
        List<SkuItemDto> skuItemDtoList = mustSkuItemsGroupDtoList.stream()
                .map(MustSkuItemsGroupDto::getSkuItems)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        if(ObjectUtils.isEmpty(skuItemDtoList)) {
            return null;
        }
        return buildResultModel(config, skuItemDtoList, dealDetailInfoModel.getProductCategories());
    }

    private String buildSubTitle(SingleSkuCfg cfg, SkuItemCfg skuItemCfg, List<String> strs) {
        return ObjectUtils.isEmpty(strs) ? null : Joiner.on(cfg.getSubTitleSplit()).join(
                strs.stream().map(value -> skuItemCfg.getSubTitlePrefixString() + value).collect(Collectors.toList()));
    }

    public String getAttrValueSingleValue(SkuItemCfg skuItemCfg, List<SkuAttrItemDto> attrItems, String attrName) {
        if (ObjectUtils.isEmpty(attrItems)) {
            return null;
        }
        return attrItems.stream().filter(attrItem -> attrItem.getAttrName().equals(attrName))
                .map(SkuAttrItemDto::getAttrValue).filter(Objects::nonNull).findFirst().orElse(null);
    }

    public List<String> getSkuAttrItemValue(SkuItemCfg skuItemCfg, List<SkuAttrItemDto> attrItems, String attrName) {
        if (ObjectUtils.isEmpty(attrItems)) {
            return null;
        }
        return attrItems.stream().filter(attrItem -> attrItem.getAttrName().equals(attrName))
                .map(SkuAttrItemDto::getAttrValue).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<DealDetailSkuListModuleGroupModel> buildResultModel(Config config, List<SkuItemDto> skuItemDtoList,
            List<ProductSkuCategoryModel> productSkuCategoryModels) {
        if (ObjectUtils.isEmpty(config)) {
            return null;
        }
        List<SingleSkuCfg> skuCfgList = Optional.ofNullable(config.getSkuCfgList()).orElse(Lists.newArrayList());
        DealDetailSkuListModuleGroupModel groupModel = new DealDetailSkuListModuleGroupModel();
        List<DealSkuVO> dealSkuVOList = skuCfgList.stream()
                .map(skuCfg -> buildDealSkuVO(skuCfg, skuItemDtoList, productSkuCategoryModels))
                .filter(Objects::nonNull).collect(Collectors.toList());
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(dealSkuVOList);
        groupModel.setDealSkuGroupModuleVOS(Lists.newArrayList(dealSkuGroupModuleVO));
        groupModel.setGroupName(config.getGroupName());
        return Collections.singletonList(groupModel);
    }

    private DealSkuVO buildDealSkuVO(SingleSkuCfg skuCfg, List<SkuItemDto> skuItemDtoList,
            List<ProductSkuCategoryModel> productSkuCategoryModels) {
        DealSkuVO dealSkuVO = new DealSkuVO();
        List<DealSkuItemVO> dealSkuItemVOList = buildDealSkuItemVOList(skuCfg, skuItemDtoList,
                productSkuCategoryModels);
        if (ObjectUtils.isEmpty(dealSkuItemVOList)) {
            return null;
        }
        BigDecimal priceSum = dealSkuItemVOList.stream().map(dsvo -> new BigDecimal(dsvo.getOriginalPrice())).reduce(new BigDecimal(0),
                BigDecimal::add);
        dealSkuVO.setTitle(skuCfg.getTitle());
        dealSkuVO.setItems(dealSkuItemVOList);
        dealSkuVO.setPrice(getPriceDesc(skuCfg.getPriceFormat(), priceSum.stripTrailingZeros().toPlainString()));
        dealSkuVO.setType(skuCfg.getType());
        return dealSkuVO;
    }

    public String getPriceDesc(String priceFormat, String price) {
        return ObjectUtils.isEmpty(priceFormat) ? price : String.format(priceFormat, price);
    }

    private List<DealSkuItemVO> buildDealSkuItemVOList(SingleSkuCfg skuCfg, List<SkuItemDto> skuItemDtoList,
            List<ProductSkuCategoryModel> productSkuCategoryModels) {

        List<SkuItemCfg> skuItemCfgList = Optional.ofNullable(skuCfg.getSkuItemCfgList()).orElse(Lists.newArrayList());
        return skuItemCfgList.stream()
                .map(skuItemCfg -> skuItemCfg.isAttrLevelRise() ? buildAttrLevelRiseSkuItemVO(skuCfg,skuItemCfg,skuItemDtoList,productSkuCategoryModels):
                        buildDealSkuItemVO(skuCfg, skuItemCfg, skuItemDtoList, productSkuCategoryModels))
                .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private List<DealSkuItemVO> buildAttrLevelRiseSkuItemVO(SingleSkuCfg skuCfg, SkuItemCfg skuItemCfg, List<SkuItemDto> skuItemDtoList, List<ProductSkuCategoryModel> productSkuCategoryModels) {
        List<SkuItemDto> catelogSkuItemDtoList = skuItemDtoList.stream()
                .filter(skuItemDto -> !ObjectUtils.isEmpty(skuItemCfg.getProductCategoryId())
                        && skuItemCfg.getProductCategoryId().equals(skuItemDto.getProductCategory()))
                .collect(Collectors.toList());
        if (ObjectUtils.isEmpty(catelogSkuItemDtoList)) {
            return null;
        }
       return catelogSkuItemDtoList.stream().map(
              skuItemDto -> {
                  DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
                  String name = getAttrValueSingleValue(skuItemCfg,skuItemDto.getAttrItems(),skuItemCfg.getAttrName());
                  if(ObjectUtils.isEmpty(name)) {
                      return null;
                  }
                  dealSkuItemVO.setName(name);
                  dealSkuItemVO.setType(skuItemCfg.getType());
                   String price = getOriginalPriceWithInteger(skuItemDto);
                  dealSkuItemVO.setRightText(String.format(skuCfg.getPriceFormat(), price));
                  dealSkuItemVO.setOriginalPrice(String.valueOf(price));
                  return dealSkuItemVO;
              }
       ).filter(Objects::nonNull).collect(Collectors.toList());

    }

    private List<DealSkuItemVO> buildDealSkuItemVO(SingleSkuCfg cfg, SkuItemCfg skuItemCfg,
            List<SkuItemDto> skuItemDtoList, List<ProductSkuCategoryModel> productSkuCategoryModels) {
        List<SkuItemDto> catelogSkuItemDtoList = skuItemDtoList.stream()
                .filter(skuItemDto -> !ObjectUtils.isEmpty(skuItemCfg.getProductCategoryId())
                        && skuItemCfg.getProductCategoryId().equals(skuItemDto.getProductCategory()))
                .collect(Collectors.toList());
        if (ObjectUtils.isEmpty(catelogSkuItemDtoList)) {
            return null;
        }
        return catelogSkuItemDtoList.stream().map(skuItemDto -> {
            DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
            dealSkuItemVO.setName(getCategoryName(skuItemCfg, skuItemDto, productSkuCategoryModels));
            dealSkuItemVO.setIcon(skuItemCfg.getIcon());
            dealSkuItemVO.setType(skuItemCfg.getType());
            String price = getOriginalPriceWithInteger(skuItemDto);
            dealSkuItemVO.setRightText(String.format(cfg.getPriceFormat(), price));
            dealSkuItemVO.setOriginalPrice(price);
            // 处理特殊逻辑
            List<String> skuItemValueList = Optional.ofNullable(getSkuItemValueList(skuItemCfg, skuItemDto))
                    .orElse(Lists.newArrayList());
            if (skuItemCfg.isFilterSkuItem() && !ObjectUtils.isEmpty(skuItemCfg.getBlackAttrList())) {
                if(skuItemValueList.equals(skuItemCfg.getBlackAttrList())) {
                    return null;
                }
            }
            dealSkuItemVO.setValue(buildSubTitle(cfg, skuItemCfg, skuItemValueList));
            return dealSkuItemVO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @NotNull
    private String getOriginalPriceWithInteger(SkuItemDto skuItemDto) {
        return ObjectUtils.isEmpty(skuItemDto.getMarketPrice()) ? "0" : skuItemDto.getMarketPrice().stripTrailingZeros().toPlainString();
    }

    @Nullable
    private List<String> getSkuItemValueList(SkuItemCfg skuItemCfg, SkuItemDto skuItemDto) {
        if (ObjectUtils.isEmpty(skuItemCfg) || ObjectUtils.isEmpty(skuItemCfg.getSubTitleAttrNameList())) {
            return null;
        }
        return skuItemCfg.getSubTitleAttrNameList().stream()
                .map(attrName -> getSkuAttrItemValue(skuItemCfg, skuItemDto.getAttrItems(), attrName))
                .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private String getCategoryName(SkuItemCfg skuItemCfg, SkuItemDto skuItemDto,
            List<ProductSkuCategoryModel> productSkuCategoryModels) {
        if (ObjectUtils.isEmpty(productSkuCategoryModels)) {
            return null;
        }
        if (skuItemCfg.isUseItemName()) {
            return skuItemDto.getName();
        }
        Long productCategoryId = skuItemDto.getProductCategory();
        return productSkuCategoryModels.stream()
                .filter(productCategory -> !ObjectUtils.isEmpty(productCategoryId)
                        && productCategoryId.equals(productCategory.getProductCategoryId()))
                .map(ProductSkuCategoryModel::getCnName).findFirst().orElse(null);
    }

    @Data
    @VPointCfg
    public static class Config {
        private String groupName;
        private List<SingleSkuCfg> skuCfgList;
    }

    @Data
    public static class SkuItemCfg {
        private Long productCategoryId;
        private String icon;
        private Integer type = 0;
        private List<String> subTitleAttrNameList;
        private boolean useItemName;
        private String subTitlePrefixString = "";
        private List<String> blackAttrList;
        private boolean filterSkuItem;
        /***
         * 用于控制属性上升到SKU展示
         */
        private boolean attrLevelRise;
        private String attrName;
    }

    @Data
    public static class SingleSkuCfg {

        private String title;

        private Integer type = 0;

        private String copiesFormat;

        private String priceFormat;

        private String subTitleSplit = "·";

        private List<SkuItemCfg> skuItemCfgList;
    }
}
