package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints.SkuSetVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuSetModel;
import com.sankuai.dzviewscene.product.dealstruct.model.SkuItemModel;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/2/10 4:45 下午
 */
@VPointOption(name = "Sku二级分组默认变化点", description = "Sku二级分组默认变化点",code = DefaultSkuSetVPO.CODE, isDefault = true)
public class DefaultSkuSetVPO extends SkuSetVP<DefaultSkuSetVPO.Config> {

    public static final String CODE = "DefaultSkuSetVPO";

    @Override
    public List<DealDetailSkuSetModel> compute(ActivityCxt context, Param param, Config config) {
        List<SkuItemDto> skuItems = param.getSkuItems();
        if (CollectionUtils.isEmpty(skuItems)) {
            return null;
        }
        List<SkuItemModel> skuItemModels = skuItems.stream().map(item -> convertSkuItemDto2SkuItemModel(item)).collect(Collectors.toList());
        DealDetailSkuSetModel dealDetailSkuSetModel = new DealDetailSkuSetModel();
        dealDetailSkuSetModel.setSkuItems(skuItemModels);
        return Lists.newArrayList(dealDetailSkuSetModel);
    }

    private SkuItemModel convertSkuItemDto2SkuItemModel(SkuItemDto skuItemDto) {
        if (skuItemDto == null) {
            return null;
        }
        SkuItemModel skuItemModel = new SkuItemModel();
        skuItemModel.setSkuId(skuItemDto.getSkuId());
        skuItemModel.setName(skuItemDto.getName());
        skuItemModel.setCopies(String.valueOf(skuItemDto.getCopies()));
        skuItemModel.setProductCategory(skuItemDto.getProductCategory());
        skuItemModel.setSkuItemDto(skuItemDto);
        return skuItemModel;
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
