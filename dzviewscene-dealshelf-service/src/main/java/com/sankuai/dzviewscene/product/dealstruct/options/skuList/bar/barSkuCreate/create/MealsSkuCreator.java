package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.AbstractBarSkuCreator;
import com.sankuai.dzviewscene.product.dealstruct.vo.PopUpWindowVO;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/9 4:50 下午
 */
public class MealsSkuCreator extends AbstractBarSkuCreator {

    @Override
    public boolean ideantify(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config) {
        return skuItemDto != null && !config.getDrinksSkuCateIds().contains(skuItemDto.getProductCategory());
    }

    @Override
    protected String buildIcon(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config, boolean isSameCategorySku) {
        if (skuItemDto == null) {
            return null;
        }
        if (isSameCategorySku) {
            return StringUtils.isNotEmpty(DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), DETAIL_PIC_SKU_ATTR_NAME)) ? DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), DETAIL_PIC_SKU_ATTR_NAME) : DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), NEW_DETAIL_PIC_SKU_ATTR_NAME);
        }
        return DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), HEAL_PIC_SKU_ATTR_NAME);
    }

    @Override
    protected Map<String, String> getSubtitlesAttrName2FormatMap(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config, boolean isHitDouhu) {
        return isHitDouhu ? null : config.getMealsSubtitlesAttrName2FormatMap();
    }

    @Override
    protected PopUpWindowVO buildPopUpWindowVO(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config, boolean isHitDouhu) {
        if (!isHitDouhu) {
            return null;
        }
        //获取弹窗主图
        String pic = StringUtils.isNotEmpty(DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), DETAIL_PIC_SKU_ATTR_NAME)) ? DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), DETAIL_PIC_SKU_ATTR_NAME) : DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), NEW_DETAIL_PIC_SKU_ATTR_NAME);
        PopUpWindowVO popUpWindowVO = new PopUpWindowVO();
        popUpWindowVO.setIcon(pic);
        return popUpWindowVO;
    }

    @Override
    protected String extractCopies(SkuItemDto skuItemDto, BarDealDetailSkuListModuleOpt.Config config) {
        //里层的餐食份数
        String value = DealDetailUtils.getAndTrimSkuAttrValue(skuItemDto.getAttrItems(), "unit");
        /**
         * 最外层的份数
         */
        int copies = skuItemDto.getCopies();
        /**
         * 最后展示=外*层
         */
        int number = copies * (StringUtils.isEmpty(value) ? 1 : NumberUtils.toInt(value, 1));
        return String.format("(%s份)", number);
    }

}
