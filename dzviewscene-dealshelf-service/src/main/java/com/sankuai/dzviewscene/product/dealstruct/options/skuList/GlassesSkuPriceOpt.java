package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuPriceVP;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/9/8 5:29 下午
 */
@VPointOption(name = "配镜sku价格默认变化点", description = "sku价格默认变化点", code = GlassesSkuPriceOpt.CODE, isDefault = false)
public class GlassesSkuPriceOpt extends SkuPriceVP<GlassesSkuPriceOpt.Config> {

    public static final String CODE = "GlassesSkuPriceOpt";

    private static final String DEFAULT_FORMAT = "%s元";

    private static final String NO_SHOW_PRICE_FLAG = "不展示价格标识";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        return null;
    }

    @Data
    @VPointCfg
    public static class Config {
        private String format;
    }
}
