package com.sankuai.dzviewscene.product.dealstruct.activity.deal;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.sankuai.athena.viewscene.framework.InterceptorContext;
import com.sankuai.athena.viewscene.framework.annotation.ActivityInterceptor;
import com.sankuai.athena.viewscene.framework.core.IActivityInterceptor;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ModelUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 **/
@ActivityInterceptor(name = "团单详情活动打点拦截器", code = "activity_deal_detail_interceptor", activity = DealDetailActivity.CODE)
public class DealDetailActivityInterceptor implements IActivityInterceptor<Object> {

    @Override
    public void beforeExecute(InterceptorContext<Object> interceptorContext) {

    }

    @Override
    public void complete(InterceptorContext<Object> interceptorContext, Object result) {
        try {
            Cat.logMetricForCount(interceptorContext.getActivityCode(), buildMetricTags(interceptorContext, result));
        } catch (Exception e) {/*静默*/}
    }

    private Map<String, String> buildMetricTags(InterceptorContext<Object> interceptorContext, Object object) {
        Object finalResult = interceptorContext.getDefaultResult() != null ? interceptorContext.getDefaultResult() : object;
        return new HashMap<String, String>() {{
            put("sceneCode", interceptorContext.getSceneCode());
            put("hasDetail", Boolean.toString(ModelUtils.hasDetail(finalResult)));
            int clientType = ParamsUtil.getIntSafely(interceptorContext.getParameters(), PmfConstants.Params.userAgent);
            String clientTypeMsg = VCClientTypeEnum.getClientTypeMsg(clientType);
            if(StringUtils.isNotEmpty(clientTypeMsg)){
                put("clientType", clientTypeMsg);
            }
        }};
    }
}
