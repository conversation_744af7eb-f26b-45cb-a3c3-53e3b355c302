package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.ImplantMultiSkuListModelOpt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ImplantGroupConfig;
import com.sankuai.dzviewscene.shelf.platform.common.model.DealAdditionalProjectM;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2025/1/3
 */
public class ImplantDealUtils {

    // 构建耗材+服务内容  skuGroupsModel1
    public static List<DealSkuGroupModuleVO> buildServiceProjectSkuGroupModuleVOs(Map<String, String> name2ValueMap, ImplantMultiSkuListModelOpt.Config config) {
        List<DealSkuVO> dealSkuVOS = buildTherapyDealSkuList(name2ValueMap, config);
        if (CollectionUtils.isEmpty(dealSkuVOS)) {
            return Lists.newArrayList();
        }
        DealSkuGroupModuleVO skuGroupModuleVO = new DealSkuGroupModuleVO();
        skuGroupModuleVO.setDealSkuList(dealSkuVOS);
        skuGroupModuleVO.setTitle(null);
        return Lists.newArrayList(skuGroupModuleVO);
    }

    // 构建模块内容 dealSkuList列表
    private static List<DealSkuVO> buildTherapyDealSkuList(Map<String, String> name2ValueMap, ImplantMultiSkuListModelOpt.Config config) {
        List<DealSkuVO> dealSkuVOS = Lists.newArrayList();
        // 种植耗材
        ImplantGroupConfig consumablesGroupConfig = config.getGroupConfigs().stream().filter(g -> g.getGroupName().equals("包含种植耗材")).findFirst().orElse(null);
        List<DealSkuItemVO> consumablesDealSkuItemVOS = Lists.newArrayList();
        if (Objects.nonNull(consumablesGroupConfig) && Objects.nonNull(consumablesGroupConfig.getSkuCfg()) && CollectionUtils.isNotEmpty(consumablesGroupConfig.getSkuCfg().getSkuItemCfgList())) {
            consumablesGroupConfig.getSkuCfg().getSkuItemCfgList().stream().forEach(implantSkuItemCfg -> {
                if (MapUtils.isEmpty(name2ValueMap)
                        || !name2ValueMap.containsKey(implantSkuItemCfg.getAttrKey())
                        || StringUtils.isBlank(name2ValueMap.get(implantSkuItemCfg.getAttrKey()))) {
                    return;
                }

                String value = name2ValueMap.get(implantSkuItemCfg.getAttrKey());
                DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
                dealSkuItemVO.setName(implantSkuItemCfg.getKey());
                dealSkuItemVO.setIcon(implantSkuItemCfg.getIcon());
                dealSkuItemVO.setType(implantSkuItemCfg.getType());
                dealSkuItemVO.setValue(StringUtils.isNotBlank(value) ? value.replaceAll(",", " 或 ") : StringUtils.EMPTY);
                consumablesDealSkuItemVOS.add(dealSkuItemVO);
            });

            if (CollectionUtils.isNotEmpty(consumablesDealSkuItemVOS)) {
                DealSkuVO consumablesSkuVO = new DealSkuVO();
                consumablesSkuVO.setTitle(consumablesGroupConfig.getSkuCfg().getTitle());
                consumablesSkuVO.setItems(consumablesDealSkuItemVOS);
                consumablesSkuVO.setType(consumablesGroupConfig.getSkuCfg().getType());
                dealSkuVOS.add(consumablesSkuVO);
            }
        }

        // 服务内容
        ImplantGroupConfig serviceContentGroupConfig = config.getGroupConfigs().stream().filter(g -> g.getGroupName().equals("包含服务-治疗")).findFirst().orElse(null);
        List<DealSkuItemVO> serviceContentSkuItemVOS = Lists.newArrayList();
        if (Objects.nonNull(serviceContentGroupConfig) && Objects.nonNull(serviceContentGroupConfig.getSkuCfg()) && MapUtils.isNotEmpty(serviceContentGroupConfig.getSkuCfg().getAttrKeyMap())) {
            // 获取服务内容
            if (MapUtils.isEmpty(name2ValueMap)
                    || !name2ValueMap.containsKey(serviceContentGroupConfig.getSkuCfg().getKey())
                    || StringUtils.isBlank(name2ValueMap.get(serviceContentGroupConfig.getSkuCfg().getKey()))) {
                return dealSkuVOS;
            }

            String serviceContentStr = name2ValueMap.get(serviceContentGroupConfig.getSkuCfg().getKey());
            Map<String, String> therapyServiceContentMap = serviceContentGroupConfig.getSkuCfg().getAttrKeyMap();
            List<String> serviceContentList = Arrays.asList(serviceContentStr.split(","));
            Map<String, String> serviceContentKVMap = serviceContentList.stream().collect(Collectors.toMap(sc -> sc, sc -> getServiceContentValue(sc, name2ValueMap, therapyServiceContentMap)));
            serviceContentList.forEach(key -> {
                if (serviceContentKVMap.containsKey(key)) {
                    DealSkuItemVO dealSkuItemVO = buildServiceContentSkuItemVO(key, serviceContentKVMap.get(key));
                    if (Objects.isNull(dealSkuItemVO)) {
                        return;
                    }
                    serviceContentSkuItemVOS.add(dealSkuItemVO);
                }
            });
        }

        DealSkuVO serviceContentSkuVO = new DealSkuVO();
        serviceContentSkuVO.setTitle(serviceContentGroupConfig.getSkuCfg().getTitle());
        if (CollectionUtils.isNotEmpty(serviceContentSkuItemVOS)) {
            serviceContentSkuVO.setItems(serviceContentSkuItemVOS);
            serviceContentSkuVO.setType(serviceContentGroupConfig.getSkuCfg().getType());
            dealSkuVOS.add(serviceContentSkuVO);
        }
        return dealSkuVOS;
    }

    private static String getServiceContentValue(String sc, Map<String, String> name2ValueMap, Map<String, String> therapyServiceContentMap) {
        if (MapUtils.isEmpty(therapyServiceContentMap)) {
            return StringUtils.EMPTY;
        }
        String attrKey = therapyServiceContentMap.get(sc);
        if (StringUtils.isBlank(attrKey)) {
            return StringUtils.EMPTY;
        }
        String attrValueStr = name2ValueMap.get(attrKey);
        if (StringUtils.isBlank(attrValueStr)) {
            return StringUtils.EMPTY;
        }

        return attrValueStr.replaceAll(",", "|");
    }

    public static List<DealSkuGroupModuleVO> buildAdditionalProject(SkuListModuleVP.Param param) {
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        if (Objects.isNull(dealDetailInfoModel) || CollectionUtils.isEmpty(dealDetailInfoModel.getAdditionalProjectList())) {
            return null;
        }
        List<DealAdditionalProjectM> dealAdditionalProjectMS = dealDetailInfoModel.getAdditionalProjectList();


        DealSkuGroupModuleVO skuGroupModuleVO = new DealSkuGroupModuleVO();
        skuGroupModuleVO.setTitleStyle(1);
        List<DealSkuVO> dealSkuVOS = Lists.newArrayList();
        dealAdditionalProjectMS.stream().filter(item -> StringUtils.isNotEmpty(item.getItemName())).forEach(entry -> {
            DealSkuVO dealSkuVO = new DealSkuVO();
            dealSkuVO.setTitle(entry.getItemName());
            dealSkuVO.setType(2);
            dealSkuVO.setPrice(getAdditionalProjectItemPrice(entry));
            dealSkuVOS.add(dealSkuVO);
        });
        if (CollectionUtils.isEmpty(dealSkuVOS)) {
            return null;
        }
        skuGroupModuleVO.setDealSkuList(dealSkuVOS);
        return Lists.newArrayList(skuGroupModuleVO);
    }

    private static String getAdditionalProjectItemPrice(DealAdditionalProjectM entry) {
        if (Objects.isNull(entry)
                || Objects.isNull(entry.getSalePrice())) {
            return null;
        }

        return "+￥" + entry.getSalePrice() + "/份" ;
    }

    public static List<DealSkuGroupModuleVO> buildDesignServiceProjectSkuGroupModuleVOs(Map<String, String> name2ValueMap, ImplantMultiSkuListModelOpt.Config config) {
        List<DealSkuVO> dealSkuVOS = buildDesignDealSkuList(name2ValueMap, config);
        DealSkuGroupModuleVO skuGroupModuleVO = new DealSkuGroupModuleVO();
        skuGroupModuleVO.setDealSkuList(dealSkuVOS);
        skuGroupModuleVO.setTitle(null);
        return Lists.newArrayList(skuGroupModuleVO);
    }

    private static List<DealSkuVO> buildDesignDealSkuList(Map<String, String> name2ValueMap, ImplantMultiSkuListModelOpt.Config config) {
        List<DealSkuVO> dealSkuVOS = Lists.newArrayList();
        // 服务内容
        ImplantGroupConfig serviceContentGroupConfig = config.getGroupConfigs().stream().filter(g -> g.getGroupName().equals("包含服务-检查")).findFirst().orElse(null);
        List<DealSkuItemVO> serviceContentSkuItemVOS = Lists.newArrayList();
        if (Objects.nonNull(serviceContentGroupConfig) && Objects.nonNull(serviceContentGroupConfig.getSkuCfg()) && MapUtils.isNotEmpty(serviceContentGroupConfig.getSkuCfg().getAttrKeyMap())) {
            // 获取服务内容
            if (MapUtils.isEmpty(name2ValueMap)
                    || !name2ValueMap.containsKey(serviceContentGroupConfig.getSkuCfg().getKey())
                    || StringUtils.isBlank(name2ValueMap.get(serviceContentGroupConfig.getSkuCfg().getKey()))) {
                return dealSkuVOS;
            }

            String serviceContentStr = name2ValueMap.get(serviceContentGroupConfig.getSkuCfg().getKey());
            Map<String, String> designServiceContentMap = serviceContentGroupConfig.getSkuCfg().getAttrKeyMap();
            List<String> serviceContentList = Arrays.asList(serviceContentStr.split(","));
            Map<String, String> serviceContentKVMap = serviceContentList.stream().collect(Collectors.toMap(sc -> sc, sc -> getServiceContentValue(sc, name2ValueMap, designServiceContentMap)));
            serviceContentList.forEach(key -> {
                if (serviceContentKVMap.containsKey(key)) {
                    DealSkuItemVO dealSkuItemVO = buildServiceContentSkuItemVO(key, serviceContentKVMap.get(key));
                    if (Objects.isNull(dealSkuItemVO)) {
                        return;
                    }
                    serviceContentSkuItemVOS.add(dealSkuItemVO);
                }
            });
        }

        DealSkuVO serviceContentSkuVO = new DealSkuVO();
        serviceContentSkuVO.setTitle(serviceContentGroupConfig.getSkuCfg().getTitle());
        if (CollectionUtils.isNotEmpty(serviceContentSkuItemVOS)) {
            serviceContentSkuVO.setItems(serviceContentSkuItemVOS);
            serviceContentSkuVO.setType(serviceContentGroupConfig.getSkuCfg().getType());
            dealSkuVOS.add(serviceContentSkuVO);
        }
        return dealSkuVOS;
    }

    private static DealSkuItemVO buildServiceContentSkuItemVO(String key, String value) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(key);
        dealSkuItemVO.setValue(value);
        dealSkuItemVO.setType(4);
        return dealSkuItemVO;
    }
}
