package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.cat.util.StringUtils;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.title.vpoints.DealDetailTitleVP;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/25 11:18 上午
 */
@VPointOption(name = "团购详情标题默认变化点", description = "团购详情标题默认变化点，支持配置", code = DefaultDealDetailTitleVPO.CODE, isDefault = true)
public class DefaultDealDetailTitleVPO extends DealDetailTitleVP<DefaultDealDetailTitleVPO.Config> {

    public static final String CODE = "DefaultDealDetailTitleVP";

    private static final String DEFAULT_DETAIL_TITLE = "团购详情";

    private static final String DEAL_TITLE_SHOW_FLAG = "团单标题展示标志";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (config == null) {
            return DEFAULT_DETAIL_TITLE;
        }
        String configTitle = config.getConfigTitle();
        if (DEAL_TITLE_SHOW_FLAG.equals(configTitle)) {
            return param.getTitle();
        }
        if (StringUtils.isEmpty(configTitle)) {
            return DEFAULT_DETAIL_TITLE;
        }
        return configTitle;
    }

    @Data
    @VPointCfg
    public static class Config {
        private String configTitle;
    }
}
