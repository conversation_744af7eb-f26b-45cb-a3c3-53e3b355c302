package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.defaultShowNum;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.ability.model.LeUncoopShopShelfAttrM;
import com.sankuai.dzviewscene.product.ability.options.LeUnCoopShopUniverseInfoOpt;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListDefaultShowNumVP;
import lombok.Data;

/**
 * @author: wuwenqi<PERSON>
 * @create: 2024-06-18
 * @description: 默认展示数量-LE非合作POI推荐列表
 */
@VPointOption(name = "LE非合作POI推荐列表默认展示数量", description = "LE非合作POI推荐列表默认展示数量", code = LEUncoopShopDefaultShowNumOpt.CODE)
public class LEUncoopShopDefaultShowNumOpt extends DealFilterListDefaultShowNumVP<LEUncoopShopDefaultShowNumOpt.Config> {

    public static final String CODE = "LEUncoopShopDefaultShowNumOpt";

    @Override
    public Integer compute(ActivityCxt activityCxt, Param param, Config config) {
        LeUncoopShopShelfAttrM leUncoopShopShelfAttrM = activityCxt.getParam(LeUnCoopShopUniverseInfoOpt.CODE);
        if (leUncoopShopShelfAttrM == null || leUncoopShopShelfAttrM.getDefaultShowNum() == null) {
            return 0;
        }
        return leUncoopShopShelfAttrM.getDefaultShowNum() > 0 ? leUncoopShopShelfAttrM.getDefaultShowNum() : 0;
    }

    @Data
    @VPointCfg
    public static class Config {

    }
}
