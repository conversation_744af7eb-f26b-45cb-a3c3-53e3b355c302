package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuTitleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import lombok.Data;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/9/8 5:29 下午
 */
@VPointOption(name = "取sku名称作为sku标题的默认变化点", description = "sku标题默认变化点，取sku名称作为标题", code = DefaultSkuTitleOpt.CODE, isDefault = true)
public class DefaultSkuTitleOpt extends SkuTitleVP<DefaultSkuTitleOpt.Config> {

    public static final String CODE = "DefaultSkuTitleOpt";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (config !=null && config.isNoShowTitleFlag()){
            return null;
        }
        //如果配置了属性key，SKU标题展示属性key对应的属性值
        if (config != null && StringUtils.isNotEmpty(config.getSkuAttrKey())) {
            return getSkuTitleByAtrKey(param, config.getSkuAttrKey());
        }
        return param.getSkuTitle();
    }

    private String getSkuTitleByAtrKey(Param param, String attrKey) {
        if (param == null
                || param.getSkuItemDto() == null
                || CollectionUtils.isEmpty(param.getSkuItemDto().getAttrItems())) {
            return param.getSkuTitle();
        }
        String attrValue = DealDetailUtils.getSkuAttrValueBySkuAttrName(param.getSkuItemDto().getAttrItems(), attrKey);
        if (StringUtils.isEmpty(attrValue)) {
            return param.getSkuTitle();
        }
        return attrValue;
    }

    @Data
    @VPointCfg
    public static class Config {
        //服务项目属性key
        private String skuAttrKey;
        private boolean noShowTitleFlag = false;
    }
}
