package com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.DealDetailSkuGroupsBuilder;
import lombok.Builder;
import lombok.Data;

/**
 * created by zhang<PERSON>yuan04 in 2022/1/4
 */
@VPoint(name = "Sku元素份数文案构造能力", description = "构造Sku元素份数信息，例如XX份", code = SkuItemCopiesVP.CODE, ability = DealDetailSkuGroupsBuilder.CODE)
public abstract class SkuItemCopiesVP<C> extends PmfVPoint<String, SkuItemCopiesVP.Param, C> {

    public static final String CODE = "SkuItemCopiesVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {

        /**
         * 是否必选
         */
        private boolean isMust;

        /**
         * 分组标题
         */
        private String setTitle;

        /**
         * Sku信息
         */
        private SkuItemDto skuItemDto;
    }
}
