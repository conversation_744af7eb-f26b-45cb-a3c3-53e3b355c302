package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.SkuPriceVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints.SkuItemPriceVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints.SkuItemTitleVP;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/11/26 6:20 下午
 */
@VPointOption(name = "团购详情sku货价格变化点", description = "团购详情sku货价格变化点，支持配置文案Format",code = DefaultSkuItemPriceVPO.CODE, isDefault = true)
public class DefaultSkuItemPriceVPO extends SkuItemPriceVP<DefaultSkuItemPriceVPO.Config> {

    public static final String CODE = "DefaultSkuItemPriceVPO";

    private static final String DEFAULT_PRICE_FORMAT = "%s元";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        SkuItemDto skuItemDto = param.getSkuItemDto();
        if (skuItemDto == null || skuItemDto.getMarketPrice() == null) {
            return null;
        }
        String priceFormat = StringUtils.isEmpty(config.getSkuPriceFormat()) ? DEFAULT_PRICE_FORMAT : config.getSkuPriceFormat();
        return String.format(priceFormat, skuItemDto.getMarketPrice().toPlainString());
    }

    @Data
    @VPointCfg
    public static class Config {
        private String skuPriceFormat;
    }
}
