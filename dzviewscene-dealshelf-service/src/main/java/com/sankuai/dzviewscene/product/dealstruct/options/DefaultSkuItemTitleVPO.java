package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints.SkuItemTitleVP;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/27 9:17 下午
 */
@VPointOption(name = "团购详情sku货标题默认变化点", description = "团购详情sku货标题默认变化点",code = DefaultSkuItemTitleVPO.CODE, isDefault = true)
public class DefaultSkuItemTitleVPO extends SkuItemTitleVP<DefaultSkuItemTitleVPO.Config> {

    public static final String CODE = "DefaultSkuItemTitleVPO";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        SkuItemDto skuItemDto = param.getSkuItemDto();
        if (skuItemDto == null) {
            return null;
        }
        return skuItemDto.getName();
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
