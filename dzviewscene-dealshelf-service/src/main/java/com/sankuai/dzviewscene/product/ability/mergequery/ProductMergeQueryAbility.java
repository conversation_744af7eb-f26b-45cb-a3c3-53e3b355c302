package com.sankuai.dzviewscene.product.ability.mergequery;

import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.ability.douhu.DouHuAbility;
import com.sankuai.dzviewscene.product.ability.extCtx.ExtContextAbility;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.GroupPaddingFetcher;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.GroupQueryFetcher;
import com.sankuai.dzviewscene.product.productdetail.activity.SpuDetailActivity;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivity;
import com.sankuai.dzviewscene.product.shelf.activity.product.ProductShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import org.apache.commons.collections4.MapUtils;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Ability(code = ProductMergeQueryAbility.CODE,
        name = "商品信息统一融合查询能力(新)",
        description = "包含商品召回、填充、排序的融合查询能力。\n" +
                "入参主要为外部传参(平台、商户ID)与能力配置信息。\n" +
                "能力为多种能力的集成，包括：1.多门店多组商品召回能力，可根据查询类型配置选择对应的召回渠道，召回门店下的多组商品。\n" +
                "2.多组商品填充能力，可根据配置的填充类型选择填充能力，根据填充参数决定填充内容。" +
                "返回标准的商品组模型集合，以商品组名为Key，商品组模型为Model的Map形式。",
        dependency = {ExtContextAbility.CODE, DouHuAbility.CODE},
        activities = {ProductShelfActivity.CODE, DealShelfActivity.CODE, SpuDetailActivity.CODE, DouHuAbility.CODE}
)
public class ProductMergeQueryAbility extends PmfAbility<Map<String, ProductGroupM>, MergeQueryParam, MergeQueryCfg> {

    public static final String CODE = "ProductMergeQueryAbility";

    @Resource
    private GroupQueryFetcher groupQueryFetcher;

    @Resource
    private GroupPaddingFetcher groupPaddingFetcher;

    @Override
    public CompletableFuture<Map<String, ProductGroupM>> build(ActivityCxt ctx,
                                                               MergeQueryParam param,
                                                               MergeQueryCfg config) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.ability.mergequery.ProductMergeQueryAbility.build(ActivityCxt,MergeQueryParam,MergeQueryCfg)");
        ctx.addParam("groupNames", config.getGroupNames());
        ctx.addParam("groupParams", config.getGroupParams());
        CompletableFuture<Map<String, ProductGroupM>> queryMultiGroups = groupQueryFetcher.build(ctx, config);
        return queryMultiGroups.thenCompose(groupNames2ProductGroupsMap -> paddingMultiGroups(groupNames2ProductGroupsMap, ctx, param, config));
    }

    private CompletableFuture<Map<String, ProductGroupM>> paddingMultiGroups(Map<String, ProductGroupM> groupNames2ProductGroupsMap,
                                                                             ActivityCxt ctx, MergeQueryParam param,
                                                                             MergeQueryCfg config) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.ability.mergequery.ProductMergeQueryAbility.paddingMultiGroups(Map,ActivityCxt,MergeQueryParam,MergeQueryCfg)");
        if (MapUtils.isEmpty(groupNames2ProductGroupsMap)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        return groupPaddingFetcher.build(ctx, groupNames2ProductGroupsMap, config);
    }
}