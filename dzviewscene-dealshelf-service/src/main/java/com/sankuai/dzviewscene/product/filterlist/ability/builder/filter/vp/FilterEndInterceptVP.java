package com.sankuai.dzviewscene.product.filterlist.ability.builder.filter.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.filter.DealFilterBuilder;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023/2/16
 */
@VPoint(name = "最终对筛选结构及内容的修改", description = "一般为业务定制，或者做结构的修改", code = FilterEndInterceptVP.CODE, ability = DealFilterBuilder.CODE)
public abstract class FilterEndInterceptVP<T> extends PmfVPoint<Void, FilterEndInterceptVP.Param, T> {

    public static final String CODE = "FilterEndInterceptVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private List<DzFilterVO> filterList;

        /**
         * {@link ShelfActivityConstants.Params#userAgent}
         */
        private int userAgent;
    }
}
