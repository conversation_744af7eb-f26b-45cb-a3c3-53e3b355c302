package com.sankuai.dzviewscene.product.dealstruct.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/28 3:53 下午
 */
@MobileDo(id = 0x16fc)
public class SkusGroupsTableVO implements Serializable {
    /**
     * 表格数据
     */
    @MobileDo.MobileField(key = 0x2877)
    private List<TableCellsRowVO> tableVO;

    /**
     * 表头
     */
    @MobileDo.MobileField(key = 0x8bee)
    private List<TableHeaderCellVO> tableHeader;

    /**
     * 商家描述
     */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
     * 说明弹窗
     */
    @MobileDo.MobileField(key = 0x1b22)
    private PopUpWindowVO statePopUp;

    /**
     * 表名
     */
    @MobileDo.MobileField(key = 0x1a0f)
    private String tableName;

    public List<TableCellsRowVO> getTableVO() {
        return tableVO;
    }

    public void setTableVO(List<TableCellsRowVO> tableVO) {
        this.tableVO = tableVO;
    }

    public List<TableHeaderCellVO> getTableHeader() {
        return tableHeader;
    }

    public void setTableHeader(List<TableHeaderCellVO> tableHeader) {
        this.tableHeader = tableHeader;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public PopUpWindowVO getStatePopUp() {
        return statePopUp;
    }

    public void setStatePopUp(PopUpWindowVO statePopUp) {
        this.statePopUp = statePopUp;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
}