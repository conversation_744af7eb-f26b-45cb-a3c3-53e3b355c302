package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery.BaseDealIdQueryHandler;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.*;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@VPointOption(name = "心理咨询相似团购", description = "心理咨询相似团购 (含排序+截断)", code = "PsychologicalSimilarFilterAcsOpt")
public class PsychologicalSimilarFilterAcsOpt extends ProductListVP<PsychologicalSimilarFilterAcsOpt.Config> {

    private static final String SERVICE_TYPE = "service_type";

    private static final List<String> PSYCHOLOGICAL_CONSULT_TYPE = Arrays.asList("心理咨询", "倾听");

    @Override
    public List<ProductM> compute(ActivityCxt context, Param param, Config config) {
        List<ProductM> productMS = param.getProductMS();
        if (CollectionUtils.isEmpty(productMS)) {
            return Lists.newArrayList();
        }
        return similarProductFilter(context, productMS, config);
    }

    private List<ProductM> similarProductFilter(ActivityCxt context, List<ProductM> productMS, Config config) {
        // 获取当前团单信息
        ProductM currentProduct = getCurrentProduct(productMS, context);
        // 获取categoryId, serviceType相同的相似团单
        List<ProductM> similarProducts = getSimilarProducts(currentProduct, productMS);
        // 团单排序规则: 销量倒序, 价格升序
        List<ProductM> sortedSimilars = getSortedSimilarProducts(similarProducts);
        // 相似团单数量限制
        return sortedSimilars.stream().limit(config.getLimit()).collect(Collectors.toList());
    }

    private ProductM getCurrentProduct(List<ProductM> productMS, ActivityCxt context) {
        return productMS.stream().filter(
                productM -> productM.getProductId() == ParamsUtil.getIntSafely(context, PmfConstants.Params.entityId))
                .findFirst().orElse(productMS.get(0));
    }

    private List<ProductM> getSimilarProducts(ProductM currentProduct, List<ProductM> productMS) {
        // 获取serviceType相同的相似团单
        String serviceType = currentProduct.getAttr(SERVICE_TYPE);
        if (isInValidServiceType(serviceType)) {
            return Lists.newArrayList();
        }
        return productMS.stream().filter(productM -> productM.getProductId() != currentProduct.getProductId())
                .filter(productM -> isSameCategoryId(productM, currentProduct.getCategoryId()))
                .filter(productM -> isSameServiceType(productM, serviceType)).collect(Collectors.toList());
    }

    public boolean isInValidServiceType(String serviceType) {
        return StringUtils.isBlank(serviceType) || !PSYCHOLOGICAL_CONSULT_TYPE.contains(serviceType);
    }

    public boolean isSameCategoryId(ProductM productM, Integer categoryId) {
        return categoryId.equals(productM.getCategoryId());
    }

    public boolean isSameServiceType(ProductM productM, String serviceType) {
        // 服务项目
        String serviceTypeNew = productM.getAttr(SERVICE_TYPE);
        if (isInValidServiceType(serviceTypeNew)) {
            return false;
        }
        return serviceType.equals(serviceTypeNew);
    }

    private List<ProductM> getSortedSimilarProducts(List<ProductM> productMS) {
        return productMS.stream().sorted(new PsychologicalConsultDealComperator()).collect(Collectors.toList());
    }

    @VPointCfg
    @Data
    public static class Config {
        private int limit = 10;
    }

    private static class PsychologicalConsultDealComperator implements Comparator<ProductM> {
        @Override
        public int compare(ProductM p1, ProductM p2) {
            // 按销量倒序
            int saleCompare = compareBySale(p1.getSale(), p2.getSale());
            if (saleCompare != 0) {
                return saleCompare;
            }
            // 销量相同，按价格升序
            return compareByPrice(p1.getSalePrice(), p2.getSalePrice());
        }

        private int compareBySale(ProductSaleM sale1, ProductSaleM sale2) {
            // 处理销量为空的情况，销量为空放至列表后面
            if (Objects.isNull(sale1)) {
                return Objects.isNull(sale2) ? 0 : 1;
            }
            if (Objects.isNull(sale2)) {
                return -1;
            }
            // 按销量倒序
            return Integer.compare(sale2.getSale(), sale1.getSale());
        }

        private int compareByPrice(BigDecimal price1, BigDecimal price2) {
            // 处理价格为空的情况
            if (Objects.isNull(price1)) {
                return Objects.isNull(price2) ? 0 : 1;
            }
            if (Objects.isNull(price2)) {
                return -1;
            }
            // 价格升序
            return price1.compareTo(price2);
        }
    }
}
