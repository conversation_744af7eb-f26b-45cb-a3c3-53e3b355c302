package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailAttrDescVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;


@VPointOption(name = "在线教育团单的免费赠品AttrVO列表变化点", description = "在线教育团单的免费赠品AttrVO列表变化点",code = EduOnlineDealPhysicalGiftVOListOpt.CODE)
@Slf4j
public class EduOnlineDealPhysicalGiftVOListOpt extends DealAttrVOListVP<EduOnlineDealPhysicalGiftVOListOpt.Config> {

    public static final String CODE = "EduOnlineDealPhysicalGiftVOListOpt";

    public static final String ATTR_MATERIAL_LIST = "material_list";

    public static final String ATTR_SUPPORT_DELIVERY = "delivery_support";
    public static final String IS_SUPPORT_DELIVERY_Y = "Y";
    public static final String IS_SUPPORT_DELIVERY_YES = "是";

    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOS = buildDealDetailStructAttrModuleVO(context, param, config);
        if (dealDetailStructAttrModuleVOS == null) {
            return null;
        }
        DealDetailStructAttrModuleGroupModel groupModule = new DealDetailStructAttrModuleGroupModel();
        groupModule.setGroupName(config.getPhysicalGiftTitle());
        groupModule.setGroupSubtitle(getGroupSubtitleAsDelivery(config, param));
        groupModule.setDotType(1);
        groupModule.setShowNum(config.getPhysicalGiftMaxShowNum());
        groupModule.setFoldStr(buildFoldStr(config, dealDetailStructAttrModuleVOS.size()));
        groupModule.setDealDetailStructAttrModuleVOS(dealDetailStructAttrModuleVOS);
        return Lists.newArrayList(groupModule);
    }

    private String getGroupSubtitleAsDelivery(Config config, Param param) {
        String supportDelivery = DealDetailUtils.getAttrSingleValueByAttrName(param.getDealAttrs(), ATTR_SUPPORT_DELIVERY);
        if (IS_SUPPORT_DELIVERY_Y.equalsIgnoreCase(supportDelivery) || IS_SUPPORT_DELIVERY_YES.equals(supportDelivery)) {
            return config.getPhysicalGiftSubTitle();
        }
        return null;
    }

    private String buildFoldStr(Config config, int size) {
        if (config.getPhysicalGiftMaxShowNum() < 1 || size <= config.getPhysicalGiftMaxShowNum()) {
            return null;
        }
        return String.format(config.getFoldStrTemplate(), (size - config.getPhysicalGiftMaxShowNum()));
    }

    private List<DealDetailStructAttrModuleVO> buildDealDetailStructAttrModuleVO(ActivityCxt context, Param param, Config config) {
        List<PhysicalGift> physicalGiftList = getPhysicalGiftList(param);
        if (CollectionUtils.isEmpty(physicalGiftList)) {
            return null;
        }
        return physicalGiftList.stream()
                .filter(gift -> gift != null && StringUtils.isNotBlank(gift.getMaterialName()))
                .map(gift -> {
                    DealDetailStructAttrModuleVO attrModuleVO = new DealDetailStructAttrModuleVO();
                    attrModuleVO.setAttrName(gift.getMaterialName());
                    if (StringUtils.isNotBlank(gift.getMaterialType())) {
                        DealDetailAttrDescVO descVO = new DealDetailAttrDescVO();
                        descVO.setTitle(gift.getMaterialType());
                        descVO.setTitleColor(config.getDescColor());
                        attrModuleVO.setDesc(Lists.newArrayList(descVO));
                    }
                    return attrModuleVO;
                }).collect(Collectors.toList());
    }

    private List<PhysicalGift> getPhysicalGiftList(Param param) {
        String json = DealDetailUtils.getAttrSingleValueByAttrName(param.getDealAttrs(), ATTR_MATERIAL_LIST);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonCodec.decode(json, new TypeReference<List<PhysicalGift>>() {});
    }


    @Data
    public static class PhysicalGift {

        /**
         * 类型
         */
        @JsonProperty("material_type")
        private String materialType;

        /**
         * 资料名称
         */
        @JsonProperty("material_name")
        private String materialName;

    }

    @Data
    @VPointCfg
    public static class Config {

        private String physicalGiftTitle = "赠送资料";

        private String physicalGiftSubTitle = "纸质版开课后由商家发货寄出";

        private int physicalGiftMaxShowNum = 4;

        private String foldStrTemplate = "更多 %s 件";

        private String descColor = "#999999";

    }

}
