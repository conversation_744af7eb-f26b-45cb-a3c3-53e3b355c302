package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.TimeCardThemeHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 次卡主题填充，复用之前的
 */
@Component
public class TimeCardThemePaddingHandler implements PaddingHandler{

    @Resource
    private TimeCardThemeHandler timeCardThemeHandler;

    @Override
    public CompletableFuture<ProductGroupM> padding(ActivityCxt ctx, ProductGroupM productGroupM, Map<String, Object> params) {
        return timeCardThemeHandler.padding(ActivityCtxtUtils.toActivityContext(ctx), productGroupM, params);
    }
}
