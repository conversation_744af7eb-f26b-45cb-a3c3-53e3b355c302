package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/6/13 3:51 下午
 */
@VPointOption(name = "sku属性列表变化点", description = "sku属性列表变化点，直接根据category名字返回配置值", code = SimpleConfigSkuAttrListOpt.CODE)
public class SimpleConfigSkuAttrListOpt extends SkuAttrListVP<SimpleConfigSkuAttrListOpt.Config> {

    public static final String CODE = "SimpleConfigSkuAttrListOpt";

    @Override
    public List<DealSkuItemVO> compute(ActivityCxt activityCxt, Param param, Config config) {
        if (config == null || MapUtils.isEmpty(config.getCategory2ShowVOS())) {
            return null;
        }
        List<ProductSkuCategoryModel> productCategories = param.getProductCategories();
        long productCategory = Optional.ofNullable(param.getSkuItemDto()).map(SkuItemDto::getProductCategory).orElse(0L);
        String categoryName = productCategories.stream()
                .filter(productSkuCategoryModel -> productSkuCategoryModel.getProductCategoryId() == productCategory)
                .map(ProductSkuCategoryModel::getCnName)
                .findFirst().orElse(StringUtils.EMPTY);
        return config.getCategory2ShowVOS().get(categoryName);
    }

    @Data
    @VPointCfg
    public static class Config {
        private Map<String, List<DealSkuItemVO>> category2ShowVOS;
    }
}
