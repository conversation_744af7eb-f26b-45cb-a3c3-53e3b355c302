package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.SkuTitleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/27 9:17 下午
 */
@VPointOption(name = "美甲sku类别名称作为sku货标题变化点", description = "部分品类sku取sku类别名作为货标题，少数sku走美甲定制化逻辑",code = NailProductCategorySkuTitleVPO.CODE, isDefault = false)
public class NailProductCategorySkuTitleVPO extends SkuTitleVP<NailProductCategorySkuTitleVPO.Config> {

    public static final String CODE = "NailProductCategorySkuTitleVPO";

    private static final long COLOR_CATEGORY_ID = 4043L;

    private static final long REMOVE_NAIL_CATEGORY_ID = 4039L;

    private static final String SECOND_CATEGORY_SKU_ATTR_NAME = "category2";

    private static final String SERVICE_CONTENT_SKU_ATTR_NAME = "content";

    private static final String STYLE_NAIL_SECOND_CATEGORY = "款式美甲";

    private static final String CONTAIN_DIFF_COLOR_SERVICE_CONTENT = "含跳色";

    private static final String SINGLE_COLOR_NAIL = "纯色美甲";

    private static final String DIff_COLOR_NAIL = "纯色/跳色美甲";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        SkuItemDto skuItemDto = param.getSkuItemDto();
        if (skuItemDto == null) {
            return null;
        }
        List<ProductSkuCategoryModel> productCategories = param.getProductCategories();
        String productCategory = DealDetailUtils.getSkuCategoryBySkuCategoryId(skuItemDto.getProductCategory(), productCategories);
        if (config == null || CollectionUtils.isEmpty(config.getSkuCategoryIdWithCustomzedTitle()) || !config.getSkuCategoryIdWithCustomzedTitle().contains(skuItemDto.getProductCategory())) {
            return productCategory;
        }
        if (param == null || param.getSkuItemDto() == null || CollectionUtils.isEmpty(param.getSkuItemDto().getAttrItems())) {
            return productCategory;
        }
        if (skuItemDto.getProductCategory() == COLOR_CATEGORY_ID) {
            return getColorSkuTitle(param.getSkuItemDto().getAttrItems(), productCategory);
        }
        if (skuItemDto.getProductCategory() == REMOVE_NAIL_CATEGORY_ID) {
            return getRemoveNailSkuTitle(param.getSkuItemDto().getAttrItems(), productCategory);
        }
        return getCategory2SkuTitle(param.getSkuItemDto().getAttrItems(), productCategory);
    }

    private String getCategory2SkuTitle(List<SkuAttrItemDto> attrItems, String productCategory) {
        if (CollectionUtils.isEmpty(attrItems)) {
            return productCategory;
        }
        String category2 = DealDetailUtils.getSkuAttrValueBySkuAttrName(attrItems, SECOND_CATEGORY_SKU_ATTR_NAME);
        if (StringUtils.isEmpty(category2)) {
            return productCategory;
        }
        return category2;
    }

    private String getRemoveNailSkuTitle(List<SkuAttrItemDto> attrItems, String productCategory) {
        if (CollectionUtils.isEmpty(attrItems)) {
            return productCategory;
        }
        String category2 = DealDetailUtils.getSkuAttrValueBySkuAttrName(attrItems, SECOND_CATEGORY_SKU_ATTR_NAME);
        if (StringUtils.isEmpty(category2)) {
            return productCategory;
        }
        return category2;
    }

    private String getColorSkuTitle(List<SkuAttrItemDto> attrItems, String productCategory) {
        if (CollectionUtils.isEmpty(attrItems)) {
            return productCategory;
        }
        String category2 = DealDetailUtils.getSkuAttrValueBySkuAttrName(attrItems, SECOND_CATEGORY_SKU_ATTR_NAME);
        if (STYLE_NAIL_SECOND_CATEGORY.equals(category2)) {
            return category2;
        }
        String serviceContent = DealDetailUtils.getSkuAttrValueBySkuAttrName(attrItems, SERVICE_CONTENT_SKU_ATTR_NAME);
        if (!CONTAIN_DIFF_COLOR_SERVICE_CONTENT.equals(serviceContent)) {
            return SINGLE_COLOR_NAIL;
        }
        return DIff_COLOR_NAIL;
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<Long> skuCategoryIdWithCustomzedTitle;
    }
}
