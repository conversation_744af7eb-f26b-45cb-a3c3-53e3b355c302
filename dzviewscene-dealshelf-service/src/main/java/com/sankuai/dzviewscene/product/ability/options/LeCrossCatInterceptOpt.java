package com.sankuai.dzviewscene.product.ability.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.InterceptHandlerVP;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@VPointOption(name = "Le留资行业cross类目品推荐",
            description = "根据行业cross类目拦截请求",
            code = LeCrossCatInterceptOpt.CODE)
public class LeCrossCatInterceptOpt extends InterceptHandlerVP<LeCrossCatInterceptOpt.Config> {

    public static final String CODE = "LeCrossCatInterceptOpt";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public Boolean compute(ActivityCxt context, Param param, Config config) {
        if (config.getCoopShopValidation() && !validation(context, config)) {
            return true;
        }
        // 仅匹配二级类目
        return !isIncludedInBackSecCats(context, config);
    }

    private boolean isIncludedInBackSecCats(ActivityCxt context, Config config) {
        List<Integer> backCatIds = context.getParam(PmfConstants.Params.shopBackCatIds);
        List<Integer> includeBackSecCats = config.getIncludeBackSecCats();

        if (CollectionUtils.isEmpty(backCatIds) || CollectionUtils.isEmpty(includeBackSecCats)) {
            return false;
        }

        return backCatIds.stream()
                .filter(Objects::nonNull)
                .anyMatch(includeBackSecCats::contains);
    }


    private Boolean validation(ActivityCxt context, Config config) {
        // 留资权限
        if (context.isParamNull("LeadsInterestFetcherOpt")) {
            return false;
        }
        Object paramValue = context.getParam(LeadsInterestFetcherOpt.CODE);
        if (paramValue instanceof Boolean) {
            return (Boolean) paramValue;
        }
        return false;
    }

    @Data
    @VPointCfg
    public static class Config {
        /**
         * 配置包含行业
         * 包含则不拦截,返回false
         * 不包含则拦截,返回true
         */
        private List<Integer> includeBackSecCats;

        /**
         * 是否需要合作校验
         */
        private Boolean coopShopValidation = false;
    }
}
