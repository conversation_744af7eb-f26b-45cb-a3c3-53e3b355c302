package com.sankuai.dzviewscene.product.filterlist.option.builder.product.marketprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductMarketPriceVP;
import com.sankuai.dzviewscene.product.utils.CardPromoUtils;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExtAdapter;

/**
 * <AUTHOR>
 * @date 2022/3/14
 */
@VPointOption(name = "默认-返回市场价",
        description = "有立减优惠时不展示市场价",
        code = "DefaultProductMarketPriceOpt",
        isDefault = true)
public class DefaultProductMarketPriceOpt extends ProductMarketPriceVP<Void> {

    @Override
    public String compute(ActivityCxt context, Param param, Void unused) {
        //有立减
        if (param.getProductM().getPromo(PromoTypeEnum.DIRECT_PROMO.getType()) != null) {
            return FloorsBuilderExtAdapter.EMPTY_VALUE;
        }
        //有卡优惠
        if (CardPromoUtils.hasCardPromo(param.getProductM().getPromoPrices())) {
            return FloorsBuilderExtAdapter.EMPTY_VALUE;
        }
        return param.getProductM().getMarketPrice();
    }
}
