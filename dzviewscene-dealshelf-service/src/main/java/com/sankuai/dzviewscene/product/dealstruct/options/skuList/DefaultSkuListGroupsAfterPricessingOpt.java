package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListGroupsAfterProcessingVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/8 5:29 下午
 */
@VPointOption(name = "所有sku列表组后置处理默认变化点", description = "所有sku列表组后置处理默认变化点",code = DefaultSkuListGroupsAfterPricessingOpt.CODE, isDefault = true)
public class DefaultSkuListGroupsAfterPricessingOpt extends SkuListGroupsAfterProcessingVP<DefaultSkuListGroupsAfterPricessingOpt.Config> {

    public static final String CODE = "DefaultSkuListGroupsAfterPricessingOpt";

    @Override
    public List<DealSkuGroupModuleVO> compute(ActivityCxt context, Param param, Config config) {
        return param.getSkuGroups();
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
