package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.douhu.DealDetailDouhuFetcher;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * created by z<PERSON><PERSON><PERSON>04 in 2021/12/14
 */
@Service
public class ModuleBuilderFactory {

    @Autowired
    private Map<String, ModuleStrategy> strategyMap;


    /**
     * 构建标准模块列表
     * @param activityCxt
     * @param dealDetailAssembleParam
     * @param moduleList
     * @return
     */
    public List<DealDetailModuleVO> buildModuleList(ActivityCxt activityCxt,
                                                    DealDetailAssembleParam dealDetailAssembleParam,
                                                    List<ModuleItem> moduleList) {
        List<DealDetailModuleVO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(moduleList)) {
            return Lists.newArrayList();
        }
        List<DouhuResultModel> douHuResultModels = activityCxt.getSource(DealDetailDouhuFetcher.CODE);
        for (ModuleItem item : moduleList) {
            String name = item.getStrategyName();
            List<String> douHuSkList = item.getDouHuSkList();
            if (!strategyMap.containsKey(name) || (CollectionUtils.isNotEmpty(douHuSkList) && !isHitDouHu(douHuResultModels, douHuSkList))) {
                continue;
            }
            ModuleStrategy moduleStrategy = strategyMap.get(name);
            DealDetailModuleVO dealDetailModuleVO = moduleStrategy.buildModelVO(activityCxt, dealDetailAssembleParam, item.getConfig());
            if (dealDetailModuleVO == null) {
                continue;
            }
            dealDetailModuleVO.setType(item.getType());
            if(item.getModuleName() != null){
                dealDetailModuleVO.setName(item.getModuleName());
            }
            resetType(activityCxt, dealDetailModuleVO, item);
            result.add(dealDetailModuleVO);
        }
        return result;
    }

    /**
     * 构建标准模块列表(自定义模块名称)
     */
    public List<DealDetailModuleVO> buildModuleListCustomName(ActivityCxt activityCxt,
                                                    DealDetailAssembleParam dealDetailAssembleParam,
                                                    List<ModuleItem> moduleList) {
        List<DealDetailModuleVO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(moduleList)) {
            return Lists.newArrayList();
        }
        for (ModuleItem item : moduleList) {
            String name = item.getStrategyName();
            if (!strategyMap.containsKey(name)) {
                continue;
            }
            ModuleStrategy moduleStrategy = strategyMap.get(name);
            DealDetailModuleVO dealDetailModuleVO = moduleStrategy.buildModelVO(activityCxt, dealDetailAssembleParam, item.getConfig());
            if (dealDetailModuleVO == null) {
                continue;
            }
            if(StringUtils.isBlank(dealDetailModuleVO.getName()) && StringUtils.isNotBlank(item.getModuleName())){
                dealDetailModuleVO.setName(item.getModuleName());
            }
            dealDetailModuleVO.setType(item.getType());
            result.add(dealDetailModuleVO);
        }
        return result;
    }

    private boolean isHitDouHu(List<DouhuResultModel> douHuResultModels, List<String> douHuSkList){
        if(CollectionUtils.isEmpty(douHuResultModels)){
            return false;
        }
        return douHuResultModels
                .stream()
                .filter(Objects::nonNull)
                .anyMatch(douhuResultModel -> douHuSkList.contains(douhuResultModel.getSk()));
    }

    /**
     * 足疗团详特殊场景。餐食为新数据时，需要使用新样式
     */
    private void resetType(ActivityCxt activityCxt, DealDetailModuleVO dealDetailModuleVO, ModuleItem item) {
        Boolean foodUseNewData = activityCxt.getParam(QueryFetcher.Params.foodUseNewData);
        if (foodUseNewData != null && foodUseNewData && StringUtils.isNotBlank(item.getNewType())) {
            dealDetailModuleVO.setType(item.getNewType());
        }
    }

}
