package com.sankuai.dzviewscene.product.ability.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreAsyncHandlerVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@VPointOption(name = "zdc标签查询", description = "zdc标签查询", code = ZdcTagIdFetcherOpt.CODE)
public class ZdcTagIdFetcherOpt extends PreAsyncHandlerVP<ZdcTagIdFetcherOpt.Config> {

    public static final String CODE = "ZdcTagIdFetcherOpt";

    public static final long TAG_PRESS_ON_NAILS_EXCLUSIVE = 20972L;

    @Resource
    private CompositeAtomService compositeAtomService;
    @Override
    public CompletableFuture<Object> compute(ActivityCxt context, Param param, Config config) {
        ShopM shopM = context.getParam(ShelfActivityConstants.Ctx.ctxShop);
        if(shopM == null){
            return CompletableFuture.completedFuture(Collections.emptyList());
        }
        if(MapUtils.isNotEmpty(config.getMockTagMap()) && config.getMockTagMap().containsKey(shopM.getLongShopId())){
            return CompletableFuture.completedFuture(config.getMockTagMap().get(shopM.getLongShopId()));
        }
        return compositeAtomService.findZdcTagByDpShopId(shopM.getLongShopId(), config.bizCode).thenApply(t->t);
    }
    @Data
    @VPointCfg
    public static class Config{
        private String bizCode = "";
        private Map<Long, List<Long>> mockTagMap = new HashMap<>();
    }
}
