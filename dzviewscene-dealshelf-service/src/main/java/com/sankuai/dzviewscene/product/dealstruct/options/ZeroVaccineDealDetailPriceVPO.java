package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.lion.client.Lion;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.price.vpoints.DealDetailPriceVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailPriceModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

@VPointOption(
        name = "团购详框架改版情价格组件变化点", description = "根据标识retailPriceStyle，判断团单的价格展示",
        code = ZeroVaccineDealDetailPriceVPO.CODE, isDefault = false
)
public class ZeroVaccineDealDetailPriceVPO extends DealDetailPriceVP<ZeroVaccineDealDetailPriceVPO.Config> {
    public static final String CODE = "ZeroVaccineDealDetailPriceVPO";

    private static final String DEFAULT_SALE_PRICE_TITLE = "团购价";

    private static final String DEFAULT_SALE_PRICE_FORMAT = "%s元";

    @Override
    public DealDetailPriceModel compute(ActivityCxt context, Param param, ZeroVaccineDealDetailPriceVPO.Config config) {
        String salePrice = param.getSalePrice();
        int dealId = param.getDealId();
        String retailPriceStyle = param.getRetailPriceStyle();
        String salePriceTag = null;
        String salePriceTitle = null;
        // retailPriceStyle为1为null 正常展示
        if (StringUtils.isNotBlank(config.getNormalSalePriceFormat()) && (("1").equals(retailPriceStyle) || StringUtils.isEmpty(retailPriceStyle))) {
            String normalSalePriceFormat = config.getNormalSalePriceFormat();
            salePriceTag = StringUtils.isEmpty(salePrice) ? null : String.format(normalSalePriceFormat, salePrice);
            salePriceTitle = StringUtils.isEmpty(config.getSalePriceTitle()) ? DEFAULT_SALE_PRICE_TITLE
                    : config.getSalePriceTitle();
        }
        // 起价
        if (StringUtils.isNotBlank(config.getStartSalePriceFormat()) && ("3").equals(retailPriceStyle)) {
            String startSalePriceFormat = config.getStartSalePriceFormat();
            salePriceTag = StringUtils.isEmpty(salePrice) ? null : String.format(startSalePriceFormat, salePrice);
            salePriceTitle = StringUtils.isEmpty(config.getSalePriceTitle()) ? DEFAULT_SALE_PRICE_TITLE
                    : config.getSalePriceTitle();
        }
        return buildDealDetailPriceModuleVO(dealId, salePriceTag, salePriceTitle);
    }

    private DealDetailPriceModel buildDealDetailPriceModuleVO(int dealId, String salePrice, String salePriceTitle) {
        if (StringUtils.isEmpty(salePrice)) {
            return null;
        }
        DealDetailPriceModel dealDetailPriceModel = new DealDetailPriceModel();
        dealDetailPriceModel.setDealId(dealId);
        if (StringUtils.isNotEmpty(salePrice)) {
            dealDetailPriceModel.setSalePrice(salePrice);
            dealDetailPriceModel.setSalePriceTitle(salePriceTitle);
        }
        return dealDetailPriceModel;
    }
    @Data
    @VPointCfg
    public static class Config {
        private String salePriceTitle;
        private String normalSalePriceFormat;
        private String startSalePriceFormat;
    }
}
