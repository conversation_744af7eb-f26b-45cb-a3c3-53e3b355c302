package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.productshelf.vu.vo.WarmUpVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/19
 */

@VPoint(name = "预热倒计时", description = "预热倒计时", code = ProductWarmUpVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductWarmUpVP<T> extends PmfVPoint<WarmUpVO, ProductWarmUpVP.Param, T> {

    public static final String CODE = "ProductWarmUpVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {

        private ProductM productM;
    }
}