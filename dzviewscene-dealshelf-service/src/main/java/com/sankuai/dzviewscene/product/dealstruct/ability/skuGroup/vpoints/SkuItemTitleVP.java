package com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.DealDetailSkuGroupsBuilder;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * created by zhang<PERSON>yuan04 in 2022/1/4
 */
@VPoint(name = "Sku元素标题构造能力", description = "构造Sku元素标题信息", code = SkuItemTitleVP.CODE, ability = DealDetailSkuGroupsBuilder.CODE)
public abstract class SkuItemTitleVP<C> extends PmfVPoint<String, SkuItemTitleVP.Param, C> {

    public static final String CODE = "SkuItemTitleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {

        /**
         * 是否必选
         */
        private boolean isMust;

        /**
         * 分组标题
         */
        private String setTitle;

        /**
         * Sku信息
         */
        private SkuItemDto skuItemDto;

        /**
         * Sku category信息
         */
        private List<ProductSkuCategoryModel> productCategories;
    }
}
