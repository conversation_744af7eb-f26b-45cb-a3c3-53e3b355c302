package com.sankuai.dzviewscene.product.dealstruct.options.standardservice;

import com.dianping.cat.Cat;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.servicefeatures.vcpoints.DealDetailStandardServiceModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.StandardServiceVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/05/23 10:16 下午
 */
@VPointOption(name = "团购详情标准服务模块变化点", description = "团购详情标准服务模块变化点-根据属性判断是否展示", code = StandardServiceAttrItemsVPO.CODE, isDefault = true)
public class StandardServiceAttrItemsVPO extends DealDetailStandardServiceModuleVP<StandardServiceAttrItemsVPO.Config> {

    public static final String CODE = "StandardServiceAttrItemsVPO";

    @Override
    public StandardServiceVO compute(ActivityCxt context, Param param, Config config) {
        if(param == null || CollectionUtils.isEmpty(param.getDealAttrs()) || config == null || MapUtils.isEmpty(config.getAttrKey2Values())) {
            return null;
        }
        if(checkDealAttrByConfig(param.getDealAttrs(), config.getAttrKey2Values())) {
            return config.getStandardServiceVO();
        }
        return null;
    }

    private boolean checkDealAttrByConfig(List<AttrM> dealAttrs, Map<String, List<String>> attrKey2Values) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.dealstruct.options.standardservice.StandardServiceAttrItemsVPO.checkDealAttrByConfig(java.util.List,java.util.Map)");
        return attrKey2Values.entrySet().stream().allMatch(entity -> {
            String attrValue = getAttrValue(dealAttrs, entity.getKey());
            return StringUtils.isNotEmpty(attrValue) && CollectionUtils.isNotEmpty(entity.getValue()) && entity.getValue().contains(attrValue);
       });
    }

    private String getAttrValue(List<AttrM> dealAttrs, String key) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.dealstruct.options.standardservice.StandardServiceAttrItemsVPO.getAttrValue(java.util.List,java.lang.String)");
        return dealAttrs.stream()
                .filter(Objects::nonNull).filter(attr -> key.equals(attr.getName()))
                .map(AttrM::getValue).filter(Objects::nonNull).findFirst().orElse(null);
    }

    @Data
    @VPointCfg
    public static class Config {
        private StandardServiceVO standardServiceVO;
        //需要判断的属性key和对应的value
        private Map<String, List<String>> attrKey2Values;
    }
}
