package com.sankuai.dzviewscene.product.filterlist.option.builder.product.title;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTitleVP;
import com.sankuai.dzviewscene.product.filterlist.option.factory.MassageFactory;
import com.sankuai.dzviewscene.product.filterlist.option.factory.MassageStrategy;
import com.sankuai.dzviewscene.product.filterlist.utils.SkuItemUtils;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;

/**
 * @author: created by hang.yu on 2024/4/9 11:28
 */
@VPointOption(name = "足疗商品筛选列表名称展示Opt",
        description = "足疗商品筛选列表名称展示Opt",
        code = "MassageTitleOpt")
public class MassageTitleOpt extends ProductTitleVP<MassageTitleOpt.Config> {

    public static final String SERVICE_TYPE = "service_type";

    @Resource
    private MassageFactory massageFactory;

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        ProductM productM = param.getProductM();
        if (productM.isTimesDealQueryFlag()) {
            return getTimesDealTitle(param.getProductM(), config);
        }
        SkuItemDto skuItem = SkuItemUtils.getSkuItem(productM);
        if (skuItem == null) {
            return productM.getTitle();
        }
        MassageStrategy strategy = massageFactory.getStrategy(skuItem.getProductCategory());
        if (strategy == null) {
            return productM.getTitle();
        }
        String filterListTitle = strategy.getFilterListTitle(skuItem, DealDetailUtils.getAttrSingleValueByAttrName(productM.getExtAttrs(), SERVICE_TYPE));
        if (StringUtils.isBlank(filterListTitle)) {
            return productM.getTitle();
        }
        return filterListTitle;
    }

    public String getTimesDealTitle(ProductM productM, Config config) {
        // 判断交易类型是团购次卡
        if (!productM.isTimesDeal()) {
            return String.format(config.getTitleTemplate(), TimesDealUtil.ONCE,
                    productM.getSalePrice().stripTrailingZeros().toPlainString());
        }
        // 获取多次卡的次数
        String times = productM.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER);
        String singlePrice = TimesDealUtil.getSinglePrice(productM.getSalePrice(), times);
        if (StringUtils.isBlank(singlePrice)) {
            return null;
        }
        return String.format(config.getTitleTemplate(), times, singlePrice);
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 返回描述的模板
         */
        private String titleTemplate = "%s次 单次¥%s";

    }
}
