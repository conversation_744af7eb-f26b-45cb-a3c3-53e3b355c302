package com.sankuai.dzviewscene.product.filterlist.option.builder.product.picpadding;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPicPaddingVP;

/**
 * <AUTHOR>
 * @date 2022/5/1
 */
@VPointOption(name = "空实现，不填充任何数据",
        description = "",
        code = "NullProductPicPaddingOpt")
public class NullProductPicPaddingOpt extends ProductPicPaddingVP<Void> {
    @Override
    public Void compute(ActivityCxt context, Param param, Void unused) {
        return null;
    }
}
