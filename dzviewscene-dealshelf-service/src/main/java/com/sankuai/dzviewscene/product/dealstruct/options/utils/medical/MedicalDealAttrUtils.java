package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor.*;
import com.sankuai.dzviewscene.product.dealstruct.vo.*;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.*;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import net.sf.oval.ogn.ObjectGraphNavigatorJXPathImpl;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class MedicalDealAttrUtils {

    private static final Integer EQUAL_TYPE = 1;

    private static final Integer NON_EQUAL_TYPE = 2;

    private static final Integer CONTAINS = 3;

    private static final Map<Integer, ValueProcessor> processorMap = new HashMap<>();

    static {
        processorMap.put(ValueProcessorEnum.NORMAL.getCode(), new NormalValueProcessor());
        processorMap.put(ValueProcessorEnum.MAPPING.getCode(), new MappingValueProcessor());
        processorMap.put(ValueProcessorEnum.REGEX.getCode(), new RegexValueProcessor());
        processorMap.put(ValueProcessorEnum.LINKED.getCode(), new LinkedValueProcessor());
        processorMap.put(ValueProcessorEnum.ORIGINAL.getCode(), new OriginalValueProcessor());
        processorMap.put(ValueProcessorEnum.SUB_PROJECT_NUM.getCode(), new SubProjectNumProcessor());
    }

    public static Map<String, String> convertName2ValueMap(DealDetailInfoModel infoModel) {
        return Optional.ofNullable(infoModel)
                .map(DealDetailInfoModel::getDealAttrs)
                .orElse(Lists.newArrayList()).stream()
                .collect(Collectors.toMap(AttrM::getName, AttrM::getValue, (a, b) -> a));
    }

    public static boolean validate(Map<String, String> name2ValueMap, Object object) {
        return ObjectUtils.isEmpty(name2ValueMap) || ObjectUtils.isEmpty(object);
    }

    //第一层title，dealSkuList
    public static List<DealSkuGroupModuleVO> buildDealSkusByAttrs(Map<String, String> name2ValueMap, List<SkuGroupModuleConfig> skuGroupModuleConfigs) {
        if (validate(name2ValueMap, skuGroupModuleConfigs)) {
            return null;
        }
        return skuGroupModuleConfigs.stream().map(skuGroupModuleConfig -> {
            String title = buildCommonAttrValue(skuGroupModuleConfig.getTitleConfig(), name2ValueMap, null);
            List<DealSkuVO> skuVOS = buildSkuList(skuGroupModuleConfig.getSkuConfigList(), name2ValueMap);
            if ((!ObjectUtils.isEmpty(skuGroupModuleConfig.getTitleConfig()) && ObjectUtils.isEmpty(title)) ||
                    (!ObjectUtils.isEmpty(skuGroupModuleConfig.getSkuConfigList()) && ObjectUtils.isEmpty(skuVOS))) {
                return null;
            }
            DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
            dealSkuGroupModuleVO.setTitle(title);
            dealSkuGroupModuleVO.setDealSkuList(skuVOS);
            return dealSkuGroupModuleVO;
        }).filter(v -> !ObjectUtils.isEmpty(v)).collect(Collectors.toList());
    }

    // 第二层title、subtitle、items
    private static List<DealSkuVO> buildSkuList(List<SkuConfig> skuConfigList, Map<String, String> name2ValueMap) {
        if (validate(name2ValueMap, skuConfigList)) {
            return null;
        }
        return skuConfigList.stream().map(skuConfig -> {
            String title = buildCommonAttrValue(skuConfig.getTitleConfig(), name2ValueMap, null);
            String priceItem = null;
            boolean isSumPrice = isSumPrice(skuConfig);
            if (!isSumPrice) {
                priceItem = buildCommonAttrValue(skuConfig.getPriceItemConfig(), name2ValueMap, null);
            }
            String subTitle = buildCommonAttrValue(skuConfig.getSubTitleConfig(), name2ValueMap, null);
            List<DealSkuItemVO> skuItemVOList = buildSkuItems(skuConfig.getSkuItemConfigList(), name2ValueMap);
            if ((!ObjectUtils.isEmpty(skuConfig.getTitleConfig()) && ObjectUtils.isEmpty(title)) ||
                    (!ObjectUtils.isEmpty(skuConfig.getSubTitleConfig()) && ObjectUtils.isEmpty(subTitle)) ||
                    (!ObjectUtils.isEmpty(skuConfig.getSkuItemConfigList()) && ObjectUtils.isEmpty(skuItemVOList))) {
                return null;
            }
            DealSkuVO dealSkuVO = new DealSkuVO();
            dealSkuVO.setTitle(title);
            dealSkuVO.setSubTitle(subTitle);
            if (!ObjectUtils.isEmpty(skuConfig.getType())) {
                dealSkuVO.setType(skuConfig.getType());
            }
            dealSkuVO.setItems(skuItemVOList);
            if (isSumPrice && CollUtil.isNotEmpty(skuItemVOList)) {
                priceItem = sumPrice(skuConfig.getPriceItemConfig(), skuItemVOList);
            }
            if((!ObjectUtils.isEmpty(skuConfig.getPriceItemConfig()) && ObjectUtils.isEmpty(priceItem))){
                return null;
            }
            dealSkuVO.setPrice(priceItem);
            return dealSkuVO;
        }).filter(v -> !ObjectUtils.isEmpty(v)).collect(Collectors.toList());
    }

    private static String sumPrice(SkuItemValueConfig priceItemConfig, List<DealSkuItemVO> skuItemVOList) {
        ValueConfig valueConfig = priceItemConfig.getValueKeys().get(0);
        String regex = valueConfig.getRegex();
        Pattern pattern = Pattern.compile(regex);
        BigDecimal sum = new BigDecimal(0);
        for (DealSkuItemVO dealSkuItemVO : skuItemVOList) {
            if (dealSkuItemVO == null || dealSkuItemVO.getValue() == null) {
                continue;
            }
            String value = dealSkuItemVO.getValue();
            Matcher matcher = pattern.matcher(value);
            if (matcher.find()) {
                sum = sum.add(new BigDecimal(matcher.group(1)));
            }
        }
        sum = sum.setScale(2, RoundingMode.HALF_UP);
        String sumString = null;
        if (sum.stripTrailingZeros().scale() <= 0) {
            sumString = sum.toBigInteger().toString();
        } else {
            sumString = sum.toString().replaceAll("0*$", "");
        }
        if (StringUtils.isNotEmpty(valueConfig.getFormat())) {
            return String.format(valueConfig.getFormat(), sumString);
        }
        return sumString;
    }

    private static boolean isSumPrice(SkuConfig skuConfig) {
        return Optional.ofNullable(skuConfig.getPriceItemConfig())
                .map(SkuItemValueConfig::getValueKeys)
                .filter(config -> !config.isEmpty())
                .map(config -> config.get(0))
                .map(ValueConfig::getProcessType)
                .filter(type -> ValueProcessorEnum.SUM_PRICE.getCode().equals(type))
                .isPresent();
    }

    //第三层 name、valueAttrs
    private static List<DealSkuItemVO> buildSkuItems(List<SkuItemConfig> skuItemConfigList, Map<String, String> name2ValueMap) {
        if (skuItemConfigList == null) {
            return null;
        }
        ArrayList<SkuItemConfig> skuItemConfigsClone = new ArrayList<>(skuItemConfigList);
        //处理value为数组的类型，展开配置到skuItemConfigList中
        flattenArrayConfiguration(skuItemConfigsClone, name2ValueMap);

        if (validate(name2ValueMap, skuItemConfigsClone)) {
            return null;
        }

        return skuItemConfigsClone.stream().map(skuItemConfig -> {
            // 这里简单处理下吧
            if (!validateSkuItem(skuItemConfig, name2ValueMap)) {
                return null;
            }

            List<SkuAttrAttrItemVO> valueAttrs = buildSkuAttrAttrItemVOS(skuItemConfig.getSkuItemValueConfigList(), name2ValueMap);
            String name = buildCommonAttrValue(skuItemConfig.getNameConfig(), name2ValueMap, null);
            String value = buildCommonAttrValue(skuItemConfig.getValueConfig(), name2ValueMap, valueAttrs);
            if ((ObjectUtils.isEmpty(skuItemConfig.getNameConfig()) && ObjectUtils.isEmpty(name)) ||
                    (!ObjectUtils.isEmpty(skuItemConfig.getSkuItemValueConfigList()) && ObjectUtils.isEmpty(valueAttrs)) ||
                    (!ObjectUtils.isEmpty(skuItemConfig.getValueConfig()) && ObjectUtils.isEmpty(value))) {
                return null;
            }

            DealSkuItemVO itemVO = new DealSkuItemVO();
            itemVO.setName(name);
            if (!ObjectUtils.isEmpty(skuItemConfig.getType())) {
                itemVO.setType(skuItemConfig.getType());
            }
            itemVO.setValue(value);
            itemVO.setValueAttrs(valueAttrs);
            itemVO.setExtraExplain(buildExtraExplain(skuItemConfig.getExplainConfig(), name2ValueMap));
            return itemVO;
        }).filter(v -> !ObjectUtils.isEmpty(v)).collect(Collectors.toList());
    }

    private static void flattenArrayConfiguration(List<SkuItemConfig> skuItemConfigList, Map<String, String> name2ValueMap) {
        List<SkuItemConfig> arrayConfig = filterArrayTypes(skuItemConfigList);
        skuItemConfigList.removeAll(arrayConfig);
        for (SkuItemConfig skuItemConfig : arrayConfig) {
            String key = getKey(skuItemConfig.getNameConfig(), 0);
            String[] nameKey = getKeys(skuItemConfig.getNameConfig(), 1);
            String[] valueKey = getKeys(skuItemConfig.getValueConfig(), 1);
            String[] itemKey = null;
            if (CollUtil.isNotEmpty(skuItemConfig.getSkuItemValueConfigList())) {
                itemKey = getKeys(skuItemConfig.getSkuItemValueConfigList().get(0).getNameConfig(), 1);
            }
            JSONArray array = getJSONArray(name2ValueMap, key);
            for (Object o : array) {
                JSONObject obj = (JSONObject)o;
                String nValue = getValue(obj, nameKey);
                if (nValue == null) {
                    continue;
                }
                SkuItemConfig itemConfig = new SkuItemConfig();
                skuItemConfigList.add(itemConfig);
                itemConfig.setType(skuItemConfig.getType());
                itemConfig.setNameConfig(convertToSkuItemConfig(getFormat(skuItemConfig.getNameConfig()), nValue, null));

                String vValue = getValue(obj, valueKey);
                if (vValue != null) {
                    Boolean priceProcess = skuItemConfig.getValueConfig().getValueKeys().get(0).getPriceProcess();
                    itemConfig.setValueConfig(convertToSkuItemConfig(getFormat(skuItemConfig.getValueConfig()), vValue, priceProcess));
                }

                String itemValue = getValue(obj, itemKey);
                if (itemValue != null) {
                    itemConfig.setSkuItemValueConfigList(new ArrayList<>());
                    ValueAttrConfig config = new ValueAttrConfig();
                    itemConfig.getSkuItemValueConfigList().add(config);
                    config.setNameConfig(convertToSkuItemConfig(getFormat(skuItemConfig.getSkuItemValueConfigList().get(0).getNameConfig()), itemValue, null));
                }
            }
        }
    }

    private static void flattenAttrArrayConfiguration(List<ValueAttrConfig> valueAttrConfigs, Map<String, String> name2ValueMap) {
        List<ValueAttrConfig> arrayConfig = filterAttrArrayTypes(valueAttrConfigs);
        valueAttrConfigs.removeAll(arrayConfig);
        for (ValueAttrConfig attrConfig : arrayConfig) {
            String key = getKey(attrConfig.getNameConfig(), 0);
            String[] nameKey = getKeys(attrConfig.getNameConfig(), 1);
            JSONArray array = getJSONArray(name2ValueMap, key);
            for (Object o : array) {
                JSONObject obj = (JSONObject)o;
                String nValue = getValue(obj, nameKey);
                if (nValue == null) {
                    continue;
                }
                if (StringUtils.isNotEmpty(attrConfig.getNameConfig().getSplit())) {
                    for (String v : nValue.split(attrConfig.getNameConfig().getSplit())) {
                        addConfig(valueAttrConfigs, attrConfig, v);
                    }
                } else {
                    addConfig(valueAttrConfigs, attrConfig, nValue);
                }
            }
        }
    }

    private static void addConfig(List<ValueAttrConfig> valueAttrConfigs, ValueAttrConfig attrConfig, String v) {
        ValueAttrConfig config = new ValueAttrConfig();
        valueAttrConfigs.add(config);
        config.setNameConfig(convertToSkuItemConfig(getFormat(attrConfig.getNameConfig()), v, null));
        config.setValueConfigList(attrConfig.getValueConfigList());
        config.setPopConfig(attrConfig.getPopConfig());
    }

    private static SkuItemValueConfig convertToSkuItemConfig(String format, String value, Boolean priceProcess) {
        SkuItemValueConfig config = new SkuItemValueConfig();
        ArrayList<ValueConfig> valueKeys = new ArrayList<>();
        config.setValueKeys(valueKeys);
        ValueConfig valueConfig = new ValueConfig();
        valueKeys.add(valueConfig);
        valueConfig.setProcessType(ValueProcessorEnum.ORIGINAL.getCode());
        if (StringUtils.isNotEmpty(format)) {
            value = String.format(format, value);
        }
        valueConfig.setKey(value);
        valueConfig.setPriceProcess(priceProcess);
        return config;
    }

    private static List<SkuItemConfig> filterArrayTypes(List<SkuItemConfig> skuItemConfigList) {
        return skuItemConfigList.stream()
                .filter(skuItemConfig -> Optional.ofNullable(skuItemConfig)
                .map(SkuItemConfig::getNameConfig)
                .map(SkuItemValueConfig::getValueKeys)
                .filter(CollectionUtil::isNotEmpty)
                .map(values -> values.get(0))
                .map(ValueConfig::getProcessType)
                .filter(type -> type.equals(ValueProcessorEnum.ARRAY.getCode()))
                .isPresent()).collect(Collectors.toList());
    }

    private static List<ValueAttrConfig> filterAttrArrayTypes(List<ValueAttrConfig> valueAttrConfigList) {
        return valueAttrConfigList.stream()
                .filter(valueAttrConfig -> Optional.ofNullable(valueAttrConfig)
                        .map(ValueAttrConfig::getNameConfig)
                        .map(SkuItemValueConfig::getValueKeys)
                        .filter(CollectionUtil::isNotEmpty)
                        .map(values -> values.get(0))
                        .map(ValueConfig::getProcessType)
                        .filter(type -> type.equals(ValueProcessorEnum.ARRAY.getCode()))
                        .isPresent()).collect(Collectors.toList());
    }

    //第四层 attrattritem
    public static List<SkuAttrAttrItemVO> buildSkuAttrAttrItemVOS(List<ValueAttrConfig> skuItemValueConfigList, Map<String, String> name2ValueMap) {
        if (skuItemValueConfigList == null){
            return null;
        }
        //处理数组的类型，展开配置到skuItemValueConfigList中
        List<ValueAttrConfig> skuItemValueConfigListClone = new ArrayList<>(skuItemValueConfigList);
        flattenAttrArrayConfiguration(skuItemValueConfigListClone, name2ValueMap);
        if (validate(name2ValueMap, skuItemValueConfigListClone)) {
            return null;
        }
        return skuItemValueConfigListClone.stream().map(valueAttrConfig -> {
            String name = buildCommonAttrValue(valueAttrConfig.getNameConfig(), name2ValueMap, null);
            List<CommonAttrVO> values = buildCommonAttrs(valueAttrConfig.getValueConfigList(), name2ValueMap);
            if ((!ObjectUtils.isEmpty(valueAttrConfig.getNameConfig()) && ObjectUtils.isEmpty(name)) ||
                    (!ObjectUtils.isEmpty(valueAttrConfig.getValueConfigList()) && ObjectUtils.isEmpty(values))) {
                return null;
            }
            SkuAttrAttrItemVO itemVO = new SkuAttrAttrItemVO();
            itemVO.setName(name);
            itemVO.setValues(values);
            itemVO.setPopup(buildSkuPopConfig(valueAttrConfig.getPopConfig(), name2ValueMap));
            return itemVO;
        }).filter(v -> !ObjectUtils.isEmpty(v)).collect(Collectors.toList());
    }

    public static PopUpWindowVO buildSkuPopConfig(SkuPopConfig popConfig, Map<String, String> name2ValueMap) {
        if(popConfig == null) {
            return null;
        }
        String content = buildCommonAttrValue(popConfig.getContentConfig(), name2ValueMap, null);
        /**无内容 或 icon 直接返回 null*/
        if(ObjectUtils.isEmpty(content) || ObjectUtils.isEmpty(popConfig.getIcon())){
            return null;
        }
        PopUpWindowVO popUp = new PopUpWindowVO();
        popUp.setIcon(popConfig.getIcon());
        popUp.setTitle(popConfig.getTitle());
        popUp.setContent(content);
        popUp.setType(popConfig.getType());
        return popUp;
    }

    //这里如果需要更多层级 就继续新增
    private static List<CommonAttrVO> buildCommonAttrs(List<SkuItemValueConfig> valueConfigList, Map<String, String> name2ValueMap) {
        if (validate(name2ValueMap, valueConfigList)) {
            return null;
        }
        return valueConfigList.stream().map(valueConfig -> {
            String value = buildCommonAttrValue(valueConfig, name2ValueMap, null);
            if (ObjectUtils.isEmpty(value)) {
                return null;
            }
            CommonAttrVO commonAttrVO = new CommonAttrVO();
            commonAttrVO.setName(value);
            return commonAttrVO;
        }).filter(v -> !ObjectUtils.isEmpty(v)).collect(Collectors.toList());
    }

    public static String buildCommonAttrValue(SkuItemValueConfig valueConfig, Map<String, String> name2ValueMap, Object data) {
        if (ObjectUtils.isEmpty(valueConfig) || ObjectUtils.isEmpty(valueConfig.getValueKeys())
                || ObjectUtils.isEmpty(name2ValueMap)) {
            return null;
        }
        List<String> valueList = valueConfig.getValueKeys().stream().map(valueKey -> {
            ValueProcessor valueProcessor = processorMap.get(valueKey.getProcessType());
            if (ObjectUtils.isEmpty(valueProcessor)) {
                return null;
            }
            return valueProcessor.process(valueKey, name2ValueMap, data);
        }).filter(v -> !ObjectUtils.isEmpty(v)).flatMap(Collection::stream).collect(Collectors.toList());

        if (ObjectUtils.isEmpty(valueList)) {
            return null;
        }
        StringBuilder builder = new StringBuilder();
        if (!ObjectUtils.isEmpty(valueConfig.getBeforeText())) {
            builder.append(valueConfig.getBeforeText());
        }
        if (!ObjectUtils.isEmpty(valueConfig.getMultiChoiceFormat()) && valueList.size() > 1) {
            builder.append(String.format(valueConfig.getMultiChoiceFormat(), valueList.size()));
        }
        if (!ObjectUtils.isEmpty(valueConfig.getMidText())) {
            builder.append(valueConfig.getMidText());
        }
        if (valueConfig.getNeedRealValues()) {
            builder.append(String.join(ObjectUtils.isEmpty(valueConfig.getSplit()) ? "" : valueConfig.getSplit(), valueList));
        }
        if (valueConfig.getNeedPostType() && !ObjectUtils.isEmpty(valueConfig.getMultiChoiceFormatPost()) && valueList.size() > 1) {
            builder.append(String.format(valueConfig.getMultiChoiceFormatPost(), valueList.size()));
        }
        return builder.toString();
    }

    private static ExtraExplainVO buildExtraExplain(SkuItemExplainConfig explainConfig, Map<String, String> name2ValueMap) {
        if (ObjectUtils.isEmpty(explainConfig) || !validateSkuItemExplain(explainConfig, name2ValueMap)) {
            return null;
        }

        List<CommonAttrConfig> validateInfos = Optional.ofNullable(explainConfig.getPopup())
                .map(PopUpWindowConfig::getInfos)
                .orElse(Lists.newArrayList())
                .stream()
                .filter(info -> info.getValidateType() == null
                        || info.getValidateKey() == null
                        || info.getValidateValue() == null
                        || validateInfo(info, name2ValueMap))
                .collect(Collectors.toList());
        Optional.ofNullable(explainConfig.getPopup())
                .ifPresent(poup -> poup.setInfos(validateInfos));

        return buildExtraExplainVO(explainConfig, name2ValueMap);
    }

    private static ExtraExplainVO buildExtraExplainVO(SkuItemExplainConfig explainConfig, Map<String, String> name2ValueMap) {
        ExtraExplainVO extraExplainVO = new ExtraExplainVO();
        extraExplainVO.setIcon(explainConfig.getIcon());
        if (StringUtils.isNotEmpty(explainConfig.getTitle())) {
            extraExplainVO.setTitle(explainConfig.getTitle());
        } else if (explainConfig.getTitleConfig() != null) {
            extraExplainVO.setTitle(buildCommonAttrValue(explainConfig.getTitleConfig(), name2ValueMap, null));
        }

        if (StringUtils.isEmpty(extraExplainVO.getTitle())) {
            return null;
        }

        if (explainConfig.getPopup() == null) {
            return extraExplainVO;
        }
        PopUpWindowConfig popupConfig = explainConfig.getPopup();
        PopUpWindowVO popup = new PopUpWindowVO();
        extraExplainVO.setPopup(popup);
        popup.setIcon(popupConfig.getIcon());
        popup.setContent(popupConfig.getContent());
        popup.setTitle(popupConfig.getTitle());
        popup.setType(popupConfig.getType());

        if (CollUtil.isEmpty(popupConfig.getInfos())) {
            return extraExplainVO;
        }
        List<CommonAttrConfig> infosConfig = popupConfig.getInfos();
        ArrayList<CommonAttrVO> infos = new ArrayList<>();
        popup.setInfos(infos);

        List<CommonAttrsVO> commonAttrs = new ArrayList<>();
        for (CommonAttrConfig attrConfig : infosConfig) {
            if (Boolean.TRUE.equals(attrConfig.getAddCommonAttrsToLast()) && CollUtil.isNotEmpty(attrConfig.getCommonAttrs())) {
                commonAttrs.addAll(attrConfig.getCommonAttrs());
                continue;
            }
            CommonAttrVO commonAttrVO = new CommonAttrVO();
            commonAttrVO.setName(attrConfig.getName());
            commonAttrVO.setValue(attrConfig.getValue());
            commonAttrVO.setPic(attrConfig.getPic());
            commonAttrVO.setCommonAttrs(attrConfig.getCommonAttrs());
            infos.add(commonAttrVO);
        }

        if (CollUtil.isNotEmpty(infos) && CollUtil.isNotEmpty(commonAttrs)) {
            CommonAttrVO last = infos.get(infos.size() - 1);
            if (CollUtil.isEmpty(last.getCommonAttrs())) {
                last.setCommonAttrs(commonAttrs);
            } else {
                last.getCommonAttrs().addAll(commonAttrs);
            }
        }

        return extraExplainVO;
    }

    private static boolean validateInfo(CommonAttrConfig config, Map<String, String> name2ValueMap) {
        return validateSkuItem(name2ValueMap, config.getValidateKey(), config.getValidateType(), config.getValidateValue());
    }

    private static boolean validateSkuItemExplain(SkuItemExplainConfig itemConfig, Map<String, String> name2ValueMap) {
        return validateSkuItem(name2ValueMap, itemConfig.getValidateKey(), itemConfig.getValidateType(), itemConfig.getValidateValue());
    }

    private static boolean validateSkuItem(Map<String, String> name2ValueMap, String validateKey, Integer validateType, String validateValue) {
        String value = name2ValueMap.get(validateKey);
        List<String> validateValues = new ArrayList<>();
        if (validateValue != null) {
            validateValues = Lists.newArrayList(validateValue.split("/"));
        }
        if (Objects.equals(EQUAL_TYPE, validateType)) {
            return validateValues.contains(value);
        } else if (Objects.equals(NON_EQUAL_TYPE, validateType)) {
            return !validateValues.contains(value);
        } else if (Objects.equals(CONTAINS, validateType)) {
            for (String s : validateValues) {
                if (value != null && value.contains(s)) {
                    return true;
                }
            }
            return false;
        }
        return true;
    }

    private static boolean validateSkuItem(SkuItemConfig itemConfig, Map<String, String> name2ValueMap) {
        return validateSkuItem(name2ValueMap, itemConfig.getValidateKey(), itemConfig.getValidateType(), itemConfig.getValidateValue());
    }

    private static JSONArray getJSONArray(Map<String, String> name2ValueMap, String key) {
        String arrayString = name2ValueMap.get(key);
        if (StringUtils.isEmpty(arrayString)) {
            return new JSONArray();
        }
        return JSON.parseArray(arrayString);
    }


    private static String getKey(SkuItemValueConfig nameConfig, int index) {
        String nameKey = nameConfig.getValueKeys().get(0).getKey();
        if (StringUtils.isEmpty(nameKey)) {
            return null;
        }
        String[] nameSplit = nameKey.split("\\.");
        if (nameSplit.length > index) {
            return nameSplit[index];
        }
        return null;
    }

    private static String[] getKeys(SkuItemValueConfig nameConfig, int startIndex) {
        if (nameConfig == null || CollUtil.isEmpty(nameConfig.getValueKeys())) {
            return null;
        }
        String nameKey = nameConfig.getValueKeys().get(0).getKey();
        if (StringUtils.isEmpty(nameKey)) {
            return null;
        }
        String[] nameSplit = nameKey.split("\\.");
        if (nameSplit.length > startIndex) {
            String[] keys = new String[nameSplit.length - startIndex];
            for (int i = startIndex, j = 0; i < nameSplit.length; i++, j++) {
                keys[j] = nameSplit[i];
            }
            return keys;
        }
        return null;
    }

    private static String getValue(JSONObject obj, String[] nameKey) {
        if (nameKey == null || nameKey.length == 0) {
            return null;
        }
        JSONObject temp = obj;
        for (int i = 0; i < nameKey.length; i++) {
            if (!temp.containsKey(nameKey[i])) {
                return null;
            }
            if (i == nameKey.length - 1) {
                return temp.getString(nameKey[i]);
            }
            temp = temp.getJSONObject(nameKey[i]);
        }
        return null;
    }

    private static String getFormat(SkuItemValueConfig nameConfig){
        return nameConfig.getValueKeys().get(0).getFormat();
    }
}
