package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.lang.DateUtils;
import com.google.common.collect.Lists;
import com.googlecode.aviator.AviatorEvaluator;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.ActivityTag;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.product.filterlist.utils.LifeCleanUtils;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.uniform.UniformStructContentModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.uniform.UniformStructModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;

import java.util.*;
import java.util.stream.Collectors;

/***
 * <AUTHOR>
 * @date 2023/4/13
 * @apiNote
 */
@VPointOption(name = "生活服务保洁DealAttrVO列表变化点", description = "生活服务保洁DealAttrVO列表变化点",code = LifeCleanAttrVOListOpt.CODE)
@Slf4j
public class LifeCleanAttrVOListOpt extends DealAttrVOListVP<LifeCleanAttrVOListOpt.Config> {

    public static final String CODE = "LifeCleanSkuAttrOpt";



    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        if (config == null || param == null) {
            return null;
        }
        List<AttrM> attrs = new ArrayList<>();
        boolean isSelfSupport = LifeCleanUtils.isLifeClearSelf(param.getDealDetailInfoModel().getDealAttrs());
        //1.获取团单属性
        List<AttrM> dealAttrs = param.getDealAttrs();
        if (CollectionUtils.isNotEmpty(dealAttrs)) {
            attrs.addAll(dealAttrs);
        }
        //2.获取第一个sku属性
        List<AttrM> firstSkuAttrs = extractFirstSkuAttrFromDealDetailDtoModel(param.getDealDetailDtoModel());
        if (CollectionUtils.isNotEmpty(firstSkuAttrs)) {
            attrs.addAll(firstSkuAttrs);
        }
        //3.构造DealDetailStructAttrModuleVO列表
        return config.getAttrListGroupModels().stream().map(model -> buildDealDetailStructAttrModuleVOList(model, attrs, isSelfSupport)).filter(model -> model != null).collect(Collectors.toList());
    }

    public DealDetailStructAttrModuleGroupModel buildDealDetailStructAttrModuleVOList(AttrListGroupModel attrListGroupModel, List<AttrM> attrs, boolean isSelfSupport) {
        if (isSelfSupport && !attrListGroupModel.isSelfSupport()) {
            return null;
        }
        if (attrListGroupModel == null || CollectionUtils.isEmpty(attrListGroupModel.getAttrModelList()) || CollectionUtils.isEmpty(attrs)) {
            return null;
        }
        List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOS = attrListGroupModel.getAttrModelList().stream().map(model -> buildDealDetailStructAttrModuleVO(model, attrs)).filter(model -> model != null).collect(Collectors.toList());
        return buildDealDetailStructAttrModuleGroupModel(attrListGroupModel.getGroupName(), dealDetailStructAttrModuleVOS);
    }

    private DealDetailStructAttrModuleVO buildDealDetailStructAttrModuleVO(MergeSortMapJoinFilterAttrModel attrModel, List<AttrM> attrs) {
        if (CollectionUtils.isEmpty(attrs) || attrModel == null) {
            return null;
        }
        //对一个模型加入了过滤条件
        if(!isConfigApplicable(attrModel, attrs)){
            return null;
        }
        //属性值获取
        List<String> attrValues = getAttrValues(attrModel, attrs);
        //获取排序/过滤/映射模型
        List<AttrValueMapModel> attrValueMapModels = attrModel.getAttrValueMapModels();
        //属性值排序
        attrValues = sortAttrValues(attrValues, attrValueMapModels);
        //属性值拼接
        attrValues = joinAttrValue(attrValues, attrModel);
        String icon = getIcon(attrModel, attrs);
        String subTitle = getSubTitle(attrModel, attrs);
        ActivityTags activityTag = getActivityTag(attrModel, attrs);
        DealDetailStructAttrModuleVO attrModuleFromActivityVO = buildDealDetailStructForActivityModuleVO(attrModel.getActivityDisplayInfos());
        return attrModuleFromActivityVO == null ? buildDealDetailStructAttrModuleVO(attrModel.getDisplayName(), attrValues, icon, subTitle, activityTag) : attrModuleFromActivityVO;
    }

    /**
     *
     * @param model 配置
     * @param dealAttrs 团单的属性
     * @return 这个配置(model)是否适用于这次场景，如果配置中没有配置filterExpression，默认都适用
     */
    private boolean isConfigApplicable(MergeSortMapJoinFilterAttrModel model, List<AttrM> dealAttrs){
        try{
            //没有填写filterExpression的配置，不进行过滤
            if(StringUtils.isEmpty(model.getFilterExpression())){
                return true;
            }
            Map<String, Object> dealProductAttrsMap = buildDealProductAttrsMap(dealAttrs);
            return (Boolean)AviatorEvaluator.execute(model.getFilterExpression(), dealProductAttrsMap);
        }catch (Exception e){
            log.error("表达式出错了,attrs = {}, filterExpression = {}", JSONObject.toJSONString(dealAttrs), model.getFilterExpression(), e);
            //aviator执行出错的配置，也不进行过滤
            return true;
        }
    }


    /**
     * @param dealAttrs 团单属性
     * @return 构建团单-服务项目的属性
     */
    private Map<String, Object> buildDealProductAttrsMap(List<AttrM> dealAttrs){
        if(CollectionUtils.isEmpty(dealAttrs)){
            return Collections.emptyMap();
        }
        return dealAttrs.stream().collect(Collectors.toMap(AttrM::getName, AttrM::getValue, (old, newOne) -> old));
    }

    private List<String> getAttrValues(MergeSortMapJoinFilterAttrModel model, List<AttrM> dealAttrs) {
        return model.getAttrNameList().stream().flatMap(name -> getValueWithFormat(dealAttrs, name, model.getAttrFormatModels(), model.getProductCategoryList()).stream())
                .filter(value -> StringUtils.isNotEmpty(value)).collect(Collectors.toList());
    }

    private String getIcon(MergeSortMapJoinFilterAttrModel model, List<AttrM> dealAttrs) {
        AttrM skuCategoryAttrm = dealAttrs.stream().filter(dealAttr -> "skuProductCategories".equals(dealAttr.getName())).findFirst().orElse(null);
        if (skuCategoryAttrm == null || !model.getProductCategoryList().contains(skuCategoryAttrm.getValue())) {
            return null;
        }
        return model.getIcon();
    }

    private String getSubTitle(MergeSortMapJoinFilterAttrModel model, List<AttrM> dealAttrs) {
        AttrM skuCategoryAttrm = dealAttrs.stream().filter(dealAttr -> "skuProductCategories".equals(dealAttr.getName())).findFirst().orElse(null);
        if (skuCategoryAttrm == null || !model.getProductCategoryList().contains(skuCategoryAttrm.getValue())) {
            return null;
        }
        return model.getSubTitle();
    }

    private ActivityTags getActivityTag(MergeSortMapJoinFilterAttrModel model, List<AttrM> dealAttrs) {
        AttrM skuCategoryAttrm = dealAttrs.stream().filter(dealAttr -> "skuProductCategories".equals(dealAttr.getName())).findFirst().orElse(null);
        if (skuCategoryAttrm == null || !model.getProductCategoryList().contains(skuCategoryAttrm.getValue())) {
            return null;
        }
        return model.getActivityTags();
    }

    private List<String> getValueWithFormat(List<AttrM> dealAttrs, String attrName, List<AttrFormatModel> attrFormatModels, List<String> productCategories) {
        //商家补充信息
        if ("detailInfo".equals(attrName)) {
            UniformStructModel uniformStruct = JsonCodec.decode(DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "detailInfo"), UniformStructModel.class);
            if(uniformStruct == null) {
                return Lists.newArrayList();
            }
            return Lists.newArrayList(getRichText(uniformStruct.getContent()));
        }
        List<String> values = DealDetailUtils.getAttrValueByAttrName(dealAttrs, attrName);
        if (CollectionUtils.isEmpty(values) || CollectionUtils.isEmpty(productCategories)) {
            return Lists.newArrayList();
        }
        //根据productCategory过滤
        AttrM skuCategoryAttrm = dealAttrs.stream().filter(dealAttr -> "skuProductCategories".equals(dealAttr.getName())).findFirst().orElse(null);
        if (skuCategoryAttrm == null || !productCategories.contains(skuCategoryAttrm.getValue())) {
            return Lists.newArrayList();
        }
        AttrFormatModel attrFormatModel = getAttrFormatMapModelByAttrValue(attrFormatModels, attrName, dealAttrs);
        if (attrFormatModel == null) {
            return values;
        }
        //过滤：将attrName2FilteredAttrValueMap中key作为属性名查询到的属性值为value时不展示该属性
        boolean isFiltered = isFiltered(attrFormatModel.getAttrName2FilteredAttrValueMap(), dealAttrs);
        if (isFiltered) {
            return Lists.newArrayList();
        }
        List<String> result = Lists.newArrayList();

        for (String value : values) {
            //将属性值中的某个字符串替换为另一个字符串
            if (attrFormatModel.getAttrValueReplaceModel() != null && attrFormatModel.getAttrValueReplaceModel().getStr() != null && attrFormatModel.getAttrValueReplaceModel().getPreStr() != null) {
                value = value.replaceAll(attrFormatModel.getAttrValueReplaceModel().getPreStr(), attrFormatModel.getAttrValueReplaceModel().getStr());
            }
            if (StringUtils.isNotEmpty(attrFormatModel.getPrefix())) {
                value = attrFormatModel.getPrefix() + value;
            }
            if (StringUtils.isNotEmpty(attrFormatModel.getSuffix())) {
                value = value + attrFormatModel.getSuffix();
            }
            if (StringUtils.isNotEmpty(attrFormatModel.getAttrValueJoiner())) {
                value = value + attrFormatModel.getAttrValueJoiner();
            }
            result.add(value);
        }
        return result;
    }

    private String getRichText(List<UniformStructContentModel> structContentModels) {
        if(CollectionUtils.isEmpty(structContentModels)) {
            return null;
        }
        Object struct = structContentModels.stream().filter(model -> StringUtils.isNotEmpty(model.getType()) && model.getType().equals("richtext")).findFirst().orElse(null);
        if(struct == null) {
            return null;
        }
        if(!(struct instanceof UniformStructContentModel)) {
            return null;
        }
        UniformStructContentModel richTextModel = (UniformStructContentModel)struct;
        if(richTextModel == null || richTextModel.getData() == null) {
            return null;
        }
        return richTextModel.getData().toString().replace("<p>", "").replace("</p>", "");
    }

    @Data
    public static class AttrFormatModel {
        private String attrName;
        // 连接符
        private String attrValueJoiner;
        // 前缀
        private String prefix;
        // 后缀
        private String suffix;
        //将属性值中的指定字符串替换成另一个字符串
        private AttrValueReplaceModel attrValueReplaceModel;
        //过滤模型：只有将key作为属性名查询到的属性值为value时才展示该属性
        private Map<String, String> attrName2FilteredAttrValueMap;

    }

    @Data
    @VPointCfg
    public static class Config {
        private List<AttrListGroupModel> attrListGroupModels;
    }

    @Data
    public static class AttrListGroupModel {
        private List<MergeSortMapJoinFilterAttrModel> attrModelList;
        private String groupName;
        private boolean selfSupport;
    }

    /**
     * 融合命名/排序/映射/拼接/过滤功能的属性配置模型
     */
    @Data
    public static class MergeSortMapJoinFilterAttrModel {
        //适用 团单分类productCategory 列表
        private List<String> productCategoryList;
        //模块副标题
        private String subTitle;
        //模块活动标签
        private ActivityTags activityTags;
        //模块活动信息
        private List<ActivityDisplayInfo> activityDisplayInfos;
        //属性名列表
        private List<String> attrNameList;
        //全局拼接符
        private String joiner;
        //总前缀
        private String totalPrefix;
        //总后缀
        private String totalSuffix;
        //属性名 - 展示format映射
        private List<AttrFormatModel> attrFormatModels;
        //图标
        private String icon;
        //属性值 - 展示值映射 ； 展示顺序按照map中的展示顺序
        private List<AttrValueMapModel> attrValueMapModels;
        //属性展示名称
        private String displayName;
        /**
         * 配置过滤配置，是一个aviator的表达式
         *
         * 如果不填、或者aviator表达式执行失败，对该配置不进行过滤
         * 如果aviator表达式执行成功返回true，对该配置不进行过滤
         * 如果aviator表达式执行成功返回false，对该配置进行过滤
         */
        private String filterExpression;
    }

    @Data
    public static class ActivityDisplayInfo {
        // 获取活动展示信息的起始时间
        private String startTime;
        // 获取活动展示信息的结束时间
        private String endTime;
        //图标
        private String icon;
        //副标题
        private String subTitle;
        //活动标签
        private ActivityTags activityTags;
        //活动展示名字
        private String displayName;
        //活动属性值
        private List<String> attrValues;
    }

    @Data
    public static class ActivityTags {

        private String backgroundColor;

        private String textColor;

        private String text;
    }

    @Data
    public static class AttrValueMapModel {
        private String attrValue;
        private String displayValue;
        //优先级从1开始
        private int priority;
    }

    @Data
    public static class AttrValueReplaceModel {
        private String preStr;
        private String str;
    }

    private DealDetailStructAttrModuleGroupModel buildDealDetailStructAttrModuleGroupModel(String groupName, List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOS) {
        if (CollectionUtils.isEmpty(dealDetailStructAttrModuleVOS)) {
            return null;
        }
        DealDetailStructAttrModuleGroupModel dealDetailStructAttrModuleGroupModel = new DealDetailStructAttrModuleGroupModel();
        dealDetailStructAttrModuleGroupModel.setGroupName(groupName);
        dealDetailStructAttrModuleGroupModel.setDealDetailStructAttrModuleVOS(dealDetailStructAttrModuleVOS);
        return dealDetailStructAttrModuleGroupModel;
    }

    private DealDetailStructAttrModuleVO buildDealDetailStructForActivityModuleVO(List<ActivityDisplayInfo> activityDisplayInfos) {
        if (CollectionUtils.isEmpty(activityDisplayInfos)) {
            return null;
        }
        return activityDisplayInfos.stream().filter(activityInfo -> isOpening(activityInfo.getStartTime(), activityInfo.getEndTime())).findFirst().map(activityInfo ->
                buildDealDetailStructAttrModuleVO(activityInfo.getDisplayName(), activityInfo.getAttrValues(), activityInfo.getIcon(), activityInfo.getSubTitle(), activityInfo.getActivityTags())).orElse(null);
    }

    private boolean isOpening(String startTime, String endTime) {
        DateTime activityStart = new DateTime(DateUtils.FULL_TIME_PARSER.parse(startTime));
        DateTime activityEnd = new DateTime(DateUtils.FULL_TIME_PARSER.parse(endTime));
        return activityStart.isBefore(DateTime.now().toDate().getTime()) && activityEnd.isAfter(DateTime.now().toDate().getTime());
    }

    private DealDetailStructAttrModuleVO buildDealDetailStructAttrModuleVO(String displayName, List<String> attrValues, String icon, String subTitle, ActivityTags activityTags) {
        if (CollectionUtils.isEmpty(attrValues)) {
            return null;
        }
        DealDetailStructAttrModuleVO dealDetailStructAttrModuleVO = new DealDetailStructAttrModuleVO();
        dealDetailStructAttrModuleVO.setAttrName(displayName);
        dealDetailStructAttrModuleVO.setAttrValues(attrValues);
        dealDetailStructAttrModuleVO.setIcon(icon);
        dealDetailStructAttrModuleVO.setSubTitle(subTitle);
        dealDetailStructAttrModuleVO.setAdditionalTags(conver2AddtionalTag(activityTags));
        return dealDetailStructAttrModuleVO;
    }

    private ActivityTag conver2AddtionalTag(ActivityTags activityTags) {
        if (activityTags == null) {
            return null;
        }
        ActivityTag activityTag = new ActivityTag();
        activityTag.setBackgroundColor(activityTags.getBackgroundColor());
        activityTag.setText(activityTags.getText());
        activityTag.setTextColor(activityTags.getTextColor());
        return activityTag;
    }

    private List<String> joinAttrValue(List<String> attrValues, MergeSortMapJoinFilterAttrModel model) {
        if (model == null || CollectionUtils.isEmpty(attrValues)) {
            return attrValues;
        }
        String joinValue;
        if (model.getJoiner() != null) {
            joinValue = StringUtils.join(attrValues, model.getJoiner());
        } else {
            joinValue = StringUtils.join(attrValues, "");
        }
        if (StringUtils.isNotEmpty(model.getTotalPrefix())) {
            joinValue = model.getTotalPrefix() + joinValue;
        }
        if (StringUtils.isNotEmpty(model.getTotalSuffix())) {
            joinValue = joinValue + model.getTotalSuffix();
        }
        return Lists.newArrayList(joinValue);
    }

    private List<String> sortAttrValues(List<String> attrValues, List<AttrValueMapModel> attrValueMapModels) {
        if (CollectionUtils.isNotEmpty(attrValueMapModels) || CollectionUtils.isEmpty(attrValues)) {
            return attrValues;
        }
        return attrValues.stream().sorted(Comparator.comparingInt(o -> getAttrPriority(attrValueMapModels, o))).collect(Collectors.toList());
    }

    private int getAttrPriority(List<AttrValueMapModel> attrValueMapModels, String attrValue) {
        AttrValueMapModel attrValueMapModel = getAttrValueMapModelByAttrValue(attrValueMapModels, attrValue);
        if (attrValueMapModel == null || attrValueMapModel.getPriority() == 0) {
            return Integer.MAX_VALUE;
        }
        return attrValueMapModel.getPriority();
    }

    private AttrValueMapModel getAttrValueMapModelByAttrValue(List<AttrValueMapModel> attrValueMapModels, String attrValue) {
        if (CollectionUtils.isEmpty(attrValueMapModels) || StringUtils.isEmpty(attrValue)) {
            return null;
        }
        return attrValueMapModels.stream().filter(model -> attrValue.equals(model.getAttrValue())).findFirst().orElse(null);
    }

    private AttrFormatModel getAttrFormatMapModelByAttrValue(List<AttrFormatModel> attrFormatModels, String attrName, List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(attrFormatModels) || StringUtils.isEmpty(attrName) || CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        return attrFormatModels.stream().filter(model -> attrName.equals(model.getAttrName())).findFirst().orElse(null);
    }

    private boolean isFiltered(Map<String, String> attrName2FilteredAttrValueMap, List<AttrM> dealAttrs) {
        //没有配置该模型时，不进行过滤处理
        if (MapUtils.isEmpty(attrName2FilteredAttrValueMap)) {
            return false;
        }
        return attrName2FilteredAttrValueMap.entrySet().stream().map(entry -> {
            String value = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, entry.getKey());
            //该属性值为null或者和期望值不同时，进行过滤处理
            if (value == null) {
                return true;
            }
            return !value.equals(entry.getValue());
        }).filter(Boolean::booleanValue).findFirst().orElse(false);
    }

    private List<AttrM> extractFirstSkuAttrFromDealDetailDtoModel(DealDetailDtoModel dealDetailDtoModel) {
        if (dealDetailDtoModel == null || dealDetailDtoModel.getSkuUniStructuredDto() == null || CollectionUtils.isEmpty(dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups())) {
            return null;
        }
        List<MustSkuItemsGroupDto> mustSkuItemsGroupDtos = dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups();
        MustSkuItemsGroupDto mustSkuItemsGroupDto = CollectUtils.firstValue(mustSkuItemsGroupDtos);
        if (mustSkuItemsGroupDto == null || CollectionUtils.isEmpty(mustSkuItemsGroupDto.getSkuItems())) {
            return null;
        }
        SkuItemDto skuItemDto = CollectUtils.firstValue(mustSkuItemsGroupDto.getSkuItems());
        if (skuItemDto == null) {
            return null;
        }
        return skuItemDto.getAttrItems().stream().map(skuDto -> {
            if (skuDto == null) {
                return null;
            }
            AttrM attrM = new AttrM();
            attrM.setName(skuDto.getAttrName());
            attrM.setValue(skuDto.getAttrValue());
            return attrM;
        }).filter(attr -> attr != null).collect(Collectors.toList());
    }
}
