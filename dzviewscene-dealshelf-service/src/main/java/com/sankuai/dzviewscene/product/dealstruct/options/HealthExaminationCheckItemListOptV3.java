package com.sankuai.dzviewscene.product.dealstruct.options;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.healthExamination.checkItems.vpoints.HealthExaminationCheckItemListV3VP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination.HealthExaminationItemsGroupVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination.PrimaryExaminationItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination.SecondaryExaminationItemVO;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@VPointOption(name = "体检团详新包检查项列表变化点V3", description = "体检团详新包检查项列表变化点V3", code = HealthExaminationCheckItemListOptV3.CODE, isDefault = false)
public class HealthExaminationCheckItemListOptV3 extends HealthExaminationCheckItemListV3VP<HealthExaminationCheckItemListOptV3.Config> {
    public static final String CODE = "HealthExaminationCheckItemListOptV3";

    @Override
    public HealthExaminationItemsGroupVO compute(ActivityCxt activityCxt, Param param, Config config) {
        List<StandardServiceProjectGroupDTO> projectGroupDTOS = Optional.ofNullable(param)
                .map(Param::getDealDetailBasicInfo).map(DealDetailInfoModel::getStandardServiceProjectDTO).map(StandardServiceProjectDTO::getMustGroups).orElse(null);
        if (CollectionUtils.isEmpty(projectGroupDTOS)) {
            return null;
        }
        List<PrimaryExaminationItemVO> examinationItemVOS = buildPrimaryExaminationItemVOS(config, projectGroupDTOS);
        return buildHealthExaminationItemsGroupVO(examinationItemVOS, getAllMustTertiaryItemNum(examinationItemVOS, config.getAllMustTertiaryItemNumFormat()));
    }

    private List<PrimaryExaminationItemVO> buildPrimaryExaminationItemVOS(Config config, List<StandardServiceProjectGroupDTO> projectGroupDTOS) {
        List<HealthExaminationItem> itemList = parseItem(projectGroupDTOS);
        //根据商户分类去分组
        Map<String, List<HealthExaminationItem>> categoryIdMap = itemList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getShopCategoryName()))
                .collect(Collectors.groupingBy(e -> e.getShopCategoryName(), LinkedHashMap::new, Collectors.toList()));
        ArrayListMultimap<String, SecondaryExaminationItemVO> result = ArrayListMultimap.create();
        Map<String, Long> categoryItemCountMap = Maps.newHashMap();
        categoryIdMap.forEach((shopCategory, items) -> {

            //平台一级分类
            List<String> platformFirstCategoryList = items.stream().map(e -> e.getCategory1()).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            //如果平台一级分类为空，或者平台一级分类不唯一的话
            if (CollectionUtils.isEmpty(platformFirstCategoryList) || platformFirstCategoryList.size() > 1) { //塞到其他里
                addMap(result, "其他", shopCategory, items);
            }  else { //如果平台一级分类唯一的话
                addMap(result, platformFirstCategoryList.get(0), shopCategory, items);
            }

            //检查项去重一下
            long itemsCount = items.stream().map(e-> e.getShopItemName()).filter(StringUtils::isNotBlank).distinct().count();
            categoryItemCountMap.put(shopCategory, itemsCount);
        });

        //构建一级分类
        List<PrimaryExaminationItemVO> examinationItemVOS = buildPrimaryExaminationItemVOS(result, categoryItemCountMap, config.getPrimaryItemsortedMap());
        return examinationItemVOS;
    }

    private List<PrimaryExaminationItemVO> buildPrimaryExaminationItemVOS(ArrayListMultimap<String, SecondaryExaminationItemVO> result, Map<String, Long> categoryItemCountMap,Map<String,Integer> primaryItemSortedMap) {

        return result.asMap().entrySet().stream().map(e -> {
            PrimaryExaminationItemVO itemVO = new PrimaryExaminationItemVO();
            //一级分类
            itemVO.setName(e.getKey());

            //一级分类下，所有二级分类的检查项总数
            int totalCount = e.getValue().stream().map(secondItem -> categoryItemCountMap.getOrDefault(secondItem.getName(), 0L))
                    .collect(Collectors.reducing(0L, Long::sum)).intValue();

            itemVO.setDesc(String.format("%d项", totalCount));
            itemVO.setSecondaryExaminationItemList(Lists.newArrayList(e.getValue()));
            return itemVO;
            //从小到大排序，取不到给99放在后面
        }).sorted(Comparator.comparingInt(e -> primaryItemSortedMap.getOrDefault(e.getName(), 99))).collect(Collectors.toList());
    }

    private void addMap(ArrayListMultimap<String, SecondaryExaminationItemVO> map, String firstPlatformCategory, String shopCategory, List<HealthExaminationItem> itemList) {

        SecondaryExaminationItemVO secondaryExaminationItemVO = new SecondaryExaminationItemVO();
        secondaryExaminationItemVO.setName(shopCategory);

        //分类下的检查项用顿号分隔
        String itemStr = itemList.stream().map(e -> e.getShopItemName()).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining("、"));
        secondaryExaminationItemVO.setTertiaryExaminations(itemStr);

        //检查意义的确定
        List<String> explainList = itemList.stream().map(e -> e.getExplain()).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        String explain = null;
        //如果检查意义不为空，并且检查意义唯一的话，就取该意义
        if (CollectionUtils.isNotEmpty(explainList) && explainList.size() == 1) {
            explain = explainList.get(0);
        }
        secondaryExaminationItemVO.setCheckSignificance(explain);

        //塞进去
        map.put(firstPlatformCategory, secondaryExaminationItemVO);
    }

    private List<HealthExaminationItem> parseItem(List<StandardServiceProjectGroupDTO> projectGroupDTOS) {
        List<HealthExaminationItem> itemList = new ArrayList<>();
        projectGroupDTOS.stream().forEach(projectGroupDTO -> {
            List<StandardServiceProjectItemDTO > serviceProjectItemDTOS = projectGroupDTO.getServiceProjectItems();
            if (CollectionUtils.isNotEmpty(serviceProjectItemDTOS)) {
                serviceProjectItemDTOS.forEach(serviceProject -> {
                    List<StandardAttributeItemDTO> standardAttributeItemDTOS = Optional.ofNullable(serviceProject).map(StandardServiceProjectItemDTO::getStandardAttribute).map(StandardAttributeDTO::getAttrs).orElse(null);
                    Map<String, String> map = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(standardAttributeItemDTOS)) {
                        standardAttributeItemDTOS.stream().forEach(attribute -> {
                            if (StringUtils.isNotBlank(attribute.getAttrName())) {
                                String value = null;
                                List<StandardAttributeValueDTO> attrValues = attribute.getAttrValues();
                                if (CollectionUtils.isNotEmpty(attrValues)) {
                                    StandardAttributeValueDTO standardAttributeValueDTO = attrValues.get(0);
                                    List<String> simpleValues = standardAttributeValueDTO.getSimpleValues();
                                    if (CollectionUtils.isNotEmpty(simpleValues)) {
                                        value = simpleValues.get(0);
                                    }
                                }
                                map.put(attribute.getAttrName(), value);
                            }});
                    }
                    HealthExaminationItem item = HealthExaminationItem.builder()
                            .category1(map.get("category1"))
                            .category2(map.get("category2"))
                            .standardName(map.get("standardName"))
                            .explain(map.get("explain"))
                            .shopItemName(map.get("projectName"))
                            .shopCategoryName(map.get("categoryName"))
                            .build();
                    itemList.add(item);
                });
            }
        });
        return itemList;
    }

    private HealthExaminationItemsGroupVO buildHealthExaminationItemsGroupVO(List<PrimaryExaminationItemVO> primaryExaminationItemVOS, String desc) {
        if (CollectionUtils.isEmpty(primaryExaminationItemVOS)) {
            return null;
        }
        HealthExaminationItemsGroupVO healthExaminationItemsGroupVO = new HealthExaminationItemsGroupVO();
        healthExaminationItemsGroupVO.setGroupName("包含项目");
        healthExaminationItemsGroupVO.setPrimaryExaminationItemList(primaryExaminationItemVOS);
        healthExaminationItemsGroupVO.setDesc(desc);
        return healthExaminationItemsGroupVO;
    }

    private String getAllMustTertiaryItemNum(List<PrimaryExaminationItemVO> mustSkuCheckItemList, String format) {
        if (CollectionUtils.isEmpty(mustSkuCheckItemList)) {
            return null;
        }
        int sum = mustSkuCheckItemList.stream()
                .filter(item -> StringUtils.isNotEmpty(item.getDesc()))
                .map(item -> {
                    int num = NumberUtils.objToInt(item.getDesc().replaceAll("项", StringUtils.EMPTY));
                    return num == -1 ? 0 : num;
                }).reduce(Integer::sum).orElse(0);
        if (sum == 0) {
            return null;
        }
        return String.format(format, sum);
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HealthExaminationItem {
        private String shopItemName; //商户输入的检查项目名称
        private String shopCategoryName; //商户输入的检查分类名称
        private String explain; //如果商户没有填写，则默认使用平台库的，如果平台库也没有则不传
        private String category1; //平台一级分类名称
        private String category2; //平台二级分类名称
        private String standardName; //平台检查项目名称
    }

    @Data
    @VPointCfg
    public static class Config {
        String allMustTertiaryItemNumFormat;
        Map<String, Integer> primaryItemsortedMap = Maps.newHashMap();
    }
}
