package com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.DealDetailSkuListsBuilder;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/26 3:12 下午
 */

@VPoint(name = "团购详情sku列表组名变化点", description = "团购详情sku列表组名变化点，支持配置",code = SkuListGroupTitleVP.CODE, ability = DealDetailSkuListsBuilder.CODE)
public abstract class SkuListGroupTitleVP<T> extends PmfVPoint<String, SkuListGroupTitleVP.Param, T> {

    public static final String CODE = "SkuListGroupTitleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private int totalNum;
        private int optionalNum;
        private boolean isMustGroup;
    }

}
