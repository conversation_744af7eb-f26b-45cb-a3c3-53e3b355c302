package com.sankuai.dzviewscene.product.ability.douhu;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.douhu.absdk.bean.DouHuRequest;
import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivity;
import com.sankuai.dzviewscene.product.shelf.activity.product.ProductShelfActivity;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import graphql.execution.Async;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Ability(code = DouHuAbility.CODE,
        name = "斗斛ab能力",
        description = "提供ab结果查询能力",
        activities = {ProductShelfActivity.CODE, DealShelfActivity.CODE}
)
public class DouHuAbility extends PmfAbility<List<DouHuResponse>, Void, DouHuAbilityConfig> {

    public static final String CODE = "douHuAbility";

    @Resource
    private AtomFacadeService atomFacadeService;

    @Override
    public CompletableFuture<List<DouHuResponse>> build(ActivityCxt ctx,
                                                 Void param,
                                                 DouHuAbilityConfig config) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.ability.douhu.DouHuAbility.build(com.sankuai.athena.viewscene.framework.ActivityCxt,java.lang.Void,com.sankuai.dzviewscene.product.ability.douhu.DouHuAbilityConfig)");
        if (CollectionUtils.isEmpty(config.getDpExpIds()) && CollectionUtils.isEmpty(config.getMtExpIds())) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        return defaultHandler(ctx, config);
    }

    private CompletableFuture<List<DouHuResponse>> defaultHandler(ActivityCxt ctx, DouHuAbilityConfig config) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.ability.douhu.DouHuAbility.defaultHandler(com.sankuai.athena.viewscene.framework.ActivityCxt,com.sankuai.dzviewscene.product.ability.douhu.DouHuAbilityConfig)");
        List<DouHuRequest> reqList = buildReqList(ctx, config);
        List<CompletableFuture<DouHuResponse>> cfList = reqList.stream().map(req ->  atomFacadeService.getPoiABTest(req)).collect(Collectors.toList());
        return Async.each(cfList);
    }

    private List<DouHuRequest> buildReqList(ActivityCxt ctx, DouHuAbilityConfig config) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.ability.douhu.DouHuAbility.buildReqList(com.sankuai.athena.viewscene.framework.ActivityCxt,com.sankuai.dzviewscene.product.ability.douhu.DouHuAbilityConfig)");
        int platform = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.platform);
        List<String> expIds = platform == VCPlatformEnum.MT.getType() || VCClientTypeEnum.isMtClientTypeByCode(platform) ? config.getMtExpIds() : config.getDpExpIds();
        if (CollectionUtils.isEmpty(expIds)) {
            return Lists.newArrayList();
        }
        return expIds.stream().map(expId->buildRequest(expId, ctx)).collect(Collectors.toList());
    }

    private DouHuRequest buildRequest(String expId, ActivityCxt ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.ability.douhu.DouHuAbility.buildRequest(java.lang.String,com.sankuai.athena.viewscene.framework.ActivityCxt)");
        String deviceId = ParamsUtil.getStringSafely(ctx, PmfConstants.Params.deviceId);
        String unionId = ParamsUtil.getStringSafely(ctx, PmfConstants.Params.unionId);
        int platform = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.platform);
        DouHuRequest douHuRequest = new DouHuRequest();
        douHuRequest.setExpId(expId);
        douHuRequest.setUnionId(unionId);
        if (platform == VCPlatformEnum.MT.getType() || VCClientTypeEnum.isMtClientTypeByCode(platform)) {
            douHuRequest.setUuid(deviceId);
        }else {
            douHuRequest.setDpid(deviceId);
        }
        return douHuRequest;
    }
}