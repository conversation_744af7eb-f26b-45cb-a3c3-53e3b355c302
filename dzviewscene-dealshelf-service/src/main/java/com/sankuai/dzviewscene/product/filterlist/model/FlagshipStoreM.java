package com.sankuai.dzviewscene.product.filterlist.model;

import com.sankuai.mtstore.aggregate.thrift.dto.decorate.StoreRecommendProjectItemDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/16
 */
@Data
public class FlagshipStoreM {

    private long storeId;

    /**
     * 自定义的模块类型，数据源来源于旗舰店定义的"业务样式数据"
     * 1-推荐项目
     * 2-推荐团购
     */
    private int moduleType;

    /**
     * 推荐项目列表
     */
    private List<StoreRecommendProjectItemDTO> projectList;
}
