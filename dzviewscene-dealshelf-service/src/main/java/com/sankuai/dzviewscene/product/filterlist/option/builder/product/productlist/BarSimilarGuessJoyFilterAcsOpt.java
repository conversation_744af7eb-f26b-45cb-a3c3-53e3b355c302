package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 2. 猜喜路径
 * 召回规则：召回商户内全部团单
 * <p>
 * 排序规则：
 * 首先展示当前团单，其他团单排序规则如下，
 * 整体：团单动销卡控 > 团单价格
 * 团单动销卡控：优先推荐近7天销量>0的团单，其次推荐其他团单。
 * 团单价格：在动销卡控基础上，按团单间价格由低到高排序，即团单价格越低，排序优先级越高。
 *
 * <AUTHOR>
 * @since 2023/10/16 20:47
 */
@VPointOption(name = "相似团购猜喜路径过滤和排序",
        description = "相似团购猜喜路径过滤和排序",
        code = "BarSimilarGuessJoyFilterAcsOpt")
public class BarSimilarGuessJoyFilterAcsOpt extends ProductListVP<BarSimilarGuessJoyFilterAcsOpt.Config> {

    @Override
    public List<ProductM> compute(ActivityCxt context, Param param, Config config) {
        List<ProductM> productMS = param.getProductMS();
        //pageSource=shelf  pageSource=guess
        if (productMS.size() == 1) {
            return null;
        }
        List<ProductM> collect = productMS.stream()
                .filter(p -> p.getBasePrice() != null)
                .filter(p -> StringUtils.isNotEmpty(p.getTitle()))
                .sorted(
                        Comparator.comparing(this::getOrderValue).reversed()
                                .thenComparing(this::getSalePrice)
                )
                .limit(config.getLimit())
                .collect(Collectors.toList());
        return topAndFilterResult(collect, ParamsUtil.getIntSafely(context, PmfConstants.Params.entityId));
    }

    private List<ProductM> topAndFilterResult(List<ProductM> result, int entityId) {
        if (CollectionUtils.isEmpty(result) || result.size() <= 1) {
            return null;
        }
        int finalTopIndex = findTopProductIndex(result, entityId);
        if (finalTopIndex < 0) {
            return null;
        }
        Collections.swap(result, 0, finalTopIndex);
        return result;
    }

    private int findTopProductIndex(List<ProductM> productMS, int entityId) {
        int topIndex = -1;
        for (int index = 0; index < productMS.size(); index++) {
            if (productMS.get(index).getProductId() != entityId) {
                continue;
            }
            topIndex = index;
            break;
        }
        return topIndex;
    }

    /**
     * 团单动销卡控：优先推荐近30天销量>0的团单，其次推荐其他团单
     **/
    private int getOrderValue(ProductM productM) {
        return Optional.ofNullable(productM.getSale()).map(ProductSaleM::getSale).orElse(0);
    }

    //团单价格
    private BigDecimal getSalePrice(ProductM productM) {
        //立减
        ProductPromoPriceM productPromoPriceM = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
        BigDecimal salePrice = Optional.ofNullable(productPromoPriceM)
                .map(ProductPromoPriceM::getPromoPrice)
                .filter(price -> Objects.nonNull(price))
                .filter(price -> price.compareTo(BigDecimal.ZERO) > 0)
                .orElse(productM.getBasePrice());
        return salePrice == null ? BigDecimal.ZERO : salePrice;
    }

    @VPointCfg
    @Data
    public static class Config {
        private int limit = 5;
    }
}
