package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.SkuCopiesVP;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/11/26 6:37 下午
 */
@VPointOption(name = "团购详情sku货份数变化点", description = "团购详情sku货份数变化点，支持配置文案Format",code = DefaultSkuCopiesVPO.CODE, isDefault = true)
public class DefaultSkuCopiesVPO extends SkuCopiesVP<DefaultSkuCopiesVPO.Config> {

    public static final String CODE = "DefaultSkuCopiesVPO";

    private static final String DEFAULT_COPIES_FORMAT = "%s个";

    @Override
    public String compute(ActivityCxt context, SkuCopiesVP.Param param, Config config) {
        if(StringUtils.isEmpty(param.getCopies())) {
            return null;
        }
        String format = StringUtils.isEmpty(config.getSkuCopiesFormat()) ? DEFAULT_COPIES_FORMAT : config.getSkuCopiesFormat();
        return String.format(format, param.getCopies());
    }

    @Data
    @VPointCfg
    public static class Config {
        private String skuCopiesFormat;
    }
}
