package com.sankuai.dzviewscene.product.filterlist.option.builder.product.priceabovetags;

import com.dianping.cat.Cat;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceAboveTagVP;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 与货架相同
 * 代码来自于com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempriceabovetags.ConfigItemPriceAboveTagsOpt
 */
@VPointOption(name = "根据团单标签配置化标签属性",
        description = "根据团单标签配置化标签属性",
        code = "ProductListConfigPriceAboveTagsOpt")
public class ProductListConfigPriceAboveTagsOpt extends ProductPriceAboveTagVP<ProductListConfigPriceAboveTagsOpt.Config> {
    @Override
    public List<DzTagVO> compute(ActivityCxt context, Param param, Config config) {
        //堆头
        if(ProductMAttrUtils.isTopDisplayProduct(param.getProductM())){
            return buildDzTagVOList(config.getTopDisplayAttrKeys(),config.getAttrKey2Cfg(), param.getProductM());
        }
        return buildDzTagVOList(config.getAttrKeys(), config.getAttrKey2Cfg(), param.getProductM());
    }

    private List<DzTagVO> buildDzTagVOList(List<String> attrKeys, Map<String, TagCfg> attrKey2Cfg, ProductM productM){
        if(CollectionUtils.isEmpty(attrKeys)){
            return Lists.newArrayList();
        }
        List<DzTagVO> dzTagVOS = Lists.newArrayList();
        for(String attrKey : attrKeys){
            String attrValue = productM.getAttr(attrKey);
            if(StringUtils.isEmpty(attrValue)){
                continue;
            }
            dzTagVOS.add(buildDzTagVO(attrValue, attrKey2Cfg.get(attrKey)));
        }
        return dzTagVOS;
    }

    private DzTagVO buildDzTagVO(String tagName, TagCfg tagCfg){
        if(tagCfg == null){
            return buildSimpleTagVO(tagName);
        }
        DzTagVO dzTagVO = new DzTagVO();
        dzTagVO.setText(tagName);
        dzTagVO.setTextColor(tagCfg.getTextColor());
        dzTagVO.setBackground(tagCfg.getBackground());
        if(StringUtils.isNotEmpty(tagCfg.getPrePicUrl())){
            dzTagVO.setPrePic(buildPictureComponent(tagCfg.getPrePicUrl(), tagCfg.getPrePicHeight(), tagCfg.getPreAspectRadio()));
        }
        if(StringUtils.isNotEmpty(tagCfg.getAfterPicUrl())){
            dzTagVO.setAfterPic(buildPictureComponent(tagCfg.getAfterPicUrl(), tagCfg.getAfterPicHeight(), tagCfg.getAfterAspectRadio()));
        }
        return dzTagVO;
    }

    private DzTagVO buildSimpleTagVO(String tagName){
        DzTagVO dzTagVO = new DzTagVO();
        dzTagVO.setText(tagName);
        return dzTagVO;
    }

    private DzPictureComponentVO buildPictureComponent(String picUrl, int picHeight, double aspectRadio){
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.filterlist.option.builder.product.priceabovetags.ProductListConfigPriceAboveTagsOpt.buildPictureComponent(java.lang.String,int,double)");
        DzPictureComponentVO pic = new DzPictureComponentVO();
        pic.setPicUrl(picUrl);
        pic.setPicHeight(picHeight);
        pic.setAspectRadio(aspectRadio);
        return pic;
    }


    @VPointCfg
    @Data
    public static class Config {

        /**
         * 展示标签属性key
         */
        private List<String> attrKeys;

        /**
         * 标签属性配置
         */
        private Map<String, TagCfg> attrKey2Cfg;

        /**
         * 堆头展示标签属性key
         */
        private List<String>  topDisplayAttrKeys;
    }

    @Data
    public static class TagCfg {

        /**
         * 文字颜色
         */
        private String textColor;

        /**
         * 背景色
         */
        private String background;

        /**
         * 标签前置图片url
         */
        private String prePicUrl;

        /**
         * 前置图片宽高比
         */
        private double preAspectRadio;

        /**
         * 前置图片高
         */
        private int prePicHeight;

        /**
         * 标签后置图片url
         */
        private String afterPicUrl;

        /**
         * 后置图片宽高比
         */
        private double afterAspectRadio;

        /**
         * 后置图片高
         */
        private int afterPicHeight;
    }
}
