package com.sankuai.dzviewscene.product.ability.options;

import com.google.common.collect.Maps;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.athena.test.annotation.Rpc;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreSyncHandlerVP;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.CompletableFutureUtil;
import com.sankuai.fbi.faas.wed.api.CrossCatRecommendService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@VPointOption(name = "LEcross推荐列表标题信息",
        description = "LEcross推荐列表标题信息",
        code = LeCrossTitleFetcherOpt.CODE)
@Slf4j
public class LeCrossTitleFetcherOpt extends PreSyncHandlerVP<LeCrossTitleFetcherOpt.Config> {

    @Resource
    private CompositeAtomService compositeAtomService;

    public static final String CODE = "LeCrossTitleFetcherOpt";

    @Override
    public Map<String, Object> compute(ActivityCxt activityCxt, Param param, Config config) {
        CompletableFuture<String> appendTitle = getAppendTitle(activityCxt);
        Map<String, CompletableFuture<Object>> resultMap = Maps.newHashMap();
        resultMap.put(LeCrossTitleFetcherOpt.CODE, CompletableFutureUtil.covert2ObjCf(appendTitle));
        return CompletableFutureUtil.each(resultMap).join();
    }

    private CompletableFuture<String> getAppendTitle(ActivityCxt activityCxt) {
        List<Integer> backCatIds = activityCxt.getParam(PmfConstants.Params.shopBackCatIds);
        return compositeAtomService.queryCrossTitleByBackCat(backCatIds);
    }


    @Data
    @VPointCfg
    public static class Config {
    }
}
