package com.sankuai.dzviewscene.product.filterlist.option.builder.product.producttags;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTagsVP;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/3/29 12:47 下午
 */
@VPointOption(name = "商品-ProductTags",
        description = "支持预售",
        code = "ProductTagsSupportPreSaleOpt")
public class ProductTagsSupportPreSaleOpt extends ProductTagsVP<ProductTagsSupportPreSaleOpt.Config> {

    @Override
    public List<String> compute(ActivityCxt activityCxt, Param param, ProductTagsSupportPreSaleOpt.Config config) {
        ProductM productM = param.getProductM();
        List<String> result = new ArrayList<>();
        if (PreSaleUtils.isPreSaleDeal(productM)) {
            List<String> preSaleTags = getPreSaleProductTags(productM, config);
            if (CollectionUtils.isNotEmpty(preSaleTags)) {
                result.addAll(preSaleTags);
                return result;
            }
        }
        if (CollectionUtils.isNotEmpty(productM.getProductTags())) {
            result.addAll(productM.getProductTags());
        }
        return result;
    }

    public List<String> getPreSaleProductTags(ProductM productM, ProductTagsSupportPreSaleOpt.Config config) {
        String preSaleDate = productM.getAttr(config.getPreSaleDateAttrKey());
        if (StringUtils.isEmpty(config.getPreSaleTemplate()) || StringUtils.isEmpty(preSaleDate)) {
            return productM.getProductTags();
        }
        return Lists.newArrayList(String.format(config.getPreSaleTemplate(), preSaleDate));
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 预售时间Key
         */
        private String preSaleDateAttrKey = "preSaleStartDate";

        private String preSaleTemplate = "%s后可用";

    }
}
