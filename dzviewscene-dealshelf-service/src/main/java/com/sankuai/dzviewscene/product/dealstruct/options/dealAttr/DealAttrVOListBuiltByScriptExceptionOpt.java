package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.OptionalSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.docProcessing.DocBuilderUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022/6/20 5:22 下午
 */
@VPointOption(name = "【已废弃】根据脚本表达式构造属性的DealAttrVO列表变化点", description = "根据脚本表达式构造属性的DealAttrVO列表变化点，构造数据源包含团单属性、服务项目属性",code = DealAttrVOListBuiltByScriptExceptionOpt.CODE, isDefault = true)
@Deprecated
public class DealAttrVOListBuiltByScriptExceptionOpt extends DealAttrVOListVP<DealAttrVOListBuiltByScriptExceptionOpt.Config> {

    public static final String CODE = "DealAttrVOListBuiltByScriptExceptionOpt";

    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        if (config == null || CollectionUtils.isEmpty(config.getAttrListGroupModels())) {
            return null;
        }
        //团单属性
        List<AttrM> dealAttrs = param.getDealAttrs();
        //服务项目属性
        List<SkuAttrItemDto> skuAttrItemDtos = extractSkuAttrsFromDealDetailDtoModel(param.getDealDetailDtoModel());
        return config.getAttrListGroupModels().stream().map(model -> buildDealDetailStructAttrModuleVOList(context, model, dealAttrs, skuAttrItemDtos)).filter(model -> model != null).collect(Collectors.toList());
    }

    private DealDetailStructAttrModuleGroupModel buildDealDetailStructAttrModuleVOList(ActivityCxt context, AttrListGroupModel attrListGroupModel, List<AttrM> dealAttrs, List<SkuAttrItemDto> skuAttrItemDtos) {
        if (attrListGroupModel == null || CollectionUtils.isEmpty(attrListGroupModel.getAttrModelList())) {
            return null;
        }
        List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOS = attrListGroupModel.getAttrModelList().stream().map(model -> buildDealDetailStructAttrModuleVO(context, model, dealAttrs, skuAttrItemDtos)).filter(model -> model != null).collect(Collectors.toList());
        return buildDealDetailStructAttrModuleGroupModel(attrListGroupModel.getGroupName(), dealDetailStructAttrModuleVOS);
    }

    private DealDetailStructAttrModuleGroupModel buildDealDetailStructAttrModuleGroupModel(String groupName, List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOS) {
        if (CollectionUtils.isEmpty(dealDetailStructAttrModuleVOS)) {
            return null;
        }
        DealDetailStructAttrModuleGroupModel dealDetailStructAttrModuleGroupModel = new DealDetailStructAttrModuleGroupModel();
        dealDetailStructAttrModuleGroupModel.setGroupName(groupName);
        dealDetailStructAttrModuleGroupModel.setDealDetailStructAttrModuleVOS(dealDetailStructAttrModuleVOS);
        return dealDetailStructAttrModuleGroupModel;
    }

    private DealDetailStructAttrModuleVO buildDealDetailStructAttrModuleVO(ActivityCxt context, AttrModel attrModel, List<AttrM> dealAttrs, List<SkuAttrItemDto> skuAttrItemDtos) {
        if (CollectionUtils.isEmpty(attrModel.getAttrBuildConfigs()) || attrModel == null) {
            return null;
        }
        //通过配置的属性值构造脚本表达式构造属性值
        return getDealDetailStructAttrModuleVOByScriptExpressionConfig(context, attrModel, dealAttrs, skuAttrItemDtos);
    }

    /**
     * 通过配置的属性值构造脚本表达式模型构造DealDetailStructAttrModuleVO
     *@param
     *@return
     */
    private DealDetailStructAttrModuleVO getDealDetailStructAttrModuleVOByScriptExpressionConfig(ActivityCxt context, AttrModel attrModel, List<AttrM> dealAttrs, List<SkuAttrItemDto> skuAttrItemDtos) {
        Object attrValue = getAttrValueByScriptExpression(context, dealAttrs, skuAttrItemDtos, attrModel.getAttrBuildConfigs());
        if (attrValue == null) {
            return null;
        }
        if (attrValue instanceof String[]) {
            String[] attrValues = (String[]) attrValue;
            return buildDealDetailStructAttrModuleVO(attrModel.getAttrTitle(), Lists.newArrayList(attrValues), attrModel.getIcon());
        }
        return buildDealDetailStructAttrModuleVO(attrModel.getAttrTitle(), Lists.newArrayList(attrValue.toString()), attrModel.getIcon());
    }

    /**
     * 通过配置的属性值构造脚本表达式构造属性值
     *@param dealAttrs 属性值构造数据源
     *@param attrBuildModels 属性值构造配置模型
     *@return 构造好的属性值
     */
    private Object getAttrValueByScriptExpression(ActivityCxt context, List<AttrM> dealAttrs, List<SkuAttrItemDto> skuAttrItemDtos, List<ScriptExceptionBuilderConfig> attrBuildModels) {
        if (CollectionUtils.isEmpty(attrBuildModels)) {
            return null;
        }
        //1. resourceDataMap用于存放构造属性值文案所需的所有数据
        Map<String, Object> resourceDataMap = new HashMap<>();
        //1.1 团单属性
        Map<String, Object> resourceDataFromDealAttrs = extractResourceDateFromDealAttrs(dealAttrs);
        resourceDataMap.putAll(resourceDataFromDealAttrs);
        //1.2 服务项目属性
        Map<String, Object> resourceDataFromSkuAttrs = extractResourceDateFromSkuAttrs(skuAttrItemDtos);
        resourceDataMap.putAll(resourceDataFromSkuAttrs);
        //2. 构造最终展示的sku属性值文案
        for (ScriptExceptionBuilderConfig buildModel : attrBuildModels) {
            //2.1 通过配置的属性值展示校验语句判断该属性值是否需要展示
            if (DocBuilderUtils.verify(buildModel.getAttrVerifyExpression(), resourceDataMap, context)) {
                //2.2 通过配置的属性展示构造语句构造属性值文案
                return DocBuilderUtils.build(buildModel.getAttrBuildExpression(), resourceDataMap, context);
            }
        }
        return null;
    }

    private Map<String, Object> extractResourceDateFromDealAttrs(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return new HashMap<>();
        }
        return dealAttrs.stream().collect(HashMap::new, (map, attr) -> map.put(attr.getName(), attr.getValue()), HashMap::putAll);
    }

    private Map<String, Object> extractResourceDateFromSkuAttrs(List<SkuAttrItemDto> skuAttrItemDtos) {
        if (CollectionUtils.isEmpty(skuAttrItemDtos)) {
            return new HashMap<>();
        }
        return skuAttrItemDtos.stream().collect(HashMap::new, (map, attr) -> map.put(attr.getAttrName(), attr.getAttrValue()), HashMap::putAll);
    }

    private DealDetailStructAttrModuleVO buildDealDetailStructAttrModuleVO(String displayName, List<String> attrValues, String icon) {
        if (CollectionUtils.isEmpty(attrValues)) {
            return null;
        }
        DealDetailStructAttrModuleVO dealDetailStructAttrModuleVO = new DealDetailStructAttrModuleVO();
        dealDetailStructAttrModuleVO.setAttrName(displayName);
        dealDetailStructAttrModuleVO.setAttrValues(attrValues);
        dealDetailStructAttrModuleVO.setIcon(icon);
        return dealDetailStructAttrModuleVO;
    }

    /**
     * 提取该团单下所有服务项目的所有属性列表
     *@param
     *@return
     */
    private List<SkuAttrItemDto> extractSkuAttrsFromDealDetailDtoModel(DealDetailDtoModel dealDetailDtoModel) {
        if (dealDetailDtoModel == null || dealDetailDtoModel.getSkuUniStructuredDto() == null) {
            return null;
        }
        List<SkuAttrItemDto> skuAttrItemDtos = new ArrayList<>();
        List<MustSkuItemsGroupDto> mustSkuItemsGroupDtos = dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups();
        if (CollectionUtils.isNotEmpty(mustSkuItemsGroupDtos)) {
            skuAttrItemDtos.addAll(mustSkuItemsGroupDtos.stream().flatMap(mustGroup -> extractSkuAttrItemDto(mustGroup.getSkuItems()).stream()).collect(Collectors.toList()));
        }
        List<OptionalSkuItemsGroupDto> optionalSkuItemsGroupDtos = dealDetailDtoModel.getSkuUniStructuredDto().getOptionalGroups();
        if (CollectionUtils.isNotEmpty(optionalSkuItemsGroupDtos)) {
            skuAttrItemDtos.addAll(optionalSkuItemsGroupDtos.stream().flatMap(optionalGroup -> extractSkuAttrItemDto(optionalGroup.getSkuItems()).stream()).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(skuAttrItemDtos)) {
            return null;
        }
        return skuAttrItemDtos;
    }

    private List<SkuAttrItemDto> extractSkuAttrItemDto(List<SkuItemDto> skuItemDtos) {
        if (CollectionUtils.isEmpty(skuItemDtos)) {
            return Lists.newArrayList();
        }
        return skuItemDtos.stream().filter(skuItem -> CollectionUtils.isNotEmpty(skuItem.getAttrItems())).flatMap(skuItem -> skuItem.getAttrItems().stream()).collect(Collectors.toList());
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<AttrListGroupModel> attrListGroupModels;
    }

    @Data
    public static class AttrListGroupModel {
        private List<AttrModel> attrModelList;
        private String groupName;
    }

    /**
     * 通过脚本表达式构造属性值的属性配置模型
     */
    @Data
    public static class AttrModel {
        //属性值构造脚本表达式配置模型
        private List<ScriptExceptionBuilderConfig> attrBuildConfigs;
        //属性展示名称
        private String attrTitle;
        //图标
        private String icon;
    }

    /**
     * 脚本表达式配置
     */
    @Data
    private static class ScriptExceptionBuilderConfig {
        //用于判该情况下的属性值是否应该展示的校验表达式
        private String attrVerifyExpression;
        //用于构造属性值最终展示结果的构造表达式
        private String attrBuildExpression;
    }

}
