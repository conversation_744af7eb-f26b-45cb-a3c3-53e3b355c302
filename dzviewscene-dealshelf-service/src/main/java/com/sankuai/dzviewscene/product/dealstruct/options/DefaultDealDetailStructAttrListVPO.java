package com.sankuai.dzviewscene.product.dealstruct.options;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.struct.vpoints.DealDetailStructAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrItemModel;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrsModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/27 9:17 下午
 */
@VPointOption(name = "团购详情结构化属性卡片认变化点", description = "团购详情结构化属性卡片认变化点",code = DefaultDealDetailStructAttrListVPO.CODE, isDefault = true)
public class DefaultDealDetailStructAttrListVPO extends DealDetailStructAttrListVP<DefaultDealDetailStructAttrListVPO.Config> {

    public static final String CODE = "DefaultDealDetailStructAttrListVPO";

    @Override
    public List<StructAttrsModel> compute(ActivityCxt context, Param param, Config config) {
        List<AttrM> dealAttrs = param.getDealAttrs();
        //1.判断含有命名功能饿简单属性配置模型中是否有值，有则按照该模型进行属性值的获取和填充
        List<AttrsModel> attrsModels = config.getAttrsModels();
        if (CollectionUtils.isNotEmpty(attrsModels)) {
            return attrsModels.stream().map(attrsModel -> buildStructAttrsModel(attrsModel, dealAttrs)).filter(model -> model != null).collect(Collectors.toList());
        }
        //2.判断融合命名/排序/映射/拼接/过滤功能的属性配置模型中是否有值，有则按照该模型进行属性值的获取和填充
        List<MergeSortMapJoinAttrsModel> mergeSortMapJoinAttrsModels = config.getMergeSortMapJoinAttrsModels();
        if (CollectionUtils.isNotEmpty(mergeSortMapJoinAttrsModels)) {
            return mergeSortMapJoinAttrsModels.stream().map(attrsModel -> buildStructAttrsModel(attrsModel, dealAttrs)).filter(model -> model != null).collect(Collectors.toList());
        }
        //3.随后可以再扩展其他模型
        return null;
     }

    private StructAttrsModel buildStructAttrsModel(MergeSortMapJoinAttrsModel attrsModel, List<AttrM> dealAttrs) {
        if (attrsModel == null || CollectionUtils.isEmpty(attrsModel.getMergeSortMapJoinAttrModels())) {
            return null;
        }
        List<MergeSortMapJoinFilterAttrModel> mergeSortMapJoinAttrModels = attrsModel.getMergeSortMapJoinAttrModels();
        List<StructAttrItemModel> structAttrModels = mergeSortMapJoinAttrModels.stream().map(model -> convertMergeSortMapJoinAttrModel2StructAttrsModel(model, dealAttrs))
                .filter(model -> model != null).collect(Collectors.toList());
        return buildStructAttrsModel(attrsModel.getGroupName(), structAttrModels);
    }

    private StructAttrItemModel convertMergeSortMapJoinAttrModel2StructAttrsModel(MergeSortMapJoinFilterAttrModel model, List<AttrM> dealAttrs) {
        if (model == null || CollectionUtils.isEmpty(model.getAttrNameList())) {
            return null;
        }
        //属性值获取 如果有format则属性值会进行format转换
        List<String> attrValues = getAttrValues(model, dealAttrs, model.getAttrFormatModels());
        //获取排序/过滤/映射模型
        List<AttrValueMapModel> attrValueMapModels = model.getAttrValueMapModels();
        //属性值排序
        attrValues = sortAttrValues(attrValues, attrValueMapModels);
        //属性值映射 + 过滤（映射对象为null即被过滤）
        attrValues = mapAndFilterAttrValues(attrValues, attrValueMapModels);
        //属性值拼接
        attrValues = joinAttrValue(attrValues, model);
        //属性值计算为空，且配置了默认展示值时展示默认值
        if(CollectionUtils.isEmpty(attrValues) && StringUtils.isNotEmpty(model.getDefaultDisplayValue())){
            attrValues = Lists.newArrayList(model.getDefaultDisplayValue());
        }
        return buildStructAttrItemModel(model.getDisplayName(), attrValues, model.getIcon(), model.getRelateMainAttrName());
    }

    private StructAttrItemModel buildStructAttrItemModel(String displayName, List<String> attrValues, String icon, String relateMainAttrName) {
        if (CollectionUtils.isEmpty(attrValues)) {
            return null;
        }
        StructAttrItemModel structAttrItemModel = new StructAttrItemModel();
        structAttrItemModel.setAttrName(displayName);
        structAttrItemModel.setAttrValues(attrValues);
        structAttrItemModel.setIcon(icon);
        structAttrItemModel.setRelateMainAttrName(relateMainAttrName);
        return structAttrItemModel;
    }

    private List<String> joinAttrValue(List<String> attrValues, MergeSortMapJoinFilterAttrModel model) {
        if (model == null || model.getSeperator() == null || CollectionUtils.isEmpty(attrValues)) {
            return attrValues;
        }
        return Lists.newArrayList(StringUtils.join(attrValues, model.getSeperator()));
    }

    private List<String> sortAttrValues(List<String> attrValues, List<AttrValueMapModel> attrValueMapModels) {
        if (CollectionUtils.isNotEmpty(attrValueMapModels) || CollectionUtils.isEmpty(attrValues)) {
            return attrValues;
        }
        return attrValues.stream().sorted(Comparator.comparingInt(o -> getAttrPriority(attrValueMapModels, o))).collect(Collectors.toList());
    }

    private List<String> mapAndFilterAttrValues(List<String> attrValues, List<AttrValueMapModel> attrValueMapModels) {
        if (CollectionUtils.isEmpty(attrValueMapModels) || CollectionUtils.isEmpty(attrValues)) {
            return attrValues;
        }
        return attrValues.stream().map(value -> getAttrDisplayValueByAttrValue(attrValueMapModels, value)).filter(value -> StringUtils.isNotEmpty(value)).collect(Collectors.toList());
    }

    private List<String> getAttrValues(MergeSortMapJoinFilterAttrModel model, List<AttrM> dealAttrs, List<AttrFormatModel> attrFormatModels) {
        return model.getAttrNameList().stream().flatMap(name -> getValueWithFormat(dealAttrs, name, attrFormatModels).stream())
                .filter(value -> StringUtils.isNotEmpty(value)).collect(Collectors.toList());
    }

    private List<String> getValueWithFormat(List<AttrM> dealAttrs, String attrName, List<AttrFormatModel> attrFormatModels) {
        List<String> values = DealDetailUtils.getAttrValueByAttrName(dealAttrs, attrName);
        if (CollectionUtils.isEmpty(values)) {
            return Lists.newArrayList();
        }
        AttrFormatModel attrFormatModel = getAttrFormatMapModelByAttrValue(attrFormatModels, attrName);
        if (attrFormatModel == null) {
            return values;
        }
        return values.stream().map(value -> {
            //将属性值中的某个字符串替换为另一个字符串
            if (attrFormatModel.getAttrValueReplaceModel() != null && attrFormatModel.getAttrValueReplaceModel().getStr() != null && attrFormatModel.getAttrValueReplaceModel().getPreStr() != null) {
                value = value.replaceAll(attrFormatModel.getAttrValueReplaceModel().getPreStr(), attrFormatModel.getAttrValueReplaceModel().getStr());
            }
            //将属性值format
            if (StringUtils.isNotEmpty(attrFormatModel.getDisplayFormat())) {
                return String.format(attrFormatModel.getDisplayFormat(), value);
            }
            return value;
        }).collect(Collectors.toList());
    }

    private String getAttrDisplayValueByAttrValue(List<AttrValueMapModel> attrValueMapModels, String attrValue) {
        AttrValueMapModel attrValueMapModel = getAttrValueMapModelByAttrValue(attrValueMapModels, attrValue);
        if (attrValueMapModel == null) {
            return attrValue;
        }
        return attrValueMapModel.getDisplayValue();
    }

    private AttrFormatModel getAttrFormatMapModelByAttrValue(List<AttrFormatModel> attrFormatModels, String attrName) {
        if (CollectionUtils.isEmpty(attrFormatModels) || StringUtils.isEmpty(attrName)) {
            return null;
        }
        return attrFormatModels.stream().filter(model -> attrName.equals(model.getAttrName())).findFirst().orElse(null);
    }

    private int getAttrPriority(List<AttrValueMapModel> attrValueMapModels, String attrValue) {
        AttrValueMapModel attrValueMapModel = getAttrValueMapModelByAttrValue(attrValueMapModels, attrValue);
        if (attrValueMapModel == null || attrValueMapModel.getPriority() == 0) {
            return Integer.MAX_VALUE;
        }
        return attrValueMapModel.getPriority();
    }

    private AttrValueMapModel getAttrValueMapModelByAttrValue(List<AttrValueMapModel> attrValueMapModels, String attrValue) {
        if (CollectionUtils.isEmpty(attrValueMapModels) || StringUtils.isEmpty(attrValue)) {
            return null;
        }
        return attrValueMapModels.stream().filter(model -> attrValue.equals(model.getAttrValue())).findFirst().orElse(null);
    }

    private StructAttrsModel buildStructAttrsModel(AttrsModel attrsModel, List<AttrM> dealAttrs) {
        if (attrsModel == null) {
            return null;
        }
        Map<String, String> attrName2ShowNameMap = attrsModel.getAttrName2ShowNameMap();
        List<StructAttrItemModel> structAttrModels = attrName2ShowNameMap.entrySet().stream().map(entry -> {
            List<String> values;
            if (DealDetailUtils.isWearableNail(dealAttrs)) {
                values = DealDetailUtils.parseAttrValueByAttrName(dealAttrs, entry.getKey());
            } else {
                values = DealDetailUtils.getAttrValueByAttrName(dealAttrs, entry.getKey());
            }
            return buildStructAttrModel(entry.getValue(), values);
        }).filter(model -> model != null).collect(Collectors.toList());
        return buildStructAttrsModel(attrsModel.getGroupName(), structAttrModels);
    }

    private StructAttrsModel buildStructAttrsModel(String name, List<StructAttrItemModel> structAttrModels) {
        if (CollectionUtils.isEmpty(structAttrModels)) {
            //此处不能返回null，否则之后各属性组的index将会前移一位
            return new StructAttrsModel();
        }
        StructAttrsModel structAttrsModel = new StructAttrsModel();
        structAttrsModel.setName(name);
        structAttrsModel.setStructAttrModels(structAttrModels);
        return structAttrsModel;
    }

    private StructAttrItemModel buildStructAttrModel(String name, List<String> values) {
        if (StringUtils.isEmpty(name) || CollectionUtils.isEmpty(values)) {
            return null;
        }
        StructAttrItemModel structAttrModel = new StructAttrItemModel();
        structAttrModel.setAttrName(name);
        structAttrModel.setAttrValues(values);
        return structAttrModel;
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<AttrsModel> attrsModels;
        private List<MergeSortMapJoinAttrsModel> mergeSortMapJoinAttrsModels;
    }

    /**
     * 含有命名功能的属性简单配置模型
     */
    @Data
    public static class AttrsModel {
        private Map<String, String> attrName2ShowNameMap;
        private String groupName;
    }

    @Data
    public static class MergeSortMapJoinAttrsModel {
        private List<MergeSortMapJoinFilterAttrModel> mergeSortMapJoinAttrModels;
        private String groupName;
    }

    /**
     * 融合命名/排序/映射/拼接/过滤功能的属性配置模型
     */
    @Data
    public static class MergeSortMapJoinFilterAttrModel {
        //属性展示名称
        private String displayName;
        //属性名列表
        private List<String> attrNameList;
        //多个属性之间的拼接符
        private String seperator;
        //属性值 - 展示值映射 ； 展示顺序按照map中的展示顺序
        private List<AttrValueMapModel> attrValueMapModels;
        //属性名 - 展示format映射
        private List<AttrFormatModel> attrFormatModels;
        //图标
        private String icon;
        //关联的主属性名
        private String relateMainAttrName;
        //默认展示值
        private String defaultDisplayValue;
    }

    @Data
    public static class AttrValueReplaceModel {
        private String preStr;
        private String str;
    }

    @Data
    public static class AttrValueMapModel {
        private String attrValue;
        private String displayValue;
        //优先级从1开始
        private int priority;
    }

    @Data
    public static class AttrFormatModel {
        private String attrName;
        private String displayFormat;
        //将属性值中的指定字符串替换成另一个字符串
        private AttrValueReplaceModel attrValueReplaceModel;
    }
}
