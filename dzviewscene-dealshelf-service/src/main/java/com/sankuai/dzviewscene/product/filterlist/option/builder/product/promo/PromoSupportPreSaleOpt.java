package com.sankuai.dzviewscene.product.filterlist.option.builder.product.promo;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPromosVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzPromoVO;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/29 2:00 下午
 */

@VPointOption(name = "通用优惠信息",
        description = "支持预售",
        code = "PromoSupportPreSaleOpt")
public class PromoSupportPreSaleOpt extends ProductPromosVP<PromoSupportPreSaleOpt.Config> {

    @Override
    public List<DzPromoVO> compute(ActivityCxt context, ProductPromosVP.Param param, PromoSupportPreSaleOpt.Config config) {
        if (config.isEnablePreSale() && PreSaleUtils.isPreSaleDeal(param.getProductM())) {
            return getPreSalePromo(param, config);
        }
        return getDefPromo(param);
    }

    private List<DzPromoVO> getPreSalePromo(Param param, PromoSupportPreSaleOpt.Config config) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.filterlist.option.builder.product.promo.PromoSupportPreSaleOpt.getPreSalePromo(ProductPromosVP$Param,PromoSupportPreSaleOpt$Config)");
        ProductM productM = param.getProductM();
        if (CollectionUtils.isEmpty(productM.getPromoPrices())) {
            return Lists.newArrayList();
        }
        ProductPromoPriceM productPromoPriceM = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
        if (productPromoPriceM == null) {
            return Lists.newArrayList();
        }
        DzPromoVO dzPromoVO = new DzPromoVO();
        dzPromoVO.setPromo(productPromoPriceM.getPromoTag());
        return Lists.newArrayList(dzPromoVO);
    }

    @VPointCfg
    @Data
    public static class Config {

        private boolean enablePreSale;

        private int position;

        private String preSaleTemplate;
    }
}
