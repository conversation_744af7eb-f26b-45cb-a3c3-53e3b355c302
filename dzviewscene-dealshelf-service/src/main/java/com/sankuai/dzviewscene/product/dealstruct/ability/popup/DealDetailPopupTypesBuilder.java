package com.sankuai.dzviewscene.product.dealstruct.ability.popup;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.popup.vpoints.DealDetailPopupTypesVP;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.vo.PopupTypeVO;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2021/11/28 12:17 下午
 */
@Ability(code = DealDetailPopupTypesBuilder.CODE,
        name = "团购详情模块弹窗样式组件构造能力",
        description = "团购详情模块弹窗样式组件构造能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
        }
)
public class DealDetailPopupTypesBuilder extends PmfAbility<List<PopupTypeVO>, DealDetailPopupTypesParam, DealDetailPopupTypesCfg> {

    public static final String CODE = "dealDetailPopupTypesBuilder";

    @Override
    public CompletableFuture<List<PopupTypeVO>> build(ActivityCxt activityCxt, DealDetailPopupTypesParam popupTypesParam, DealDetailPopupTypesCfg popupTypesCfg) {
        DealDetailPopupTypesVP<?> dealDetailPopupTypesVP = findVPoint(activityCxt, DealDetailPopupTypesVP.CODE);
        List<PopupTypeVO> popupTypeVOS = dealDetailPopupTypesVP.execute(activityCxt, DealDetailPopupTypesVP.Param.builder().build());
        return CompletableFuture.completedFuture(popupTypeVOS);
    }
}
