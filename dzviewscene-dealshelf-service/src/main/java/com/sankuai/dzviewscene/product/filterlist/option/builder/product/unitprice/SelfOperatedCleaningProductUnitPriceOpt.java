package com.sankuai.dzviewscene.product.filterlist.option.builder.product.unitprice;

import com.dianping.lion.common.util.JsonUtils;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductUnitPriceVP;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.LifeCleanSimilarFilterAcsOpt;
import com.sankuai.dzviewscene.product.filterlist.vo.DzProductVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealDetailSkuUniStructuredModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealSkuAttrModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealStructModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.MustSkusGroupModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-11-21
 * @desc 自营保洁团单单位价格
 */
@Slf4j
@VPointOption(name = "自营保洁团单单位价格计算", description = "自营保洁团单单位价格计算", code = SelfOperatedCleaningProductUnitPriceOpt.CODE)
public class SelfOperatedCleaningProductUnitPriceOpt extends ProductUnitPriceVP<SelfOperatedCleaningProductUnitPriceOpt.Config> {

    public static final String CODE = "SelfOperatedCleaningProductUnitPriceOpt";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (inValidParam(param)) {
            return null;
        }
        ProductM productM = param.getProductM();
        DzProductVO dzProductVO = param.getDzProductVO();
        String dealStructContent = DealDetailUtils.getAttrSingleValueByAttrName(productM.getExtAttrs(), "dealStructContent");
        DealStructModel dealStructModel;
        try {
            dealStructModel = JsonUtils.fromJson(dealStructContent, DealStructModel.class);
            if (Objects.isNull(dealStructModel) || Objects.isNull(dealStructModel.getDealDetailStructuredData())
                    || Objects.isNull(dealStructModel.getDealDetailStructuredData().getDealDetailSkuUniStructuredModel())) {
                return null;
            }
        } catch (Exception e) {
            log.error("getDealServiceItem json parse error, dealStructContent={}", dealStructContent, e);
            return null;
        }
        DealDetailSkuUniStructuredModel dealDetailSkuUniStructuredModel = dealStructModel.getDealDetailStructuredData().getDealDetailSkuUniStructuredModel();
        String serviceDurationStr = getServiceDurationAttr(dealDetailSkuUniStructuredModel.getMustGroups());
        int serviceDuration = getServiceDuration(serviceDurationStr, config);
        String salePriceStr = dzProductVO.getSalePrice();
        return calculateUnitPrice(salePriceStr, serviceDuration, config);
    }

    private boolean inValidParam(Param param) {
        return Objects.isNull(param)
                || Objects.isNull(param.getProductM()) || CollectionUtils.isEmpty(param.getProductM().getExtAttrs())
                || Objects.isNull(param.getDzProductVO()) || StringUtils.isBlank(param.getDzProductVO().getSalePrice());
    }

    private String calculateUnitPrice(String salePriceStr, int serviceDuration, Config config) {
        if (StringUtils.isBlank(salePriceStr) || serviceDuration <= 0) {
            return StringUtils.EMPTY;
        }
        try {
            salePriceStr = salePriceStr.replace(config.getPriceSymbol(), "");
            BigDecimal salePrice = new BigDecimal(salePriceStr);
            BigDecimal timeDecimal = new BigDecimal(serviceDuration);
            // 计算单位价格，需要保留1位小数
            BigDecimal unitPrice = salePrice.divide(timeDecimal, 1, RoundingMode.HALF_UP);
            return String.format("%s%s%s", config.getPriceSymbol(), unitPrice.toPlainString(),config.getSuffix());
        } catch (Exception e) {
            log.error("calculateUnitPrice error", e);
            return StringUtils.EMPTY;
        }
    }

    private int getServiceDuration(String serviceDurationStr, Config config) {
        if (StringUtils.isBlank(serviceDurationStr)) {
            return 0;
        }
        String serviceDuration = serviceDurationStr.replace(config.getPriceUnit(), "");
        return NumberUtils.isDigits(serviceDuration) ? Integer.parseInt(serviceDuration) : 0;
    }

    private String getServiceDurationAttr(List<MustSkusGroupModel> mustGroups) {
        if (CollectionUtils.isEmpty(mustGroups)) {
            return StringUtils.EMPTY;
        }
        Optional<String> serviceDuration = mustGroups.stream()
                .flatMap(mustGroup -> mustGroup.getSkus().stream())
                .flatMap(skuModel -> skuModel.getSkuAttrs().stream())
                .filter(skuAttrModel -> Objects.equals(skuAttrModel.getAttrName(), "serviceDuration"))
                .map(DealSkuAttrModel::getAttrValue)
                .findFirst();
        return serviceDuration.orElse(StringUtils.EMPTY);
    }

    @Data
    @VPointCfg
    public static class Config {
        /**
         * 价格符号
         */
        private String priceSymbol = "￥";

        /**
         * 后缀
         */
        private String suffix = "/小时";

        /**
         * 价格单位
         */
        private String priceUnit = "小时";
    }
}
