package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bath.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

/**
 * @author: created by hang.yu on 2023/9/27 16:17
 */
@Getter
@AllArgsConstructor
public enum BathServiceProjectEnum {

    BATH_TICKET(895L, "浴资票"),
    /**
     * 服务费(浴资票)
     */
    SERVICE_FEE_1(2105646L, "服务费"),
    /**
     * 服务费(浴资票/店内服务)
     */
    SERVICE_FEE_2(2105661L, "服务费"),
    /**
     * 餐饮(浴资票)
     */
    FOOD_1(2105655L, "餐饮"),
    /**
     * 餐饮(浴资票/店内服务)
     */
    FOOD_2(2105656L, "餐饮"),

    /**
     * 餐饮
     */
    FOOD_3(1060L, "餐饮"),
    SCRUB(70066L, "搓澡"),
    /**
     * 按摩养生
     */
    MASSAGE(897L, "按摩"),
    JOY(899L, "玩乐"),
    SPA(898L, "美容spa"),
    /**
     * 住宿休憩
     */
    REST(896L, "住宿/休憩"),
    ;

    public static final List<Long> SORTED_LIST = Lists.newArrayList();

    static {
        SORTED_LIST.add(BathServiceProjectEnum.BATH_TICKET.getServiceProjectId());
        SORTED_LIST.add(BathServiceProjectEnum.FOOD_1.getServiceProjectId());
        SORTED_LIST.add(BathServiceProjectEnum.FOOD_2.getServiceProjectId());
        SORTED_LIST.add(BathServiceProjectEnum.FOOD_3.getServiceProjectId());
        SORTED_LIST.add(BathServiceProjectEnum.SCRUB.getServiceProjectId());
        SORTED_LIST.add(BathServiceProjectEnum.MASSAGE.getServiceProjectId());
        SORTED_LIST.add(BathServiceProjectEnum.REST.getServiceProjectId());
        SORTED_LIST.add(BathServiceProjectEnum.JOY.getServiceProjectId());
        SORTED_LIST.add(BathServiceProjectEnum.SPA.getServiceProjectId());
        SORTED_LIST.add(BathServiceProjectEnum.SERVICE_FEE_1.getServiceProjectId());
        SORTED_LIST.add(BathServiceProjectEnum.SERVICE_FEE_2.getServiceProjectId());
    }

    private final long serviceProjectId;

    private final String serviceProjectName;


    public static BathServiceProjectEnum getEnumByServiceProjectId(long serviceProjectId) {
        for (BathServiceProjectEnum value : BathServiceProjectEnum.values()) {
            if (value.getServiceProjectId() == serviceProjectId) {
                return value;
            }
        }
        return null;
    }

}
