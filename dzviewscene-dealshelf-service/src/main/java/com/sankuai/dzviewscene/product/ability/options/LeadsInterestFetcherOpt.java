package com.sankuai.dzviewscene.product.ability.options;

import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.clr.content.process.common.enums.MerchantTypeEnum;
import com.sankuai.clr.content.process.common.leads.enums.EntranceCodeEnum;
import com.sankuai.clr.content.process.thrift.dto.leads.req.BatchQueryLeadsInfoReqDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.req.QueryLeadsInfoReqDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.resp.BatchQueryLeadsInfoRespDTO;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreAsyncHandlerVP;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@VPointOption(name = "查询留资权益", description = "前置查询门店是否拥有留资权益，用于留资型货架展示控制", code = LeadsInterestFetcherOpt.CODE)
public class LeadsInterestFetcherOpt extends PreAsyncHandlerVP<LeadsInterestFetcherOpt.Config> {

    public static final String CODE = "LeadsInterestFetcherOpt";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<Object> compute(ActivityCxt context, Param param, Config config) {
        BatchQueryLeadsInfoReqDTO infoReqDTO = new BatchQueryLeadsInfoReqDTO();
        infoReqDTO.setEntranceCode(EntranceCodeEnum.DEAL_GROUP_SHELF.getCode());
        List<QueryLeadsInfoReqDTO> reqDTOList = Lists.newArrayList(toQueryLeadsInfoReqDTO(ParamsUtil.getLongSafely(context, PmfConstants.Params.dpPoiId)));
        infoReqDTO.setQueryLeadsInfoReqDTOList(reqDTOList);
        return compositeAtomService.batchQueryLeadsInfo(infoReqDTO).thenApply(this::leadsAllowable);
    }

    private Boolean leadsAllowable(BatchQueryLeadsInfoRespDTO respDTO) {
        if (respDTO == null || !respDTO.isSuccess()) {
            return false;
        }
        if (CollectionUtils.isEmpty(respDTO.getLeadsInfoDTOs()) || respDTO.getLeadsInfoDTOs().get(0) == null) {
            return false;
        }
        return respDTO.getLeadsInfoDTOs().get(0).getLeadsAllowable();
    }

    private QueryLeadsInfoReqDTO toQueryLeadsInfoReqDTO(long dpShopId) {
        QueryLeadsInfoReqDTO reqDTO = new QueryLeadsInfoReqDTO();
        reqDTO.setEntranceCode(EntranceCodeEnum.DEAL_GROUP_SHELF.getCode());
        reqDTO.setMerchantType(MerchantTypeEnum.SHOP.getType());
        reqDTO.setPlatform(VCPlatformEnum.DP.getType());
        reqDTO.setDpShopId(dpShopId);
        return reqDTO;
    }


    @Data
    @VPointCfg
    public static class Config {
    }
}
