package com.sankuai.dzviewscene.product.filterlist.option.builder.product.unitprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductUnitPriceVP;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;

/**
 * @description :
 * @date : 2025/1/15
 */
@Slf4j
@VPointOption(name = "优惠码团单次卡单位价格", description = "优惠码团单次卡单位价格计算", code = PromoCodeTimeDealCardUnitPriceOpt.CODE)
public class PromoCodeTimeDealCardUnitPriceOpt extends ProductUnitPriceVP<PromoCodeTimeDealCardUnitPriceOpt.Config> {

    public static final String CODE = "PromoCodeDealTimeCardUnitPriceOpt";

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Config config) {
        if (!param.getProductM().isTimesDeal() || param.getProductM().getSalePrice() == null) {
            return null;
        }
        // 多次卡的次数
        String times = param.getProductM().getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER);
        if (NumberUtils.toLong(times) == 0) {
            return null;
        }
        return TimesDealUtil.getSinglePrice(param.getProductM().getSalePrice(), times);
    }

    @Data
    @VPointCfg
    public static class Config {

    }

}
