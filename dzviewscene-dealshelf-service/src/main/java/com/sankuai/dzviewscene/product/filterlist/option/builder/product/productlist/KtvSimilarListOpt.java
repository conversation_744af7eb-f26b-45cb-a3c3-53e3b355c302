package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.filterlist.utils.SkuItemUtils;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: created by hang.yu on 2024/4/8 10:55
 */
@VPointOption(name = "Ktv相似团购筛选列表过滤与排序",
        description = "Ktv相似团购筛选列表过滤与排序",
        code = "KtvSimilarListOpt")
public class KtvSimilarListOpt extends AbstractTimesDealListOpt {

    @Override
    protected List<ProductM> getSimilarDealList(ActivityCxt context, ProductM currentProduct, List<ProductM> list) {
        String pageSource = context.getParam(ShelfActivityConstants.Params.pageSource);
        if (GUESS.equals(pageSource)) {
            return getGuessList(currentProduct, list);
        } else if (SHELF.equals(pageSource)) {
            return getShelfList(currentProduct, list);
        }
        return Lists.newArrayList();
    }

    public List<ProductM> getGuessList(ProductM currentProduct, List<ProductM> list) {
        String currentServiceType = DealDetailUtils.getAttrSingleValueByAttrName(currentProduct.getExtAttrs(), SERVICE_TYPE);
        if (ServiceTypeConstants.SING_SET.contains(currentServiceType)) {
            return list.stream().filter(this::filterSingServiceType).sorted(sortSale()).collect(Collectors.toList());
        } else if (ServiceTypeConstants.FOOD_SERVICE_TYPE.equals(currentServiceType)) {
            return sortProductBySale(list);
        }
        return Lists.newArrayList();
    }

    public List<ProductM> getShelfList(ProductM currentProduct, List<ProductM> list) {
        String currentServiceType = DealDetailUtils.getAttrSingleValueByAttrName(currentProduct.getExtAttrs(), SERVICE_TYPE);
        List<Integer> currentDisableDays = getDisableDays(currentProduct);
        Map<String, String> currentPackageType = getPackageType(currentProduct);
        List<String> currentPeriodList = getPeriodList(currentProduct);

        if (ServiceTypeConstants.FOOD_SERVICE_TYPE.equals(currentServiceType)) {
            // 纯美食酒水套餐
            List<ProductM> filterList = list.stream().filter(this::filterWineServiceType).collect(Collectors.toList());
            return sortFoodList(filterList);
        } else if (ServiceTypeConstants.SING_AND_WINE_SERVICE_TYPE.equals(currentServiceType)) {
            // 欢唱和含酒类套餐
            List<ProductM> filterList = list.stream()
                    .filter(e -> filterDisableDays(currentDisableDays, e))
                    .filter(e -> filterPackageType(currentPackageType, e))
                    .filter(e -> filterPeriod(currentPeriodList, currentProduct))
                    .collect(Collectors.toList());
            return sortSingAndWineList(filterList);
        } else if (ServiceTypeConstants.SING_AND_BEVERAGE_SERVICE_TYPE.equals(currentServiceType)) {
            // 欢唱和软饮类套餐
            List<ProductM> filterList = list.stream()
                    .filter(e -> filterDisableDays(currentDisableDays, e))
                    .filter(e -> filterPackageType(currentPackageType, e))
                    .filter(e -> filterPeriod(currentPeriodList, currentProduct))
                    .collect(Collectors.toList());
            return sortSingAndBeverageList(filterList);
        } else if (ServiceTypeConstants.SING_SERVICE_TYPE.equals(currentServiceType)) {
            // 纯欢唱套餐
            List<ProductM> filterList = list.stream().filter(this::filterSingServiceType)
                    .filter(e -> filterDisableDays(currentDisableDays, e))
                    .filter(e -> filterPackageType(currentPackageType, e))
                    .filter(e -> filterPeriod(currentPeriodList, e))
                    .collect(Collectors.toList());
            return sortSingList(filterList);
        }
        return Lists.newArrayList();
    }

    public boolean filterSingServiceType(ProductM productM) {
        String serviceType = DealDetailUtils.getAttrSingleValueByAttrName(productM.getExtAttrs(), SERVICE_TYPE);
        return ServiceTypeConstants.SING_SET.contains(serviceType);
    }

    public boolean filterWineServiceType(ProductM productM) {
        String serviceType = DealDetailUtils.getAttrSingleValueByAttrName(productM.getExtAttrs(), SERVICE_TYPE);
        return ServiceTypeConstants.WINE_SET.contains(serviceType);
    }

    public boolean filterDisableDays(List<Integer> currentDisableDays, ProductM productM) {
        List<Integer> disableDays = getDisableDays(productM);
        if (CollectionUtils.isEmpty(currentDisableDays)) {
            return CollectionUtils.isEmpty(disableDays);
        }
        if (CollectionUtils.isEmpty(disableDays)) {
            return true;
        }
        // 二者有交集
        return !Collections.disjoint(currentDisableDays, disableDays);
    }

    public boolean filterPackageType(Map<String, String> currentPackageType, ProductM productM) {
        Map<String, String> packageType = getPackageType(productM);
        String serviceType = DealDetailUtils.getAttrSingleValueByAttrName(productM.getExtAttrs(), "service_type");
        // 欢唱+含酒类套餐，欢唱+软饮类套餐，则额外卡控包型和可用时段
        if (ServiceTypeConstants.SING_AND_WINE_SERVICE_TYPE.equals(serviceType) || ServiceTypeConstants.SING_AND_BEVERAGE_SERVICE_TYPE.equals(serviceType)) {
            if (currentPackageType.containsKey(PackageTypeConstants.SMALL_PACKAGE)) {
                return packageType.containsKey(PackageTypeConstants.SMALL_PACKAGE)
                        || packageType.containsKey(PackageTypeConstants.SMALL_MID_PACKAGE)
                        || packageType.containsKey(PackageTypeConstants.SMALL_MID_BIG_PACKAGE);
            } else if (currentPackageType.containsKey(PackageTypeConstants.SMALL_MID_PACKAGE)) {
                return packageType.containsKey(PackageTypeConstants.SMALL_PACKAGE)
                        || packageType.containsKey(PackageTypeConstants.SMALL_MID_PACKAGE)
                        || packageType.containsKey(PackageTypeConstants.MID_PACKAGE)
                        || packageType.containsKey(PackageTypeConstants.SMALL_MID_BIG_PACKAGE);
            } else if (currentPackageType.containsKey(PackageTypeConstants.SMALL_MID_BIG_PACKAGE)) {
                return true;
            } else if (currentPackageType.containsKey(PackageTypeConstants.MID_PACKAGE)) {
                return packageType.containsKey(PackageTypeConstants.MID_PACKAGE)
                        || packageType.containsKey(PackageTypeConstants.SMALL_MID_PACKAGE)
                        || packageType.containsKey(PackageTypeConstants.MID_BIG_PACKAGE)
                        || packageType.containsKey(PackageTypeConstants.SMALL_MID_BIG_PACKAGE);
            } else if (currentPackageType.containsKey(PackageTypeConstants.MID_BIG_PACKAGE)) {
                return packageType.containsKey(PackageTypeConstants.MID_PACKAGE)
                        || packageType.containsKey(PackageTypeConstants.SMALL_MID_PACKAGE)
                        || packageType.containsKey(PackageTypeConstants.BIG_PACKAGE)
                        || packageType.containsKey(PackageTypeConstants.MID_BIG_PACKAGE)
                        || packageType.containsKey(PackageTypeConstants.SMALL_MID_BIG_PACKAGE);
            } else if (currentPackageType.containsKey(PackageTypeConstants.BIG_PACKAGE)) {
                return packageType.containsKey(PackageTypeConstants.BIG_PACKAGE)
                        || packageType.containsKey(PackageTypeConstants.MID_BIG_PACKAGE)
                        || packageType.containsKey(PackageTypeConstants.SMALL_MID_BIG_PACKAGE);
            }
            return false;
        }
        return true;
    }

    public boolean filterPeriod(List<String> currentPeriodList, ProductM productM) {
        String serviceType = DealDetailUtils.getAttrSingleValueByAttrName(productM.getExtAttrs(), "service_type");
        if (ServiceTypeConstants.SING_AND_WINE_SERVICE_TYPE.equals(serviceType) || ServiceTypeConstants.SING_AND_BEVERAGE_SERVICE_TYPE.equals(serviceType)) {
            List<String> periodList = getPeriodList(productM);
            return !Collections.disjoint(currentPeriodList, periodList);
        }
        return true;
    }

    public List<Integer> getDisableDays(ProductM productM) {
        if (productM.getUseRuleM() == null || productM.getUseRuleM().getDisableDate() == null || CollectionUtils.isEmpty(productM.getUseRuleM().getDisableDate().getDisableDays())) {
            return Lists.newArrayList();
        }
        return productM.getUseRuleM().getDisableDate().getDisableDays().stream().filter(WeekConstants.ALL_DAYS::contains).collect(Collectors.toList());
    }

    public Map<String, String> getPackageType(ProductM productM) {
        String productMetaTags = DealDetailUtils.getAttrSingleValueByAttrName(productM.getExtAttrs(), "productMetaTags");
        if (StringUtils.isBlank(productMetaTags)) {
            return Maps.newHashMap();
        }
        return JsonCodec.decode(productMetaTags, new TypeReference<Map<String, String>>() {
        });
    }

    public List<String> getPeriodList(ProductM productM) {
        List<SkuItemDto> skuItemList = SkuItemUtils.getTotalSkuItem(productM);
        if (CollectionUtils.isEmpty(skuItemList)) {
            return Lists.newArrayList();
        }
        return skuItemList.stream().map(skuItemDto -> DealStructUtils.getAttrValueByAttrKey(skuItemDto, "period")).filter(Objects::nonNull)
                .flatMap(str -> Arrays.stream(str.split("、"))).distinct().collect(Collectors.toList());
    }

    public List<ProductM> sortSingList(List<ProductM> filterList) {
        List<ProductM> result = Lists.newArrayList();
        List<ProductM> list1 = filterList.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.SING_SERVICE_TYPE))
                .sorted(sortSale()).collect(Collectors.toList());
        result.addAll(list1);
        List<ProductM> list2 = filterList.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.SING_AND_WINE_SERVICE_TYPE))
                .sorted(sortSale()).collect(Collectors.toList());
        result.addAll(list2);
        List<ProductM> list3 = filterList.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.SING_AND_BEVERAGE_SERVICE_TYPE))
                .sorted(sortSale()).collect(Collectors.toList());
        result.addAll(list3);
        return result;
    }

    public List<ProductM> sortFoodList(List<ProductM> filterList) {
        List<ProductM> result = Lists.newArrayList();
        List<ProductM> list1 = filterList.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.FOOD_SERVICE_TYPE))
                .sorted(sortSale()).collect(Collectors.toList());
        result.addAll(list1);
        List<ProductM> list2 = filterList.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.SING_AND_WINE_SERVICE_TYPE))
                .sorted(sortSale()).collect(Collectors.toList());
        result.addAll(list2);
        List<ProductM> list3 = filterList.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.SING_AND_BEVERAGE_SERVICE_TYPE))
                .sorted(sortSale()).collect(Collectors.toList());
        result.addAll(list3);
        return result;
    }

    public List<ProductM> sortSingAndWineList(List<ProductM> filterList) {
        List<ProductM> result = Lists.newArrayList();
        List<ProductM> list1 = filterList.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.SING_AND_WINE_SERVICE_TYPE))
                .sorted(sortSale()).collect(Collectors.toList());
        result.addAll(list1);
        List<ProductM> list2 = filterList.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.SING_AND_BEVERAGE_SERVICE_TYPE))
                .sorted(sortSale()).collect(Collectors.toList());
        result.addAll(list2);
        List<ProductM> list3 = filterList.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.SING_SERVICE_TYPE))
                .sorted(sortSale()).collect(Collectors.toList());
        result.addAll(list3);
        List<ProductM> list4 = filterList.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.FOOD_SERVICE_TYPE))
                .sorted(sortSale()).collect(Collectors.toList());
        result.addAll(list4);
        return result;
    }

    public List<ProductM> sortSingAndBeverageList(List<ProductM> filterList) {
        List<ProductM> result = Lists.newArrayList();
        List<ProductM> list1 = filterList.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.SING_AND_BEVERAGE_SERVICE_TYPE))
                .sorted(sortSale()).collect(Collectors.toList());
        result.addAll(list1);
        List<ProductM> list2 = filterList.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.SING_AND_WINE_SERVICE_TYPE))
                .sorted(sortSale()).collect(Collectors.toList());
        result.addAll(list2);
        List<ProductM> list3 = filterList.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.SING_SERVICE_TYPE))
                .sorted(sortSale()).collect(Collectors.toList());
        result.addAll(list3);
        List<ProductM> list4 = filterList.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.FOOD_SERVICE_TYPE))
                .sorted(sortSale()).collect(Collectors.toList());
        result.addAll(list4);
        return result;
    }

    static class ServiceTypeConstants {
        public static final Set<String> SING_SET = Sets.newHashSet("纯欢唱套餐", "欢唱和含酒类套餐", "欢唱和软饮类套餐");

        public static final Set<String> WINE_SET = Sets.newHashSet("欢唱和含酒类套餐", "欢唱和软饮类套餐", "纯美食酒水套餐");

        public static final String SING_SERVICE_TYPE = "纯欢唱套餐";

        public static final String SING_AND_WINE_SERVICE_TYPE = "欢唱和含酒类套餐";

        public static final String SING_AND_BEVERAGE_SERVICE_TYPE = "欢唱和软饮类套餐";

        public static final String FOOD_SERVICE_TYPE = "纯美食酒水套餐";
    }

    static class PackageTypeConstants {
        public static final String SMALL_PACKAGE = "100205353";

        public static final String SMALL_MID_PACKAGE = "100234114";

        public static final String SMALL_MID_BIG_PACKAGE = "100211373";

        public static final String MID_PACKAGE = "100205354";

        public static final String MID_BIG_PACKAGE = "100236133";

        public static final String BIG_PACKAGE = "100231134";
    }

}
