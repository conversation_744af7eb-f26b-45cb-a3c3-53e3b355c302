package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Component
public class NoPaddingHandler implements PaddingHandler {

    @Override
    public CompletableFuture<ProductGroupM> padding(ActivityCxt ctx, ProductGroupM productGroupM, Map<String, Object> params) {
        return CompletableFuture.completedFuture(productGroupM);
    }
}
