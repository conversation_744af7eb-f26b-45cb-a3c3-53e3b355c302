package com.sankuai.dzviewscene.product.factory.impl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleItem;
import com.sankuai.dzviewscene.product.factory.DealCategoryStrategy;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: created by hang.yu on 2023/10/16 11:27
 */
@Component
public class DefaultCategoryStrategyImpl implements DealCategoryStrategy {
    @Override
    public List<ModuleItem> getModuleList(ActivityCxt activityCxt, DealDetailAssembleCfg assembleCfg) {
        return assembleCfg.getModuleList();
    }

}
