package com.sankuai.dzviewscene.product.ability.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreSyncHandlerVP;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@VPointOption(name = "定位信息兜底",
            description = "当用户未开启定位的时候，将经纬度城市用店铺信息进行兜底",
            code = LocationFallbackOpt.CODE)
@Slf4j
public class LocationFallbackOpt extends PreSyncHandlerVP<LocationFallbackOpt.Config> {

    public static final String CODE = "LocationFallbackOpt";

    @Override
    public Map<String, Object> compute(ActivityCxt ctx, Param param, Config config) {
        double epsilon = 1e-6;
        Double lat = ParamsUtil.getDoubleSafely(ctx, PmfConstants.Params.lat);
        Double lng = ParamsUtil.getDoubleSafely(ctx, PmfConstants.Params.lng);
        Integer cityId = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.dpCityId);
        if ((lat != null && Math.abs(lat) > epsilon)
                || (lng != null && Math.abs(lng) > epsilon)) {
            return Collections.emptyMap();
        }
        ShopM shopM = ctx.getParam(ShelfActivityConstants.Ctx.ctxShop);
        if (shopM == null) {
            return Collections.emptyMap();
        }

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(PmfConstants.Params.dpCityId, ParamsUtil.getIntSafely(ctx, PmfConstants.Params.shopDpCityId));
        resultMap.put(PmfConstants.Params.mtCityId, ParamsUtil.getIntSafely(ctx, PmfConstants.Params.shopMtCityId));
        resultMap.put(PmfConstants.Params.lat, shopM.getLat());
        resultMap.put(PmfConstants.Params.lng, shopM.getLng());
        return resultMap;
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
