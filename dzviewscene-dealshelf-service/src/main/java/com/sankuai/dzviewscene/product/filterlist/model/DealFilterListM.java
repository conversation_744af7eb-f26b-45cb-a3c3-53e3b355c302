package com.sankuai.dzviewscene.product.filterlist.model;

import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 团单筛选列表数据模型
 * 2022.05 当前大多还是单个商品组的情况，但为了保持模型兼容依旧按多组设计。下游取数可以直接先取第一个
 */
@Data
public class DealFilterListM {
    /**
     * 斗斛打点数据, 一个货架数据模型多个斗斛打点数据
     */
    private List<DouHuM> douHus = new CopyOnWriteArrayList<>();

    /**
     * 商品组->商品筛选
     */
    private Map<String, FilterM> filterMs;

    /**
     * 商品组->商品列表
     */
    private Map<String, ProductGroupM> productGroupMs;

    public ProductGroupM getFirstProductGroup() {
        if (MapUtils.isEmpty(productGroupMs)) {
            return null;
        }
        return productGroupMs.values().stream().filter(Objects::nonNull).findFirst().orElse(null);
    }
    
    public int getTotalCount() {
        int realTotalCount = MapUtils.isEmpty(filterMs) ? 0 : filterMs.values().stream().filter(Objects::nonNull)
                .map(FilterM::getProductTotalCount).findFirst().orElse(0);
        if (realTotalCount > 0) {
            return realTotalCount;
        }
        return MapUtils.isEmpty(productGroupMs) ? 0
                : productGroupMs.values().stream().filter(Objects::nonNull).mapToInt(ProductGroupM::getTotal).sum();
    }
}
