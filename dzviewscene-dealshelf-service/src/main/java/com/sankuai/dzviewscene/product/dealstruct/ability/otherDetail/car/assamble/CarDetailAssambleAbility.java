package com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.car.assamble;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.car.windowFilmSpecification.CarWindomFilmSpecificationBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.car.windowFilmserviceInfo.CarWindiwFilmServiceInfoBuilder;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealModuleDetailVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.car.CarDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.car.CarDetailVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2022/12/8 4:04 下午
 */
@Ability(code = CarDetailAssambleAbility.CODE,
        name = "爱车团详组装能力",
        description = "爱车团详组装能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                CarWindiwFilmServiceInfoBuilder.CODE,
                CarWindomFilmSpecificationBuilder.CODE
        }
)
public class CarDetailAssambleAbility extends PmfAbility<DealModuleDetailVO, CarDetailAssambleParam, CarDetailAssambleCfg> {

    public static final String CODE = "CarDetailAssambleAbility";

    @Override
    public CompletableFuture<DealModuleDetailVO> build(ActivityCxt ctx, CarDetailAssambleParam carDetailAssambleParam, CarDetailAssambleCfg carDetailAssambleCfg) {
        List<CarDetailModuleVO> carDetailModuleVOS = getCarDetailModuleVOList(ctx);
        CarDetailVO carDetailVO = buildCarDetailVO(carDetailModuleVOS);
        return CompletableFuture.completedFuture(buildDealModuleDetailVO(ctx.getSceneCode(), carDetailVO));
    }

    private List<CarDetailModuleVO> getCarDetailModuleVOList(ActivityCxt ctx) {
        List<CarDetailModuleVO> carDetailModuleVOS = new ArrayList<>();
        CarDetailModuleVO carWindomFilmSpecification = ctx.getSource(CarWindomFilmSpecificationBuilder.CODE);
        if (carWindomFilmSpecification != null) {
            carDetailModuleVOS.add(carWindomFilmSpecification);
        }
        CarDetailModuleVO carWindiwFilmServiceInfo = ctx.getSource(CarWindiwFilmServiceInfoBuilder.CODE);
        if (carWindiwFilmServiceInfo != null) {
            carDetailModuleVOS.add(carWindiwFilmServiceInfo);
        }
        return carDetailModuleVOS;
    }

    private CarDetailVO buildCarDetailVO(List<CarDetailModuleVO> carDetailModuleVOS) {
        if (CollectionUtils.isEmpty(carDetailModuleVOS)) {
            return null;
        }
        CarDetailVO carDetailVO = new CarDetailVO();
        carDetailVO.setModuleList(carDetailModuleVOS);
        return carDetailVO;
    }

    private DealModuleDetailVO buildDealModuleDetailVO(String sceneCode, CarDetailVO carDetailVO) {
        DealModuleDetailVO dealModuleDetailVO = new DealModuleDetailVO();
        dealModuleDetailVO.setCarDetailVO(carDetailVO);
        dealModuleDetailVO.setSceneCode(sceneCode);
        return dealModuleDetailVO;
    }
}
