package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.ability.model.LeUncoopShopShelfAttrM;
import com.sankuai.dzviewscene.product.ability.options.LeUnCoopShopUniverseInfoOpt;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListTitleVP;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * @author: wuwenqiang
 * @create: 2024-07-04
 * @description:
 */
@VPointOption(name = "LE非合作商户团购列表标题",
        description = "LE非合作商户团购列表标题",
        code = LEUnCoopShopDealListTitleOpt.CODE)
public class LEUnCoopShopDealListTitleOpt extends DealFilterListTitleVP<LEUnCoopShopDealListTitleOpt.Config> {

    public static final String CODE = "LEUnCoopShopDealListTitleOpt";

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Config config) {
        if (config != null && config.isUseConfigTitle()) {
            return config.getTitle();
        }
        LeUncoopShopShelfAttrM leUncoopShopShelfAttrM = activityCxt.getParam(LeUnCoopShopUniverseInfoOpt.CODE);
        if (leUncoopShopShelfAttrM != null && StringUtils.isNotBlank(leUncoopShopShelfAttrM.getMainTitle())) {
            return leUncoopShopShelfAttrM.getMainTitle();
        }
        return StringUtils.EMPTY;
    }

    @Data
    @VPointCfg
    public static class Config {
        /**
         * 是否使用配置标题，默认不使用
         */
        private boolean useConfigTitle = false;
        /**
         * 标题
         */
        private String title;
    }
}
