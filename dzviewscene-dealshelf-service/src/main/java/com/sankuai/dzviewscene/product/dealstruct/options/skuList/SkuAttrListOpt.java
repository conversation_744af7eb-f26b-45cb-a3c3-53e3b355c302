package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.cat.Cat;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/12 5:29 下午
 */
@VPointOption(name = "sku属性列表变化点", description = "sku属性列表变化点，根据配置展示sku属性", code = SkuAttrListOpt.CODE)
public class SkuAttrListOpt extends SkuAttrListVP<SkuAttrListOpt.Config> {

    public static final String CODE = "SkuAttrListOpt";

    @Override
    public List<DealSkuItemVO> compute(ActivityCxt context, Param param, Config config) {
        if (config == null
                || param == null
                || CollectionUtils.isEmpty(config.skuAttrDisplayRules)
                || param.getSkuItemDto() == null) {
            return null;
        }
        return buildDealSkuItems(config.skuAttrDisplayRules, param.getSkuItemDto(), param.getDealAttrs());
    }

    private List<DealSkuItemVO> buildDealSkuItems(List<SkuAttrDisplayRuleCfg> skuAttrDisplayRules, SkuItemDto skuItemDto, List<AttrM> dealAttrs) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.dealstruct.options.skuList.SkuAttrListOpt.buildDealSkuItems(java.util.List,com.dianping.deal.struct.query.api.entity.dto.SkuItemDto,java.util.List)");
        if (CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return null;
        }
        List<DealSkuItemVO> dealSkuItemVOS = new ArrayList<>();
        for (SkuAttrDisplayRuleCfg skuAttrDisplayRule : skuAttrDisplayRules) {
            if (skuAttrDisplayRule.getRouteCondition() == null || CollectionUtils.isEmpty(skuAttrDisplayRule.getDisplayAttrRules())) {
                continue;
            }
            boolean satisfyAllAttrKeyValue = ProductMAttrUtils.satisfyAllAttrKeyValue(dealAttrs, skuAttrDisplayRule.getRouteCondition().getSatisfyAllAttrKeyValuesMap());
            boolean satisfyAllSkuAttrKeyValue = ProductMAttrUtils.satisfyAllSkuAttrKeyValue(skuItemDto, skuAttrDisplayRule.getRouteCondition().getSatisfySkuAllAttrKeyValuesMap());
            if (satisfyAllAttrKeyValue && satisfyAllSkuAttrKeyValue) {
                return buildDealSkuItemsByConfig(skuItemDto.getAttrItems(), skuAttrDisplayRule.getDisplayAttrRules());
            }
        }
        return dealSkuItemVOS;
    }

    private List<DealSkuItemVO> buildDealSkuItemsByConfig(List<SkuAttrItemDto> attrItems, List<DisplayAttrRuleCfg> displayAttrRules) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.dealstruct.options.skuList.SkuAttrListOpt.buildDealSkuItemsByConfig(java.util.List,java.util.List)");
        List<DealSkuItemVO> dealSkuItemVOS = new ArrayList<>();
        displayAttrRules.forEach(attrRule -> {
            DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
            dealSkuItemVO.setName(attrRule.getAttrTitle());
            String attrValue = getAttrValue(DealDetailUtils.getSkuAttrValueBySkuAttrName(attrItems, attrRule.getAttrKey()), attrRule.getAttrValueAliasMap());
            if (StringUtils.isEmpty(attrValue)) {
                return;
            }
            dealSkuItemVO.setValue(attrValue);
            dealSkuItemVOS.add(dealSkuItemVO);
        });
        return dealSkuItemVOS;
    }

    private String getAttrValue(String attrValue, Map<String, String> attrValueAliasMap) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.dealstruct.options.skuList.SkuAttrListOpt.getAttrValue(java.lang.String,java.util.Map)");
        //别名配置为空，或没有配置，则直接展示
        if (MapUtils.isEmpty(attrValueAliasMap) || !attrValueAliasMap.keySet().contains(attrValue)) {
            return attrValue;
        }
        return attrValueAliasMap.get(attrValue);
    }

    @Data
    @VPointCfg
    public static class Config {
        //sku属性构造模型列表
        private List<SkuAttrDisplayRuleCfg> skuAttrDisplayRules;
    }

    @Data
    private static class SkuAttrDisplayRuleCfg {
        //路由条件
        private RouteConditionCfg routeCondition;
        //sku属性规则配置
        private List<DisplayAttrRuleCfg> displayAttrRules;
    }

    @Data
    private static class DisplayAttrRuleCfg {
        //sku属性展示标题
        private String attrTitle;
        //属性key
        private String attrKey;
        //属性value与别名映射
        private Map<String, String> attrValueAliasMap;
    }

    @Data
    private static class RouteConditionCfg {
        //满足所有属性key及属性值的映射，当该字段为空时不需要校验
        private Map<String, List<String>> satisfyAllAttrKeyValuesMap;
        //满足服务项目Sku所有属性key及属性值的映射，当该字段为空时不需要校验
        private Map<String, List<String>> satisfySkuAllAttrKeyValuesMap;
    }
}
