package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.List;


@VPoint(name = "价格上方标签", description = "价格上方标签", code = ProductPriceAboveTagVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductPriceAboveTagVP<T> extends PmfVPoint<List<DzTagVO>, ProductPriceAboveTagVP.Param, T> {
    public static final String CODE = "ProductPriceAboveTagVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;
    }
}
