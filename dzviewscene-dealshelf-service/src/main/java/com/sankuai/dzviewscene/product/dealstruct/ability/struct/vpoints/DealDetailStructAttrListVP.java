package com.sankuai.dzviewscene.product.dealstruct.ability.struct.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.struct.DealDetailStructAttrListBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrsModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Builder;
import lombok.Data;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2021/11/25 2:41 下午
 */
@VPoint(name = "团购详情结构化属性卡片变化点", description = "团购详情结构化属性卡片变化点，支持组件名称和单位",code = DealDetailStructAttrListVP.CODE, ability = DealDetailStructAttrListBuilder.CODE)
public abstract class DealDetailStructAttrListVP<T> extends PmfVPoint<List<StructAttrsModel>, DealDetailStructAttrListVP.Param, T> {

    public static final String CODE = "DealDetailStructAttrListVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private List<AttrM> dealAttrs;
        private DealDetailDtoModel dealDetailDtoModel;
        private List<ProductSkuCategoryModel> productCategories;
    }
}


