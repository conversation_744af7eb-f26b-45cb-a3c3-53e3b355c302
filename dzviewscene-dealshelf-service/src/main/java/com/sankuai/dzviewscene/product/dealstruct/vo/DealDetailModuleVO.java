package com.sankuai.dzviewscene.product.dealstruct.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/30 10:56 上午
 */
@MobileDo(id = 0xb072)
public class DealDetailModuleVO implements Serializable {
    /**
     * 服务特色标准服务模块
     */
    @MobileDo.MobileField(key = 0x6ac)
    private StandardServiceVO standardServiceModel;
    /**
     * 视频组件
     */
    @MobileDo.MobileField(key = 0x15ff)
    private VideoModuleVO videoModel;
    /**
     * sku组表格组件
     */
    @MobileDo.MobileField(key = 0x68db)
    private SkusGroupsTableVO skuGroupsModel2;
    /**
     * 组件名
     */
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;
    /**
     * 组件前端展示样式
     */
    @MobileDo.MobileField(key = 0x8f0c)
    private String type;

    /**
     * 组件副标题
     */
    @MobileDo.MobileField(key = 0xd894)
    private String subTitle;


    /**
     * 补充说明组件
     */
    @MobileDo.MobileField(key = 0x2847)
    private String descModel;

    /**
     * 价格组件
     */
    @MobileDo.MobileField(key = 0x18a9)
    private DealDetailPriceModuleVO priceModel;

    /**
     * 标题组件
     */
    @MobileDo.MobileField(key = 0xafd)
    private String titleModel;

    /**
     * sku项目组列表组件
     */
    @MobileDo.MobileField(key = 0x68d8)
    private List<DealSkuGroupModuleVO> skuGroupsModel1;

    /**
     * 团单结构化属性信息组件
     */
    @MobileDo.MobileField(key = 0x8c93)
    private List<DealDetailStructAttrModuleVO> dealStructAttrsModel1;

    /**
     * 团单结构化属性信息组组件
     */
    @MobileDo.MobileField(key = 0x8c90)
    private DealDetailStructAttrGroupVO dealStructAttrsModel2;

    /**
     * 标题-弹窗-问号模型
     */
    @MobileDo.MobileField(key = 0xeb4a)
    private ExtraExplainVO extraExplain;

    /**
     * 最小显示几个，超过后会折叠
     */
    @MobileDo.MobileField(key = 0x6071)
    private int showNum;

    /**
     * 折叠文案
     */
    @MobileDo.MobileField(key = 0xf936)
    private String foldStr;

    /**
     * 列表形式，默认0，1代表原点，2代表数字
     */
    @MobileDo.MobileField(key = 0xd1c0)
    private int dotType;

    /**
     * 跳转链接
     */
    @MobileDo.MobileField(key = 0x774e)
    private JumpUrlVO jumpUrl;

    /**
     * 团详嵌套层
     */
    @MobileDo.MobileField(key = 0xf38e)
    private List<DealDetailModuleVO> dealDetailModuleList2;

    /**
     * 团详标题附加字段
     */
    @MobileDo.MobileField(key = 0xe229)
    private List<DealDetailStructAttrModuleVO> subTitleItems;

    public List<DealDetailStructAttrModuleVO> getSubTitleItems() {
        return subTitleItems;
    }

    public void setSubTitleItems(List<DealDetailStructAttrModuleVO> subTitleItems) {
        this.subTitleItems = subTitleItems;
    }

    public List<DealDetailModuleVO> getDealDetailModuleList2() {
        return dealDetailModuleList2;
    }

    public void setDealDetailModuleList2(List<DealDetailModuleVO> dealDetailModuleList2) {
        this.dealDetailModuleList2 = dealDetailModuleList2;
    }

    public StandardServiceVO getStandardServiceModel() {
        return standardServiceModel;
    }

    public void setStandardServiceModel(StandardServiceVO standardServiceModel) {
        this.standardServiceModel = standardServiceModel;
    }

    public void setVideoModel(VideoModuleVO videoModel) {
        this.videoModel = videoModel;
    }

    public VideoModuleVO getVideoModel() {
        return videoModel;
    }

    public void setVideoModuleVO(VideoModuleVO videoModel) {
        this.videoModel = videoModel;
    }

    public SkusGroupsTableVO getSkuGroupsModel2() {
        return skuGroupsModel2;
    }

    public void setSkuGroupsModel2(SkusGroupsTableVO skuGroupsModel2) {
        this.skuGroupsModel2 = skuGroupsModel2;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public DealDetailStructAttrGroupVO getDealStructAttrsModel2() {
        return dealStructAttrsModel2;
    }

    public void setDealStructAttrsModel2(
            DealDetailStructAttrGroupVO dealStructAttrsModel2) {
        this.dealStructAttrsModel2 = dealStructAttrsModel2;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDescModel() {
        return descModel;
    }

    public void setDescModel(String descModel) {
        this.descModel = descModel;
    }

    public DealDetailPriceModuleVO getPriceModel() {
        return priceModel;
    }

    public void setPriceModel(DealDetailPriceModuleVO priceModel) {
        this.priceModel = priceModel;
    }

    public String getTitleModel() {
        return titleModel;
    }

    public void setTitleModel(String titleModel) {
        this.titleModel = titleModel;
    }

    public List<DealSkuGroupModuleVO> getSkuGroupsModel1() {
        return skuGroupsModel1;
    }

    public void setSkuGroupsModel1(List<DealSkuGroupModuleVO> skuGroupsModel1) {
        this.skuGroupsModel1 = skuGroupsModel1;
    }

    public List<DealDetailStructAttrModuleVO> getDealStructAttrsModel1() {
        return dealStructAttrsModel1;
    }

    public void setDealStructAttrsModel1(
            List<DealDetailStructAttrModuleVO> dealStructAttrsModel1) {
        this.dealStructAttrsModel1 = dealStructAttrsModel1;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public ExtraExplainVO getExtraExplain() {
        return extraExplain;
    }

    public void setExtraExplain(ExtraExplainVO extraExplain) {
        this.extraExplain = extraExplain;
    }

    public JumpUrlVO getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(JumpUrlVO jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public int getShowNum() {
        return showNum;
    }

    public void setShowNum(int showNum) {
        this.showNum = showNum;
    }

    public int getDotType() {
        return dotType;
    }

    public void setDotType(int dotType) {
        this.dotType = dotType;
    }

    public String getFoldStr() {
        return foldStr;
    }

    public void setFoldStr(String foldStr) {
        this.foldStr = foldStr;
    }
}