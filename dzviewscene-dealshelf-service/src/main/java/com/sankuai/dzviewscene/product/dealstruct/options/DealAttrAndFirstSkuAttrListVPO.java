package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.struct.vpoints.DealDetailStructAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrItemModel;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrsModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/14 4:59 下午
 */
@VPointOption(name = "取团单属性和团单第一个sku属性作为团单结构化属性信息认变化点", description = "取团单属性和团单第一个sku属性作为团单结构化属性信息认变化点",code = DealAttrAndFirstSkuAttrListVPO.CODE, isDefault = false)
public class DealAttrAndFirstSkuAttrListVPO extends DealDetailStructAttrListVP<DealAttrAndFirstSkuAttrListVPO.Config> {

    public static final String CODE = "DealAttrAndFirstSkuAttrListVPO";

    private static final String SEPERATOR = "、";

    @Override
    public List<StructAttrsModel> compute(ActivityCxt context, Param param, Config config) {
        if (config == null || CollectionUtils.isEmpty(config.getAttrListModels()) || param == null) {
            return null;
        }
        List<AttrM> dealAttrs = param.getDealAttrs();
        List<SkuAttrItemDto> skuAttrItems = extractFirstSkuAttrFromDealDetailDtoModel(param.getDealDetailDtoModel());
        return config.getAttrListModels().stream().map(model -> convertAttrListModel2StructAttrsModel(model, dealAttrs, skuAttrItems)).filter(model -> model != null).collect(Collectors.toList());
    }

    private List<SkuAttrItemDto> extractFirstSkuAttrFromDealDetailDtoModel(DealDetailDtoModel dealDetailDtoModel) {
        if (dealDetailDtoModel == null || dealDetailDtoModel.getSkuUniStructuredDto() == null || CollectionUtils.isEmpty(dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups())) {
            return null;
        }
        List<MustSkuItemsGroupDto> mustSkuItemsGroupDtos = dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups();
        MustSkuItemsGroupDto mustSkuItemsGroupDto = CollectUtils.firstValue(mustSkuItemsGroupDtos);
        if (mustSkuItemsGroupDto == null || CollectionUtils.isEmpty(mustSkuItemsGroupDto.getSkuItems())) {
            return null;
        }
        SkuItemDto skuItemDto = CollectUtils.firstValue(mustSkuItemsGroupDto.getSkuItems());
        if (skuItemDto == null) {
            return null;
        }
        return skuItemDto.getAttrItems();
    }

    private StructAttrsModel convertAttrListModel2StructAttrsModel(AttrListModel attrListModel, List<AttrM> dealAttrs, List<SkuAttrItemDto> skuAttrItems) {
        if (attrListModel == null) {
            return null;
        }
        List<StructAttrItemModel> structAttrModels = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attrListModel.getAttrModels()) && CollectionUtils.isNotEmpty(dealAttrs)) {
            addStructAttrItemModelsFromDealAttrs(structAttrModels, attrListModel.getAttrModels(), dealAttrs);
        }
        if (CollectionUtils.isNotEmpty(attrListModel.getSkuAttrModels()) && CollectionUtils.isNotEmpty(skuAttrItems)) {
            addStructAttrItemModelsFromSkuAttrs(structAttrModels, attrListModel.getSkuAttrModels(), skuAttrItems);
        }
        if (MapUtils.isEmpty(attrListModel.getAttrTitle2PriorityMap())) {
            return buildStructAttrsModel(structAttrModels);
        }
        structAttrModels = sortAttrItemList(structAttrModels, attrListModel.getAttrTitle2PriorityMap());
        return buildStructAttrsModel(structAttrModels);
    }

    private List<StructAttrItemModel> sortAttrItemList(List<StructAttrItemModel> structAttrModels, Map<String, Integer> attrTitle2PriorityMap) {
        if (CollectionUtils.isEmpty(structAttrModels) || MapUtils.isEmpty(attrTitle2PriorityMap)) {
            return structAttrModels;
        }
        return structAttrModels.stream().sorted(Comparator.comparingInt(o -> getPriority(o.getAttrName(), attrTitle2PriorityMap))).collect(Collectors.toList());
    }

    private int getPriority(String attrTitle, Map<String, Integer> attrTitle2PriorityMap) {
        if (MapUtils.isEmpty(attrTitle2PriorityMap) || StringUtils.isEmpty(attrTitle) || !attrTitle2PriorityMap.containsKey(attrTitle)) {
            return Integer.MAX_VALUE;
        }
        return attrTitle2PriorityMap.get(attrTitle);
    }

    private StructAttrsModel buildStructAttrsModel(List<StructAttrItemModel> structAttrModels) {
        if (CollectionUtils.isEmpty(structAttrModels)) {
            return null;
        }
        StructAttrsModel structAttrsModel = new StructAttrsModel();
        structAttrsModel.setStructAttrModels(structAttrModels);
        return structAttrsModel;
    }

    private void addStructAttrItemModelsFromSkuAttrs(List<StructAttrItemModel> structAttrModels, List<AttrModel> attrModels, List<SkuAttrItemDto> skuAttrItems) {
        if (CollectionUtils.isEmpty(attrModels) || CollectionUtils.isEmpty(skuAttrItems) || structAttrModels == null) {
            return;
        }
        List<StructAttrItemModel> structAttrItemModels = attrModels.stream().map(model -> convertSkuAttr2StructAttrItemModel(model, skuAttrItems)).filter(item -> item != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(structAttrItemModels)) {
            return;
        }
        structAttrModels.addAll(structAttrItemModels);
    }

    private StructAttrItemModel convertSkuAttr2StructAttrItemModel(AttrModel dealAttrModel, List<SkuAttrItemDto> skuAttrItems) {
        if (dealAttrModel == null || CollectionUtils.isEmpty(skuAttrItems) || StringUtils.isEmpty(dealAttrModel.getAttrName())) {
            return null;
        }
        SkuAttrItemDto skuAttrItemDto = skuAttrItems.stream().filter(skuAttr -> dealAttrModel.getAttrName().equals(skuAttr.getAttrName())).findFirst().orElse(null);
        if (skuAttrItemDto == null || StringUtils.isEmpty(skuAttrItemDto.getAttrValue())) {
            return null;
        }
        List<String> skuAttrValues = Lists.newArrayList(skuAttrItemDto.getAttrValue().split(SEPERATOR));
        return buildStructAttrItemModel(dealAttrModel.getAttrTitle(), skuAttrValues, dealAttrModel.getAttrIcon());
    }

    private void addStructAttrItemModelsFromDealAttrs(List<StructAttrItemModel> structAttrModels, List<AttrModel> attrModels, List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(attrModels) || CollectionUtils.isEmpty(dealAttrs) || structAttrModels == null) {
            return;
        }
        List<StructAttrItemModel> structAttrItemModels = attrModels.stream().map(model -> convertDealAttr2StructAttrItemModel(model, dealAttrs)).filter(item -> item != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(structAttrItemModels)) {
            return;
        }
        structAttrModels.addAll(structAttrItemModels);
    }

    private StructAttrItemModel convertDealAttr2StructAttrItemModel(AttrModel dealAttrModel, List<AttrM> dealAttrs) {
        if (dealAttrModel == null || CollectionUtils.isEmpty(dealAttrs) || StringUtils.isEmpty(dealAttrModel.getAttrName())) {
            return null;
        }
        AttrM attrM = dealAttrs.stream().filter(dealAttr -> dealAttrModel.getAttrName().equals(dealAttr.getName())).findFirst().orElse(null);
        if (attrM == null || StringUtils.isEmpty(attrM.getValue())) {
            return null;
        }
        return buildStructAttrItemModel(dealAttrModel.getAttrTitle(), Lists.newArrayList(attrM.getValue()), dealAttrModel.getAttrIcon());
    }

    private StructAttrItemModel buildStructAttrItemModel(String attrTitle,  List<String> attrValues, String icon) {
        StructAttrItemModel structAttrItemModel = new StructAttrItemModel();
        structAttrItemModel.setAttrName(attrTitle);
        structAttrItemModel.setAttrValues(attrValues);
        structAttrItemModel.setIcon(icon);
        return structAttrItemModel;
    }



    @Data
    @VPointCfg
    public static class Config {
        private List<AttrListModel> attrListModels;
    }


    @Data
    public static class AttrListModel {
        private List<AttrModel> attrModels;
        private List<AttrModel> skuAttrModels;
        private Map<String, Integer> attrTitle2PriorityMap;
    }

    @Data
    public static class AttrModel {
        private String attrName;
        private String attrTitle;
        private String attrIcon;
    }
}
