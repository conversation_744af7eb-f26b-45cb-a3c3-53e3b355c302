package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler;

import com.dianping.cat.Cat;
import com.dianping.gm.marketing.times.card.api.dto.TimesCardDetailExposureDTO;
import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.TimeCardThemeHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.merchantcard.timescard.exposure.eum.ChannelSourceEnum;
import com.sankuai.merchantcard.timescard.exposure.eum.SceneTypeEnum;
import com.sankuai.merchantcard.timescard.exposure.req.QueryTimesCardByShopRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024-07-09-15:13
 */

@Component
public class PoiTimesCardQueryHandler implements QueryHandler {

    @Resource
    private CompositeAtomService compositeAtomService;

    @Resource
    private TimeCardThemeHandler timeCardThemeHandler;

    @Override
    public CompletableFuture<ProductGroupM> query(ActivityCxt ctx, String groupName, Map<String, Object> groupParams) {
        int platform = ctx.getParam(ShelfActivityConstants.Params.platform);
        long shopId = PlatformUtil.isMT(platform) ? ParamsUtil.getLongSafely(ctx, ShelfActivityConstants.Params.mtPoiIdL)
                : ParamsUtil.getLongSafely(ctx, ShelfActivityConstants.Params.dpPoiIdL);
        return compositeAtomService.batchQueryTimesCardsByShop(buildRequest(ctx, shopId, platform)).thenApply(response -> {

            if (response == null || MapUtils.isEmpty(response.getData()) || !response.getData().containsKey(shopId)) {
                return new ProductGroupM();
            }
            ProductGroupM productGroupM = new ProductGroupM();
            productGroupM.setProducts(buildProducts(response.getData().get(shopId), ctx, groupParams));
            productGroupM.setTotal(productGroupM.getProducts().size());
            return productGroupM;
        });
    }

    private QueryTimesCardByShopRequest buildRequest(ActivityCxt ctx, long shopId, int platform) {

        QueryTimesCardByShopRequest request = new QueryTimesCardByShopRequest();
        long userId = PlatformUtil.isMT(platform) ? Optional.ofNullable((Long) ctx.getParam(ShelfActivityConstants.Params.mtUserId)).orElse(0L)
                : Optional.ofNullable((Long) ctx.getParam(ShelfActivityConstants.Params.dpUserId)).orElse(0L);

        request.setScene(SceneTypeEnum.MERGED_SHELF.getCode());
        request.setPlatform(PlatformUtil.isMT(platform) ? VCPlatformEnum.MT.getType() : VCPlatformEnum.DP.getType());
        request.setLongShopIdList(Lists.newArrayList(shopId));
        request.setUserId(userId);
        request.setChannelSourceList(Lists.newArrayList(ChannelSourceEnum.MERCHANT_NORMAL.getSourceType()));
        return request;
    }

    private List<ProductM> buildProducts(List<TimesCardDetailExposureDTO> timesCardDetailList, ActivityCxt ctx, Map<String, Object> groupParams) {

        if (CollectionUtils.isEmpty(timesCardDetailList)) {
            return Lists.newArrayList();
        }
        ActivityContext activityContext = ActivityCtxtUtils.toActivityContext(ctx);

        return timesCardDetailList.stream().map(timesCardDetailExposureDTO -> {
            ProductM productM = new ProductM();
            productM.setProductId(timesCardDetailExposureDTO.getProductId());
            productM.setProductType(ProductTypeEnum.TIME_CARD.getType());
            // 同后续Padding保持一致
            timeCardThemeHandler.paddingProductM(activityContext, productM, timesCardDetailExposureDTO, groupParams);
            productM.setPrePadded(true);
            return productM;
        }).collect(Collectors.toList());
    }
}
