package com.sankuai.dzviewscene.product.filterlist.option.builder.product.sale;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePriceDescVP;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/28
 */
@VPointOption(
        name = "价格描述标签",
        description = "价格描述标签",
        code = "ConfigProductSalePriceDescOpt")
public class ConfigProductSalePriceDescOpt extends ProductSalePriceDescVP<ConfigProductSalePriceDescOpt.Config> {

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        return config.getDefalutDesc();
    }

    @VPointCfg
    @Data
    public static class Config {
        private String defalutDesc = "券后";
    }

}
