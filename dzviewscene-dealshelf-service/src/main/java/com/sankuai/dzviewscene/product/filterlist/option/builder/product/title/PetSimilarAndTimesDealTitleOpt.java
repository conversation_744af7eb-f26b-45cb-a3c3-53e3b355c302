package com.sankuai.dzviewscene.product.filterlist.option.builder.product.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTitleVP;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@VPointOption(name = "宠物相似团购和多次卡团单名称展示",
        description = "宠物相似团购和多次卡团单名称展示 例如：3次 单次￥110",
        code = "PetSimilarAndTimesDealTitleOpt")
public class PetSimilarAndTimesDealTitleOpt extends ProductTitleVP<PetSimilarAndTimesDealTitleOpt.Config> {

    @Override
    public String compute(ActivityCxt context, Param param, PetSimilarAndTimesDealTitleOpt.Config config) {
        // 判断交易类型是团购次卡
        if (!param.getProductM().isTimesDeal()) {
            return param.getProductM().getTitle();
        }
        // 获取多次卡的次数
        String times = param.getProductM().getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER);
        String singlePrice = TimesDealUtil.getSinglePrice(param.getProductM().getSalePrice(), times);
        if (StringUtils.isBlank(config.getTitleTemplate()) || StringUtils.isBlank(singlePrice)) {
            return param.getProductM().getTitle();
        }
        return String.format(config.getTitleTemplate(), times, singlePrice);
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 返回描述的模板
         */
        private String titleTemplate = "%s次 单次¥%s";

    }

}
