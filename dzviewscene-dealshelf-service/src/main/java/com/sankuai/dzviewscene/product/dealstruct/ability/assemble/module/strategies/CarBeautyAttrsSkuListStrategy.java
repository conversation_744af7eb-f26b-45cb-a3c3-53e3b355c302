package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.*;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/15 8:49 下午
 */
@Component("carBeautyAttrsSkuListStrategy")
public class CarBeautyAttrsSkuListStrategy implements ModuleStrategy {

    private static final int CAR_BEAUTY_COLOR_POPUP_ATTR_TYEP = 4;

    private static final int SKU_PROCESS_ATTR_TYPE = 2;

    private static final String SEPERATOR = "、";

    private static final String SKU_NAME = "服务信息";

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        //1. 获取需要的依赖数据
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        //1.1 依赖数据 - 团单属性
        List<AttrM>  dealAttrs = getDealAttrs(dealDetailInfoModels);
        //1.2 依赖数据 - 货属性
        List<SkuAttrItemDto> skuAttrs = getSkuAttrItems(dealDetailInfoModels);
        //2. 组装DealSkuVO
        DealSkuVO dealSkuVO = getDealSkuVO(dealAttrs, skuAttrs, config);
        //3. 组装为结果VO
        return buildDealDetailModuleVO(dealSkuVO);
    }

    private DealDetailModuleVO buildDealDetailModuleVO(DealSkuVO dealSkuVO) {
        if (dealSkuVO == null) {
            return null;
        }
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(Lists.newArrayList(dealSkuVO));
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        dealDetailModuleVO.setSkuGroupsModel1(Lists.newArrayList(dealSkuGroupModuleVO));
        return dealDetailModuleVO;
    }

    private DealSkuVO getDealSkuVO(List<AttrM> dealAttrs, List<SkuAttrItemDto> skuAttrs, String config) {
        List<DealSkuItemVO> dealSkuItemVOS = convertDealAttrsAndSkuAttrs2DealSkuVOList(dealAttrs, skuAttrs, config);
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(SKU_NAME);
        dealSkuVO.setItems(dealSkuItemVOS);
        return dealSkuVO;
    }

    private List<DealSkuItemVO> convertDealAttrsAndSkuAttrs2DealSkuVOList(List<AttrM> dealAttrs, List<SkuAttrItemDto> skuAttrs, String config) {
        AttrListModel attrListModel = getAttrListConfig(config);
        if (attrListModel == null) {
            return null;
        }
        List<DealSkuItemVO> dealSkuItemVOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attrListModel.getSkuAttrModels())) {
            addDealSkuItemVOsFromSkuAttrs(attrListModel.getSkuAttrModels(), skuAttrs, dealSkuItemVOS);
        }
        if (CollectionUtils.isNotEmpty(attrListModel.getAttrModels()) && CollectionUtils.isNotEmpty(dealAttrs)) {
            addDealSkuItemVOsFromDealAttrs(attrListModel.getAttrModels(), dealAttrs, dealSkuItemVOS);
        }
        if (CollectionUtils.isEmpty(dealSkuItemVOS)) {
            return null;
        }
        if (MapUtils.isEmpty(attrListModel.getAttrTitle2PriorityMap())) {
            return dealSkuItemVOS;
        }
        return getSortedDealSkuItemVos(dealSkuItemVOS, attrListModel.getAttrTitle2PriorityMap());
    }

    private List<DealSkuItemVO> getSortedDealSkuItemVos(List<DealSkuItemVO> dealSkuItemVOS, Map<String, Integer> attrTitle2PriorityMap) {
        if (CollectionUtils.isEmpty(dealSkuItemVOS) || MapUtils.isEmpty(attrTitle2PriorityMap)) {
            return dealSkuItemVOS;
        }
        return dealSkuItemVOS.stream().sorted(Comparator.comparingInt(o -> getPriority(o.getName(), attrTitle2PriorityMap))).collect(Collectors.toList());
    }

    private int getPriority(String attrTitle, Map<String, Integer> attrTitle2PriorityMap) {
        if (MapUtils.isEmpty(attrTitle2PriorityMap) || StringUtils.isEmpty(attrTitle) || !attrTitle2PriorityMap.containsKey(attrTitle)) {
            return Integer.MAX_VALUE;
        }
        return attrTitle2PriorityMap.get(attrTitle);
    }

    private void addDealSkuItemVOsFromDealAttrs(List<AttrModel> attrModels, List<AttrM> dealAttrs, List<DealSkuItemVO> dealSkuItemVOList) {
        List<DealSkuItemVO> dealAttrsDealSkuItemVOS = convertDealAttrs2DealSkuVOList(attrModels, dealAttrs);
        addDealSkuItemVOs(dealSkuItemVOList, dealAttrsDealSkuItemVOS);
    }

    private void addDealSkuItemVOsFromSkuAttrs(List<AttrModel> attrModels, List<SkuAttrItemDto> skuAttrs, List<DealSkuItemVO> dealSkuItemVOList) {
        List<DealSkuItemVO> dealAttrsDealSkuItemVOS = convertSkuAttrs2DealSkuVOList(attrModels, skuAttrs);
        addDealSkuItemVOs(dealSkuItemVOList, dealAttrsDealSkuItemVOS);
    }

    private void addDealSkuItemVOs(List<DealSkuItemVO> dealSkuItemVOList, List<DealSkuItemVO> dealSkuItemVOS) {
        if (dealSkuItemVOList == null || CollectionUtils.isEmpty(dealSkuItemVOS)) {
            return;
        }
        dealSkuItemVOList.addAll(dealSkuItemVOS);
    }

    private List<DealSkuItemVO> convertSkuAttrs2DealSkuVOList(List<AttrModel> attrModels, List<SkuAttrItemDto> skuAttrs) {
        if (CollectionUtils.isEmpty(attrModels) || CollectionUtils.isEmpty(skuAttrs)) {
            return null;
        }
        return attrModels.stream().map(model -> getSkuAttrDealSkuItemVO(model, skuAttrs)).filter(vo -> vo != null).collect(Collectors.toList());
    }

    private DealSkuItemVO getSkuAttrDealSkuItemVO(AttrModel attrModel, List<SkuAttrItemDto> skuAttrs) {
        if (CollectionUtils.isEmpty(skuAttrs) || attrModel == null || StringUtils.isEmpty(attrModel.getAttrName())) {
            return null;
        }
        String attrValue = getAttrValue(attrModel, skuAttrs, null);
        if (attrModel.getType() == SKU_PROCESS_ATTR_TYPE) {
            return buildProcessDealSkuItemVO(attrValue, attrModel);
        }
        return buildValueDealSkuItemVO(attrValue, attrModel);
    }

    private String getAttrValue(AttrModel attrModel, List<SkuAttrItemDto> skuAttrs, List<AttrM> dealAttrs) {
        if (attrModel == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(skuAttrs) && CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        if (attrModel.getAttrValueModel() != null && CollectionUtils.isNotEmpty(attrModel.getAttrValueModel().getAttrItemModels())) {
            return getAttrValueByAttrValueModel(attrModel.getAttrValueModel(), skuAttrs, dealAttrs);
        }
        return getAttrValueByAttrName(attrModel.getAttrName(), skuAttrs, dealAttrs);
    }

    private String getAttrValueByAttrName(String attrName, List<SkuAttrItemDto> skuAttrs, List<AttrM> dealAttrs) {
        if (StringUtils.isEmpty(attrName)) {
            return null;
        }
        if (CollectionUtils.isEmpty(skuAttrs) && CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        if (CollectionUtils.isNotEmpty(dealAttrs)) {
            return DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, attrName);
        }
        return DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, attrName);
    }

    private String getAttrValueByAttrValueModel(AttrValueModel attrValueModel, List<SkuAttrItemDto> skuAttrs, List<AttrM> dealAttrs) {
        if (attrValueModel == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(skuAttrs) && CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        List<String> values = attrValueModel.getAttrItemModels().stream().map(item -> getAttrValue(item, skuAttrs, dealAttrs)).filter(value -> value != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(values)) {
            return null;
        }
        return StringUtils.join(values, attrValueModel.getJoinStr());
    }

    private String getAttrValue(AttrItemModel attrItemModel, List<SkuAttrItemDto> skuAttrs, List<AttrM> dealAttrs) {
        if (attrItemModel == null) {
            return null;
        }
        String value = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrs, attrItemModel.getAttrName());
        if (CollectionUtils.isNotEmpty(dealAttrs)) {
            value = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, attrItemModel.getAttrName());
        }
        if (StringUtils.isEmpty(attrItemModel.getFormat()) || value == null) {
            return value;
        }
        return String.format(attrItemModel.getFormat(), value);
    }

    private List<DealSkuItemVO> convertDealAttrs2DealSkuVOList(List<AttrModel> attrModels, List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(attrModels) || CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        return attrModels.stream().map(model -> getDealAttrDealSkuItemVO(model, dealAttrs)).filter(vo -> vo != null).collect(Collectors.toList());
    }

    private DealSkuItemVO getDealAttrDealSkuItemVO(AttrModel attrModel, List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs) || attrModel == null) {
            return null;
        }
        String attrValue = getAttrValue(attrModel, null, dealAttrs);
        if (attrModel.getType() == SKU_PROCESS_ATTR_TYPE) {
            return buildProcessDealSkuItemVO(attrValue, attrModel);
        }
        if (attrModel.getType() == CAR_BEAUTY_COLOR_POPUP_ATTR_TYEP) {
            return buildColorListDealSkuItemVO(attrValue, attrModel);
        }
        return buildValueDealSkuItemVO(attrValue, attrModel);
    }

    private DealSkuItemVO buildColorListDealSkuItemVO(String attrValue, AttrModel attrModel) {
        if (StringUtils.isEmpty(attrValue) || attrModel == null) {
            return null;
        }
        List<FilmColorModel> filmColorModels = JsonCodec.converseList(attrValue, FilmColorModel.class);
        if (CollectionUtils.isEmpty(filmColorModels)) {
            return null;
        }
        List<PicItemVO> picItemVOS = filmColorModels.stream().map(model -> convertFilmColorModel2PicItemVO(model)).filter(vo -> vo != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(picItemVOS)) {
            return null;
        }
        return buildDealSkuItemVO(attrModel.getAttrTitle(), attrModel.getType(), null, null, picItemVOS);
    }

    private PicItemVO convertFilmColorModel2PicItemVO(FilmColorModel model) {
        if (model == null) {
            return null;
        }
        PicItemVO picItemVO = new PicItemVO();
        picItemVO.setTitle(model.getText());
        picItemVO.setUrl(model.getImgUrl());
        return picItemVO;
    }

    private DealSkuItemVO buildValueDealSkuItemVO(String attrValue, AttrModel attrModel) {
        if (StringUtils.isEmpty(attrValue) || attrModel == null) {
            return null;
        }
        if (StringUtils.isNotEmpty(attrModel.getFormat())) {
            attrValue = String.format(attrModel.getFormat(), attrValue);
        }
        return buildDealSkuItemVO(attrModel.getAttrTitle(), attrModel.getType(), attrValue, null, null );
    }

    private DealSkuItemVO buildProcessDealSkuItemVO(String attrValue, AttrModel attrModel) {
        if (StringUtils.isEmpty(attrValue) || attrModel == null) {
            return null;
        }
        List<String> attrValues = Lists.newArrayList(attrValue.split(SEPERATOR));
        attrValues = getSortedProcess(attrValues);
        if (CollectionUtils.isEmpty(attrValues)) {
            return null;
        }
        List<SkuAttrAttrItemVO> skuAttrAttrItemVOS = attrValues.stream().map(attr -> buildSkuAttrAttrItemVO(attr)).filter(attr -> attr != null).collect(Collectors.toList());
        return buildDealSkuItemVO(attrModel.getAttrTitle(), attrModel.getType(), null, skuAttrAttrItemVOS, null );
    }

    /**
     * 对服务流程进行排序：产品要求展示顺序为："整车外表清洁 > 外观车况检查 > 专车专用裁膜 > 施工贴膜 > 自定义"，上单页面的提交顺序不满足该要求，因此C端进行兼容
     *@param
     *@return
     */
    private List<String> getSortedProcess(List<String> process) {
        if (CollectionUtils.isEmpty(process)) {
            return null;
        }
        List<String> sortList = Lists.newArrayList("整车外表清洁", "外观车况检查", "专车专用裁膜", "施工贴膜");
        //将自定义的流程步骤依次加到顺序列表的最后，以便将自定义步骤排序到最后
        for(String processItem : process) {
            if (!sortList.contains(processItem)) {
                sortList.add(processItem);
            }
        }
        return process.stream().sorted(Comparator.comparingInt(sortList::indexOf)).collect(Collectors.toList());
    }

    private DealSkuItemVO buildDealSkuItemVO(String title, int type, String value, List<SkuAttrAttrItemVO> valueAttrs, List<PicItemVO> picValues) {
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(title);
        dealSkuItemVO.setValue(value);
        dealSkuItemVO.setType(type);
        dealSkuItemVO.setValueAttrs(valueAttrs);
        dealSkuItemVO.setPicValues(picValues);
        return dealSkuItemVO;
    }

    private SkuAttrAttrItemVO buildSkuAttrAttrItemVO(String name) {
        SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
        skuAttrAttrItemVO.setName(name);
        return skuAttrAttrItemVO;
    }

    private AttrListModel getAttrListConfig(String config) {
        if (StringUtils.isEmpty(config)) {
            return null;
        }
        AttrListModel attrListModel = JsonCodec.decode(config, AttrListModel.class);
        return attrListModel;
    }

    private List<AttrM> getDealAttrs(List<DealDetailInfoModel> dealDetailInfoModels) {
        DealDetailInfoModel dealDetailInfoModel = getDealDetailInfoModel(dealDetailInfoModels);
        if (dealDetailInfoModel == null) {
            return null;
        }
        return dealDetailInfoModel.getDealAttrs();
    }

    private DealDetailInfoModel getDealDetailInfoModel(List<DealDetailInfoModel> dealDetailInfoModels) {
        if (CollectionUtils.isEmpty(dealDetailInfoModels)) {
            return null;
        }
        return CollectUtils.firstValue(dealDetailInfoModels);
    }

    private List<SkuAttrItemDto> getSkuAttrItems(List<DealDetailInfoModel> dealDetailInfoModels) {
        DealDetailInfoModel dealDetailInfoModel = getDealDetailInfoModel(dealDetailInfoModels);
        if (dealDetailInfoModel == null) {
            return null;
        }
        DealDetailDtoModel dealDetailDtoModel = dealDetailInfoModel.getDealDetailDtoModel();
        if (dealDetailDtoModel == null || dealDetailDtoModel.getSkuUniStructuredDto() == null) {
            return null;
        }
        MustSkuItemsGroupDto firstMustSkuItemsGroupDto = CollectUtils.firstValue(dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups());
        if (firstMustSkuItemsGroupDto == null) {
            return null;
        }
        SkuItemDto firstSuItemDto = CollectUtils.firstValue(firstMustSkuItemsGroupDto.getSkuItems());
        if (firstSuItemDto == null) {
            return null;
        }
        return firstSuItemDto.getAttrItems();
    }

    @Data
    private static class FilmColorModel{
        private String imgUrl;
        private String text;
    }

    @Data
    public static class AttrListModel {
        private List<AttrModel> attrModels;
        private List<AttrModel> skuAttrModels;
        private Map<String, Integer> attrTitle2PriorityMap;
    }

    @Data
    public static class AttrModel {
        private String attrName;
        private String attrTitle;
        private String attrIcon;
        private int type;
        private String format;
        private AttrValueModel attrValueModel;
    }

    @Data
    public static class AttrValueModel {
        private List<AttrItemModel> attrItemModels;
        private String joinStr;
    }

    @Data
    public static class AttrItemModel {
        private String attrName;
        private String format;
    }

}
