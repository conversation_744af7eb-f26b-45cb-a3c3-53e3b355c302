package com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.DealListResponseAssembler;
import lombok.Builder;
import lombok.Data;

/**
 * @author: wuwen<PERSON><PERSON>
 * @create: 2024-06-18
 * @description:
 */
@VPoint(name = "默认展示数量", description = "默认展示数量", code = DealFilterListDefaultShowNumVP.CODE, ability = DealListResponseAssembler.CODE)
public abstract class DealFilterListDefaultShowNumVP<T> extends PmfVPoint<Integer, DealFilterListDefaultShowNumVP.Param, T> {

    public static final String CODE = "DealFilterListDefaultShowNumVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private int defaultShowNum;
    }
}
