package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor;

import lombok.Getter;

@Getter
public enum ValueProcessorEnum {
    NORMAL(1, "常规处理"),
    MAPPING(2, "映射"),
    REGEX(3, "正则"),
    LINKED(4, "关联"),

    ORIGINAL(5, "原始值"),

    SUB_PROJECT_NUM(6,"子服务项目数量"),

    ARRAY(7,"数组处理"),
    SUM_PRICE(8, "价格为子项目value总和");


    private final Integer code;
    private final String desc;

    ValueProcessorEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
