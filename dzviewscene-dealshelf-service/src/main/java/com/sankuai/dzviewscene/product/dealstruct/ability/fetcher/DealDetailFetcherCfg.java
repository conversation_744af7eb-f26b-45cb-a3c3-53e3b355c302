package com.sankuai.dzviewscene.product.dealstruct.ability.fetcher;

import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * created by zhangzhiyuan04 in 2021/12/8
 */
@AbilityCfg
@Data
public class DealDetailFetcherCfg {

    private String planId;

    private List<String> attributeKeys;

    private List<String> dealAttributeKeys;

    private Map<String, String> params;

    private RelatedContentConfig relatedContentConfig;

    /**
     * 请求查询中心的Operator类型
     */
    private List<Integer> bpDealGroupTypeList;

    /**
     * 餐食是否为新数据的设值
     */
    private boolean foodUseNewDataSetValue;

    private List<String> additionalAttrKeys;

    private int combineType;
}
