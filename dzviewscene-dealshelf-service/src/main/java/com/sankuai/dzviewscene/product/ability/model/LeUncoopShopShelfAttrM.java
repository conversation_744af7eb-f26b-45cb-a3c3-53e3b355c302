package com.sankuai.dzviewscene.product.ability.model;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;

import java.util.List;

/**
 * @author: wu<PERSON><PERSON><PERSON>
 * @create: 2024-06-12
 * @description: LE非合作商户货架属性
 */
@Data
public class LeUncoopShopShelfAttrM {
    /**
     * 标题组文案
     */
    private String mainTitle;
    /**
     * 堆头主标题
     */
    private String recommendTopTitle;
    /**
     * 默认展示数量
     */
    private Integer defaultShowNum;
    /**
     * 最多展示数量
     */
    private Integer maxShowNum;
    /**
     * 更多展示文案
     */
    private String moreShowText;
    /**
     * 倾斜流量召回距离(m)
     */
    private Integer slopeCoverDistance;
    /**
     * 自然流量第一优先级召回距离(m)
     */
    private Integer naturalFirstCoverDistance;
    /**
     * 自然流量第二优先级召回距离(m)
     */
    private Integer naturalSecondCoverDistance;
    /**
     * 置顶商品列表（商品ID和商品类型）
     */
    private List<ProductM> topProducts;
}
