package com.sankuai.dzviewscene.product.dealstruct.ability.dealstyle;

import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/8/24 3:42 下午
 */
@AbilityCfg
@Data
public class DealStyleSwitchCfg {
    private List<String> barExpId;

    private boolean returnCustom = false;

    private MedicalHealthCondition medicalHealthCondition;

    /**
     * 走自定义样式的serviceType列表
     */
    private List<String> customStyleServiceTypes;

    /**
     * 小程序是否展示自定义样式
     */
    private boolean miniProgramShowCustomStyle;

    private Map<String, String> serviceTypeStyleMap;

    @Data
    public static class MedicalHealthCondition {
        public boolean openMatch;
        //orItems任一满足 && andItems全部满足
        public List<ConditionItem> orItems;
        public List<ConditionItem> andItems;
    }

    @Data
    public static class ConditionItem implements Serializable {
        private Map<String,List<String>> matchDealAttrValue;
        private List<String> hasDealAttr;
        private List<String> hasSkuAttr;
    }
}
