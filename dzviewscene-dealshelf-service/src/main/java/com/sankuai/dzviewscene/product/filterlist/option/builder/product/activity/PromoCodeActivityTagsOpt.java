package com.sankuai.dzviewscene.product.filterlist.option.builder.product.activity;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductActivityTagsVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzActivityTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.TagM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;

/**
 * @description : 用于优惠码的商品货架的标签处理，支持安心学团单
 * @date : 2025/1/22
 */
@VPointOption(name = "优惠码货架商品标签处理类",
        description = "优惠码货架商品标签处理类，用于展示对应的浮层标签，目前支持安心学团单标题前缀标签展示",
        code = "PromoCodeActivityTagsOpt")
public class PromoCodeActivityTagsOpt extends ProductActivityTagsVP<PromoCodeActivityTagsOpt.Config> {

    @Override
    public List<DzActivityTagVO> compute(ActivityCxt activityCxt, Param param, Config config) {
        ProductM productM = param.getProductM();
        if (productM == null || MapUtils.isEmpty(config.getProductTagId2TagCfg()) || CollectionUtils.isEmpty(productM.getProductTagList())) {
            return null;
        }
        TagCfg tagCfg = findFirstMatchTagCfg(config.getProductTagId2TagCfg(), productM.getProductTagList());
        return buildFloatTag(tagCfg);
    }

    private TagCfg findFirstMatchTagCfg(Map<String, TagCfg> productTagId2TagCfg, List<TagM> tags) {
        return tags.stream().filter(tag -> productTagId2TagCfg.containsKey(tag.getId()))
                .map(tag -> productTagId2TagCfg.get(tag.getId())).findFirst().orElse(null);
    }

    private List<DzActivityTagVO> buildFloatTag(TagCfg tagCfg) {
        if (tagCfg == null) {
            return null;
        }
        DzActivityTagVO tagVO = new DzActivityTagVO();
        tagVO.setImgUrl(tagCfg.getPicUrl());
        tagVO.setLabel(tagCfg.getLabel());
        tagVO.setBackgroundImg(tagCfg.getBackgroundImg());
        tagVO.setPosition(tagCfg.getPosition() != null ? tagCfg.getPosition() : 1);// 默认设置在左上角
        return Lists.newArrayList(tagVO);
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 商品标签id对应的标题前标签策略
         */
        private Map<String, PromoCodeActivityTagsOpt.TagCfg> productTagId2TagCfg;
    }

    @Data
    public static class TagCfg {
        /**
         * 图片链接
         */
        private String picUrl;
        /**
         * 标签
         */
        private String label;

        /**
         * 背景图，仅label才有效
         */
        private String backgroundImg;
        /**
         * 位置：位置（1左上单个，2右上单个，3右下单个，4左下单个，5底部平铺）
         */
        private Integer position;
    }
}
