package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.struct.DealDetailStructAttrListBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrsModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrGroupVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import jodd.util.StringUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import java.util.List;


@Component("serviceUsageFlow")
public class ServiceUsageFlowStrategy implements ModuleStrategy {

    private static final List<String> ATTR_NAME_LIST = Lists.newArrayList("使用规则","服务功效") ;
    public static final String CODE = "dealAttrVOListModuleBuilder";

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        List<DealDetailStructAttrModuleGroupModel> attrModuleGroupModels = activityCxt.getSource(CODE);
        if (CollectionUtils.isEmpty(attrModuleGroupModels) || StringUtils.isEmpty(config)) {
            return null;
        }
        DealDetailStructAttrModuleGroupModel groupModel = attrModuleGroupModels.stream().filter(group -> config.equals(group.getGroupName())).findFirst().orElse(null);
        if (groupModel == null || CollectionUtils.isEmpty(groupModel.getDealDetailStructAttrModuleVOS())) {
            return null;
        }
        List<DealDetailStructAttrModuleVO> attrModuleVOS = groupModel.getDealDetailStructAttrModuleVOS();
        if (CollectionUtils.isEmpty(attrModuleVOS)) {
            return null;
        }
        String usageSlowAttrName = attrModuleVOS.stream().filter(item -> item != null && ATTR_NAME_LIST.contains(item.getAttrName())).map(item -> CollectUtils.firstValue(item.getAttrValues()).toString()).findFirst().orElse(null);
        if (StringUtils.isBlank(usageSlowAttrName)) {
            return null;
        }
        DealDetailStructAttrGroupVO dealDetailStructAttrGroupVO = buildDealDetailStructAttrGroupVO(usageSlowAttrName);
        return buildDealDetailModuleVO(dealDetailStructAttrGroupVO);
    }
    private DealDetailModuleVO buildDealDetailModuleVO(DealDetailStructAttrGroupVO dealDetailStructAttrGroupVO) {
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        dealDetailModuleVO.setDealStructAttrsModel2(dealDetailStructAttrGroupVO);
        return dealDetailModuleVO;
    }
    private DealDetailStructAttrGroupVO buildDealDetailStructAttrGroupVO(String content) {
        DealDetailStructAttrGroupVO dealDetailStructAttrGroupVO = new DealDetailStructAttrGroupVO();
        dealDetailStructAttrGroupVO.setContent(content);
        return dealDetailStructAttrGroupVO;
    }
}