package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/2 11:30
 */
@VPointOption(name = "默认商品列表",
        description = "紧急置空",
        code = "EmptyProductListOpt")
public class EmptyProductListOpt extends ProductListVP<EmptyProductListOpt.Config> {

    @Override
    public List<ProductM> compute(ActivityCxt context, Param param, Config config) {
        return null;
    }


    @VPointCfg
    @Data
    public static class Config {

    }
}
