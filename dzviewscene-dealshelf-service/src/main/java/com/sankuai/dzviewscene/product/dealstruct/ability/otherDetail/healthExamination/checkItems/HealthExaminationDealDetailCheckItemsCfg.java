package com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.healthExamination.checkItems;

import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import lombok.Data;


import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/19 9:05 下午
 */
@AbilityCfg
@Data
public class HealthExaminationDealDetailCheckItemsCfg {
    private Map<String, Integer> tabName2TabIdMap;

    //筛选tab模型配置，用于通过tab名获取tabId
    private List<FilterTabConfigModel> filterTabConfigModels;

    //筛选tab默认选中配置，用于获取被选中的tabId以及判断某个tab的选中状态
    private List<SelectedFilterTabConfigModel> selectedFilterTabConfigModels;

    //查看更多文案
    private String readMoreText;

    //项目详情查看更多跳转链接format 美团
    private String mtMoreJumpUrlFormat;

    //项目详情查看更多跳转链接format 点评
    private String dpMoreJumpUrlFormat;



    /**
     * 筛选tab配置模型（tabId和tab名称）
     */
    @Data
    public static class FilterTabConfigModel {
        private int tabId;
        private String tabName;
    }

    /**
     * 服务类型下选中tab模型
     */
    @Data
    public static class SelectedFilterTabConfigModel  {
        //团单服务类型
        private String serviceType;
        //该服务类型下应被选中的二级tab名
        private String secondTabName;
        //该服务类型下应被选中的三级tab名
        private String thirdTabName;
    }
}
