package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;
import com.alibaba.fastjson.JSONPath;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
@VPointOption(name = "齿科门店仪器差异DealAttrVO列表变化点",
        description = "齿科门店仪器差异DealAttrVO列表变化点",
        code = DentistryAttrVOListOpt.CODE)
public class DentistryAttrVOListOpt extends DealAttrVOListVP<DentistryAttrVOListOpt.StepConfig> {
    public static final String CODE = "DentistryAttrVOListOpt";
    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt context,
                                                              Param param,
                                                              StepConfig config) {
        List<DealDetailStructAttrModuleVO> processStepAttrModules = getProcessStepAttrModules(param,
                config);
        if (CollectionUtils.isEmpty(processStepAttrModules)) {
            return null;
        }
        DealDetailStructAttrModuleGroupModel dealDetailStructAttrModuleGroupModel = new DealDetailStructAttrModuleGroupModel();
        dealDetailStructAttrModuleGroupModel.setGroupName(config.getTitle());
        dealDetailStructAttrModuleGroupModel.setDealDetailStructAttrModuleVOS(processStepAttrModules);
        return Lists.newArrayList(dealDetailStructAttrModuleGroupModel);
    }
    private List<DealDetailStructAttrModuleVO> getProcessStepAttrModules(Param param,
                                                                         StepConfig config) {
        if (config == null || param == null || StringUtils.isBlank(config.getTitle())
                || StringUtils.isBlank(config.getAttrKey()) || StringUtils.isBlank(config.getMemoKey())) {
            return null;
        }
        String stepStr = DealDetailUtils.getAttrSingleValueByAttrName(param.getDealAttrs(),
                config.getAttrKey());
        if (StringUtils.isBlank(stepStr)) {
            return null;
        }
        Object read = JSONPath.read(stepStr, config.getMemoKey());
        if (read == null) {
            return null;
        }
        String memo = read.toString();
        if (StringUtils.isBlank(memo)) {
            return null;
        }
        DealDetailStructAttrModuleVO processStepAttrModule = new DealDetailStructAttrModuleVO();
        processStepAttrModule.setAttrName(config.getTitle());
        processStepAttrModule.setAttrValues(Lists.newArrayList(memo));
        return Lists.newArrayList(processStepAttrModule);
    }
    @Data
    @VPointCfg
    public static class StepConfig {
        private String title;
        private String attrKey;
        private String memoKey;
    }
}
