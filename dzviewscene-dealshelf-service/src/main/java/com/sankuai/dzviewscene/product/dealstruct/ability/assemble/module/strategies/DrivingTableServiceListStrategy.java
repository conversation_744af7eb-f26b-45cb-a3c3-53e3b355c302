package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.DealDetailSkuGroupsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuUniModel;
import com.sankuai.dzviewscene.product.dealstruct.model.SkuAttrModel;
import com.sankuai.dzviewscene.product.dealstruct.model.SkuItemModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.*;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import java.util.*;
import java.util.stream.Collectors;

/**
 * created by zhangzhiyuan04 in 2021/12/14
 */
@Component("drivingTableServiceListStrategy")
public class DrivingTableServiceListStrategy implements ModuleStrategy {

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.driving.deal.detail.config", defaultValue = "{}")
    private DrivingDealDetailConfig drivingDealDetailConfig;

    private static Map<String, String> TABLE_HEADER_DATA_2_KEY_MAP = new LinkedHashMap<String, String>() {{
        put("类型", "col0");
        put("名称", "col1");
        put("内容", "col2");
        put("价格", "col3");
        put("是否包含", "col4");
    }};

    private static final int DEFAULT_CELL_COUNT = 1;
    // 课时数
    private static final String SKU_CLASS_HOURS_ATTR_NAME = "classTotalHours";

    private static final String SKU_CLASS_HOURS_ATTR_SHOW_NAME = "课时";

    private static final String PRICE_UNITE = "元";

    private static final String TRAINING_EXPENSE_MERGE_ROW_NAME = "培训费";

    private static final String EXTRA_EXPENSE_MERGE_ROW_NAME = "额外费用";

    private static final String EXAM_EXPENSE_MERGE_ROW_NAME = "考试费";

    private static final String MAKE_UP_EXAM_EXPENSE_MERGE_ROW_NAME = "补考费";
    // 套餐内是否包含
    private static final String PACKAGE_INCLUDE_ATTR_NAME = "dealContain";
    // 套餐内包含数量
    private static final String PACKAGE_NUM_ATTR_NAME = "dealContainsNumber";
    // 计费单位
    private static final String EXPENZE_UNIT_ATTR_NAME = "billingUnit";
    // 计费价格
    private static final String BILING_ATTR_NAME = "billing";

    private static final String BAR = "-";

    private static final String TRUE_ICON = "✓";

    private static final String TRUE_CHN = "是";

    private static final String CLASS_HOURS_LIMIT = "限制课时";

    private static final String CLASS_HOURS_NO_LIMIT = "不限课时";
    // 是否限制课时
    private static final String CLASS_HOURS_LIMITED_ATTR_NAME = "limitClassHour";

    private static final String POP_UP_ICON = "https://img.meituan.net/dpmobile/fdeb83e2678b4c8e385b6e3795f038943067.png";

    private static final String DRIVING_SUBJECT_TWO_EXTRA_EXPENSE_SKU_NAME = "科目二超时培训费";

    private static final String DRIVING_SUBJECT_THTEE_EXTRA_EXPENSE_SKU_NAME = "科目三超时培训费";

    private static final String DRIVING_SUBJECT_TWO = "科目二";

    private static final String DRIVING_SUBJECT_THTEE = "科目三";

    private static final String EXAM_EXPENSE_CONTENT = "一次";

    private static final String SEPERATOR = "/";

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        List<DealDetailSkuUniModel> skuUniModels = activityCxt.getSource(DealDetailSkuGroupsBuilder.CODE);
        if (CollectionUtils.isEmpty(skuUniModels)) {
            return null;
        }
        //获取用于构造表的sku信息
        DealDetailSkuUniModel dealDetailSkuUniModel = CollectUtils.firstValue(skuUniModels);

        SkusGroupsTableVO skusGroupsTableVO = new SkusGroupsTableVO();
        //表名
        skusGroupsTableVO.setTableName(drivingDealDetailConfig.getTableName());
        //表头
        skusGroupsTableVO.setTableHeader(buildTableHeader());
        //表格内容
        skusGroupsTableVO.setTableVO(getTableCellsRowVOs(dealDetailSkuUniModel, activityCxt));
        //表格弹窗
        skusGroupsTableVO.setStatePopUp(buildPopUpWindowVO());
        //商家描述
        skusGroupsTableVO.setDesc(drivingDealDetailConfig.getDesc());

        return buildDealDetailModuleVO(skusGroupsTableVO);
    }

    private DealDetailModuleVO buildDealDetailModuleVO(SkusGroupsTableVO skusGroupsTableVO) {
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        dealDetailModuleVO.setSkuGroupsModel2(skusGroupsTableVO);
        return dealDetailModuleVO;
    }

    private PopUpWindowVO buildPopUpWindowVO() {
        PopUpWindowVO popUpWindowVO = new PopUpWindowVO();
        popUpWindowVO.setIcon(POP_UP_ICON);
        popUpWindowVO.setContent(drivingDealDetailConfig.getExpenseDetailPopupContent());
        popUpWindowVO.setTitle(drivingDealDetailConfig.getExpenseDetailPopupTitle());
        return popUpWindowVO;
    }

    private List<SkuItemModel> getMustGroupSkuItems(DealDetailSkuUniModel dealDetailModuleVO) {
        if (dealDetailModuleVO == null || CollectionUtils.isEmpty(dealDetailModuleVO.getMustGroups())) {
            return null;
        }
        return getSkuItems(dealDetailModuleVO.getMustGroups());
    }

    private List<SkuItemModel> getOptionalGroupSkuItems(DealDetailSkuUniModel dealDetailModuleVO) {
        if (dealDetailModuleVO == null || CollectionUtils.isEmpty(dealDetailModuleVO.getOptionGroups())) {
            return null;
        }
        return getSkuItems(dealDetailModuleVO.getOptionGroups());
    }

    private List<SkuItemModel> getSkuItems(List<DealDetailSkuGroupModel> skuGroupModels) {
        if (CollectionUtils.isEmpty(skuGroupModels)) {
            return null;
        }
        return skuGroupModels.stream().flatMap(group -> group.getSkuSetModels().stream()).flatMap(set -> set.getSkuItems().stream()).collect(Collectors.toList());
    }

    //获取表内容（行元素列表）
    private List<TableCellsRowVO> getTableCellsRowVOs(DealDetailSkuUniModel dealDetailModuleVO, ActivityCxt activityCxt) {
        if (dealDetailModuleVO == null) {
            return null;
        }
        List<TableCellsRowVO> tableCellsRowVOS = new ArrayList<>();
        //1.培训费对应表行元素列表
        List<SkuItemModel> mustSkus = getMustGroupSkuItems(dealDetailModuleVO);
        if (CollectionUtils.isNotEmpty(mustSkus)) {
            List<TableCellsRowVO> trainingExpenseTableCellsRowVOs = getTrainingExpenseTableCellsRowVOs(mustSkus);
            tableCellsRowVOS.addAll(trainingExpenseTableCellsRowVOs);
        }
        //2.额外费用对应表行元素列表
        List<SkuItemModel> optionalSkus = getOptionalGroupSkuItems(dealDetailModuleVO);
        if (CollectionUtils.isNotEmpty(optionalSkus)) {
            List<TableCellsRowVO> extraExpenseTableCellsRowVOs = getExtraExpenseTableCellsRowVOs(optionalSkus, mustSkus);
            tableCellsRowVOS.addAll(extraExpenseTableCellsRowVOs);
        }

        //3.考试费对应表行元素列表
        DrivingExpenseModel drivingExpenseModel = getDrivingExpenseConfig(activityCxt);
        List<TableCellsRowVO> examExpenseTableCellsRowVO = getExamExpenseTableCellsRowVOs(drivingExpenseModel);
        if (CollectionUtils.isNotEmpty(examExpenseTableCellsRowVO)) {
            tableCellsRowVOS.addAll(examExpenseTableCellsRowVO);
        }
        //4.补考费对应表行元素列表
        List<TableCellsRowVO> makeUpExamExpenseTableCellsRowVO = getMakeUpExamExpenseTableCellsRowVOs(drivingExpenseModel);
        if (CollectionUtils.isNotEmpty(makeUpExamExpenseTableCellsRowVO)) {
            tableCellsRowVOS.addAll(makeUpExamExpenseTableCellsRowVO);
        }
        return tableCellsRowVOS;
    }

    private DrivingExpenseModel getDrivingExpenseConfig(ActivityCxt activityCxt) {
        ActivityContext ctx = ActivityCtxtUtils.toActivityContext(activityCxt);
        int cityId = ParamsUtil.getIntSafely(ctx, ProductDetailActivityConstants.Params.dpCityId);
        if (drivingDealDetailConfig == null || drivingDealDetailConfig.getDrivingExpense() == null || CollectionUtils.isEmpty(drivingDealDetailConfig.getDrivingExpense().getDrivingExpenseModels())) {
            return null;
        }
        return drivingDealDetailConfig.getDrivingExpense().getDrivingExpenseModels().stream().filter(model -> CollectionUtils.isNotEmpty(model.getCityIds()) && model.getCityIds().contains(cityId)).findFirst().orElse(null);
    }

    //获取补考费对应表行元素列表
    private List<TableCellsRowVO> getMakeUpExamExpenseTableCellsRowVOs(DrivingExpenseModel drivingExpenseModel) {
        if (drivingExpenseModel == null || MapUtils.isEmpty(drivingExpenseModel.getMakeUpExamCourse2ExpenseMap())) {
            return null;
        }
        //获取不带表行名的表行列表
        List<List<TableCellVO>> makeUpExamExpenseTableCellVOsList = getExamExpenseTableCellVOsList(drivingExpenseModel.getMakeUpExamCourse2ExpenseMap());
        //添加表行名
        return addMergeRowName(makeUpExamExpenseTableCellVOsList, MAKE_UP_EXAM_EXPENSE_MERGE_ROW_NAME);
    }

    //获取考试费对应表行元素列表
    private List<TableCellsRowVO> getExamExpenseTableCellsRowVOs(DrivingExpenseModel drivingExpenseModel) {
        if (drivingExpenseModel == null || MapUtils.isEmpty(drivingExpenseModel.getExamCourse2ExpenseMap())) {
            return null;
        }
        //获取不带表行名的表行列表
        List<List<TableCellVO>> examExpenseTableCellVOsList = getExamExpenseTableCellVOsList(drivingExpenseModel.getExamCourse2ExpenseMap());
        //添加表行名
        return addMergeRowName(examExpenseTableCellVOsList, EXAM_EXPENSE_MERGE_ROW_NAME);
    }

    //获取费用对应表格
    private List<List<TableCellVO>> getExamExpenseTableCellVOsList(Map<String, String> course2ExpenseMap) {
        if (MapUtils.isEmpty(course2ExpenseMap)) {
            return null;
        }
        return course2ExpenseMap.entrySet().stream().map(entry -> {
            TableCellVO nameTableCellVO = buildTableCellVO(DEFAULT_CELL_COUNT, DEFAULT_CELL_COUNT, entry.getKey(), null);
            TableCellVO contentTableCellVO = buildTableCellVO(DEFAULT_CELL_COUNT, DEFAULT_CELL_COUNT, EXAM_EXPENSE_CONTENT, null);
            TableCellVO priceTableCellVO = buildTableCellVO(DEFAULT_CELL_COUNT, DEFAULT_CELL_COUNT, entry.getValue(), null);
            TableCellVO containTableCellVO = buildTableCellVO(DEFAULT_CELL_COUNT, DEFAULT_CELL_COUNT, BAR, null);
            return new ArrayList<TableCellVO>() {{
                add(nameTableCellVO);
                add(contentTableCellVO);
                add(priceTableCellVO);
                add(containTableCellVO);
            }}.stream().filter(vo -> vo != null).collect(Collectors.toList());
        }).collect(Collectors.toList());
    }

    //获取额外费用对应表行元素列表
    private List<TableCellsRowVO> getExtraExpenseTableCellsRowVOs(List<SkuItemModel> skuItemModels, List<SkuItemModel> mustSkus) {
        if (CollectionUtils.isEmpty(skuItemModels)) {
            return null;
        }
        //获取不带表行名的表行列表
        List<List<TableCellVO>> extraExpenseTableCellVOsList = skuItemModels.stream().map(sku -> getExtraExpenseTableCellVOs(sku, mustSkus)).filter(list -> list != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(extraExpenseTableCellVOsList)) {
            return null;
        }
        //添加表行名
        return addMergeRowName(extraExpenseTableCellVOsList, EXTRA_EXPENSE_MERGE_ROW_NAME);
    }

    //获取培训费对应表行元素列表
    private List<TableCellsRowVO> getTrainingExpenseTableCellsRowVOs(List<SkuItemModel> skuItemModels) {
        if (CollectionUtils.isEmpty(skuItemModels)) {
            return null;
        }
        //获取不带表行名的表行列表
        List<List<TableCellVO>> trainingExpenseTableCellVOsList = skuItemModels.stream().map(sku -> getTrainingExpenseTableCellVOs(sku)).filter(list -> list != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(trainingExpenseTableCellVOsList)) {
            return null;
        }
        //添加表行名
        return addMergeRowName(trainingExpenseTableCellVOsList, TRAINING_EXPENSE_MERGE_ROW_NAME);
    }

    private List<TableCellsRowVO> addMergeRowName(List<List<TableCellVO>> tableCellVOsList, String mergeRowName) {
        if (CollectionUtils.isEmpty(tableCellVOsList)) {
            return null;
        }
        int index = 0;
        for (List<TableCellVO> list : tableCellVOsList) {
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            if (index == 0) {
                index++;
                list.add(0, buildTableCellVO(mergeRowName, tableCellVOsList.size()));
                continue;
            }
            list.add(0, buildEmptyTableCellVO());
        }
        return tableCellVOsList.stream().map(list -> buildTableCellsRowVO(list)).collect(Collectors.toList());
    }

    private TableCellsRowVO buildTableCellsRowVO(List<TableCellVO> tableCellsRow) {
        TableCellsRowVO tableCellsRowVO = new TableCellsRowVO();
        tableCellsRowVO.setTableCellsRow(tableCellsRow);
        return tableCellsRowVO;
    }

    private TableCellVO buildEmptyTableCellVO() {
        TableCellVO tableCellVO = new TableCellVO();
        tableCellVO.setCellColumnCount(0);
        tableCellVO.setCellRowCount(0);
        tableCellVO.setPopUp(null);
        tableCellVO.setContent(null);
        return tableCellVO;
    }

    private TableCellVO buildTableCellVO(String content, int cellRowCount) {
        if (StringUtils.isEmpty(content) || cellRowCount <= 0) {
            return null;
        }
        TableCellVO tableCellVO = new TableCellVO();
        tableCellVO.setContent(content);
        tableCellVO.setPopUp(null);
        tableCellVO.setCellRowCount(cellRowCount);
        tableCellVO.setCellColumnCount(1);
        return tableCellVO;
    }

    //获取培训费对应表单元格元素列表
    private List<TableCellVO> getTrainingExpenseTableCellVOs(SkuItemModel sku) {
        if (sku == null) {
            return null;
        }
        TableCellVO nameTableCellVO = buildTableCellVO(DEFAULT_CELL_COUNT, DEFAULT_CELL_COUNT, sku.getName(), null);
        TableCellVO classHoursTableCellVO = buildTableCellVO(DEFAULT_CELL_COUNT, DEFAULT_CELL_COUNT, getTrainingExpenseClassHours(sku.getAttrItems()), null);
        TableCellVO priceTableCellVO = buildTableCellVO(DEFAULT_CELL_COUNT, DEFAULT_CELL_COUNT, sku.getPrice(), null);
        TableCellVO containTableCellVO = buildTableCellVO(DEFAULT_CELL_COUNT, DEFAULT_CELL_COUNT, "✓", null);
        List<TableCellVO> tableCellVOS = new ArrayList<TableCellVO>() {{
            add(nameTableCellVO);
            add(classHoursTableCellVO);
            add(priceTableCellVO);
            add(containTableCellVO);
        }}.stream().filter(vo -> vo != null).collect(Collectors.toList());
        return tableCellVOS;
    }

    public String getTrainingExpenseClassHours(List<SkuAttrModel> AttrItems) {
        String classHoursLimitedAttrValue = DealDetailUtils.getSkuAttrModelValueByAttrNameNew(AttrItems, CLASS_HOURS_LIMITED_ATTR_NAME);
        String classHour = DealDetailUtils.getSkuAttrModelValueByAttrNameNew(AttrItems, SKU_CLASS_HOURS_ATTR_NAME);
        if (!CLASS_HOURS_LIMIT.equals(classHoursLimitedAttrValue) || StringUtils.isEmpty(classHour)) {
            return CLASS_HOURS_NO_LIMIT;
        }
        return classHour + SKU_CLASS_HOURS_ATTR_SHOW_NAME;
    }

    //获取额外费用对应表单元格元素列表
    public List<TableCellVO> getExtraExpenseTableCellVOs(SkuItemModel sku, List<SkuItemModel> mustSkus) {
        if (!isShowExtraExpense(sku, mustSkus)) {
            return null;
        }
        TableCellVO nameTableCellVO = buildTableCellVO(DEFAULT_CELL_COUNT, DEFAULT_CELL_COUNT, sku.getName(), null);
        String isIncluded = DealDetailUtils.getSkuAttrModelValueByAttrNameNew(sku.getAttrItems(), PACKAGE_INCLUDE_ATTR_NAME);
        TableCellVO contentTableCellVO = buildTableCellVO(DEFAULT_CELL_COUNT, DEFAULT_CELL_COUNT, getExtraExpenseContent(sku, isIncluded), null);
        TableCellVO priceTableCellVO = buildTableCellVO(DEFAULT_CELL_COUNT, DEFAULT_CELL_COUNT, getExtraExpensePrice(sku, isIncluded), getExtraExpensePopUp(sku, isIncluded));
        TableCellVO containTableCellVO = buildTableCellVO(DEFAULT_CELL_COUNT, DEFAULT_CELL_COUNT, getExtraExpenseContain(isIncluded), null);
        List<TableCellVO> tableCellVOS = new ArrayList<TableCellVO>() {{
            add(nameTableCellVO);
            add(contentTableCellVO);
            add(priceTableCellVO);
            add(containTableCellVO);
        }}.stream().filter(vo -> vo != null).collect(Collectors.toList());
        return tableCellVOS;
    }

    private String getExtraExpenseContain(String isIncluded) {
        return TRUE_CHN.equals(isIncluded) ? TRUE_ICON : BAR;
    }

    private boolean isShowExtraExpense(SkuItemModel sku, List<SkuItemModel> mustSkus) {
        if (sku == null) {
            return false;
        }
        //当科目二培训费为不限学时时不显示科目二超时培训费
        if (DRIVING_SUBJECT_TWO_EXTRA_EXPENSE_SKU_NAME.equals(sku.getName()) && !isSkuClassHoursLimited(DRIVING_SUBJECT_TWO, mustSkus)) {
            return false;
        }
        //当科目三培训费为不限学时时不显示科目三超时培训费
        if (DRIVING_SUBJECT_THTEE_EXTRA_EXPENSE_SKU_NAME.equals(sku.getName()) && !isSkuClassHoursLimited(DRIVING_SUBJECT_THTEE, mustSkus)) {
            return false;
        }
        return true;
    }

    public boolean isSkuClassHoursLimited(String skuName, List<SkuItemModel> mustSkus) {
        if (CollectionUtils.isEmpty(mustSkus) || StringUtils.isEmpty(skuName)) {
            return true;
        }
        SkuItemModel skuItemModel = mustSkus.stream().filter(sku -> skuName.equals(sku.getName())).findFirst().orElse(null);
        if (skuItemModel == null) {
            return true;
        }
        String classHoursLimitedAttrValue = DealDetailUtils.getSkuAttrModelValueByAttrNameNew(skuItemModel.getAttrItems(), CLASS_HOURS_LIMITED_ATTR_NAME);
        return CLASS_HOURS_LIMIT.equals(classHoursLimitedAttrValue);
    }

    public String getExtraExpenseContent(SkuItemModel sku, String isIncluded) {
        if (sku == null || CollectionUtils.isEmpty(sku.getAttrItems())) {
            return null;
        }
        String packageNum = DealDetailUtils.getSkuAttrModelValueByAttrNameNew(sku.getAttrItems(), PACKAGE_NUM_ATTR_NAME);
        String unit = getExtraExpenseUnit(sku.getAttrItems());
        if (TRUE_CHN.equals(isIncluded) && !BAR.equals(unit)) {
            return packageNum + unit;
        }
        return getExtraExpenseDefaultContent(unit);
    }

    public String getExtraExpenseUnit(List<SkuAttrModel> AttrItems) {
        String unit = DealDetailUtils.getSkuAttrModelValueByAttrNameNew(AttrItems, EXPENZE_UNIT_ATTR_NAME);
        return getNormalizedUnit(unit);
    }

    private String getExtraExpenseDefaultContent(String unit) {
        return BAR.equals(unit) ? unit : "1" + unit;
    }

    private String getNormalizedUnit(String unit) {
        if (StringUtils.isEmpty(unit)) {
            return BAR;
        }
        List<String> unitItems = Lists.newArrayList(unit.split(SEPERATOR));
        if (CollectionUtils.isEmpty(unitItems) || unitItems.size() <= 1) {
            return BAR;
        }
        return unitItems.get(1);
    }

    public String getExtraExpensePrice(SkuItemModel sku, String isIncluded) {
        if (sku == null || CollectionUtils.isEmpty(sku.getAttrItems())) {
            return null;
        }
        String price = sku.getPrice();
        String billing = DealDetailUtils.getSkuAttrModelValueByAttrNameNew(sku.getAttrItems(), BILING_ATTR_NAME);
        if (TRUE_CHN.equals(isIncluded)) {
            return price;
        }
        return StringUtils.isEmpty(billing) ? BAR :  billing + PRICE_UNITE;
    }

    public PopUpWindowVO getExtraExpensePopUp(SkuItemModel sku, String isIncluded) {
        String billing = DealDetailUtils.getSkuAttrModelValueByAttrNameNew(sku.getAttrItems(), BILING_ATTR_NAME);
        String billingUnit = DealDetailUtils.getSkuAttrModelValueByAttrNameNew(sku.getAttrItems(), EXPENZE_UNIT_ATTR_NAME);
        if (!isShowExtraExpensePopUp(sku, isIncluded, billing, billingUnit)) {
            return null;
        }
        PopUpWindowVO popUpWindowVO = new PopUpWindowVO();
        popUpWindowVO.setTitle(drivingDealDetailConfig.getExtraExpensePopUpTitle());
        popUpWindowVO.setIcon(POP_UP_ICON);
        popUpWindowVO.setContent(String.format(drivingDealDetailConfig.getExtraExpensePopUpFormat(), billing + billingUnit));
        return popUpWindowVO;
    }

    private boolean isShowExtraExpensePopUp(SkuItemModel sku, String isIncluded, String billing, String billingUnit) {
        if (!TRUE_CHN.equals(isIncluded)) {
            return false;
        }
        return StringUtils.isNotEmpty(sku.getPrice()) && StringUtils.isNotEmpty(billing) && StringUtils.isNotEmpty(billingUnit);
    }


    private TableCellVO buildTableCellVO(int cellColumnCount, int cellRowCount, String content, PopUpWindowVO popUp) {
        TableCellVO tableCellVO = new TableCellVO();
        tableCellVO.setContent(content);
        tableCellVO.setPopUp(popUp);
        tableCellVO.setCellRowCount(cellRowCount);
        tableCellVO.setCellColumnCount(cellColumnCount);
        return tableCellVO;
    }

    private List<TableHeaderCellVO> buildTableHeader() {
        if (MapUtils.isEmpty(TABLE_HEADER_DATA_2_KEY_MAP)) {
            return null;
        }
        return TABLE_HEADER_DATA_2_KEY_MAP.entrySet().stream().map(entry -> buildTableHeaderCellVO(entry.getKey(), entry.getValue())).collect(Collectors.toList());
    }

    private TableHeaderCellVO buildTableHeaderCellVO(String data, String key) {
        TableHeaderCellVO tableHeaderCellVO = new TableHeaderCellVO();
        tableHeaderCellVO.setData(data);
        tableHeaderCellVO.setKey(key);
        return tableHeaderCellVO;
    }

    @Data
    private static class DrivingDealDetailConfig {
        private DrivingExpense drivingExpense;
        private LinkedHashMap<String, String> tableHeaderData2KeyMap;
        private String tableName;
        private String desc;
        private String expenseDetailPopupContent;
        private String expenseDetailPopupTitle;
        private String extraExpensePopUpTitle;
        private String extraExpensePopUpFormat;
        private Map<String, String> receivingMethod2ShowDocMap;
    }

    @Data
    private static class DrivingExpense {
        private List<DrivingExpenseModel> drivingExpenseModels;
    }

    @Data
    private static class DrivingExpenseModel {
        private List<Integer> cityIds;
        private Map<String, String> examCourse2ExpenseMap;
        private Map<String, String> makeUpExamCourse2ExpenseMap;
    }
}
