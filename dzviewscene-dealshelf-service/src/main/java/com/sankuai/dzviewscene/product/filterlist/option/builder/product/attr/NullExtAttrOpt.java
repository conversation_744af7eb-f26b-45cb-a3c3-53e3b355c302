package com.sankuai.dzviewscene.product.filterlist.option.builder.product.attr;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductExtAttrVP;

/**
 * Created by zhangfeibiao on 2023/2/24.
 */
@VPointOption(name = "商品扩展信息空实现",
        description = "无商品扩展信息",
        code = "NullExtAttrOpt",
        isDefault = true)
public class NullExtAttrOpt extends ProductExtAttrVP<Void> {
    @Override
    public String compute(ActivityCxt context, Param param, Void aVoid) {
        return null;
    }

}
