package com.sankuai.dzviewscene.product.ability.options;

import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.haima.entity.haima.HaimaConfig;
import com.dianping.haima.entity.haima.HaimaContent;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreSyncHandlerVP;
import com.sankuai.dzviewscene.product.ability.model.EduMappingAttrM;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.CompletableFutureUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;


import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @author: wuwenqiang
 * @create: 2024-06-12
 * @description:
 */
@VPointOption(name = "教育非合作POI商品倾斜流量映射信息和可选标品", description = "包含教育非合作POI页映射词/类目筛选后的商品映射信息以及所有教育可选标品ID", code = EduUncoopShopSpecialOpt.CODE)
@Slf4j
public class EduUncoopShopSpecialOpt extends PreSyncHandlerVP<EduUncoopShopSpecialOpt.Config> {

    public static final String CODE = "EduUncoopShopInfoOpt";

    private static final String EDU_STANDARD_PRODUCT_SCENE = "edu_standard_product";
    private static final String SEARCH_WORD_PRODUCT_RELATION_SCENE = "edu_search_word_product_relation";
    private static final String BACK_CATEGORY_PRODUCT_RELATION_SCENE = "edu_backcat_product_relation";
    private static final String PRODUCT_ID = "productId";
    private static final String SEARCH_WORD = "searchWord";
    private static final String FIRST_SUBJECT = "firstSubject";
    private static final String FIRST_EXPLAIN_DOC = "firstExplainDoc";
    private static final String SECOND_SUBJECT = "secondSubject";
    private static final String SECOND_EXPLAIN_DOC = "secondExplainDoc";
    private static final String FIRST_PRODUCT_LABEL = "firstProductLabel";
    private static final String SECOND_PRODUCT_LABEL = "secondProductLabel";
    private static final String BACK_CATE_ID_HAI_MA = "backCategoryId";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public Map<String, Object> compute(ActivityCxt context, Param param, Config config) {
        // 获取线上可用标品ID
        Integer dpCityId = Optional.ofNullable((Integer) context.getParam(ShelfActivityConstants.Params.dpCityId)).orElse(0);
        HaimaRequest standardProductRequest = buildHaimaRequestWithCity(EDU_STANDARD_PRODUCT_SCENE, dpCityId);
        CompletableFuture<HaimaResponse> standardProductFuture = compositeAtomService.getHaiMaResponse(standardProductRequest);

        // 获取搜索词商品映射关系
        HaimaRequest searchProductRequest = buildHaimaRequest(SEARCH_WORD_PRODUCT_RELATION_SCENE);
        CompletableFuture<HaimaResponse> searchProductFuture = compositeAtomService.getHaiMaResponse(searchProductRequest);

        // 获取后台类目商品映射关系
        List<Integer> backCatList = context.getParam(PmfConstants.Params.shopBackCatIds);
        HaimaRequest backCatProductRequest = buildHaimaRequestWithCategory(BACK_CATEGORY_PRODUCT_RELATION_SCENE, backCatList);
        CompletableFuture<HaimaResponse> backCatProductFuture = compositeAtomService.getHaiMaResponse(backCatProductRequest);

        // 组合信息
        CompletableFuture<EduMappingAttrM> eduMappingAttrFuture = searchProductFuture.thenCombine(backCatProductFuture,
                (searchProductRes, backCatProductRes) -> buildEduMappingAttr(context, searchProductRes, backCatProductRes));
        CompletableFuture<EduMappingAttrM> eduMappingAttrCf = eduMappingAttrFuture.thenCombine(standardProductFuture,
                (eduMappingAttr, standardProductRes) -> fillEduMappingAttr(eduMappingAttr, standardProductRes));
        Map<String, CompletableFuture<Object>> resultMap = Maps.newHashMap();
        resultMap.put(EduUncoopShopSpecialOpt.CODE, CompletableFutureUtil.covert2ObjCf(eduMappingAttrCf));
        return CompletableFutureUtil.each(resultMap).join();
    }

    private HaimaRequest buildHaimaRequestWithCity(String sceneKey, Integer dpCityId) {
        HaimaRequest request = buildHaimaRequest(sceneKey);
        request.setCityId(dpCityId);
        return request;
    }

    private HaimaRequest buildHaimaRequestWithCategory(String sceneKey, List<Integer> backCateList) {
        HaimaRequest request = buildHaimaRequest(sceneKey);
        // 后台类目需要子类目在前，父类目在后
        request.addField(BACK_CATE_ID_HAI_MA, convertList2Str(reverseList(backCateList)));
        request.setSceneKey(sceneKey);
        return request;
    }

    private HaimaRequest buildHaimaRequest(String sceneKey) {
        HaimaRequest request = new HaimaRequest();
        request.setSceneKey(sceneKey);
        return request;
    }

    private EduMappingAttrM buildEduMappingAttr(ActivityCxt context, HaimaResponse searchProductRes, HaimaResponse backCatProductRes) {
        // 优先匹配搜索词
        List<HaimaConfig> searchConfigs = (searchProductRes == null || !searchProductRes.isSuccess()) ? Lists.newArrayList() : searchProductRes.getData();
        String searchKeyword = Optional.ofNullable((String) context.getParam(ShelfActivityConstants.Params.keyword)).orElse("");
        HaimaContent searchContent = null;
        if (CollectionUtils.isNotEmpty(searchConfigs) && searchConfigs.get(0) != null && CollectionUtils.isNotEmpty(searchConfigs.get(0).getContents())) {
            List<HaimaContent> searchContents = searchConfigs.get(0).getContents();
            searchContent = searchContents.stream().filter(content -> findMatchedSearchWord(content, searchKeyword)).findFirst().orElse(null);
        }
        if (searchContent != null) {
            return buildBaseEduMappingAttr(searchContent);
        }

        // 如果没有搜索词可以匹配，则尝试匹配后台类目
        // 由于海马已有筛选后台类目的功能，则直接处理第一项即可
        List<HaimaConfig> backCatConfigs = (backCatProductRes == null || !backCatProductRes.isSuccess()) ? Lists.newArrayList() : backCatProductRes.getData();
        if (CollectionUtils.isEmpty(backCatConfigs) || backCatConfigs.get(0) == null || CollectionUtils.isEmpty(backCatConfigs.get(0).getContents())) {
            return new EduMappingAttrM();
        }
        HaimaContent backCatContent= backCatConfigs.get(0).getContents().get(0);
        return buildBaseEduMappingAttr(backCatContent);
    }

    private boolean findMatchedSearchWord(HaimaContent searchContent, String searchKeyWord) {
        if (searchContent == null || StringUtils.isBlank(searchKeyWord)) {
            return false;
        }
        String configSearchWord = searchContent.getContentString(SEARCH_WORD);
        return StringUtils.isNotBlank(configSearchWord) && configSearchWord.contains(searchKeyWord);
    }

    private EduMappingAttrM buildBaseEduMappingAttr(HaimaContent content) {
        EduMappingAttrM eduMappingAttrM = new EduMappingAttrM();
        eduMappingAttrM.setFirstSubject(content.getContentString(FIRST_SUBJECT));
        eduMappingAttrM.setFirstExplainDoc(content.getContentString(FIRST_EXPLAIN_DOC));
        eduMappingAttrM.setSecondSubject(content.getContentString(SECOND_SUBJECT));
        eduMappingAttrM.setSecondExplainDoc(content.getContentString(SECOND_EXPLAIN_DOC));
        eduMappingAttrM.setFirstProductLabel(content.getContentString(FIRST_PRODUCT_LABEL));
        eduMappingAttrM.setSecondProductLabel(content.getContentString(SECOND_PRODUCT_LABEL));
        return eduMappingAttrM;
    }

    private EduMappingAttrM fillEduMappingAttr(EduMappingAttrM eduMappingAttr, HaimaResponse standardProductRes) {
        if (eduMappingAttr == null) {
            return null;
        }
        List<ProductM> productList = loadProducts(standardProductRes);
        eduMappingAttr.setStandardProductList(productList);
        return eduMappingAttr;
    }

    private List<ProductM> loadProducts(HaimaResponse response) {
        if (response == null || !response.isSuccess() || CollectionUtils.isEmpty(response.getData())) {
            return Lists.newArrayList();
        }
        List<HaimaConfig> configs = response.getData();
        if (configs.get(0) == null || CollectionUtils.isEmpty(configs.get(0).getContents())) {
            return Lists.newArrayList();
        }
        List<HaimaContent> contents = configs.get(0).getContents();
        List<String> spuProductIds = Lists.newArrayList();
        contents.forEach(content -> addSpuProductIds(spuProductIds, content));
        return spuProductIds.stream().filter(Objects::nonNull)
                .map(productId -> buildProduct(productId, ProductTypeEnum.GENERAL_SPU.getType()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private void addSpuProductIds(List<String> spuProductIds, HaimaContent content) {
        if (content == null) {
            return;
        }
        List<String> configProductIds = convertStr2List(content.getContentString(PRODUCT_ID));
        spuProductIds.addAll(configProductIds);
    }

    private ProductM buildProduct(String productId, int productType) {
        try {
            ProductM productM = new ProductM();
            productM.setProductId(Integer.parseInt(productId));
            productM.setProductType(productType);
            return productM;
        } catch (Exception e) {
            log.error("LeUnCoopShopUniverseInfoOpt build Product error", e);
        }
        return null;
    }

    private List<Integer> reverseList(List<Integer> source) {
        if (CollectionUtils.isEmpty(source)) {
            return Lists.newArrayList();
        }
        return IntStream.range(0, source.size())
                .mapToObj(i -> source.get(source.size() - i - 1))
                .collect(Collectors.toList());
    }

    private List<String> convertStr2List(String str) {
        if (StringUtils.isBlank(str)) {
            return Lists.newArrayList();
        }
        return Arrays.stream(str.split(","))
                .map(String::trim).collect(Collectors.toList());
    }

    private String convertList2Str(List<Integer> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        return StringUtils.join(list, ",");
    }

    @Data
    @VPointCfg
    public static class Config {

    }
}
