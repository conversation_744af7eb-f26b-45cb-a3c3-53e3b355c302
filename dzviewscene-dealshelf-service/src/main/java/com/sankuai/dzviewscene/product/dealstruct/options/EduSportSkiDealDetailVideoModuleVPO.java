package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.ability.videoModule.vpoints.DealDetailVideoModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.VideoModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/08/18 9:03 下午
 */
@VPointOption(name = "教培-运动培训-滑雪团购详情模块视频组件配置化变化点", description = "教培-运动培训-滑雪团购详情模块视频组件配置化变化点",code = EduSportSkiDealDetailVideoModuleVPO.CODE, isDefault = false)
public class EduSportSkiDealDetailVideoModuleVPO extends DealDetailVideoModuleVP<EduSportSkiDealDetailVideoModuleVPO.Config> {

    public static final String CODE = "EduSportSkiDealDetailVideoModuleVPO";

    private static final String COURSE_TARGET_ATTR_NAME = "course_target_ski";

    private static final String COSTUMIZED_COURSE_TARGET_ATTR_NAME = "customize_course_target_ski_detail";

    private static final String COMMON_COURSE_TARGET_ATTR_NAME = "course_target_ski_detail";

    private static final String CUSTOMIZED_COURSE_TARGET = "自定义课程目标";

    private static final String SKI_BOARD_TYPE_ATTR_NAME = "ski_type";

    private static final String TECH_LOCATION_ATTR_NAME = "tech_location";

    @Override
    public VideoModuleVO compute(ActivityCxt context, Param param, Config config) {
        List<AttrM> dealAttrs = param.getDealAttrs();
        String videoContent = getVideoContent(dealAttrs);
        if (StringUtils.isEmpty(videoContent)) {
            return null;
        }
        VideoModuleVO videoModuleVO = new VideoModuleVO();
        videoModuleVO.setContent(videoContent);
        VideoUrlModel videoUrlModel = getVideoUrlModel(config, dealAttrs);
        if (videoUrlModel == null) {
            return videoModuleVO;
        }
        videoModuleVO.setThumbnailURL(videoUrlModel.getThumbnailURL());
        videoModuleVO.setUrl(videoUrlModel.getVidelUrl());
        videoModuleVO.setDesc(config.getVideoDesc());
        return videoModuleVO;
    }

    /**
     * 根据雪板类型和授课场地获取配置的视频链接
     *@param
     *@return
     */
    private VideoUrlModel getVideoUrlModel(Config config, List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs) || config == null || CollectionUtils.isEmpty(config.getVideoUrlModels()) || isCustomizedCourseTarget(dealAttrs)) {
            return null;
        }
        String skiBoardType = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, SKI_BOARD_TYPE_ATTR_NAME);
        String techLocation = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, TECH_LOCATION_ATTR_NAME);
        String skiTypeAndLocation = skiBoardType + techLocation;
        return config.getVideoUrlModels().stream().filter(model -> StringUtils.isNotEmpty(model.getSkiTypeAndLocation()) && model.getSkiTypeAndLocation().equals(skiTypeAndLocation)).findFirst().orElse(null);
    }

    /**
     * 判断课程目标是否是自定义课程目标
     *@param
     *@return
     */
    private boolean isCustomizedCourseTarget(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return false;
        }
        String courseTarget = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, COURSE_TARGET_ATTR_NAME);
        return CUSTOMIZED_COURSE_TARGET.equals(courseTarget);
    }

    /**
     * 获取视频内容文案：当课程目标是"自定义课程目标"，展示自定义课程目标文案；当课程目标是"采用平台统一描述"，展示平台统一文案
     *@param
     *@return
     */
    private String getVideoContent(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        String customizedCourseTargetContent = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, COSTUMIZED_COURSE_TARGET_ATTR_NAME);
        String commonCourseTargetContent = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, COMMON_COURSE_TARGET_ATTR_NAME);
        return isCustomizedCourseTarget(dealAttrs) ? customizedCourseTargetContent : commonCourseTargetContent;
    }


    @Data
    @VPointCfg
    public static class Config {
        //备注信息
        private String videoDesc;
        //视频配置信息
        private List<VideoUrlModel> videoUrlModels;
    }

    @Data
    public static class VideoUrlModel {
        //雪板类型和场地信息
        private String skiTypeAndLocation;
        //视频链接
        private String videlUrl;
        //视频首图
        private String thumbnailURL;
    }
}

