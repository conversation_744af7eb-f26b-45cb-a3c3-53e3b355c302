package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.general.product.query.center.client.builder.model.DealBasicInfoBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 获取门店全量在线团单
 *
 * <AUTHOR>
 * @date :2023/8/1
 */
@Component
@Slf4j
public class PoiSaleDealGroupQueryHandler implements QueryHandler {

    private static final int QUERY_CENTER_LIMIT = 30;

    private static final int SDK_LIMIT = 100;

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.deal.id.mapper.query.gray.percent", defaultValue = "0")
    public static int idMapperQueryGrayPercent;

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<ProductGroupM> query(ActivityCxt ctx, String groupName, Map<String, Object> groupParams) {
        long dpPoiId = ParamsUtil.getLongSafely(ctx, ShelfActivityConstants.Params.dpPoiIdL);
        int platform = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform);
        boolean querySku = querySku(ctx, groupParams);
        return compositeAtomService.queryShopSaleDealIds(dpPoiId, platform).thenApply(dealIds -> {
            Cat.logEvent("PoiSaleDealGroupQueryHandler", "deal batchSize:" + getBatch(dealIds));
            //dealIds为点评侧id，美团环境需要转换一下
            dealIds = convertDealId(dpPoiId, dealIds, platform);
            Map<Long, List<String>> dealIds2SkuIds = getDealSkuIds(dealIds, platform, querySku);
            ProductGroupM productGroupM = new ProductGroupM();
            productGroupM.setProducts(buildProducts(dealIds, dealIds2SkuIds));
            productGroupM.setTotal(CollectionUtils.isEmpty(dealIds) ? 0 : dealIds.size());
            ctx.addParam(ShelfActivityConstants.Params.hasShopDeal, CollectionUtils.isNotEmpty(dealIds));
            return productGroupM;
        });
    }

    private Map<Long, List<String>> getDealSkuIds(List<Long> dealIds, int platform, boolean querySku){
        if(!querySku || CollectionUtils.isEmpty(dealIds)){
            return Maps.newHashMap();
        }
        List<List<Long>> partitions = Lists.partition(dealIds, QUERY_CENTER_LIMIT);
        List<CompletableFuture<QueryDealGroupListResult>> futures = Lists.newArrayList();
        for (List<Long> partition : partitions) {
            futures.add(compositeAtomService.queryByDealGroupIds(buildRequest(partition, platform)));
        }
        CompletableFuture<Void> allDoneFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        return allDoneFuture.thenApply(v -> {
            Map<Long, List<String>> dealIds2SkuIds = Maps.newHashMap();
            futures.stream().map(CompletableFuture::join).collect(Collectors.toList()).forEach(dealGroupDTOS -> {
                if(dealGroupDTOS == null || CollectionUtils.isEmpty(dealGroupDTOS.getList())){
                    return;
                }
                for (DealGroupDTO dealGroupDTO : dealGroupDTOS.getList()) {
                    if(dealGroupDTO == null || CollectionUtils.isEmpty(dealGroupDTO.getDeals())){
                        continue;
                    }
                    List<String> skuIds = dealGroupDTO.getDeals().stream().filter(dealGroupDealDTO -> dealGroupDealDTO.getDealId() != null && dealGroupDealDTO.getBasic() != null
                            && dealGroupDealDTO.getBasic().getStatus() == 1).map(dealGroupDealDTO -> String.valueOf(dealGroupDealDTO.getDealId())).collect(Collectors.toList());
                    //单sku不需要查
                    if(CollectionUtils.isEmpty(skuIds) || skuIds.size() == 1){
                        continue;
                    }
                    dealIds2SkuIds.put(PlatformUtil.isMT(platform) ? dealGroupDTO.getMtDealGroupId() : dealGroupDTO.getDpDealGroupId(), skuIds);
                }
            });
            return dealIds2SkuIds;
        }).join();
    }

    private QueryByDealGroupIdRequest buildRequest(List<Long> dealIds, int platform) {
        IdTypeEnum idTypeEnum = PlatformUtil.isMT(platform) ? IdTypeEnum.MT : IdTypeEnum.DP;
        return QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(dealIds), idTypeEnum)
                .dealBasicInfo(DealBasicInfoBuilder.builder().status())
                .build();
    }

    private boolean querySku(ActivityCxt activityContext, Map<String, Object> groupParams){
        try {
            ShopM shopM = activityContext.getParam(ShelfActivityConstants.Ctx.ctxShop);
            if(shopM == null){
                return false;
            }
            List<Integer> skuShopCategory = (List<Integer>) groupParams.get("skuShopCategory");
            if(CollectionUtils.isEmpty(skuShopCategory) || CollectionUtils.isEmpty(shopM.getBackCategory())){
                return false;
            }
            return shopM.getBackCategory().stream().anyMatch(skuShopCategory::contains);
        } catch (Exception e) {
            Cat.logError("PoiSaleDealGroupQueryHandler.querySku error", e);
            log.error("PoiSaleDealGroupQueryHandler.querySku error", e);
        }
        return false;
    }

    private int getBatch(List<Long> dealIds) {
        if (CollectionUtils.isEmpty(dealIds)) {
            return 0;
        }
        return dealIds.size() / 100 + ((dealIds.size() % 100 == 0) ? 0 : 1);
    }

    private List<Long> convertDealId(long dpPoiId, List<Long> dealIds, int platform) {
        if (CollectionUtils.isEmpty(dealIds)) {
            return Lists.newArrayList();
        }
        if (platform == VCPlatformEnum.DP.getType()) {
            return dealIds;
        }
        //灰度
        if (dpPoiId % 100 < idMapperQueryGrayPercent) {
            return convertDp2MTBySDK(dealIds);
        }
        return convertDp2MT(dealIds);
    }

    private List<Long> convertDp2MTBySDK(List<Long> dpDealIds) {
        List<List<Long>> partitions = Lists.partition(dpDealIds, SDK_LIMIT);
        List<CompletableFuture<Map<Long, Long>>> futures = partitions.stream()
                .map(compositeAtomService::getMtDealIdByDp)
                .collect(Collectors.toList());
        List<Long> mtDealIds = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .flatMap(future -> {
                            Map<Long, Long> result = future.join();
                            return result != null ? result.values().stream() : Stream.empty();
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())).join();
        Cat.logEvent("PoiSaleDealGroupQueryHandler", dpDealIds.size() == mtDealIds.size() ? "idMapper match perfect" : "idMapper mapper not perfect");
        return mtDealIds;
    }

    private List<Long> convertDp2MT(List<Long> dpDealIds) {
        List<DealGroupDTO> dealIdMappers = batchGetDealGroupDto(dpDealIds);
        if (CollectionUtils.isEmpty(dealIdMappers)) {
            return Lists.newArrayList();
        }
        Map<Long, Long> dp2MtDealId = dealIdMappers.stream().collect(Collectors.toMap(DealGroupDTO::getDpDealGroupId, DealGroupDTO::getMtDealGroupId, (v1, v2) -> v1));
        List<Long> mtDealIds = Lists.newArrayList();
        for (long dealId : dpDealIds) {
            Long mtDealId = dp2MtDealId.get(dealId);
            if (mtDealId != null) {
                mtDealIds.add(mtDealId);
            }
        }
        return mtDealIds;
    }

    private List<DealGroupDTO> batchGetDealGroupDto(List<Long> dealIds) {
        if (CollectionUtils.isEmpty(dealIds)) {
            return Lists.newArrayList();
        }
        List<List<Long>> partitions = Lists.partition(dealIds, QUERY_CENTER_LIMIT);
        Cat.logEvent("PoiSaleDealGroupQueryHandler", "queryCenter batchSize:" + getBatch(dealIds));
        List<CompletableFuture<List<DealGroupDTO>>> futures = Lists.newArrayList();
        for (List<Long> partition : partitions) {
            futures.add(compositeAtomService.batchGetMtDealIdByDp(partition));
        }
        CompletableFuture<Void> allDoneFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        return allDoneFuture.thenApply(v -> {
            List<List<DealGroupDTO>> dealGroupDTOSList = futures.stream().map(CompletableFuture::join).collect(Collectors.toList());
            List<DealGroupDTO> dealIdMapperList = Lists.newArrayList();
            for (List<DealGroupDTO> dealGroupDTOS : dealGroupDTOSList) {
                if (CollectionUtils.isNotEmpty(dealGroupDTOS)) {
                    dealIdMapperList.addAll(dealGroupDTOS);
                }
            }
            return dealIdMapperList;
        }).join();
    }

    private List<ProductM> buildProducts(List<Long> dealIds, Map<Long, List<String>> dealIds2SkuIds) {
        if (CollectionUtils.isEmpty(dealIds)) {
            return Lists.newArrayList();
        }
        return dealIds.stream().map(dealId -> {
            ProductM productM = new ProductM();
            productM.setProductId(dealId.intValue());
            productM.setProductType(ProductTypeEnum.DEAL.getType());
            productM.setSkuIdList(dealIds2SkuIds.get(dealId));
            return productM;
        }).collect(Collectors.toList());
    }

}
