package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 填充处理器(新框架使用)
 */
public interface PaddingHandler {

    /**
     * 填充一组商品
     *
     * @param ctx
     * @param productGroupM
     * @param params
     * @return
     */
    CompletableFuture<ProductGroupM> padding(ActivityCxt ctx, ProductGroupM productGroupM, Map<String, Object> params);

}
