package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints.SkuUniTitleVP;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/27 9:17 下午
 */
@VPointOption(name = "ku总标题默认变化点", description = "ku总标题默认变化点",code = DefaultSkuUniTitleVPO.CODE, isDefault = true)
public class DefaultSkuUniTitleVPO extends SkuUniTitleVP<DefaultSkuUniTitleVPO.Config> {

    public static final String CODE = "DefaultSkuUniTitleVPO";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        return null;
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
