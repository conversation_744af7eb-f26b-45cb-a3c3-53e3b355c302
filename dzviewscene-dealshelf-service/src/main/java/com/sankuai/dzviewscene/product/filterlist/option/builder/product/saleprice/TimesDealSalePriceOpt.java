package com.sankuai.dzviewscene.product.filterlist.option.builder.product.saleprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePriceVP;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.card.CardFetcher;
import com.sankuai.dzviewscene.product.utils.CardPromoUtils;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.utils.ShelfErrorUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;

/**
 * @author: created by hang.yu on 2024/3/5 20:10
 */
@VPointOption(name = "多次卡团单价格展示",
        description = "多次卡团单价格展示",
        code = "TimesDealSalePriceOpt")
public class TimesDealSalePriceOpt extends ProductSalePriceVP<Void> {

    private static final String PRE_PRICE = "¥";

    @Override
    public String compute(ActivityCxt context, Param param, Void unused) {
        if (param.getProductM().getSalePrice() == null) {
            return null;
        }
        // 团购
        return getSalePrice(param.getProductM().getSalePrice());
    }

    private String getSalePrice(BigDecimal salePrice) {
        return PRE_PRICE + salePrice.stripTrailingZeros().toPlainString();
    }

}
