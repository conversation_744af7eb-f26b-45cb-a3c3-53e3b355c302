package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class LinkedValueProcessor extends AbstractValueProcessor {

    @Override
    public List<String> convertDisplayValues(String value, ValueConfig valueConfig, Map<String, String> name2ValueMap) {
        if (validate(value, valueConfig, name2ValueMap)) {
            return Lists.newArrayList(value);
        }

        String linkedValue = name2ValueMap.get(valueConfig.getLinkedMap().get(value).getKey());

        List<String> linkedValueList = valueConfig.getIsList() ?
                convertListByStr(linkedValue) : Lists.newArrayList(linkedValue);
        if (ObjectUtils.isEmpty(linkedValueList)) {
            return Lists.newArrayList(value);
        }
        return linkedValueList.stream().map(v -> {
            v = value + v;
            if (!ObjectUtils.isEmpty(valueConfig.getFormat())) {
                v = String.format(valueConfig.getFormat(), v);
            }
            return v;
        }).filter(v -> !ObjectUtils.isEmpty(v)).collect(Collectors.toList());
    }

    private boolean validate(String value, ValueConfig valueConfig, Map<String, String> name2ValueMap) {
        return ObjectUtils.isEmpty(value) || ObjectUtils.isEmpty(valueConfig)
                || ObjectUtils.isEmpty(name2ValueMap)
                || ObjectUtils.isEmpty(valueConfig.getLinkedMap())
                || ObjectUtils.isEmpty(valueConfig.getLinkedMap().get(value))
                || ObjectUtils.isEmpty(valueConfig.getLinkedMap().get(value).getKey())
                || ObjectUtils.isEmpty(name2ValueMap.get(valueConfig.getLinkedMap().get(value).getKey()));
    }
}
