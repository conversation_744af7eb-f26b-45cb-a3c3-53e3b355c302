package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.moreText;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListMoreTextVP;
import lombok.Data;

/**
 * <AUTHOR>
 **/
@VPointOption(name = "更多跳转文案内容选项", description ="更多跳转文案内容", code = DealFilterConfigMoreJumpTextOpt.CODE)
public class DealFilterConfigMoreJumpTextOpt extends DealFilterListMoreTextVP<DealFilterConfigMoreJumpTextOpt.Config> {

    public static final String CODE = "DealFilterConfigMoreJumpTextOpt";

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Config config) {
        if(param.getDealCount() <= config.getDefaultDisplayCount()) {
            return null;
        }
        return String.format(config.getMoreJumpText(), param.getDealCount() - config.getDefaultDisplayCount());
    }

    @Data
    @VPointCfg
    public static class Config {
        private String moreJumpText;
        private int defaultDisplayCount = 3;
    }
}
