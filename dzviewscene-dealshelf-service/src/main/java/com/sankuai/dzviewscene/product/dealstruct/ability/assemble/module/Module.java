package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module;

/**
 * created by zhangzhiyuan04 in 2021/12/14
 */
public enum Module {
    DESC("desc", "团详补充描述模块"),
    PRICE("price", "团详价格模块"),
    TITLE("title", "团详标题模块"),
    SERVICE_LIST("serviceList", "团详服务列表模块"),
    STRUCT_ATTR("structAttr", "团详结构化属性列表模块"),
    STRUCT_ATTRS_GROUP("structAttrsGroup", "团详结构化属性列表组模块");

    Module(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    private String name;
    private String desc;
}
