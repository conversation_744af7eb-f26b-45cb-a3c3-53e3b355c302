package com.sankuai.dzviewscene.product.filterlist.option.builder.product.remark;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePriceDescVP;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

@VPointOption(name = "次数",
        description = "返回 /3次",
        code = "TimesDealRemarkFilterListOpt")
public class TimesDealRemarkFilterListOpt extends ProductSalePriceDescVP<TimesDealRemarkFilterListOpt.Config> {

    @Override
    public String compute(ActivityCxt context, ProductSalePriceDescVP.Param param, TimesDealRemarkFilterListOpt.Config config) {
        // 判断交易类型是团购次卡
        if (!param.getProductM().isTimesDeal()) {
            return null;
        }
        if (StringUtils.isBlank(config.getTimesKey()) || StringUtils.isBlank(config.getRemarkTemplate())) {
            return null;
        }
        String attr = param.getProductM().getAttr(config.getTimesKey());
        if (StringUtils.isBlank(attr) || !NumberUtils.isDigits(attr)) {
            return null;
        }
        return String.format(config.getRemarkTemplate(), attr);
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 获取次数的key
         */
        private String timesKey = "sys_multi_sale_number";

        /**
         * 返回描述的模板
         */
        private String remarkTemplate = "/%s次";

    }

}
