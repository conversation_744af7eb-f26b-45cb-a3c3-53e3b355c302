package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor;

import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;

import java.util.List;
import java.util.Map;

public interface ValueProcessor {
    List<String> process(ValueConfig valueConfig, Map<String, String> name2ValueMap,Object data);

    List<String> convertDisplayValues(String value,ValueConfig valueConfig,Map<String, String> name2ValueMap);
}
