package com.sankuai.dzviewscene.product.ability.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.InterceptHandlerVP;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@VPointOption(name = "斗斛实验拦截",
        description = "根据斗斛实验拦截货架",
        code = DouHuInterceptOpt.CODE)
public class DouHuInterceptOpt extends InterceptHandlerVP<DouHuInterceptOpt.Config> {

    public static final String CODE = "DouHuInterceptOpt";

    @Override
    public Boolean compute(ActivityCxt context, Param param, Config config) {
        return CollectionUtils.isNotEmpty(config.getSkipExps())
                && DouHuUtils.hitAnySk(context.getParam(ShelfActivityConstants.Params.douHus), config.getSkipExps());
    }

    @Data
    @VPointCfg
    public static class Config {

        /**
         * 配置斗斛实验跳过
         */
        private List<String> skipExps;
    }
}
