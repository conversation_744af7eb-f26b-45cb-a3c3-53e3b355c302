package com.sankuai.dzviewscene.product.dealstruct.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/12/12 5:29 下午
 */
@MobileDo(id = 0x2c16ac06)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DealSkuItemConfig implements Serializable {

    private static final long serialVersionUID = 2998932601238133346L;

    @MobileDo.MobileField(key = 0x4d98)
    private int limit;

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }
}