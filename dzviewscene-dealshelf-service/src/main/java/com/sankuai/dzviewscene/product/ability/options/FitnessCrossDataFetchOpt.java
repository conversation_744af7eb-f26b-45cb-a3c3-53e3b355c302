package com.sankuai.dzviewscene.product.ability.options;

import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Sets;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzcard.fulfill.api.dto.MemberCardDTO;
import com.sankuai.dzcard.fulfill.core.enums.MemberCardStatusEnum;
import com.sankuai.dzcard.supply.enums.RightPackageTypeEnum;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreAsyncHandlerVP;
import com.sankuai.dzviewscene.product.enums.FitnessCrossIdentityEnum;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

@VPointOption(name = "健身通查询用户是否有开卡记录取数", description = "健身通查询用户是否有开卡记录取数", code = FitnessCrossDataFetchOpt.CODE)
public class FitnessCrossDataFetchOpt extends PreAsyncHandlerVP<FitnessCrossDataFetchOpt.Config> {

    public static final String CODE = "FitnessCrossDataFetchOpt";

    /**
     * 固定参数：卡状态
     */
    private static final Set<Integer> CARD_STATUS_SET = Sets.newHashSet(MemberCardStatusEnum.CREATED.getStatus(), MemberCardStatusEnum.UN_ACTIVE.getStatus(), MemberCardStatusEnum.NORMAL.getStatus());

    /**
     * 固定参数：卡类型
     */
    private static final int CARD_RIGHT_PACKAGE_TYPE = RightPackageTypeEnum.FITNESS_COUPON_PACKAGE.getCode();

    @Resource
    private CompositeAtomService service;

    @Override
    public CompletableFuture<Object> compute(ActivityCxt context, Param param, Config config) {
        // 获取健身通身份 返回对象
        return CompletableFuture.completedFuture(new Context(fetchFitnessCrossIdentity(context)));
    }

    /**
     * 获取健身通身份
     */
    private FitnessCrossIdentityEnum fetchFitnessCrossIdentity(ActivityCxt context) {
        // 获取userId
        long userId = 0;
        int platform = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.platform);
        if (VCPlatformEnum.DP.getType() == platform) {
            userId = ParamsUtil.getLongSafely(context, ShelfActivityConstants.Params.dpUserId);
        } else if (VCPlatformEnum.MT.getType() == platform) {
            userId = ParamsUtil.getLongSafely(context, ShelfActivityConstants.Params.mtUserId);
        }
        // 异常case，没有获取到userId，或者platform不是1或者2，游客状态
        if (userId <= 0) {
            return FitnessCrossIdentityEnum.TOURIST;
        }

        // 查询手机号码
        String mobile = service.queryMobile(userId, platform).join();

        // 查询卡信息
        CompletableFuture<List<MemberCardDTO>> future = service.queryFitnessCrossCardListByUserId(mobile, platform, CARD_STATUS_SET, CARD_RIGHT_PACKAGE_TYPE);
        if (future == null) {
            // 查询失败：游客
            return FitnessCrossIdentityEnum.TOURIST;
        }

        List<MemberCardDTO> cardDTOS = future.join();
        if (CollectionUtils.isEmpty(cardDTOS)) {
            // 新客
            return FitnessCrossIdentityEnum.NOT_PAY;
        }

        Set<Integer> hasPayStatus = Sets.newHashSet(
                MemberCardStatusEnum.CREATED.getStatus(),
                MemberCardStatusEnum.UN_ACTIVE.getStatus(),
                MemberCardStatusEnum.NORMAL.getStatus());
        if (cardDTOS.stream().anyMatch(cardDTO -> hasPayStatus.contains(cardDTO.getStatus()))) {
            // 老客
            return FitnessCrossIdentityEnum.HAS_PAY;
        }

        // 游客兜底
        return FitnessCrossIdentityEnum.TOURIST;
    }

    @Data
    @VPointCfg
    public static class Config {
    }

    /**
     * 健身通预处理数据上下文
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Context {

        /**
         * 卡购买身份枚举
         */
        private FitnessCrossIdentityEnum identityEnum;

    }

}
