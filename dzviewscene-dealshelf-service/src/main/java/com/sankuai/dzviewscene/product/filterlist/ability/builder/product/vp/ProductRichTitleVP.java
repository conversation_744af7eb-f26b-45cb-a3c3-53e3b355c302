package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import lombok.Builder;
import lombok.Data;

@VPoint(
        name = "团单筛选富文本列表展示名称", description = "团单筛选富文本列表展示名称", code = ProductRichTitleVP.CODE,
        ability = DealListBuilder.CODE
)
public abstract class ProductRichTitleVP<T> extends PmfVPoint<String, ProductRichTitleVP.Param, T> {

    public static final String CODE = "ProductRichTitleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;
    }
}