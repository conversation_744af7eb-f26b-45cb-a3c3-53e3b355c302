package com.sankuai.dzviewscene.product.factory.impl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleItem;
import com.sankuai.dzviewscene.product.factory.AbstractDealCategoryStrategy;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("pipeCategoryStrategyImpl")
public class PipeCategoryStrategyImpl extends AbstractDealCategoryStrategy {

    @Override
    public List<ModuleItem> getModuleList(ActivityCxt activityCxt, DealDetailAssembleCfg assembleCfg) {
        if (CollectionUtils.isEmpty(assembleCfg.getNewModuleList())) {
            return assembleCfg.getModuleList();
        }
        //如果命中小程序，走new，不命中，走Old
        String mpSource = ParamsUtil.getStringSafely(activityCxt, ProductDetailActivityConstants.Params.mpSource);
        if (PlatformUtil.isAnyXcx(mpSource)) {
            return assembleCfg.getNewModuleList();
        }
        return assembleCfg.getModuleList();
    }
}
