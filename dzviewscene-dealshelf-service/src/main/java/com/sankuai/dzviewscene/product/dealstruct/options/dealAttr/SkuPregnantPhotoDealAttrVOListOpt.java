package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/10
 */
@VPointOption(name = "孕婴童摄影、快照、月子根据团单属性构造DealAttrVO列表变化点-太极团单", description = "孕婴童摄影、快照、月子根据团单属性构造DealAttrVO列表变化点", code = SkuPregnantPhotoDealAttrVOListOpt.CODE, isDefault = false)
public class SkuPregnantPhotoDealAttrVOListOpt extends DealAttrVOListVP<SkuPregnantPhotoDealAttrVOListOpt.Config> {

    public static final String CODE = "SkuPregnantPhotoDealAttrVOListOpt";

    public static final String PEOPLE_NUM_UNIT = "人";

    public static final List<String> KEY_WITH_LIST_VALUE = Lists.newArrayList("ProvideClothingObjects", "photoBackground");


    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        if (config == null || param == null) {
            return null;
        }
        List<AttrM> attrs = new ArrayList<>();
        //1.获取团单属性
        List<AttrM> dealAttrs = param.getDealAttrs();
        if (CollectionUtils.isNotEmpty(dealAttrs)) {
            attrs.addAll(dealAttrs);
        }
        //2.构造DealDetailStructAttrModuleVO列表
        return config.getAttrListGroupModels().stream().map(model -> buildDealDetailStructAttrModuleVOList(model, attrs)).filter(model -> model != null).collect(Collectors.toList());
    }

    private DealDetailStructAttrModuleGroupModel buildDealDetailStructAttrModuleVOList(AttrListGroupModel attrListGroupModel, List<AttrM> attrs) {
        if (attrListGroupModel == null || CollectionUtils.isEmpty(attrListGroupModel.getAttrModelList()) || CollectionUtils.isEmpty(attrs)) {
            return null;
        }
        List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOS = attrListGroupModel.getAttrModelList().stream().map(model -> buildDealDetailStructAttrModuleVO(model, attrs)).filter(model -> model != null).collect(Collectors.toList());
        return buildDealDetailStructAttrModuleGroupModel(attrListGroupModel, dealDetailStructAttrModuleVOS);
    }

    private DealDetailStructAttrModuleGroupModel buildDealDetailStructAttrModuleGroupModel(AttrListGroupModel attrListGroupModel, List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOS) {
        if (CollectionUtils.isEmpty(dealDetailStructAttrModuleVOS)) {
            return null;
        }
        DealDetailStructAttrModuleGroupModel dealDetailStructAttrModuleGroupModel = new DealDetailStructAttrModuleGroupModel();
        dealDetailStructAttrModuleGroupModel.setGroupName(attrListGroupModel.getGroupName());
        dealDetailStructAttrModuleGroupModel.setGroupSubtitle(attrListGroupModel.getGroupSubtitle());
        dealDetailStructAttrModuleGroupModel.setDealDetailStructAttrModuleVOS(dealDetailStructAttrModuleVOS);
        return dealDetailStructAttrModuleGroupModel;
    }

    private DealDetailStructAttrModuleVO buildDealDetailStructAttrModuleVO(MergeSortMapJoinFilterAttrModel attrModel, List<AttrM> attrs) {
        if (CollectionUtils.isEmpty(attrs) || attrModel == null) {
            return null;
        }
        //属性值获取 如果有format则属性值会进行format转换
        List<String> attrValues = getAttrValues(attrModel, attrs);
        //获取排序/过滤/映射模型
        List<AttrValueMapModel> attrValueMapModels = attrModel.getAttrValueMapModels();
        //属性值排序
        attrValues = sortAttrValues(attrValues, attrValueMapModels);
        //属性值映射 + 过滤（映射对象为null即被过滤）
        attrValues = mapAndFilterAttrValues(attrValues, attrValueMapModels);
        //全部属性值过滤
        attrValues = filterAllAttrValues(attrValues, attrModel);
        //属性值拼接
        attrValues = joinAttrValue(attrValues, attrModel);
        return buildDealDetailStructAttrModuleVO(attrModel.getDisplayName(), attrValues, attrModel.getIcon());
    }

    private DealDetailStructAttrModuleVO buildDealDetailStructAttrModuleVO(String displayName, List<String> attrValues, String icon) {
        if (CollectionUtils.isEmpty(attrValues)) {
            return null;
        }
        DealDetailStructAttrModuleVO dealDetailStructAttrModuleVO = new DealDetailStructAttrModuleVO();
        dealDetailStructAttrModuleVO.setAttrName(displayName);
        dealDetailStructAttrModuleVO.setAttrValues(attrValues);
        dealDetailStructAttrModuleVO.setIcon(icon);
        return dealDetailStructAttrModuleVO;
    }

    private List<String> mapAndFilterAttrValues(List<String> attrValues, List<AttrValueMapModel> attrValueMapModels) {
        if (CollectionUtils.isEmpty(attrValueMapModels) || CollectionUtils.isEmpty(attrValues)) {
            return attrValues;
        }
        return attrValues.stream().map(value -> getAttrDisplayValueByAttrValue(attrValueMapModels, value)).filter(value -> StringUtils.isNotEmpty(value)).collect(Collectors.toList());
    }

    private List<String> filterAllAttrValues(List<String> attrValues, MergeSortMapJoinFilterAttrModel attrModel) {
        if (CollectionUtils.isEmpty(attrValues) || attrModel == null) {
            return attrValues;
        }
        // 判断是否过滤等值属性
        boolean shouldFilter = Optional.ofNullable(attrModel.getFilterEqualAttr()).orElse(false);
        return shouldFilter ? attrValues.stream().distinct().collect(Collectors.toList()) : attrValues;
    }

    private List<String> joinAttrValue(List<String> attrValues, MergeSortMapJoinFilterAttrModel model) {
        if (model == null || model.getSeperator() == null || CollectionUtils.isEmpty(attrValues)) {
            return attrValues;
        }
        return Lists.newArrayList(StringUtils.join(attrValues, model.getSeperator()));
    }

    private String getAttrDisplayValueByAttrValue(List<AttrValueMapModel> attrValueMapModels, String attrValue) {
        AttrValueMapModel attrValueMapModel = getAttrValueMapModelByAttrValue(attrValueMapModels, attrValue);
        if (attrValueMapModel == null) {
            return attrValue;
        }
        return attrValueMapModel.getDisplayValue();
    }

    private List<String> sortAttrValues(List<String> attrValues, List<AttrValueMapModel> attrValueMapModels) {
        if (CollectionUtils.isNotEmpty(attrValueMapModels) || CollectionUtils.isEmpty(attrValues)) {
            return attrValues;
        }
        return attrValues.stream().sorted(Comparator.comparingInt(o -> getAttrPriority(attrValueMapModels, o))).collect(Collectors.toList());
    }

    private int getAttrPriority(List<AttrValueMapModel> attrValueMapModels, String attrValue) {
        AttrValueMapModel attrValueMapModel = getAttrValueMapModelByAttrValue(attrValueMapModels, attrValue);
        if (attrValueMapModel == null || attrValueMapModel.getPriority() == 0) {
            return Integer.MAX_VALUE;
        }
        return attrValueMapModel.getPriority();
    }

    private AttrValueMapModel getAttrValueMapModelByAttrValue(List<AttrValueMapModel> attrValueMapModels, String attrValue) {
        if (CollectionUtils.isEmpty(attrValueMapModels) || StringUtils.isEmpty(attrValue)) {
            return null;
        }
        return attrValueMapModels.stream().filter(model -> attrValue.equals(model.getAttrValue())).findFirst().orElse(null);
    }

    private List<String> getAttrValues(MergeSortMapJoinFilterAttrModel model, List<AttrM> dealAttrs) {
        return model.getAttrNameList().stream().flatMap(name -> getValueWithFormat(dealAttrs, name, model.getAttrFormatModels()).stream())
                .filter(value -> StringUtils.isNotEmpty(value)).collect(Collectors.toList());
    }

    private List<String> getValueWithFormat(List<AttrM> dealAttrs, String attrName, List<AttrFormatModel> attrFormatModels) {
        List<String> values;

        //孕妇摄影特殊逻辑-可拍交付物属性
        if (StringUtils.equals("FinishedDeliverables", attrName)) {
            values = getAttrValueByFinishedDeliverables(dealAttrs, attrName);
        } else if (KEY_WITH_LIST_VALUE.contains(attrName)) {
            values = getAttrValueByListValue(dealAttrs, attrName);
        } else if (StringUtils.equals("shootpersoncount", attrName) && StringUtils.isNotEmpty(DealDetailUtils.findAttrValue(dealAttrs, "class_people_restriction"))) {
            return DealDetailUtils.splicedName(dealAttrs);
        } else {
            values = DealDetailUtils.getAttrValueByAttrName(dealAttrs, attrName);
        }
        if (CollectionUtils.isEmpty(values)) {
            return Lists.newArrayList();
        }
        // 太极团单特殊逻辑——服装
        if (StringUtils.equals("shootpersoncount", attrName)) {
            values = getAttrValueByShootPersonCount(values);
        }
        //全家福/亲子照特殊逻辑 拍摄人数理想效是：2～3人
        if ("minpeoplenum".equals(attrName) && !values.isEmpty() && values.get(0).endsWith(PEOPLE_NUM_UNIT)) {
            String minPeopleNumStr = values.get(0);
            String newValue = minPeopleNumStr.substring(0, minPeopleNumStr.length() - 1);
            values.set(0, newValue);
        }
        AttrFormatModel attrFormatModel = getAttrFormatMapModelByAttrValue(attrFormatModels, attrName);
        if (attrFormatModel == null) {
            return values;
        }
        //过滤：将attrName2FilteredAttrValueMap中key作为属性名查询到的属性值为value时不展示该属性
        boolean isFiltered = isFiltered(attrFormatModel.getAttrName2FilteredAttrValueMap(), dealAttrs);
        if (isFiltered) {
            return Lists.newArrayList();
        }
        List<String> result = Lists.newArrayList();

        for (String value : values) {
            // 将属性值按照指定字符分割返回多个
            if (StringUtils.isNotBlank(attrFormatModel.getAttrValueSeperator()) && StringUtils.isEmpty(attrFormatModel.getDisplayFormat())) {
                result.addAll(Lists.newArrayList(value.split(attrFormatModel.getAttrValueSeperator())));
                continue;
            }
            //补集构造模型
            if (attrFormatModel.getComplementarySetBuilderModel() != null) {
                value = addComplementarySet(attrFormatModel.getComplementarySetBuilderModel(), value);
            }
            //将属性值中的某个字符串替换为另一个字符串
            if (attrFormatModel.getAttrValueReplaceModel() != null && attrFormatModel.getAttrValueReplaceModel().getStr() != null && attrFormatModel.getAttrValueReplaceModel().getPreStr() != null) {
                value = value.replaceAll(attrFormatModel.getAttrValueReplaceModel().getPreStr(), attrFormatModel.getAttrValueReplaceModel().getStr());
            }
            //将属性值format
            if (StringUtils.isNotEmpty(attrFormatModel.getDisplayFormat())) {
                // 统计属性值个数并将个数拼接入字符串
                if (attrFormatModel.getDisplayCount() != null && attrFormatModel.getDisplayCount() && StringUtils.isNotBlank(attrFormatModel.getAttrValueSeperator())) {
                    value = String.format(attrFormatModel.getDisplayFormat(), value.split(attrFormatModel.getAttrValueSeperator()).length, value);
                } else {
                    value = String.format(attrFormatModel.getDisplayFormat(), value);
                }
            }
            result.add(value);
        }
        return result;
    }

    /**
     * 孕妇摄影特殊逻辑-获取可拍交付物拼接后attrValue
     *
     * @param dealAttrs
     * @param name
     * @return
     */
    private List<String> getAttrValueByFinishedDeliverables(List<AttrM> dealAttrs, String name) {
        if (CollectionUtils.isEmpty(dealAttrs) || StringUtils.isEmpty(name)) {
            return null;
        }
        List<FinishedDeliverableModel> finishedDeliverableModels = getFinishedDeliverableModelList(dealAttrs, name);
        if (CollectionUtils.isEmpty(finishedDeliverableModels)) {
            return null;
        }
        return getFinishedDeliverableStrList(finishedDeliverableModels);
    }

    private List<String> getFinishedDeliverableStrList(List<FinishedDeliverableModel> finishedDeliverableModels) {
        List<String> finishedDeliverableStrList = Lists.newArrayList();
        for (int i = 0; i < finishedDeliverableModels.size(); i++) {
            FinishedDeliverableModel finishedDeliverableModel = finishedDeliverableModels.get(i);
            if (StringUtils.isEmpty(finishedDeliverableModel.getFinishedProductName()) || StringUtils.isEmpty(finishedDeliverableModel.getQuantity())) {
                return null;
            }
            finishedDeliverableStrList.add(finishedDeliverableModel.getFinishedProductName() + "×" + finishedDeliverableModel.getQuantity());
        }
        return finishedDeliverableStrList;
    }

    private List<FinishedDeliverableModel> getFinishedDeliverableModelList(List<AttrM> dealAttrs, String name) {
        AttrM attrM = dealAttrs.stream().filter(attr -> name.equals(attr.getName())).findFirst().orElse(null);
        if (attrM == null || StringUtils.isEmpty(attrM.getValue())) {
            return null;
        }
        return JSONObject.parseArray(attrM.getValue(), FinishedDeliverableModel.class);
    }

    /**
     * 在原属性值基础上加上补集。例如"装备"这一属性，上单页面该属性可以选择雪板、雪杖、雪服、学鞋、头盔、雪镜、护具、手套中的多个，商家选择了雪板、雪杖和雪服，供应链传过来的属性值是"雪板、雪杖、雪服"，产品希望在c端展示为："包含雪板、雪杖、雪服，不包含学鞋、头盔、雪镜、护具、手套"
     *
     * @param
     * @return
     */
    private String addComplementarySet(ComplementarySetBuilderModel complementarySetBuilderModel, String value) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.SkuPregnantPhotoDealAttrVOListOpt.addComplementarySet(SkuPregnantPhotoDealAttrVOListOpt$ComplementarySetBuilderModel,String)");
        if (complementarySetBuilderModel == null || StringUtils.isEmpty(complementarySetBuilderModel.getSeperator()) || CollectionUtils.isEmpty(complementarySetBuilderModel.getUniversalSet())) {
            return value;
        }
        String seperator = complementarySetBuilderModel.getSeperator();
        String prevalueFormat = complementarySetBuilderModel.getPreValueFormat();
        String complementaryValueFormat = complementarySetBuilderModel.getComplementaryValueFormat();
        List<String> originalSet = StringUtils.isEmpty(value) ? Lists.newArrayList() : Lists.newArrayList(value.split(seperator));
        List<String> complementarySet = complementarySetBuilderModel.getUniversalSet();
        complementarySet.removeAll(originalSet);
        String prevalue = prevalueFormat == null || value == null ? StringUtils.EMPTY : String.format(prevalueFormat, value);
        if (CollectionUtils.isEmpty(complementarySet)) {
            return prevalue;
        }
        String complementaryValue = complementaryValueFormat == null ? StringUtils.EMPTY : String.format(complementaryValueFormat, StringUtils.join(complementarySet, seperator));
        return prevalue + complementaryValue;
    }

    private boolean isFiltered(Map<String, String> attrName2FilteredAttrValueMap, List<AttrM> dealAttrs) {
        //没有配置该模型时，不进行过滤处理
        if (MapUtils.isEmpty(attrName2FilteredAttrValueMap)) {
            return false;
        }
        return attrName2FilteredAttrValueMap.entrySet().stream().map(entry -> {
            String value = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, entry.getKey());
            //该属性值为null或者和期望值不同时，进行过滤处理
            if (value == null) {
                return true;
            }
            return !value.equals(entry.getValue());
        }).filter(Boolean::booleanValue).findFirst().orElse(false);
    }

    private AttrFormatModel getAttrFormatMapModelByAttrValue(List<AttrFormatModel> attrFormatModels, String attrName) {
        if (CollectionUtils.isEmpty(attrFormatModels) || StringUtils.isEmpty(attrName)) {
            return null;
        }
        return attrFormatModels.stream().filter(model -> attrName.equals(model.getAttrName())).findFirst().orElse(null);
    }

    private List<String> getAttrValueByShootPersonCount(List<String> values) {
        if (values.size() == 0) {
            return values;
        } else if (values.size() == 1){
            Integer num = extractNumber(values.get(0));
            if (num > 0) {
                return Lists.newArrayList(String.format("%s人", num));
            }
        }else {
            List<Integer> intValues = values.stream()
                    .map(SkuPregnantPhotoDealAttrVOListOpt::extractNumber)
                    .collect(Collectors.toList());
            int max = Collections.max(intValues);
            int min = Collections.min(intValues);
            return Lists.newArrayList(String.format("%d～%d人", min, max));
        }
        return Lists.newArrayList();
    }

    private static Integer extractNumber(String str) {
        List<Integer> numbers = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        for (char c : str.toCharArray()) {
            if (Character.isDigit(c)) {
                sb.append(c);
            } else if (sb.length() > 0) {
                numbers.add(Integer.parseInt(sb.toString()));
                sb.setLength(0);
            }
        }
        if (sb.length() > 0) {
            numbers.add(Integer.parseInt(sb.toString()));
        }
        return numbers.get(0);
    }

    private List<String> getAttrValueByListValue(List<AttrM> dealAttrs, String attrName) {
        String valueStr = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, attrName);
        List<String> valueList = JSON.parseArray(valueStr, String.class);
        if (CollectionUtils.isEmpty(valueList)) {
            return Lists.newArrayList();
        }
        String value = String.join("、", valueList);
        return Lists.newArrayList(value);
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<AttrListGroupModel> attrListGroupModels;
    }

    @Data
    public static class AttrListGroupModel {
        private List<MergeSortMapJoinFilterAttrModel> attrModelList;
        private String groupName;
        private String groupSubtitle;
    }

    /**
     * 融合命名/排序/映射/拼接/过滤功能的属性配置模型
     */
    @Data
    public static class MergeSortMapJoinFilterAttrModel {
        //属性展示名称
        private String displayName;
        //属性名列表
        private List<String> attrNameList;
        //多个属性之间的拼接符
        private String seperator;
        //属性值 - 展示值映射 ； 展示顺序按照map中的展示顺序
        private List<AttrValueMapModel> attrValueMapModels;
        //属性名 - 展示format映射
        private List<AttrFormatModel> attrFormatModels;
        //图标
        private String icon;
        // 是否过滤等值属性
        private Boolean filterEqualAttr;
    }

    @Data
    public static class AttrValueReplaceModel {
        private String preStr;
        private String str;
    }

    @Data
    public static class AttrValueMapModel {
        private String attrValue;
        private String displayValue;
        //优先级从1开始
        private int priority;
    }

    /**
     * 补集构造模型。例如"装备"这一属性，上单页面该属性可以选择雪板、雪杖、雪服、学鞋、头盔、雪镜、护具、手套中的多个，商家选择了雪板、雪杖和雪服，供应链传过来的属性值是"雪板、雪杖、雪服"，产品希望在c端展示为："包含雪板、雪杖、雪服，不包含学鞋、头盔、雪镜、护具、手套"
     *
     * @param
     * @return
     */
    @Data
    public static class ComplementarySetBuilderModel {
        //分隔符，属性按照该分隔符分开后得到集合列表
        private String seperator;
        //全集列表
        private List<String> universalSet;
        //原集属性值构造format
        private String preValueFormat;
        //补集属性值构造format
        private String complementaryValueFormat;
    }

    @Data
    public static class AttrFormatModel {
        private String attrName;
        private String displayFormat;
        // 在返回结果里展示属性值的总数
        private Boolean displayCount;
        // 分割属性值的字符串
        private String attrValueSeperator;
        //将属性值中的指定字符串替换成另一个字符串
        private AttrValueReplaceModel attrValueReplaceModel;
        //过滤模型：只有将key作为属性名查询到的属性值为value时才展示该属性
        private Map<String, String> attrName2FilteredAttrValueMap;
        //补集构造模型
        private ComplementarySetBuilderModel complementarySetBuilderModel;
    }

    // 成品交付物
    @Data
    public static class FinishedDeliverableModel {
        //成品名称
        @JSONField(name = "FinishedProductName")
        private String finishedProductName;
        //成品数量
        private String quantity;
        //成品单位：个
        private String unit;
    }
}
