package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productrichtags;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductRichTagsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wuwenqiang
 * @create: 2024-06-17
 * @description: LE未合作推荐理由
 */
@VPointOption(name = "LE未合作推荐理由",
        description = "LE未合作推荐理由，当前支持教育、婚摄行业",
        code = EduRecommendReasonRichTagsOpt.CODE)
public class EduRecommendReasonRichTagsOpt extends ProductRichTagsVP<EduRecommendReasonRichTagsOpt.Config> {

    public static final String CODE = "EduRecommendReasonRichTagsOpt";

    @Override
    public List<IconRichLabelVO> compute(ActivityCxt activityCxt, Param param, Config config) {
        if (param.getProductM() == null) {
            return Lists.newArrayList();
        }
        ProductM productM = param.getProductM();
        // 标品处理
        if (productM.getProductType() == ProductTypeEnum.GENERAL_SPU.getType()) {
            return buildRichTags(config.getSpuAttrCfg(), productM, config.isShowSpecificTag());
        }
        // 团单处理
        else if (productM.getProductType() == ProductTypeEnum.DEAL.getType()) {
            return buildRichTags(config.getDealAttrCfg(), productM, config.isShowSpecificTag());
        }
        return Lists.newArrayList();
    }

    // 仅展示单个标签
    private List<IconRichLabelVO> buildRichTags(List<TagCfg> tagCfgs, ProductM productM, boolean showSpecificTag) {
        if (CollectionUtils.isEmpty(tagCfgs)) {
            return Lists.newArrayList();
        }
        List<IconRichLabelVO> iconRichLabelVOS = Lists.newArrayList();
        for (TagCfg tagCfg : tagCfgs) {
            if (CollectionUtils.isEmpty(tagCfg.getAttrKeys()) && CollectionUtils.isEmpty(tagCfg.getAttrVals())) {
                continue;
            }
            List<String> attrValues;
            // 如果showSpecificTag为true，则直接获取标签配置中的属性值列表，否则获取标签配置中的属性键列表，并映射到属性值列表中。
            if (showSpecificTag) {
                attrValues = tagCfg.getAttrVals().stream().filter(attrValue -> StringUtils.isNotBlank(attrValue)).collect(Collectors.toList());
                attrValues = getPhotoAttrValues(productM, tagCfg, attrValues);
            } else {
                attrValues = tagCfg.getAttrKeys().stream()
                        .map(attrKey -> getAttrVal(attrKey, productM)).filter(attrValue -> StringUtils.isNotBlank(attrValue)).collect(Collectors.toList());
            }
            String tagValue = jointAttrValues(attrValues, tagCfg.getDelimiter());
            IconRichLabelVO iconRichLabel = buildIconRichTag(tagValue, tagCfg);
            if (iconRichLabel != null && iconRichLabel.getText() != null && StringUtils.isNotBlank(iconRichLabel.getText().getText())) {
                iconRichLabelVOS.add(iconRichLabel);
                break;
            }
        }
        return iconRichLabelVOS;
    }

    public List<String> getPhotoAttrValues(ProductM productM, TagCfg tagCfg, List<String> attrValues) {
        if(productM.getProductType() == ProductTypeEnum.DEAL.getType() && CollectionUtils.isEmpty(attrValues)
                && CollectionUtils.isNotEmpty(tagCfg.getAttrKeys())){
            attrValues = tagCfg.getAttrKeys().stream()
                    .map(attrKey -> getAttrVal(attrKey, productM)).filter(attrValue -> StringUtils.isNotBlank(attrValue)).collect(Collectors.toList());
        }
        return attrValues;
    }

    private String getAttrVal(String attrKey, ProductM productM) {
        if (StringUtils.isEmpty(attrKey) || productM == null) {
            return null;
        }
        // 先尝试去团单的属性
        String attrValue = productM.getAttr(attrKey);
        if (StringUtils.isNotBlank(attrValue)) {
            return attrValue;
        }
        // 如果取不到，再尝试从标品的属性取
        return productM.getObjAttr(attrKey);
    }

    private String jointAttrValues(List<String> attrValues, String delimiter) {
        if (CollectionUtils.isEmpty(attrValues)) {
            return null;
        }
        if (attrValues.size() == 1) {
            return attrValues.get(0);
        }
        if (StringUtils.isEmpty(delimiter)) {
            return String.join(" ", attrValues);
        }
        return String.join(delimiter, attrValues);
    }

    private IconRichLabelVO buildIconRichTag(String tagValue, TagCfg tagCfg) {
        if (StringUtils.isEmpty(tagValue) || tagCfg == null) {
            return null;
        }
        RichLabelVO richLabel = new RichLabelVO();
        richLabel.setText(tagValue);
        richLabel.setTextSize(tagCfg.getTextSize());
        richLabel.setTextColor(tagCfg.getTextColor());
        richLabel.setBackgroundColor(tagCfg.getBackgroundColor());
        IconRichLabelVO iconRichLabelVO = new IconRichLabelVO();
        iconRichLabelVO.setText(richLabel);
        if (StringUtils.isNotEmpty(tagCfg.getIcon())) {
            iconRichLabelVO.setIcon(tagCfg.getIcon());
        }
        return iconRichLabelVO;
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 标品标签集合
         */
        private List<TagCfg> spuAttrCfg;

        /**
         * 团单标签集合
         */
        private List<TagCfg> dealAttrCfg;

        /**
         * 是否展示指定文字标签
         */
        private boolean showSpecificTag = false;
    }

    @Data
    public static class TagCfg {
        /**
         * 需拼接属性key
         */
        private List<String> attrKeys;

        /**
         * 需拼接属性值，可直接指定
         */
        private List<String> attrVals;

        /**
         * 拼接分隔符
         */
        private String delimiter;

        /**
         * 字体大小
         */
        private int textSize;

        /**
         * 文字颜色
         */
        private String textColor;

        /**
         * 背景色
         */
        private String backgroundColor;

        /**
         * 图标
         */
        private String icon;
    }
}
