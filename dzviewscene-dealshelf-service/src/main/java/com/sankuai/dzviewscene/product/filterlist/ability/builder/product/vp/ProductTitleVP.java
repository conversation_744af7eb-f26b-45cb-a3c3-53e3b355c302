package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

/**
 * @author: created by hang.yu on 2024/3/4 14:31
 */
@VPoint(name = "团单筛选列表展示名称", description = "团单筛选列表展示名称", code = ProductTitleVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductTitleVP<T> extends PmfVPoint<String, ProductTitleVP.Param, T> {

    public static final String CODE = "ProductTitleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;

    }

}
