package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints.SkuItemAttrVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.product.dealstruct.model.SkuAttrModel;
import com.sankuai.dzviewscene.product.dealstruct.model.SkuAttrValueModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.vo.PicItemVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/27 10:16 下午
 */
@VPointOption(name = "丽人美发团购详情货attr列表配置变化点", description = "丽人美发团购详情货attr列表配置变化点", code = BeautyHairConfiguredSkuItemAttrItemsVPO.CODE, isDefault = false)
public class BeautyHairConfiguredSkuItemAttrItemsVPO extends SkuItemAttrVP<BeautyHairConfiguredSkuItemAttrItemsVPO.Config> {

    public static final String CODE = "BeautyHairConfiguredSkuItemAttrItemsVPO";

    private static final String HAIR_DYE_PROCESS_SKU_ATTR_NAME = "染发工艺";

    private static final String HAIR_DYE_PROCESS_SKU_ATTR_VALUE = "染发";

    private static final String HAIR_PERM_PROCESS_SKU_ATTR_VALUE = "烫发";

    private static final String REFERENCE_COLOR_SKU_ATTR_NAME = "参考颜色";

    private static final String REFERENCE_SERVICE_TIME_NAME = "服务时长";
    private static final String REFERENCE_SERVICE_TIME_VALUE = "参考时长";
    private static final String SERVICE_STEP = "服务步骤";
    private static final String SERVICE_STEP_DETAIL = "服务步骤详情";

    private static final String SKU_PIC_ATTR_VALUE_SEPARATOR = "、";

    private static final String ADDITION_PRICE_ATTR_NAME = "additionPrice";

    private static final String EXTRA_HAIR_LIMIT_ATTR_NAME = "extHairLimit";

    private static final String BLEACH_ADDITION_ATTR_NAME = "bleachAddtionPrice";

    private static final String COLOR = "漂色";

    private static final String SUITABLE_HAIR_LENGTH = "适合发长";

    private static final String SUITABLE_HAIR_CONTENT = "适用款式";

    private static final String HAIR_WITHOUT_LENGTH = "不区分长度";

    private static final String HAIR_UNDER_ARM = "不过肩";

    private static final String HAIR_UNDER_CHEST = "不过胸";

    private static final String HAIR_UNDER_EAR = "不过耳";

    private static final String HAIR_UNDER_ARM_PRICE_ATTR_NAME = "additionPrice";

    private static final String HAIR_UNDER_CHEST_PRICE_ATTR_NAME = "chestAdditionPrice";

    private static final String HAIR_UNDER_EAE_PRICE_ATTR_NAME = "earAdditionPrice";

    private static final String PRECOLOR_PERM_ATTR_NAME = "底色染发";

    private static final String PERT_PRECOLOR_PERM_ATTR_VALUE = "不包含全头底色染发";

    private static final String EXTRA_PRICE_ATTR_NAME = "额外费用";

    private static final String HAIR_LENGTH_WITHOUT_LIMIT = "不限发长";

    private static final String CONTAIN_HIAR_REMOVE_FREE = "包含免费拆卸";

    private static final String RANGE_LIMITED = "限用范围";
    private static final String SUIT_RANGE = "适用范围";

    private static final String NO_LIMIT = "无限制";

    private static final String HIAR_REMOVE_FREE = "免费拆卸";

    private static final String HAIR_EXT_ATTR_NAME = "接发发长";

    private static final String HAIR_REMOVE_ATTR_NAME = "拆卸";

    private static final List<String> BRAND_SKU_ATTR_NAMES = Lists.newArrayList("洗发水品牌", "吹风机品牌");

    private static final String OTHER_BRAND = "其他品牌";

    private static final String STEP_TIME_FORMAT = "%s分钟";
    private static final String ZERO_TIME = "0分钟";

    private static final String STEP_SKU_NUM_FORMAT = "共%s个步骤";

    // 修面categoryId
    private static final long CLEAN_FACE_PRODUCT_CATEGORY_ID = 3111L;
    private static final long DEFAULT_PRODUCT_CATEGORY_ID = -1L;



    @Override
    public List<SkuAttrModel> compute(ActivityCxt context, Param param, Config config) {
        SkuItemDto skuItemDto = param.getSkuItemDto();
        List<AttrM> dealAttrs = param.getDealAttrs();
        List<DouhuResultModel> douhuResultModels = param.getDouhuResultModels();
        List<ProductSkuCategoryModel> productCategories = param.getProductCategories();
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems()) || config == null) {
            return null;
        }
        String skuCategory = getSkuCategory(productCategories, skuItemDto.getProductCategory());
        List<SkuAttrsConfigModel> skuAttrModels = getSkuAttrModels(skuCategory, config);
        //1.如果团单含有烫发款式，则隐藏烫发类别sku属性中的适用款式字段 2.如果团单含有染发款式，则隐藏染发类别sk属性中参考颜字段
        skuAttrModels = deleteContentRelatedSkuAttrs(skuAttrModels, skuCategory, dealAttrs, douhuResultModels, config);
        return getSkuAttrModel(skuAttrModels, skuItemDto.getAttrItems(), config, skuItemDto.getProductCategory());
    }

    private boolean isHitDouhu(List<DouhuResultModel> douhuResultModels, Config config) {
        if (CollectionUtils.isEmpty(douhuResultModels) || config == null || CollectionUtils.isEmpty(config.getDouhuExpIds())) {
            return false;
        }
        DouhuResultModel douhuResultModel = douhuResultModels.stream().filter(douhu -> config.getDouhuExpIds().contains(douhu.getExpId())).findFirst().orElse(null);
        return douhuResultModel != null && douhuResultModel.isHitSk();
    }

    private List<SkuAttrsConfigModel> deleteContentRelatedSkuAttrs(List<SkuAttrsConfigModel> skuAttrModels, String skuCategory, List<AttrM> dealAttrs, List<DouhuResultModel> douhuResultModels, Config config) {
        if (CollectionUtils.isEmpty(skuAttrModels) || CollectionUtils.isEmpty(dealAttrs) || StringUtils.isEmpty(skuCategory)) {
            return skuAttrModels;
        }
        boolean isHitDouhu = isHitDouhu(douhuResultModels, config);
        boolean hasHairPermContent = Boolean.TRUE.toString().equals(DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "beautyDealContainPermContent"));
        boolean hasHairDyeContent = Boolean.TRUE.toString().equals(DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "beautyDealContaiHairColorContent"));
        if (HAIR_PERM_PROCESS_SKU_ATTR_VALUE.equals(skuCategory) && hasHairPermContent && !isHitDouhu) {
            return deleteSkuAttr(skuAttrModels, SUITABLE_HAIR_CONTENT);
        }
        if (HAIR_DYE_PROCESS_SKU_ATTR_VALUE.equals(skuCategory) && hasHairDyeContent && !isHitDouhu) {
            return deleteSkuAttr(skuAttrModels, REFERENCE_COLOR_SKU_ATTR_NAME);
        }
        return skuAttrModels;
    }

    private List<SkuAttrsConfigModel> deleteSkuAttr(List<SkuAttrsConfigModel> skuAttrModels, String attrName) {
        if (CollectionUtils.isEmpty(skuAttrModels) || StringUtils.isEmpty(attrName)) {
            return skuAttrModels;
        }
        return skuAttrModels.stream().filter(model -> model != null && !attrName.equals(model.getAttrName())).collect(Collectors.toList());
    }

    private List<SkuAttrModel> getSkuAttrModel(List<SkuAttrsConfigModel> skuAttrModels, List<SkuAttrItemDto> attrItems, Config config, long productCategoryId) {
        if (CollectionUtils.isEmpty(skuAttrModels) || CollectionUtils.isEmpty(attrItems)) {
            return null;
        }
        Map<SkuAttrModel, Integer> skuAttr2PriorityMap = attrItems.stream().collect(HashMap::new, (map, attr) -> {
            SkuAttrsConfigModel skuAttrsConfigModel = getAttrConfigModel(attr, skuAttrModels);
            int attrPriority = getAttrPriority(skuAttrsConfigModel);
            String attrTitle = getAttrTitle(skuAttrsConfigModel);
            String attrValue = getDealStructVOValue(attrTitle, attr.getAttrValue(), attrItems, config);
            SkuAttrModel skuAttrModel = buildSkuAttrModel(attrTitle, attrValue, config, productCategoryId);
            if (skuAttrModel == null) {
                return;
            }
            map.put(skuAttrModel, attrPriority);
            // 构建服务步骤-详情
            if (SERVICE_STEP.equals(attrTitle)){
                SkuAttrModel skuAttrStepDetailModel = buildSkuAttrModel(SERVICE_STEP_DETAIL, attrValue, config, DEFAULT_PRODUCT_CATEGORY_ID);
                if (skuAttrStepDetailModel == null) {
                    return;
                }
                map.put(skuAttrStepDetailModel, attrPriority+1);
            }
        }, HashMap::putAll);
        if (MapUtils.isEmpty(skuAttr2PriorityMap)) {
            return null;
        }
        return skuAttr2PriorityMap.entrySet().stream().sorted(Comparator.comparingInt(Map.Entry::getValue)).map(entry -> entry.getKey()).collect(Collectors.toList());
    }

    private SkuAttrModel buildSkuAttrModel(String attrTitle, String attrValue, Config config, long productCategoryId) {
        if(StringUtils.isEmpty(attrTitle) || StringUtils.isEmpty(attrValue)) {
            return null;
        }
        if (BRAND_SKU_ATTR_NAMES.contains(attrTitle) && attrValue.equals(OTHER_BRAND)) {
            return null;
        }
        // 如果是修面，当适用范围为"无限制"时，则不展示
        if (CLEAN_FACE_PRODUCT_CATEGORY_ID == productCategoryId && attrTitle.equals(SUIT_RANGE) && attrValue.equals(NO_LIMIT)){
            return null;
        }
        // 如果“参考时长”为"0分钟"时，则不展示
        if (REFERENCE_SERVICE_TIME_VALUE.equals(attrTitle) && ZERO_TIME.equals(attrValue)){
            return null;
        }
        if (attrTitle.equals(RANGE_LIMITED) && attrValue.equals(NO_LIMIT)) {
            return null;
        }
        if(attrTitle.equals(HAIR_DYE_PROCESS_SKU_ATTR_NAME) && attrValue.equals(HAIR_DYE_PROCESS_SKU_ATTR_VALUE)) {
            return null;
        }
        SkuAttrModel skuAttrModel = new SkuAttrModel();
        skuAttrModel.setName(attrTitle);
        if(attrTitle.equals(REFERENCE_COLOR_SKU_ATTR_NAME)) {
            skuAttrModel.setPicItemVOSExtValue(JsonCodec.encode(buildPicItemVOs(attrValue, config)));
            return skuAttrModel;
        }
        // 构建服务步骤-"共多少步骤"
        if (attrTitle.equals(SERVICE_STEP)){
            buildSkuAttrModels(skuAttrModel,attrValue);
            return skuAttrModel;
        }
        // 构建服务步骤-"服务流程"
        if (attrTitle.equals(SERVICE_STEP_DETAIL)){
            buildSkuAttrModels(skuAttrModel,attrValue);
            return skuAttrModel;
        }
        skuAttrModel.setValue(buildSkuAttrValueModel(attrValue));
        return skuAttrModel;
    }


    // 构建二级属性
    private void buildSkuAttrModels(SkuAttrModel skuAttrModel, String attrValue){
        if(skuAttrModel == null || StringUtils.isEmpty(attrValue)) {
            return;
        }
        List<SkuStepServiceModel> skuStepServiceModels = JsonCodec.converseList(attrValue, SkuStepServiceModel.class);
        if(CollectionUtils.isEmpty(skuStepServiceModels)) {
            return ;
        }
        // 构建 服务步骤
        if (SERVICE_STEP.equals(skuAttrModel.getName())){
            skuAttrModel.setValue(buildSkuAttrValueModel(String.format( STEP_SKU_NUM_FORMAT, skuStepServiceModels.size())));
        } // 构建服务步骤详情
        else if (SERVICE_STEP_DETAIL.equals(skuAttrModel.getName())){
            // 通过List<SkuStepServiceModel>构建  sku二级属性列表 List<SkuAttrAttrModel>
            List<SkuAttrAttrItemVO> skuAttrAttrItemVOS = new ArrayList<>();
            skuAttrAttrItemVOS.addAll(skuStepServiceModels.stream().map(model -> buildSkuAttrAttrModels(model)).filter(vo -> vo != null).collect(Collectors.toList()));
            skuAttrModel.setName(StringUtils.EMPTY);
            skuAttrModel.setValueAttrsExtValue(JsonCodec.encode(skuAttrAttrItemVOS));
        }
    }

    private SkuAttrAttrItemVO buildSkuAttrAttrModels(SkuStepServiceModel skuStepServiceModel){
        if (skuStepServiceModel == null){
            return null;
        }
        SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
        // 步骤名称
        String stepName = skuStepServiceModel.getContent();
        skuAttrAttrItemVO.setName(stepName);
        List<String> stepTime = getNormalizedStepTime(skuStepServiceModel.getStepTakeTime());
        skuAttrAttrItemVO.setInfo(stepTime);
        return skuAttrAttrItemVO;
    }

    // 服务步骤时长，展示%s分钟
    private List<String> getNormalizedStepTime(String stepTime) {
        if (StringUtils.isEmpty(stepTime)) {
            return null;
        }
        return Lists.newArrayList(String.format(STEP_TIME_FORMAT, stepTime));
    }

    private SkuAttrValueModel buildSkuAttrValueModel(String attrValue) {
        if (StringUtils.isEmpty(attrValue)) {
            return null;
        }
        SkuAttrValueModel skuAttrValueModel = new SkuAttrValueModel();
        skuAttrValueModel.setDoc(attrValue);
        return skuAttrValueModel;
    }

    private List<PicItemVO> buildPicItemVOs(String skuPicAttrValue, Config config) {
        if(StringUtils.isEmpty(skuPicAttrValue)) {
            return null;
        }
        List<String> skuPicAttrs = Lists.newArrayList(skuPicAttrValue.split(SKU_PIC_ATTR_VALUE_SEPARATOR));
        if(CollectionUtils.isEmpty(skuPicAttrs)) {
            return null;
        }
        Map<PicItemVO, Integer> sequenceId2PicItemVOMap = skuPicAttrs.stream().collect(HashMap::new, (map, skuPicAttr) -> {
            PicItemVO picItemVO = convertSkuPicAttr2PicItemVO(skuPicAttr);
            int sequenceId = getSkuShowPicSequenceId(picItemVO.getTitle(), config);
            map.put(picItemVO, sequenceId);
        }, HashMap::putAll);
        return sequenceId2PicItemVOMap.entrySet().stream().sorted(Comparator.comparingInt(Map.Entry::getValue)).map(entry -> entry.getKey()).collect(Collectors.toList());
    }

    private PicItemVO convertSkuPicAttr2PicItemVO(String skuPicAttr) {
        if(StringUtils.isEmpty(skuPicAttr)) {
            return null;
        }
        SkuPicModel skuPicModel = JsonCodec.decode(skuPicAttr, SkuPicModel.class);
        if(skuPicModel == null) {
            return null;
        }
        PicItemVO picItemVO = new PicItemVO();
        picItemVO.setTitle(skuPicModel.getName());
        picItemVO.setUrl(skuPicModel.getUrl());
        return picItemVO;
    }

    private int getSkuShowPicSequenceId(String color, Config config) {
        if(config == null || StringUtils.isEmpty(config.getDyeHairSkuColors())){
            return Integer.MAX_VALUE;
        }
        if(StringUtils.isEmpty(color)) {
            return Integer.MAX_VALUE;
        }
        List<String> colorList = Lists.newArrayList(config.getDyeHairSkuColors().split(SKU_PIC_ATTR_VALUE_SEPARATOR));
        if(!colorList.contains(color)) {
            return Integer.MAX_VALUE;
        }
        return Lists.newArrayList(config.getDyeHairSkuColors().split(SKU_PIC_ATTR_VALUE_SEPARATOR)).indexOf(color);
    }

    private String getDealStructVOValue(String skuTitle, String preValue, List<SkuAttrItemDto> skuAttrItemDtos, Config config) {
        if(StringUtils.isEmpty(skuTitle)) {
            return null;
        }
        if (HAIR_EXT_ATTR_NAME.equals(skuTitle)) {
            return getHairExtensionShowDoc(preValue, skuAttrItemDtos, config);
        }
        if (HAIR_REMOVE_ATTR_NAME.equals(skuTitle)) {
            return getHairRemoveShowDoc(preValue, skuAttrItemDtos, config);
        }
        String colorExtraPrice = getColorExtraPrice(skuAttrItemDtos);
        if(COLOR.equals(skuTitle) && StringUtils.isNotEmpty(colorExtraPrice)) {
            return getHairColorExtraPriceDoc(preValue, colorExtraPrice, config);
        }
        if(SUITABLE_HAIR_LENGTH.equals(skuTitle)) {
            return getSuitableHairLengthShowDoc(preValue, skuAttrItemDtos, config);
        }
        if (PRECOLOR_PERM_ATTR_NAME.equals(skuTitle)) {
            return PERT_PRECOLOR_PERM_ATTR_VALUE.equals(preValue) ? preValue : null;
        }
        if (EXTRA_PRICE_ATTR_NAME.equals(skuTitle)) {
            return StringUtils.isEmpty(preValue) ? null : String.format(config.getPermSkuExtraPriceFormat(), preValue);
        }
        return preValue;
    }

    private String getSuitableHairLengthShowDoc(String suitableHairLength, List<SkuAttrItemDto> skuAttrItemDtos, Config config) {
        if (HAIR_WITHOUT_LENGTH.equals(suitableHairLength)) {
            return suitableHairLength;
        }
        if (HAIR_UNDER_ARM.equals(suitableHairLength)) {
            return getSuitableHairLengthShowDoc(skuAttrItemDtos, HAIR_UNDER_ARM, HAIR_UNDER_ARM_PRICE_ATTR_NAME, config.getHairUnderArmExtraPriceDocFormat());
        }
        if (HAIR_UNDER_CHEST.equals(suitableHairLength)) {
            return getSuitableHairLengthShowDoc(skuAttrItemDtos, HAIR_UNDER_CHEST, HAIR_UNDER_CHEST_PRICE_ATTR_NAME, config.getHairUnderChestExtraPriceDocFormat());
        }
        if (HAIR_UNDER_EAR.equals(suitableHairLength)) {
            return getSuitableHairLengthShowDoc(skuAttrItemDtos, HAIR_UNDER_EAR, HAIR_UNDER_EAE_PRICE_ATTR_NAME, config.getHairUnderEarExtraPriceDocFormat());
        }
        return null;
    }

    private String getSuitableHairLengthShowDoc(List<SkuAttrItemDto> skuAttrItemDtos, String hairLengthType, String hairPriceAttrName, String format) {
        String priceWithLongHair = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItemDtos, hairPriceAttrName);
        if (StringUtils.isEmpty(priceWithLongHair)) {
            return hairLengthType;
        }
        return String.format(format, priceWithLongHair);
    }

    private String getColorExtraPrice(List<SkuAttrItemDto> skuAttrItemDtos) {
        if(CollectionUtils.isEmpty(skuAttrItemDtos)) {
            return null;
        }
        SkuAttrItemDto skuAttrItemDto = skuAttrItemDtos.stream().filter(attr -> BLEACH_ADDITION_ATTR_NAME.equals(attr.getAttrName())).findFirst().orElse(null);
        if(skuAttrItemDto == null) {
            return null;
        }
        return skuAttrItemDto.getAttrValue();
    }

    private String getHairExtensionShowDoc(String hairExtAttrValue, List<SkuAttrItemDto> skuAttrItemDtos, Config config) {
        if (HAIR_LENGTH_WITHOUT_LIMIT.equals(hairExtAttrValue)) {
            return hairExtAttrValue;
        }
        if (CollectionUtils.isEmpty(skuAttrItemDtos)) {
            return hairExtAttrValue;
        }
        SkuAttrItemDto hairLengthLimit = skuAttrItemDtos.stream().filter(attr -> EXTRA_HAIR_LIMIT_ATTR_NAME.equals(attr.getAttrName())).findFirst().orElse(null);
        if (hairLengthLimit == null || hairLengthLimit.getAttrValue() == null) {
            return hairExtAttrValue;
        }
        if (config == null || StringUtils.isEmpty(config.getHairExtFormat())) {
            return hairExtAttrValue;
        }
        return String.format(config.getHairExtFormat(), hairExtAttrValue, hairLengthLimit.getAttrValue());
    }

    private String getHairRemoveShowDoc(String hairExtAttrValue, List<SkuAttrItemDto> skuAttrItemDtos, Config config) {
        if (CONTAIN_HIAR_REMOVE_FREE.equals(hairExtAttrValue)) {
            return HIAR_REMOVE_FREE;
        }
        if (CollectionUtils.isEmpty(skuAttrItemDtos)) {
            return hairExtAttrValue;
        }
        SkuAttrItemDto hairAdditionPrice = skuAttrItemDtos.stream().filter(attr -> ADDITION_PRICE_ATTR_NAME.equals(attr.getAttrName())).findFirst().orElse(null);
        if (hairAdditionPrice == null || hairAdditionPrice.getAttrValue() == null) {
            return hairExtAttrValue;
        }
        if (config == null || StringUtils.isEmpty(config.getHairRemoveFormat())) {
            return hairExtAttrValue;
        }
        return String.format(config.getHairRemoveFormat(), hairAdditionPrice.getAttrValue());
    }

    private String getHairColorExtraPriceDoc(String preValue, String colorExtraPrice, Config config) {
        if(StringUtils.isEmpty(preValue)) {
            return null;
        }
        if(StringUtils.isEmpty(colorExtraPrice)) {
            return preValue;
        }
        if(colorExtraPrice.contains(config.getCopiesUnit())) {
            return String.format(config.getHairColorExtraPriceDocWithoutCopiesUnitFormat(), preValue, colorExtraPrice);
        }
        return String.format(config.getHairColorExtraPriceDocFormat(), preValue, colorExtraPrice);
    }

    private SkuAttrsConfigModel getAttrConfigModel(SkuAttrItemDto attrDto, List<SkuAttrsConfigModel> skuAttrModels) {
        if (CollectionUtils.isEmpty(skuAttrModels) || attrDto == null || StringUtils.isEmpty(attrDto.getAttrName())) {
            return null;
        }
        // 取出服务时长
        if (REFERENCE_SERVICE_TIME_NAME.equals(attrDto.getChnName())){
            return skuAttrModels.stream().filter(attrModel -> REFERENCE_SERVICE_TIME_VALUE.equals(attrModel.getAttrName())).findFirst().orElse(null);
        }
        return skuAttrModels.stream().filter(attrModel -> attrDto.getChnName().equals(attrModel.getAttrName())).findFirst().orElse(null);
    }

    private String getAttrTitle(SkuAttrsConfigModel skuAttrsConfigModel) {
        if (skuAttrsConfigModel == null) {
            return null;
        }
        return skuAttrsConfigModel.getAttrTitle();
    }

    private int getAttrPriority( SkuAttrsConfigModel skuAttrsConfigModel) {
        if (skuAttrsConfigModel == null) {
            return Integer.MAX_VALUE;
        }
        return skuAttrsConfigModel.getPriority();
    }

    private List<SkuAttrsConfigModel> getSkuAttrModels(String category, Config config) {
        if(config == null || CollectionUtils.isEmpty(config.getSkuCategoryAttrsModels()) || StringUtils.isEmpty(category)) {
            return null;
        }
        List<SkuCategoryAttrsModel> skuCategoryAttrsModels= config.getSkuCategoryAttrsModels();
        SkuCategoryAttrsModel skuCategoryAttrsModel = skuCategoryAttrsModels.stream().filter(model -> category.equals(model.getCategory())).findFirst().orElse(null);
        if (skuCategoryAttrsModel != null) {
            return skuCategoryAttrsModel.getAttrsConfigModels();
        }
        return config.getDefaultSkuAttrsConfigModels();
    }

    private String getSkuCategory(List<ProductSkuCategoryModel> productCategories, long skuCategoryId) {
        if (CollectionUtils.isEmpty(productCategories)) {
            return null;
        }
        ProductSkuCategoryModel categoryModel = productCategories.stream().filter(category -> category.getProductCategoryId() == skuCategoryId).findFirst().orElse(null);
        if (categoryModel == null) {
            return null;
        }
        return categoryModel.getCnName();
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<SkuCategoryAttrsModel> skuCategoryAttrsModels;
        private List<SkuAttrsConfigModel> defaultSkuAttrsConfigModels;
        private String dyeHairSkuColors;
        private String hairColorExtraPriceDocFormat;
        private String hairUnderArmExtraPriceDocFormat;
        private String hairUnderChestExtraPriceDocFormat;
        private String hairUnderEarExtraPriceDocFormat;
        private String hairColorExtraPriceDocWithoutCopiesUnitFormat;
        private String hairLengthExtraPriceDocFormat;
        private String permSkuExtraPriceFormat;
        private String copiesUnit;
        private List<String> skuCategoryShowingSkuName;
        private String hairRemoveFormat;
        private String hairExtFormat;
        private List<String> douhuExpIds;
    }

    @Data
    private static class SkuCategoryAttrsModel {
        //优先级
        private int priority;
        //货类别
        private String category;
        //该类别下所展示的属性配置信息
        private List<SkuAttrsConfigModel> attrsConfigModels;
    }

    @Data
    private static class SkuAttrsConfigModel {
        //优先级
        private int priority;
        //该属性展示的标题
        private String attrTitle;
        //所取属性的属性名
        private String attrName;
        //属性值和展示值映射
        private Map<String, String> attrValue2AttrShowValueMap;
    }

    @Data
    private static class SkuPicModel {
        private String name;
        private String url;
    }

    @Data
    private static class SkuStepServiceModel {
        private String content;
        private String stepTakeTime;
    }
}
