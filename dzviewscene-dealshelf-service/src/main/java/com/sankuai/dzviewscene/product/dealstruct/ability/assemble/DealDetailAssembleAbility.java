package com.sankuai.dzviewscene.product.dealstruct.ability.assemble;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleBuilderFactory;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.DealAttrVOListModuleBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.desc.DealDetailDescBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.popup.DealDetailPopupTypesBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.price.DealDetailPriceBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.servicefeatures.StandardServiceModuleBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.serviceprocess.DealStandardServiceProcessBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.DealDetailSkuProductsGroupsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.DealDetailSkuGroupsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.DealDetailSkuListsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.DealDetailSkusModuleBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.TaiJiProjectsModuleBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.struct.DealDetailStructAttrListBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.title.DealDetailTitleBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.videoModule.DealDetailVideoModuleBuilder;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealModuleDetailVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.PopupTypeVO;
import com.sankuai.dzviewscene.product.factory.DealCategoryFactory;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.product.shelf.utils.AntiCrawlerUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2021/11/30 11:10 上午
 */
@Slf4j
@Ability(code = DealDetailAssembleAbility.CODE,
        name = "团购详情模块组装能力",
        description = "团购详情模块组装能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailTitleBuilder.CODE,
                DealDetailSkuProductsGroupsBuilder.CODE,
                DealDetailSkuGroupsBuilder.CODE,
                DealDetailPriceBuilder.CODE,
                DealDetailDescBuilder.CODE,
                DealDetailStructAttrListBuilder.CODE,
                DealDetailPopupTypesBuilder.CODE,
                StandardServiceModuleBuilder.CODE,
                DealAttrVOListModuleBuilder.CODE,
                DealDetailVideoModuleBuilder.CODE,
                DealDetailSkusModuleBuilder.CODE,
                DealDetailSkuListsBuilder.CODE,
                DealStandardServiceProcessBuilder.CODE,
                TaiJiProjectsModuleBuilder.CODE
        }
)
public class DealDetailAssembleAbility extends PmfAbility<DealModuleDetailVO, DealDetailAssembleParam, DealDetailAssembleCfg> {

    @Resource
    private ModuleBuilderFactory moduleBuilderFactory;

    @Resource
    private DealCategoryFactory dealCategoryFactory;

    public static final String CODE = "dealDetailAssembleAbility";

    @Override
    public CompletableFuture<DealModuleDetailVO> build(ActivityCxt activityCxt, DealDetailAssembleParam assembleParam, DealDetailAssembleCfg assembleCfg) {
        //获取模块详情
        List<DealDetailModuleVO> detailModuleVOList = moduleBuilderFactory.buildModuleList(activityCxt, assembleParam, dealCategoryFactory.getModuleList(activityCxt, assembleCfg));
        //获取弹窗样式
        List<PopupTypeVO> popupTypes = activityCxt.getSource(DealDetailPopupTypesBuilder.CODE);
        DealModuleDetailVO dealModuleDetailVO = buildDealModuleDetailVO(activityCxt, detailModuleVOList, popupTypes);
        return CompletableFuture.completedFuture(dealModuleDetailVO);
    }

    private DealModuleDetailVO buildDealModuleDetailVO(ActivityCxt activityCxt, List<DealDetailModuleVO> dealDetailModuleVOS, List<PopupTypeVO> popuoTypes) {
        DealModuleDetailVO dealModuleDetailVO = new DealModuleDetailVO();
        dealModuleDetailVO.setModuleList(dealDetailModuleVOS);
        dealModuleDetailVO.setSceneCode(activityCxt.getSceneCode());
        dealModuleDetailVO.setPopupTypes(popuoTypes);
        ActivityContext oldContext = ActivityCtxtUtils.toActivityContext(activityCxt);
        AntiCrawlerUtils.hideProductKeyInfo(dealModuleDetailVO, oldContext);
        return dealModuleDetailVO;
    }

}
