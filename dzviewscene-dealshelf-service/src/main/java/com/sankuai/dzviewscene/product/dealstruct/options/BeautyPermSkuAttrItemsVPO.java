package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.cat.Cat;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.SkuAttrItemsVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.PicItemVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/27 10:16 下午
 */
@VPointOption(name = "丽人-烫染团购详情货attr列表默认变化点", description = "丽人-烫染团购详情货attr列表默认变化点，支持配置", code = BeautyPermSkuAttrItemsVPO.CODE, isDefault = false)
public class BeautyPermSkuAttrItemsVPO extends SkuAttrItemsVP<BeautyPermSkuAttrItemsVPO.Config> {

    public static final String CODE = "BeautyPermDyeSkuAttrItemsVPO";

    private static final String SKU_PIC_ATTR_VALUE_SEPARATOR = "、";

    private static final String COPIES_UNIT = "次";

    private static final String HAIR_LENGTH_EXTRA_PRICE_DOC_FORMAT = "%s（过肩需加收%s）";

    private static final String HAIR_COLOR_EXTRA_PRICE_DOC_FORMAT = "%s（如需特殊漂色，须加收%s/次）";

    private static final String HAIR_COLOR_EXTRA_PRICE_WITHOUT_COPIES_UNIT_DOC_FORMAT = "%s（如需特殊漂色，须加收%s）";

    private static final String COLOR = "漂色";

    private static final String SUITABLE_HAIR_LENGTH = "适合发长";

    private static final String HAIR_DYE_PROCESS_SKU_ATTR_NAME = "染发工艺";

    private static final String HAIR_DYE_PROCESS_SKU_ATTR_VALUE = "染发";

    private static final String REFERENCE_COLOR_SKU_ATTR_NAME = "参考颜色";

    private static final String ADDITION_PRICE_ATTR_NAME = "additionPrice";

    private static final String BLEACH_ADDITION_ATTR_NAME = "bleachAddtionPrice";

    @Override
    public List<DealSkuItemVO> compute(ActivityCxt context, Param param, Config config) {
        SkuItemDto skuItemDto = param.getSkuItemDto();
        List<ProductSkuCategoryModel> productCategories = param.getProductCategories();
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return null;
        }
        List<SkuAttrItemDto> skuAttrItemDtos = skuItemDto.getAttrItems();
        List<DealSkuItemVO> originalSkuList = skuAttrItemDtos.stream().map(attr -> {
            if (attr == null) {
                return null;
            }
            return buildDealSkuItemVO(attr.getChnName(), attr.getAttrValue(), null, 0);
        }).filter(dto -> dto != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(config.getSkuShowModels())) {
            return originalSkuList;
        }
        String skuCategory = DealDetailUtils.getSkuCategoryBySkuCategoryId(skuItemDto.getProductCategory(), productCategories);
        SkuShowModel skuShowModel = getSkuShowModel(skuCategory, config.getSkuShowModels());
        if (skuShowModel == null || CollectionUtils.isEmpty(skuShowModel.getSkuAttrShowModels())) {
            return originalSkuList;
        }
        Map<DealSkuItemVO, Integer> dealSkuItemVOIntegerMap = skuShowModel.getSkuAttrShowModels().stream().collect(HashMap::new, (map, model) -> {
            DealSkuItemVO dealSkuItemVO = getDealSkuItemVO(model, skuAttrItemDtos, config);
            if (dealSkuItemVO == null) {
                return;
            }
            map.put(dealSkuItemVO, model.getAttrSequenceId());
        }, HashMap::putAll);
        return dealSkuItemVOIntegerMap.entrySet().stream().sorted(Comparator.comparingInt(Map.Entry::getValue)).map(entry -> entry.getKey()).collect(Collectors.toList());
    }

    private DealSkuItemVO getDealSkuItemVO(SkuAttrShowModel model, List<SkuAttrItemDto> skuAttrItemDtos, Config config) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.dealstruct.options.BeautyPermSkuAttrItemsVPO.getDealSkuItemVO(BeautyPermSkuAttrItemsVPO$SkuAttrShowModel,List,BeautyPermSkuAttrItemsVPO$Config)");
        if (model == null) {
            return null;
        }
        SkuAttrItemDto skuAttrItemDto = getSkuAttrItemDtoByChnName(skuAttrItemDtos, model.getAttrChnName());
        if (skuAttrItemDto == null) {
            return null;
        }
        String colorExtraPrice = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItemDtos, ADDITION_PRICE_ATTR_NAME);
        if (COLOR.equals(model.getAttrTitle()) && StringUtils.isNotEmpty(skuAttrItemDto.getAttrValue())) {
            return buildDealSkuItemVO(model.getAttrTitle(), normalizeSkuColorAttrValue(skuAttrItemDto.getAttrValue(), colorExtraPrice), null, 0);
        }
        String hairLengthExtraPrice = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItemDtos, BLEACH_ADDITION_ATTR_NAME);
        if (SUITABLE_HAIR_LENGTH.equals(model.getAttrTitle()) && StringUtils.isNotEmpty(skuAttrItemDto.getAttrValue())) {
            return buildDealSkuItemVO(model.getAttrTitle(), normalizeSkuHairLengthAttrValue(skuAttrItemDto.getAttrValue(), hairLengthExtraPrice), null, 0);
        }
        if (HAIR_DYE_PROCESS_SKU_ATTR_NAME.equals(model.getAttrTitle()) && HAIR_DYE_PROCESS_SKU_ATTR_VALUE.equals(skuAttrItemDto.getAttrValue())) {
            return null;
        }
        if (REFERENCE_COLOR_SKU_ATTR_NAME.equals(model.getAttrTitle()) && StringUtils.isNotEmpty(skuAttrItemDto.getAttrValue())) {
            List<PicItemVO> picItemVOS = buildPicItemVOs(skuAttrItemDto.getAttrValue(), config.getColorList());
            return buildDealSkuItemVO(model.getAttrTitle(), null, picItemVOS, 1);
        }
        return buildDealSkuItemVO(model.getAttrTitle(), skuAttrItemDto.getAttrValue(), null, 0);
    }

    private List<PicItemVO> buildPicItemVOs(String skuPicAttrValue, String colorList) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.dealstruct.options.BeautyPermSkuAttrItemsVPO.buildPicItemVOs(java.lang.String,java.lang.String)");
        if(StringUtils.isEmpty(skuPicAttrValue)) {
            return null;
        }
        List<String> skuPicAttrs = Lists.newArrayList(skuPicAttrValue.split(SKU_PIC_ATTR_VALUE_SEPARATOR));
        if(CollectionUtils.isEmpty(skuPicAttrs)) {
            return null;
        }
        Map<PicItemVO, Integer> sequenceId2PicItemVOMap = skuPicAttrs.stream().collect(HashMap::new, (map, skuPicAttr) -> {
            PicItemVO picItemVO = convertSkuPicAttr2PicItemVO(skuPicAttr);
            int sequenceId = getSkuShowPicSequenceId(picItemVO.getTitle(), colorList);
            map.put(picItemVO, sequenceId);
        }, HashMap::putAll);
        return sequenceId2PicItemVOMap.entrySet().stream().sorted(Comparator.comparingInt(Map.Entry::getValue)).map(entry -> entry.getKey()).collect(Collectors.toList());
    }

    private int getSkuShowPicSequenceId(String color, String colorList) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.dealstruct.options.BeautyPermSkuAttrItemsVPO.getSkuShowPicSequenceId(java.lang.String,java.lang.String)");
        if(StringUtils.isEmpty(color) || StringUtils.isEmpty(colorList)) {
            return Integer.MAX_VALUE;
        }
        List<String> colors = Lists.newArrayList(colorList.split(SKU_PIC_ATTR_VALUE_SEPARATOR));
        if(!colors.contains(color)) {
            return Integer.MAX_VALUE;
        }
        return Lists.newArrayList(colorList.split(SKU_PIC_ATTR_VALUE_SEPARATOR)).indexOf(color);
    }

    private PicItemVO convertSkuPicAttr2PicItemVO(String skuPicAttr) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.dealstruct.options.BeautyPermSkuAttrItemsVPO.convertSkuPicAttr2PicItemVO(java.lang.String)");
        if(StringUtils.isEmpty(skuPicAttr)) {
            return null;
        }
        SkuPicModel skuPicModel = JsonCodec.decode(skuPicAttr, SkuPicModel.class);
        if(skuPicModel == null) {
            return null;
        }
        PicItemVO picItemVO = new PicItemVO();
        picItemVO.setTitle(skuPicModel.getName());
        picItemVO.setUrl(skuPicModel.getUrl());
        return picItemVO;
    }

    private String normalizeSkuHairLengthAttrValue(String prevalue, String hairLengthExtraPrice) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.dealstruct.options.BeautyPermSkuAttrItemsVPO.normalizeSkuHairLengthAttrValue(java.lang.String,java.lang.String)");
        if(StringUtils.isEmpty(prevalue)) {
            return null;
        }
        if(StringUtils.isEmpty(hairLengthExtraPrice)) {
            return prevalue;
        }
        return String.format(HAIR_LENGTH_EXTRA_PRICE_DOC_FORMAT, prevalue, hairLengthExtraPrice);
    }

    private String normalizeSkuColorAttrValue(String prevalue, String colorExtraPrice) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.dealstruct.options.BeautyPermSkuAttrItemsVPO.normalizeSkuColorAttrValue(java.lang.String,java.lang.String)");
        if(StringUtils.isEmpty(prevalue)) {
            return null;
        }
        if(StringUtils.isEmpty(colorExtraPrice)) {
            return prevalue;
        }
        if(colorExtraPrice.contains(COPIES_UNIT)) {
            return String.format(HAIR_COLOR_EXTRA_PRICE_WITHOUT_COPIES_UNIT_DOC_FORMAT, prevalue, colorExtraPrice);
        }
        return String.format(HAIR_COLOR_EXTRA_PRICE_DOC_FORMAT, prevalue, colorExtraPrice);
    }

    private SkuAttrItemDto getSkuAttrItemDtoByChnName(List<SkuAttrItemDto> skuAttrItemDtos, String chnName) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.dealstruct.options.BeautyPermSkuAttrItemsVPO.getSkuAttrItemDtoByChnName(java.util.List,java.lang.String)");
        if (StringUtils.isEmpty(chnName) || CollectionUtils.isEmpty(skuAttrItemDtos)) {
            return null;
        }
        return skuAttrItemDtos.stream().filter(skuAttr -> chnName.equals(skuAttr.getChnName())).findFirst().orElse(null);
    }

    private DealSkuItemVO buildDealSkuItemVO(String name, String value, List<PicItemVO> picItemVOS, int type) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.dealstruct.options.BeautyPermSkuAttrItemsVPO.buildDealSkuItemVO(java.lang.String,java.lang.String,java.util.List,int)");
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setType(type);
        dealSkuItemVO.setValue(value);
        dealSkuItemVO.setPicValues(picItemVOS);
        return dealSkuItemVO;
    }

    private SkuShowModel getSkuShowModel(String skuCategory, List<SkuShowModel> skuShowModels) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.dealstruct.options.BeautyPermSkuAttrItemsVPO.getSkuShowModel(java.lang.String,java.util.List)");
        if (CollectionUtils.isEmpty(skuShowModels) || StringUtils.isEmpty(skuCategory)) {
            return null;
        }
        return skuShowModels.stream().filter(model -> skuCategory.equals(model.getSkuCategory())).findFirst().orElse(null);
    }

    @Data
    private static class SkuPicModel {
        private String name;
        private String url;
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<SkuShowModel> skuShowModels;
        private String colorList;
    }

    @Data
    private class SkuShowModel {
        private String skuCategory;
        private List<SkuAttrShowModel> skuAttrShowModels;
    }

    @Data
    private class SkuAttrShowModel {
        //中文属性名
        private String attrChnName;
        //展示标题
        private String attrTitle;
        //顺序
        private int attrSequenceId;
    }
}
