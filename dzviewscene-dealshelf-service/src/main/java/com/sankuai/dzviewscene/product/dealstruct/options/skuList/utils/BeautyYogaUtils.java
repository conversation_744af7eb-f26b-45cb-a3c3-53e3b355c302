package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.beautyyoga.BeautyYogaDealModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class BeautyYogaUtils {
    private static final String SUB_TITLE = "dance_class_time";
    private static final String CATEGORY = "category";
    private static final String DANCE_NEW_EXPERIENCE_LIMIT_OF_USING = "dance_new_experience_class_limit_of_using";
    private static final String YOGA_NEW_EXPERIENCE_LIMIT_OF_USING = "yoga_new_experience_class_limit_of_using";
    private static final String PULATI_EXPERIENCE_LIMIT_OF_USING = "pulati_experience_class_limit_of_using";
    private static final String BEAUTY_NEW_DANCE_TYPE = "beauty_new_dance_type";
    private static final String SPECIFICATION = "specification";
    private static final String SERVICE_TYPE = "serviceType";

    public static DealSkuVO parseServiceItems(String serviceType, List<AttrM> dealAttrs, BeautyYogaDealModuleOpt.Config config) {
        Map<String, List<String>> skuName2Attr = config.getSkuName2Attr();
        List<String> attrList = skuName2Attr.get(serviceType);
        Map<String, String> attr2ValueMap = dealAttrs.stream().collect(Collectors.toMap(e->e.getName(), e->e.getValue(), ((e1, e2)->e2)));
        String title = getTitleByServiceType(serviceType, attr2ValueMap);
        String subTitle = getSubTitleByServiceType(serviceType, attr2ValueMap);
        List<DealSkuItemVO>  skuItemVOS = buildServiceItems(attrList, attr2ValueMap, config);
        return buildDealSkuVO(title, subTitle, skuItemVOS);
    }

    public static List<DealSkuVO> parseServiceItemsByUnit(String serviceType,List<AttrM> dealAttrs, JSONObject unitJSON, String optionStr, BeautyYogaDealModuleOpt.Config config) {
        List<DealSkuVO> result = Lists.newArrayList();
        try{
            Map<String, List<String>> skuName2Attr = config.getSkuName2Attr();
            List<String> attrList = skuName2Attr.get(serviceType);
            String title = safeStringGetFromJson("projectName", unitJSON);
            String properties = safeStringGetFromJson("properties", unitJSON);
            if (StringUtils.isNotBlank(optionStr)){
                title = title +"("+optionStr+")";
            }
            String subTitle = getSubTitle(unitJSON);
            List<DealSkuItemVO>  skuItemVOS = buildServiceItemsByUint(attrList, dealAttrs, unitJSON,  properties, config);
            result.add(buildDealSkuVO(title, subTitle, skuItemVOS));
        }catch (Exception e){
            Cat.logError(e);
        }
        return result;
    }

    private static String safeStringGetFromJson(String key, JSONObject unitJSON){
        if (StringUtils.isBlank(key) || Objects.isNull(unitJSON)){
            return StringUtils.EMPTY;
        }
        return  Objects.isNull(unitJSON.get(key)) ? StringUtils.EMPTY : (String) unitJSON.get(key);
    }

    private static String getSubTitle( JSONObject unitJSON){
        String duration = getValueByAttr("classDuration", unitJSON);
        return StringUtils.isBlank(duration) ? null : duration + "分钟/次";
    }
    public static String getValueByAttr(String attr, JSONObject unitJSON){
        if (StringUtils.isBlank(attr) || Objects.isNull(unitJSON) || Objects.isNull(unitJSON.get("attrValues"))){
            return StringUtils.EMPTY;
        }
        JSONObject attrValuesJSON = (JSONObject) unitJSON.get("attrValues");
        String value = Objects.isNull(attrValuesJSON) ? StringUtils.EMPTY : (String) attrValuesJSON.get(attr);
        return value;
    }

    public static JSONArray parseDetailInfo(String detailInfo){
        JSONObject jsonObject = JSONObject.parseObject(detailInfo);
        JSONArray contentJsonArray = (JSONArray) jsonObject.get("content");
        JSONObject contentJson = (JSONObject) contentJsonArray.get(0);
        JSONObject data = (JSONObject) contentJson.get("data");
        JSONArray groups = (JSONArray) data.get("groups");
        return groups;
    }

    public static JSONArray parseUnitsJson(JSONObject unitsJson){
        JSONArray unitsArray = (JSONArray) unitsJson.get("units");
        return unitsArray;
    }



    public static DealSkuVO buildDealSkuVO(String title, String subTitle, List<DealSkuItemVO> items) {
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(title);
        dealSkuVO.setSubTitle(subTitle);
        dealSkuVO.setItems(items);
        return dealSkuVO;
    }

    private static List<DealSkuItemVO> buildServiceItems(List<String> attrList, Map<String, String> attr2ValueMap, BeautyYogaDealModuleOpt.Config config) {
        Map<String, String> attr2IconMap = config.getSkuName2IconMap();
        Map<String, Integer> attr2LayerType = config.getAttr2LayerType();
        List<DealSkuItemVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(attrList)){
            return null;
        }
        for (String attr : attrList) {
            String name = getDisplayNameByAttr(attr, attr2ValueMap);
            String value = getDisplayValueByAttr(attr, attr2ValueMap);
            String icon = attr2IconMap.get(attr);
            int type = getTypeByAttr(attr, attr2LayerType);
            if (StringUtils.isNotBlank(name) && StringUtils.isNotBlank(icon)) {
                result.add(buildDealSkuItemVO(name, value, icon, type));
            }
        }
        return result;
    }

    private static int getTypeByAttr(String attr, Map<String, Integer> attr2LayerType){
        if (MapUtils.isEmpty(attr2LayerType) || Objects.isNull(attr2LayerType.get(attr))){
            return 0;
        }
        return attr2LayerType.get(attr);
    }

    private static String getDisplayNameByAttr(String attr,  Map<String, String> attr2ValueMap){
        if (StringUtils.isBlank(attr)){
            return null;
        }
        String serviceType = attr2ValueMap.get(SERVICE_TYPE);
        switch (attr) {
            case "dance_suitable_people":
                String suitPeople = attr2ValueMap.get(attr);
                if ("全部人群".equals(suitPeople)){
                    return "不限人群";
                }
                return suitPeople;
            case "dance_suitable_basic":
                String suitBasic = attr2ValueMap.get(attr);
                if ("零基础、初学、进阶".equals(suitBasic)){
                    return "不限基础";
                }
                return suitBasic;
            case "space":
                String space = attr2ValueMap.get(attr);
                if (StringUtils.isNotBlank(space)){
                    return "教室面积" + space.replace("平方米","㎡");
                }
                return space;
            case "the_number_of_people_who_use":
                String suggestPeople = attr2ValueMap.get(attr);
                if (StringUtils.isNotBlank(suggestPeople)){
                    suggestPeople = suggestPeople.replace("区间人数：","容纳").replace("-","~");
                }
                return suggestPeople;
            case "equipiment":
                String equipment = attr2ValueMap.get(attr);
                if (StringUtils.isNotBlank(equipment)){
                    int count = countType(equipment);
                    return"场地包含"+ count +"种设备";
                }
                return null;
            case SPECIFICATION:
                String specification = attr2ValueMap.get(attr);
                if ("多次卡".equals(specification)){
                    String times = attr2ValueMap.get("times");
                    return StringUtils.isNotBlank(times) ? times+"次" : null;
                } else if ("周期卡".equals(specification)) {
                    String cardDuration = attr2ValueMap.get("card_duration");
                    return StringUtils.isNotBlank(cardDuration) ? cardDuration : null;
                } else if ("团课单次卡".equals(serviceType)
                        || "私教单次卡".equals(serviceType)){
                    return "1次";
                }
                return null;
            // 舞蹈
            case DANCE_NEW_EXPERIENCE_LIMIT_OF_USING:
                String danceCategory = extraCategoryFromAttr(attr2ValueMap.get(CATEGORY));
                String danceType = attr2ValueMap.get("beauty_new_dance_type");
                int danceTypeCount = countType(danceType);
                if ("舞蹈".equals(danceCategory) && danceTypeCount > 0){
                    return danceTypeCount +"个舞种任选";
                }
                return null;
            // 瑜伽
            case YOGA_NEW_EXPERIENCE_LIMIT_OF_USING:
                String yogaCategory = extraCategoryFromAttr(attr2ValueMap.get(CATEGORY));
                String yogaCourseType = attr2ValueMap.get("beauty_new_dance_course_type");
                int yogaCourseTypeCount = countType(yogaCourseType);
                if ("瑜伽".equals(yogaCategory) && yogaCourseTypeCount > 0){
                    return yogaCourseTypeCount +"个类型任选";
                }
                return null;
            // 普拉提
            case PULATI_EXPERIENCE_LIMIT_OF_USING:
                String puLaTiCategory = extraCategoryFromAttr(attr2ValueMap.get(CATEGORY));
                String puLaTiCourceType = attr2ValueMap.get("beauty_new_dance_course_type");
                int puLaTiCourceTypeCount = countType(puLaTiCourceType);
                if ("普拉提".equals(puLaTiCategory) && puLaTiCourceTypeCount > 0){
                    return puLaTiCourceTypeCount +"个类型任选";
                }
                return null;

            default:
                return  MapUtils.isNotEmpty(attr2ValueMap) ? attr2ValueMap.get(attr) : null;
        }
    }

    private static int countType(String danceType){
        if (StringUtils.isBlank(danceType)){
            return 0;
        }
        return danceType.split(",").length;
    }
    private static String extraCategoryFromAttr(String categoryAttr){
        if (StringUtils.isBlank(categoryAttr)){
            return StringUtils.EMPTY;
        }
        return categoryAttr.split(",")[0];
    }

    private static String getDisplayValueByAttr(String attr,  Map<String, String> attr2ValueMap){
        if (StringUtils.isBlank(attr)){
            return null;
        }
        switch (attr) {
            case "equipiment":
                String equipment = attr2ValueMap.get(attr);
                if (StringUtils.isNotBlank(equipment)){
                    equipment = equipment.replace(",","、");
                }
                return equipment;
                // 舞蹈
            case DANCE_NEW_EXPERIENCE_LIMIT_OF_USING:
                String danceCategory = extraCategoryFromAttr(attr2ValueMap.get(CATEGORY));
                String danceType = attr2ValueMap.get("beauty_new_dance_type");
                if ( "舞蹈".equals(danceCategory) && StringUtils.isNotBlank(danceType)){
                    return danceType.replace(",","、");
                }
                return null;
                // 瑜伽
            case YOGA_NEW_EXPERIENCE_LIMIT_OF_USING:
                String yogaCategory = extraCategoryFromAttr(attr2ValueMap.get(CATEGORY));
                String yogaCourseType = attr2ValueMap.get("beauty_new_dance_course_type");
                if ( "瑜伽".equals(yogaCategory) && StringUtils.isNotBlank(yogaCourseType)){
                    return yogaCourseType.replace(",","、");
                }
                return null;
                // 普拉提
            case PULATI_EXPERIENCE_LIMIT_OF_USING:
                String puLaTiCategory = extraCategoryFromAttr(attr2ValueMap.get(CATEGORY));
                String puLaTiCourceType = attr2ValueMap.get("beauty_new_dance_course_type");
                if ( "普拉提".equals(puLaTiCategory) && StringUtils.isNotBlank(puLaTiCourceType)){
                    return puLaTiCourceType.replace(",","、");
                }
                return null;
            case BEAUTY_NEW_DANCE_TYPE:
                String beautyDanceNewType = attr2ValueMap.get("beauty_new_dance_type");
                if (StringUtils.isNotBlank(beautyDanceNewType)){
                    return beautyDanceNewType.replace(",","、");
                }
                return null;
            default:
                return  null;
        }
    }


    private static List<DealSkuItemVO> buildServiceItemsByUint(List<String> attrList, List<AttrM> dealAttrs, JSONObject unitJSON,  String properties, BeautyYogaDealModuleOpt.Config config) {
        List<DealSkuItemVO> result = new ArrayList<>();
        Map<String, String> attr2IconMap = config.getSkuName2IconMap();
        Map<String, Integer> attr2LayerType = config.getAttr2LayerType();
        if (CollectionUtils.isEmpty(attrList) || Objects.isNull(unitJSON) || MapUtils.isEmpty(attr2IconMap)){
            return result;
        }
        JSONObject attrValueJSON = (JSONObject) unitJSON.get("attrValues");
        Map<String, String> attr2ValueMap = dealAttrs.stream().collect(Collectors.toMap(e->e.getName(), e->e.getValue(), ((e1, e2)->e2)));
        for (String attr : attrList) {
            String name = getDisplayNameByAttr(attr, properties, attr2ValueMap, attrValueJSON);
            String value = getDisplayValueByAttr(attr, attr2ValueMap);
            String icon = attr2IconMap.get(attr);
            int type = getTypeByAttr(attr, attr2LayerType);
            if (StringUtils.isNotBlank(name) && StringUtils.isNotBlank(icon)) {
                result.add(buildDealSkuItemVO(name, value, icon, type));
            }
        }
        return result;
    }

    private static String getDisplayNameByAttr(String attr, String properties,Map<String, String> attr2ValueMap, JSONObject attrValueJSON){
        if (StringUtils.isBlank(attr) || Objects.isNull(attrValueJSON)){
            return null;
        }
        switch (attr) {
           case "TeachingMethod":
               String teachingMethod = (String) attrValueJSON.get(attr);
               String maximumNumberOfClassParticipants = (String) attrValueJSON.get("MaximumNumberOfClassParticipants");
               if (StringUtils.isNotBlank(maximumNumberOfClassParticipants)){
                   return teachingMethod + "(最多" + maximumNumberOfClassParticipants + "人)";
               }
               return teachingMethod;
            case "cikacishu":
                String times = (String) attrValueJSON.get(attr);
                if (StringUtils.isNotBlank(times)){
                    if (properties.contains("单次卡")){
                        return "1次";
                    } else if(properties.contains("多次卡")) {
                        return times + "次";
                    } else if(properties.contains("周期卡")) {
                        return times;
                    }
                }
                return times;
            case "ApplicableBasis":
                String applicableBasis = (String) attrValueJSON.get(attr);
                if ("零基础、初学、进阶".equals(applicableBasis)){
                    applicableBasis = "不限基础";
                }
                return applicableBasis;
            case "beauty_new_dance_type":
                String danceType = attr2ValueMap.get(attr);
                if (StringUtils.isBlank(danceType)){
                    return "";
                }
                int typeCount = countType(danceType);
                String category = extraCategoryFromAttr(attr2ValueMap.get(CATEGORY));
                return typeCount + "种" + category + "类型任选";
            default:
                return (String) attrValueJSON.get(attr);
        }
    }


    private static DealSkuItemVO buildDealSkuItemVO(String name, String value, String icon,int type) {
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setType(type);
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setValue(value);
        dealSkuItemVO.setIcon(icon);
        return dealSkuItemVO;
    }

    private static String getSubTitleByServiceType(String serviceType,  Map<String, String> attr2ValueMap){
        if (StringUtils.isBlank(serviceType)){
            return null;
        }
        switch (serviceType) {
            case "舞室租赁":
                return null;
            default:
                return  attr2ValueMap.get(SUB_TITLE);
        }
    }

    private static String getTitleByServiceType(String serviceType,  Map<String, String> attr2ValueMap){
        if (StringUtils.isBlank(serviceType)){
            return null;
        }
        switch (serviceType) {
            case "舞室租赁":
                String duration = attr2ValueMap.get(SUB_TITLE);
                if (StringUtils.isNotBlank(duration)){
                    serviceType = serviceType + duration.replace("/节","");
                }
                return serviceType;
            default:
                return  serviceType;
        }
    }

}