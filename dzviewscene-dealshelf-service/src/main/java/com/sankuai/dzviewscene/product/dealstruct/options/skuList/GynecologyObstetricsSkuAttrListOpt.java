package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.beauty.fundamental.base.common.util.Optional;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.CommonAttrVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemConfig;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@VPointOption(name = "妇产科sku属性列表变化点", description = "妇产科sku属性列表变化点，根据配置展示sku属性", code = GynecologyObstetricsSkuAttrListOpt.CODE)
public class GynecologyObstetricsSkuAttrListOpt extends SkuAttrListVP<GynecologyObstetricsSkuAttrListOpt.Config> {

    public static final String CODE = "GynecologyObstetricsSkuAttrListOpt";

    private static final String INSPECTION_ITEM = "inspection_item";

    private static final String PROJECTNAME =  "projectName";

    private static final String INSPECTION_TYPE = "jianchaleixing";

    @Override
    public List<DealSkuItemVO> compute(ActivityCxt context, Param param, Config config) {
        if (config == null || param == null || config.configs == null) {
            return null;
        }
        List<AttrM> attrs = param.getDealAttrs();
        if (attrs == null) {
            return null;
        }
        List<DealSkuItemVO> dealSkuItemVOS = new ArrayList<>();
        List<DisplayRuleCfg> displayRuleCfgs = Optional.ofNullable(config)
                .map(Config::getConfigs)
                .orElse(new ArrayList<>());
        displayRuleCfgs.stream().forEach(cfg->{
            if(Objects.equals(cfg.key, INSPECTION_ITEM)){
               //针对检查项目特殊处理
                DealSkuItemVO projectItemVO = buildProjectItemVO(cfg, attrs, dealSkuItemVOS);
                dealSkuItemVOS.add(projectItemVO);
            }else{
                String value = getValueFromAttrM(cfg.getKey(), attrs);
                if (StringUtils.isEmpty(value)){
                    return;
                }
                DealSkuItemVO dealSkuItemVO = buildDealSkuItemVO(cfg, value, null);
                dealSkuItemVOS.add(dealSkuItemVO);
            }

        });
        return dealSkuItemVOS;
    }



    private DealSkuItemVO buildProjectItemVO(DisplayRuleCfg cfg, List<AttrM> attrs, List<DealSkuItemVO> dealSkuItemVOS) {
        if (!Objects.equals(cfg.getKey(), INSPECTION_ITEM)){
            return new DealSkuItemVO();
        }
        String projectValue = getValueFromAttrM(INSPECTION_ITEM, attrs);
        if (StringUtils.isEmpty(projectValue)) {
            return new DealSkuItemVO();
        }
        List<Map> projectValueList = JsonCodec.converseList(projectValue, Map.class);

        //检查项目数量
        int count = projectValueList.size();
        if (count <= 0) {
            return new DealSkuItemVO();
        }

        //--检查项目
        List<Map<String, String>> processMap = projectValueList.stream().map(m -> {
            Map<String, String> nm = Maps.newHashMap();
            for (Object key : m.keySet()) {
                Object v = m.get(key);
                nm.put(key.toString(), v == null ? "" : v.toString());
            }
            return nm;
        }).collect(Collectors.toList());
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();

        InspectItemCfg config = cfg.getConfig();
        if (config == null) {
            return null;
        }

        List<SkuAttrAttrItemVO> valueAttrList = new ArrayList<>();
        for (Map<String, String> map : processMap) {
            SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
            skuAttrAttrItemVO.setInfo(buildSkuAttrAttrItemInfo(map, config));
            skuAttrAttrItemVO.setValues(buildSkuAttrAttrItemValues(map, config));
            skuAttrAttrItemVO.setName(buildSkuAttrAttrItemName(map, config));
            skuAttrAttrItemVO.setPopup(null);
            valueAttrList.add(skuAttrAttrItemVO);
        }
        dealSkuItemVO.setValueAttrs(valueAttrList);
        dealSkuItemVO.setName(buildProjectItemTitleName(attrs));
        dealSkuItemVO.setValue("共"+count+"项");
        dealSkuItemVO.setType(cfg.type);
        if(cfg.limit > 0) {
            dealSkuItemVO.setConfig(new DealSkuItemConfig(cfg.limit));
        }
        return dealSkuItemVO;

    }

    private String buildProjectItemTitleName(List<AttrM> attrs){
        String titleName = getValueFromAttrM(PROJECTNAME, attrs);
        if (StringUtils.isEmpty(titleName)){
            titleName = getValueFromAttrM(INSPECTION_TYPE, attrs);
        }
        return titleName;
    }

    private String buildSkuAttrAttrItemName(Map<String, String> map, InspectItemCfg config) {
        if (StringUtils.isEmpty(config.nameKey)){
            return null;
        }
        return map.get(config.nameKey);
    }

    private List<CommonAttrVO> buildSkuAttrAttrItemValues(Map<String, String> map, InspectItemCfg config) {
        if (StringUtils.isEmpty(config.introKey)){
            return new ArrayList<>();
        }
        String s = map.get(config.introKey);
        if (StringUtils.isEmpty(s)){
            return new ArrayList<>();
        }
        List<CommonAttrVO> result = new ArrayList<>();

        CommonAttrVO commonAttrVO = new CommonAttrVO();
        commonAttrVO.setName(null);
        commonAttrVO.setValue(s);
        result.add(commonAttrVO);
        return result;
    }

    private List<String> buildSkuAttrAttrItemInfo(Map<String, String> map, InspectItemCfg config) {
        if (StringUtils.isEmpty(config.priceKey)){
            return new ArrayList<>();
        }
        String s = map.get(config.priceKey);
        if (StringUtils.isEmpty(s)){
            return Lists.newArrayList("¥0");
        }
        return Lists.newArrayList("¥" + s);
    }

    private DealSkuItemVO buildDealSkuItemVO(DisplayRuleCfg cfg,String value,List<SkuAttrAttrItemVO> valueAttrs){
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(cfg.getDisplayName());
        dealSkuItemVO.setValue(value);
        dealSkuItemVO.setValueAttrs(valueAttrs);
        dealSkuItemVO.setType(cfg.getType());
        return dealSkuItemVO;
    }

    private String getValueFromAttrM(String key,List<AttrM> attrs){
        if(CollectionUtils.isEmpty(attrs))return null;
        return attrs.stream()
                .filter(attr -> attr.getName().equals(key))
                .findFirst()
                .map(AttrM::getValue)
                .orElse(null);
    }




    @Data
    @VPointCfg
    public static class Config {
        private List<DisplayRuleCfg> configs;
    }

    @Data
    public static class DisplayRuleCfg{
        //从attr中获取的key，检查项目key：inspection_item
        private String key;

        //展示的name
        private String displayName;

        //服务项目配置
        private InspectItemCfg config;

        //展示数量的多少
        private int limit;

        //类型 0-文案 1-流程类型(按照processDisplayRule展示)
        private int type = 0;
    }

    @Data
    public static class InspectItemCfg{
        private String nameKey;
        private String priceKey;
        private String introKey;
    }

}
