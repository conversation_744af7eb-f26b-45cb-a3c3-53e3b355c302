package com.sankuai.dzviewscene.product.ability.options;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreSyncHandlerVP;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.enums.ExtContextEnum;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.utils.CompletableFutureUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.ModelUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.enums.FieldsEnum;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2023/7/19 3:52 下午
 * ExtContextEnum可能有没补充的基础功能，详细可以搬运productshelf里面的ExtContextAbility。没时间一个个搬运了，只搬运了基础的
 */
@VPointOption(name = "美团侧基础参数取数fetcher", description = "如门店、城市等", code = MtBasicParamsFetcherOpt.CODE)
public class MtBasicParamsFetcherOpt extends PreSyncHandlerVP<MtBasicParamsFetcherOpt.Config> {

    public static final String CODE = "MtBasicParamsFetcherOpt";
    private static final String BACK_CAT_FIELD = "backMainCategoryPath";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public Map<String, Object> compute(ActivityCxt ctx, Param param, Config config) {
        List<Integer> needFields = param.getNeedFields();
        if (!PlatformUtil.isMT(param.getPlatform())) {
            return null;
        }
        long mtPoiId = ParamsUtil.getLongSafely(ctx, PmfConstants.Params.mtPoiIdL);
        int mtCityId = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.mtCityId);
        CompletableFuture<Long> dpPoiIdCf = convertDpPoiId(mtPoiId, ifNoNeed(needFields, ExtContextEnum.DP_SHOP_ID));
        CompletableFuture<Integer> dpCityIdCf = convertDpCityId(mtCityId, ifNoNeed(needFields, ExtContextEnum.DP_CITY_ID));
        CompletableFuture<ShopM> ctxDpShopCf = dpPoiIdCf.thenCompose(dpPoiId -> loadShop(dpPoiId, ctx, ifNoNeed(needFields, ExtContextEnum.SHOP_INFO)));
        CompletableFuture<Integer> shopDpCityIdCf = ctxDpShopCf.thenApply(shopM -> shopM == null ? 0 : shopM.getCityId());
        CompletableFuture<Integer> shopMtCityIdCf = ctxDpShopCf.thenCompose(ctxShop -> loadShopMtCityId(ctxShop, ctx, ifNoNeed(needFields, ExtContextEnum.SHOP_MT_CITY_ID)));
        CompletableFuture<List<Integer>> shopBackCatsCf = dpPoiIdCf.thenCompose(dpPoiId -> loadShopBackCategory(dpPoiId, ctx, ifNoNeed(needFields, ExtContextEnum.SHOP_BACK_CATS)));

        Map<String, CompletableFuture<Object>> resultMap = Maps.newHashMap();
        resultMap.put(PmfConstants.Params.mtPoiIdL, CompletableFuture.completedFuture(mtPoiId));
        resultMap.put(PmfConstants.Params.mtCityId, CompletableFuture.completedFuture(mtCityId));
        resultMap.put(PmfConstants.Params.dpPoiIdL, CompletableFutureUtil.covert2ObjCf(dpPoiIdCf));
        resultMap.put(PmfConstants.Params.dpCityId, CompletableFutureUtil.covert2ObjCf(dpCityIdCf));
        resultMap.put(PmfConstants.Params.shopDpCityId, CompletableFutureUtil.covert2ObjCf(shopDpCityIdCf));
        resultMap.put(PmfConstants.Params.shopMtCityId, CompletableFutureUtil.covert2ObjCf(shopMtCityIdCf));
        resultMap.put(PmfConstants.Params.shopBackCatIds, CompletableFutureUtil.covert2ObjCf(shopBackCatsCf));
        resultMap.put(PmfConstants.Params.dpShopM, CompletableFutureUtil.covert2ObjCf(ctxDpShopCf));
        return CompletableFutureUtil.each(resultMap).join();
    }

    private boolean ifNoNeed(List<Integer> needFields, ExtContextEnum target) {
        return !needFields.contains(target.getType());
    }

    private CompletableFuture<Integer> loadShopMtCityId(ShopM ctxDpShop, ActivityCxt ctx, boolean noNeed) {
        int shopMtCityId = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.shopMtCityId);
        if (shopMtCityId > 0) {
            return CompletableFuture.completedFuture(shopMtCityId);
        }
        if (ctxDpShop == null || ctxDpShop.getCityId() <= 0 || noNeed) {
            return CompletableFuture.completedFuture(0);
        }
        return compositeAtomService.getMtCityIdByDp(ctxDpShop.getCityId()).thenApply(id -> id == null ? 0 : id);
    }

    private CompletableFuture<Long> convertDpPoiId(long poiId, boolean noNeed) {
        if (noNeed) {
            return CompletableFuture.completedFuture(null);
        }
        return compositeAtomService.getDpByMtPoiIdL(poiId).thenApply(id -> Optional.ofNullable(id).orElse(0L));
    }

    private CompletableFuture<Integer> convertDpCityId(int mtCityId, boolean noNeed) {
        if (noNeed) {
            return CompletableFuture.completedFuture(null);
        }
        return compositeAtomService.getDpCityIdByMt(mtCityId).thenApply(id -> id == null ? 0 : id);
    }

    private CompletableFuture<ShopM> loadShop(long dpPoiId, ActivityCxt ctx, boolean ifNoNeed) {
        ShopM ctxShopM = ParamsUtil.getValue(ctx, PmfConstants.Ctx.ctxShop, null);
        if (ctxShopM != null || dpPoiId <= 0 || ifNoNeed) {
            return CompletableFuture.completedFuture(ctxShopM);
        }
        return compositeAtomService.findShopsByDpShopIds(buildDpPoiRequest(dpPoiId)).thenApply(dpPoiList -> {
            if (CollectionUtils.isEmpty(dpPoiList)) {
                return null;
            }
            DpPoiDTO dpPoiDTO = dpPoiList.get(0);
            ShopM shopM = new ShopM();
            shopM.setLongShopId(Optional.ofNullable(dpPoiDTO.getShopId()).orElse(0L));
            shopM.setShopUuid(Optional.ofNullable(dpPoiDTO.getUuid()).orElse(StringUtils.EMPTY));
            shopM.setShopName(Optional.ofNullable(dpPoiDTO.getShopName()).orElse(StringUtils.EMPTY));
            shopM.setShopType(Optional.ofNullable(dpPoiDTO.getShopType()).orElse(0));
            shopM.setCategory(Optional.ofNullable(dpPoiDTO.getMainCategoryId()).orElse(0));
            shopM.setBackCategory(ModelUtils.extractBackCat(dpPoiDTO));
            shopM.setLat(Optional.ofNullable(dpPoiDTO.getLat()).orElse(0d));
            shopM.setLng(Optional.ofNullable(dpPoiDTO.getLng()).orElse(0d));
            shopM.setCityId(Optional.ofNullable(dpPoiDTO.getCityId()).orElse(0));
            shopM.setStatus(Optional.ofNullable(dpPoiDTO.getPower()).orElse(0));
            return shopM;
        });
    }

    private CompletableFuture<List<Integer>> loadShopBackCategory(long dpPoiId, ActivityCxt ctx, boolean ifNoNeed) {
        if (dpPoiId <= 0 || ifNoNeed) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        ShopM ctxShopM = ParamsUtil.getValue(ctx, PmfConstants.Ctx.ctxShop, null);
        if (ctxShopM != null && CollectionUtils.isNotEmpty(ctxShopM.getBackCategory())) {
            return CompletableFuture.completedFuture(ctxShopM.getBackCategory());
        }
        return compositeAtomService.findShopsByDpShopIds(buildDpPoiRequest(dpPoiId)).thenApply(dpPoiList -> {
            if (CollectionUtils.isEmpty(dpPoiList)) {
                return Lists.newArrayList();
            }
            DpPoiDTO dpPoiDTO = dpPoiList.get(0);
            return ModelUtils.extractBackCat(dpPoiDTO);
        });
    }

    private DpPoiRequest buildDpPoiRequest(long dpPoiId) {
        DpPoiRequest dpPoiRequest = new DpPoiRequest();
        dpPoiRequest.setShopIds(Lists.newArrayList(dpPoiId));
        List<String> fields = Lists.newArrayList(FieldsEnum.SHOP_SERVER.getFields());
        fields.add(BACK_CAT_FIELD);
        dpPoiRequest.setFields(fields);
        return dpPoiRequest;
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
