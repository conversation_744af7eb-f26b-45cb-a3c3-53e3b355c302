package com.sankuai.dzviewscene.product.dealstruct.options.skuList;


import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.CommonAttrVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@VPointOption(name = "齿科sku属性列表变化点", description = "齿科sku属性列表变化点，根据配置展示sku属性", code = DentistrySkuAttrListOpt.CODE)
public class DentistrySkuAttrListOpt extends SkuAttrListVP<DentistrySkuAttrListOpt.Config> {
    public static final String CODE = "DentistrySkuAttrListOpt";

    @Override
    public List<DealSkuItemVO> compute(ActivityCxt context, Param param, Config config) {
        if (config == null
                || param == null
                || CollectionUtils.isEmpty(config.skuAttrDisplayRules)
                || param.getSkuItemDto() == null) {
            return null;
        }
        return buildDealSkuItems(config, param.getSkuItemDto(), param.getDealAttrs());
    }

    private List<DealSkuItemVO> buildDealSkuItems(Config config, SkuItemDto skuItemDto, List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return null;
        }
        List<DealSkuItemVO> dealSkuItemVOS = new ArrayList<>();
        buildDefaultConfig(config);
        //先将服务项目加上
        buildProcess(dealSkuItemVOS,skuItemDto,config);
        //添加sku信息
        buildSku(dealSkuItemVOS,skuItemDto,dealAttrs,config);
        return dealSkuItemVOS;
    }

    private void buildDefaultConfig(Config config) {
        if (StringUtils.isBlank(config.getProcessTitle())) {
            config.setProcessTitle("项目内容");
        }
        if (StringUtils.isBlank(config.getProcessDesc())) {
            config.setProcessDesc("项目说明");
        }
        if (StringUtils.isBlank(config.getSkuTitle())) {
            config.setSkuTitle("使用要求");
        }
    }

    private void buildSku(List<DealSkuItemVO> dealSkuItemVOS, SkuItemDto skuItemDto,List<AttrM> dealAttrs, Config config) {
        for (SkuAttrDisplayRuleCfg skuAttrDisplayRule : config.getSkuAttrDisplayRules()) {
            if (skuAttrDisplayRule.getRouteCondition() == null || CollectionUtils.isEmpty(skuAttrDisplayRule.getDisplayAttrRules())) {
                continue;
            }
            boolean satisfyAllAttrKeyValue = ProductMAttrUtils.satisfyAllAttrKeyValue(dealAttrs, skuAttrDisplayRule.getRouteCondition().getSatisfyAllAttrKeyValuesMap());
            boolean satisfyAllSkuAttrKeyValue = ProductMAttrUtils.satisfyAllSkuAttrKeyValue(skuItemDto, skuAttrDisplayRule.getRouteCondition().getSatisfySkuAllAttrKeyValuesMap());
            if (satisfyAllAttrKeyValue && satisfyAllSkuAttrKeyValue) {
                dealSkuItemVOS.add(buildDealSkuItemsByConfig(skuItemDto.getAttrItems(), skuAttrDisplayRule.getDisplayAttrRules() ,dealAttrs,config));
                break;
            }
        }
    }

    private void buildProcess(List<DealSkuItemVO> dealSkuItemVOS, SkuItemDto skuItemDto, Config config) {
        String serviceProcess = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), "serviceProcess");
        List<Map<String, String>> processList = JsonCodec.decode(serviceProcess, new TypeReference<List<Map<String, String>>>() {
        });
        if (CollectionUtils.isNotEmpty(processList)) {
            DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
            dealSkuItemVO.setType(5);
            dealSkuItemVO.setName(config.getProcessTitle());
            List<SkuAttrAttrItemVO> valueAttrs = new ArrayList<>();
            dealSkuItemVO.setValueAttrs(valueAttrs);
            processList.stream().forEach(process -> {
                String processName = process.get("processName");
                String processDescription = process.get("processDescription");
                SkuAttrAttrItemVO itemVO = new SkuAttrAttrItemVO();
                itemVO.setName(processName);
                if (StringUtils.isNotBlank(processDescription)) {
                    List<CommonAttrVO> values = new ArrayList<>();
                    CommonAttrVO attrVO = new CommonAttrVO();
                    attrVO.setValue(processDescription);
                    attrVO.setName(config.getProcessDesc());
                    values.add(attrVO);
                    itemVO.setValues(values);
                }
                valueAttrs.add(itemVO);
            });
            dealSkuItemVOS.add(dealSkuItemVO);
        }
    }

    private DealSkuItemVO buildDealSkuItemsByConfig(List<SkuAttrItemDto> attrItems, List<DisplayAttrRuleCfg> displayAttrRules,List<AttrM> dealAttrs,Config config) {
        //添加sku
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setType(5);
        dealSkuItemVO.setName(config.getSkuTitle());
        List<SkuAttrAttrItemVO> valueAttrs = new ArrayList<>();
        dealSkuItemVO.setValueAttrs(valueAttrs);
        displayAttrRules.forEach(attrRule -> {
            String attrValue = getAttrValue(DealDetailUtils.getSkuAttrValueBySkuAttrName(attrItems, attrRule.getAttrKey()), attrRule.getAttrValueAliasMap());
            if(StringUtils.isBlank(attrValue)){
                String valueFromDealAttr = getAttrValue(getValueFromDealAttr(dealAttrs, attrRule.getAttrKey()),attrRule.getAttrValueAliasMap());
                if (StringUtils.isEmpty(valueFromDealAttr)){
                    return;
                }
                attrValue= valueFromDealAttr;
            }
            SkuAttrAttrItemVO itemVO = new SkuAttrAttrItemVO();
            String join = String.join("：", Lists.newArrayList(attrRule.getAttrTitle(), attrValue));
            itemVO.setName(join);
            valueAttrs.add(itemVO);
        });
        return dealSkuItemVO;
    }

    private String getValueFromDealAttr(List<AttrM> dealAttrs, String attrKey) {
        if (CollectionUtils.isEmpty(dealAttrs) || StringUtils.isEmpty(attrKey)) {
            return null;
        }
        AttrM attrM = dealAttrs.stream().filter(attr -> attr.getName().equals(attrKey)).findFirst().orElse(null);
        if (attrM == null) {
            return null;
        }
        return attrM.getValue();
    }

    private String getAttrValue(String attrValue, Map<String, String> attrValueAliasMap) {
        //别名配置为空，或没有配置，则直接展示
        if (MapUtils.isEmpty(attrValueAliasMap) || !attrValueAliasMap.keySet().contains(attrValue)) {
            return attrValue;
        }
        return attrValueAliasMap.get(attrValue);
    }


    @Data
    @VPointCfg
    public static class Config {
        //sku属性构造模型列表
        private List<SkuAttrDisplayRuleCfg> skuAttrDisplayRules;
        private String skuTitle;

        private String processTitle;
        private String processDesc;
    }

    @Data
    public static class SkuAttrDisplayRuleCfg {
        //路由条件
        private RouteConditionCfg routeCondition;
        //sku属性规则配置
        private List<DisplayAttrRuleCfg> displayAttrRules;
    }

    @Data
    public static class DisplayAttrRuleCfg {
        //sku属性展示标题
        private String attrTitle;
        //属性key
        private String attrKey;
        //属性value与别名映射
        private Map<String, String> attrValueAliasMap;
    }

    @Data
    public static class RouteConditionCfg {
        //满足所有属性key及属性值的映射，当该字段为空时不需要校验
        private Map<String, List<String>> satisfyAllAttrKeyValuesMap;
        //满足服务项目Sku所有属性key及属性值的映射，当该字段为空时不需要校验
        private Map<String, List<String>> satisfySkuAllAttrKeyValuesMap;
    }
}
