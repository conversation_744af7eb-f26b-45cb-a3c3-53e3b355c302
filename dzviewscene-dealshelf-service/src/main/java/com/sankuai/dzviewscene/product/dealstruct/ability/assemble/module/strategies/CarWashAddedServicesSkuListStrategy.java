package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.DealDetailSkuGroupsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuSetModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuUniModel;
import com.sankuai.dzviewscene.product.dealstruct.model.SkuItemModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 洗车、精洗团详的"增值服务内容"模块展示
 *
 * <AUTHOR>
 * @date 2023/5/15
 */
@Component("carWashAddedServicesSkuListStrategy")
public class CarWashAddedServicesSkuListStrategy implements ModuleStrategy {

    private static final String DEFAULT_ICON = "https://p0.meituan.net/ingee/9c0d96f8807ed9c42cfb650127fd901e4966.png";
    private static final Map<String, String> SERVICE_ICON_MAP = new HashMap<>(5);

    static {
        SERVICE_ICON_MAP.put("检测", "https://p0.meituan.net/ingee/bcda4331fa6b70d022411cff305508725352.png");
        SERVICE_ICON_MAP.put("玻璃水", "https://p0.meituan.net/ingee/e3653464c4d1516e21942d7e6c71378e4812.png");
        SERVICE_ICON_MAP.put("防冻液", "https://p0.meituan.net/ingee/eb64b3b6e93d7e3fe4a663bbf875b9564213.png");
        SERVICE_ICON_MAP.put("洗车", "https://p0.meituan.net/ingee/bdd503256b89dca904bf2c835ec333824559.png");
        SERVICE_ICON_MAP.put("停车", "https://p0.meituan.net/ingee/e604dde4553b33b7eafc54119486528e3269.png");
    }


    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        List<DealDetailSkuUniModel> dealDetailSkuUniModels = activityCxt.getSource(DealDetailSkuGroupsBuilder.CODE);
        //1、获取需要的依赖数据
        SkuItemDto skuItemDto = getFirstValidSkuItemDto(CollectUtils.firstValue(dealDetailSkuUniModels));
        //2、组装VOList
        List<DealSkuVO> dealSkuVOList = buildDealSkuVOList(skuItemDto);
        //3、组装为结果VO
        return buildDealDetailModuleVO(dealSkuVOList);
    }

    /**
     * 获取第一个有效的服务项目
     *
     * @param dealDetail
     * @return
     */
    private SkuItemDto getFirstValidSkuItemDto(DealDetailSkuUniModel dealDetail) {
        if (dealDetail == null) {
            return null;
        }
        DealDetailSkuGroupModel skuGroupModel = CollectUtils.firstValue(dealDetail.getMustGroups());
        if (skuGroupModel == null) {
            return null;
        }
        DealDetailSkuSetModel dealDetailSkuSetModel = CollectUtils.firstValue(skuGroupModel.getSkuSetModels());
        if (dealDetailSkuSetModel == null) {
            return null;
        }
        SkuItemModel skuItemModel = CollectUtils.firstValue(dealDetailSkuSetModel.getSkuItems());
        if (skuItemModel == null) {
            return null;
        }
        return skuItemModel.getSkuItemDto();
    }

    private List<DealSkuVO> buildDealSkuVOList(SkuItemDto skuItemDto) {
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return new ArrayList<>();
        }
        //查找增值服务
        SkuAttrItemDto addedServicesAttr = skuItemDto.getAttrItems().stream().filter(attr-> Objects.equals(attr.getAttrName(), "addedServices")).findFirst().orElse(null);
        if (addedServicesAttr == null || StringUtils.isEmpty(addedServicesAttr.getAttrValue())) {
            return new ArrayList<>();
        }
        List<String> addedServices = Arrays.asList(addedServicesAttr.getAttrValue().split("、"));
        return addedServices.stream().map(this::buildDealSkuVO).collect(Collectors.toList());
    }

    private DealSkuVO buildDealSkuVO(String serviceName) {
        if (StringUtils.isEmpty(serviceName)){
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(serviceName);
        dealSkuVO.setCopies("1份");
        dealSkuVO.setIcon(getServiceIcon(serviceName));
        return dealSkuVO;
    }

    private static String getServiceIcon(String service) {
        for (Map.Entry<String, String> entry : SERVICE_ICON_MAP.entrySet()) {
            if (service.contains(entry.getKey())) {
                return entry.getValue();
            }
        }
        return DEFAULT_ICON;
    }

    private DealDetailModuleVO buildDealDetailModuleVO(List<DealSkuVO> dealSkuVOList) {
        if (CollectionUtils.isEmpty(dealSkuVOList)) {
            return null;
        }
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        List<DealSkuGroupModuleVO> skuGroupsModel1 = Lists.newArrayList();
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(dealSkuVOList);
        dealSkuGroupModuleVO.setTitle("");
        skuGroupsModel1.add(dealSkuGroupModuleVO);
        dealDetailModuleVO.setSkuGroupsModel1(skuGroupsModel1);
        return dealDetailModuleVO;
    }
}
