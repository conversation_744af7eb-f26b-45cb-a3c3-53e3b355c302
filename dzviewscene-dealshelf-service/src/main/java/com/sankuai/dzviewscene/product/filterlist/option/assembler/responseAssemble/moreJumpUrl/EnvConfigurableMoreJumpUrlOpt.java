package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.moreJumpUrl;

import com.dianping.vc.enums.VCClientTypeEnum;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListMoreJumpUrlVP;
import com.sankuai.dzviewscene.shelf.business.utils.UrlUtils;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/6
 */
@VPointOption(name = "跳转链接可配置", description = "从环境中取参数拼接", code = "EnvConfigurableMoreJumpUrlOpt")
public class EnvConfigurableMoreJumpUrlOpt extends DealFilterListMoreJumpUrlVP<EnvConfigurableMoreJumpUrlOpt.Config> {

    private static final String DP_XCX_PREFIX = "/pages/webview/webview?url=";

    private static final String MT_XCX_PREFIX = "/index/pages/h5/h5?weburl=";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if(StringUtils.isNotEmpty(context.getParam(ShelfActivityConstants.Ctx.ctxFlagshipStoreUrl))){
            return context.getParam(ShelfActivityConstants.Ctx.ctxFlagshipStoreUrl);
        }
        UrlCfg urlCfg = config.doGetPlatformUrlCfgByUserAgent(param.getUserAgent());
        if (urlCfg == null || StringUtils.isEmpty(urlCfg.getUrlFormat())) {
            return StringUtils.EMPTY;
        }
        //prepare env
        Map<String, Object> paramEnv = prepareEnvOfParam(param);
        //获取配置指定的参数，如果没有会用空字符串占位
        List<String> orderedParams = getUrlParamByConfig(paramEnv, config.getDynamicValueEnvKeys());
        String resUrl = String.format(urlCfg.getUrlFormat(), orderedParams.toArray(new String[0]));
        if (config.needMinAppUrlProcess(param.getUserAgent())) {
            return getMinAppUrl(resUrl, param.getUserAgent());
        }
        if (StringUtils.isNotEmpty(urlCfg.getPrefixWithURLEncode())) {
            //需要encode + 加前缀
            return urlCfg.getPrefixWithURLEncode() + UrlUtils.urlEncode(resUrl);
        }
        return resUrl;
    }

    /**
     * @param param
     * @return 准备参数用于配置化取值
     */
    private Map<String, Object> prepareEnvOfParam(Param param) {
        Map<String, Object> env = new HashMap<>();
        env.put("platform", param.getPlatform());
        env.put("entityId", param.getEntityId());
        env.put("userAgent", param.getUserAgent());
        env.put("platformCityId", param.getPlatformCityId());
        return env;
    }

    /**
     * @param env
     * @param dynamicValueEnvKeys
     * @return 根据指定的入参从环境参数中取 url 参数
     */
    private List<String> getUrlParamByConfig(Map<String, Object> env, List<String> dynamicValueEnvKeys) {
        if (CollectionUtils.isEmpty(dynamicValueEnvKeys) || MapUtils.isEmpty(env)) {
            return new ArrayList<>();
        }
        List<String> params = new ArrayList<>(dynamicValueEnvKeys.size());
        for (String valueEnvKey : dynamicValueEnvKeys) {
            Object paramValueObj = env.get(valueEnvKey);
            if (paramValueObj == null) {
                params.add(StringUtils.EMPTY);
            } else {
                params.add(String.valueOf(paramValueObj));
            }
        }
        return params;
    }

    /**
     * 处理小程序的跳转链接
     * @param resUrl
     * @param userAgent
     * @return
     */
    private String getMinAppUrl(String resUrl, int userAgent) {
        if (StringUtils.isEmpty(resUrl)) {
            return resUrl;
        }
        String minAppPrefix = (userAgent == VCClientTypeEnum.DP_XCX.getCode()) ? DP_XCX_PREFIX : MT_XCX_PREFIX;
        return minAppPrefix + UrlUtils.urlEncode(resUrl);
    }

    @Data
    @VPointCfg
    public static class Config {
        /**
         * 链接格式
         */
        private String dpUrl;

        private String mtUrl;

        /**
         * 从环境中取动态参数的 key，静态数据可自行在 url 中拼接完成
         */
        private List<String> dynamicValueEnvKeys;

        /**
         * 对小程序链接做特殊处理
         * 增加指定前缀：
         *  点评小程序：/pages/webview/webview?url=
         *  美团小程序：/index/pages/h5/h5?weburl=
         *  可以替换为 urlCfgMap 了
         */
        @Deprecated
        private boolean enableMinAppUrlProcess;

        /**
         * 对应平台的 Url 格式
         * key: userAgent {@link  VCClientTypeEnum}
         */
        private Map<Integer, UrlCfg> urlCfgMap;

        /**
         * @param userAgent
         * @return 根据平台获取url
         */
        public UrlCfg doGetPlatformUrlCfgByUserAgent(int userAgent) {
            if (MapUtils.isEmpty(this.urlCfgMap)) {
                //没有时返回默认平台定义的
                return PlatformUtil.isMT(userAgent) ? new UrlCfg(this.mtUrl) : new UrlCfg(this.dpUrl);
            }
            return this.urlCfgMap.get(userAgent);
        }

        public boolean needMinAppUrlProcess(int userAgent) {
            return this.enableMinAppUrlProcess && (userAgent == VCClientTypeEnum.DP_XCX.getCode() || userAgent == VCClientTypeEnum.MT_XCX.getCode());
        }
    }

    @Data
    public static class UrlCfg {
        private String urlFormat;

        /**
         * 固定前缀，在生成 URL 后，需对 URL 进行 Encode 后，拼接固定前缀
         * 若为空，则不进行 Encode
         */
        private String prefixWithURLEncode;

        public UrlCfg(String urlFormat) {
            this.urlFormat = urlFormat;
        }

        public UrlCfg() {
        }
    }
}
