package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints.SkuItemPriorityVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/27 10:45 下午
 */
@VPointOption(name = "通过sku类别确定sku优先级变化点", description = "通过sku类别确定sku优先级变化点",code = SkuPriorityBySkuCategoryVPO.CODE, isDefault = false)
public class SkuPriorityBySkuCategoryVPO extends SkuItemPriorityVP<SkuPriorityBySkuCategoryVPO.Config> {

    public static final String CODE = "SkuPriorityBySkuCategoryVPO";

    @Override
    public Integer compute(ActivityCxt context, Param param, Config config) {
        SkuItemDto skuItemDto = param.getSkuItemDto();
        List<ProductSkuCategoryModel> productCategories = param.getProductCategories();
        List<SkuCategoryPriorityModel> skuCategoryPriorityModels = config.getSkuCategoryPriorityModels();
        return getPriority(skuItemDto, productCategories, skuCategoryPriorityModels);
    }

    private int getPriority(SkuItemDto skuItemDto, List<ProductSkuCategoryModel> productCategories, List<SkuCategoryPriorityModel> skuCategoryPriorityModels) {
        if (skuItemDto == null || CollectionUtils.isEmpty(skuCategoryPriorityModels)) {
            return Integer.MAX_VALUE;
        }
        String skuCategory = getSkuCategory(skuItemDto, productCategories);
        if (StringUtils.isEmpty(skuCategory)) {
            return Integer.MAX_VALUE;
        }
        SkuCategoryPriorityModel priorityModel = skuCategoryPriorityModels.stream().filter(model -> skuCategory.equals(model.getSkuCategory())).findFirst().orElse(null);
        if (priorityModel == null) {
            return Integer.MAX_VALUE;
        }
        if (CollectionUtils.isEmpty(priorityModel.getSkuChildCategoryPriorityModels())) {
            return priorityModel.getPriority();
        }
        SkuChildCategoryPriorityModel skuChildCategory = priorityModel.getSkuChildCategoryPriorityModels().stream().filter(childCategory -> isSkuChildCategory(childCategory, skuItemDto)).findFirst().orElse(null);
        if (skuChildCategory == null) {
            return priorityModel.getPriority();
        }
        return skuChildCategory.getPriority();
    }

    private boolean isSkuChildCategory(SkuChildCategoryPriorityModel skuChildCategoryPriorityModel, SkuItemDto skuItemDto) {
        if (skuChildCategoryPriorityModel == null || skuItemDto == null) {
            return false;
        }
        String skuChildCategory = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), skuChildCategoryPriorityModel.getCategoryName());
        return StringUtils.isNotEmpty(skuChildCategory) && skuChildCategory.equals(skuChildCategoryPriorityModel.getSkuCategory());
    }

    private String getSkuCategory(SkuItemDto skuItemDto, List<ProductSkuCategoryModel> productCategories) {
        if (skuItemDto == null || CollectionUtils.isEmpty(productCategories)) {
            return null;
        }
        ProductSkuCategoryModel productSkuCategoryModel = productCategories.stream().filter(category -> category.getProductCategoryId() != null && skuItemDto.getProductCategory() == category.getProductCategoryId()).findFirst().orElse(null);
        if (productSkuCategoryModel == null) {
            return null;
        }
        return productSkuCategoryModel.getCnName();
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<SkuCategoryPriorityModel> skuCategoryPriorityModels;
    }

    @Data
    public static class SkuCategoryPriorityModel {
        private int priority;
        private String skuCategory;
        private List<SkuChildCategoryPriorityModel> skuChildCategoryPriorityModels;
    }

    @Data
    public static class SkuChildCategoryPriorityModel {
        private int priority;
        private String skuCategory;
        private String categoryName;
    }

}
