package com.sankuai.dzviewscene.product.ability.extCtx;

import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.IVPoint;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.InterceptHandlerVP;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreAsyncHandlerVP;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreSyncHandlerVP;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivity;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivity;
import com.sankuai.dzviewscene.product.shelf.utils.ShelfInterceptUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivity;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.framework.monitor.AbilityExecuteMonitor;
import com.sankuai.dzviewscene.shelf.platform.utils.CompletableFutureUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2023/7/19 2:24 下午
 */
@Ability(code = PreHandlerContextAbility.CODE,
        name = "上下文预处理能力，写上下文统一处理",
        description = "提供可选的上下文填充(仅上下文中没有该字段则填充)，详见枚举类ExtContextEnum",
        activities = {DealFilterListActivity.CODE, DealShelfActivity.CODE, DealDetailActivity.CODE, UnifiedShelfActivity.CODE},
        dependency = {ShelfDouHuFetcher.CODE}
)
public class PreHandlerContextAbility extends PmfAbility<Void, Void, ExtContextCfg> {

    public static final String CODE = "PreHandlerContextAbility";

    @Override
    public CompletableFuture<Void> build(ActivityCxt ctx, Void request, ExtContextCfg contextCfg) {
        long startTime = System.currentTimeMillis();
        return AbilityExecuteMonitor.executeMonitor(execute(ctx, request, contextCfg), ctx, CODE, startTime);
    }

    private CompletableFuture<Void> execute(ActivityCxt ctx, Void request, ExtContextCfg contextCfg){
        //可以并行处理的opt
        CompletableFuture<Map<String, Object>> asyncCf = executeAsyncVP(ctx);
        //需要串行处理的opt
        executeSyncVP(ctx, contextCfg);
        //先执行完executeSyncVP再处理asyncCf
        return asyncCf.thenApply(asyncResMap -> {
            putResultIntoCtx(asyncResMap, ctx);
            executeInterceptVP(ctx);
            return null;
        });
    }

    private void putResultIntoCtx(Map<String, Object> resMap, ActivityCxt ctx) {
        if (MapUtils.isEmpty(resMap)) {
            return;
        }
        resMap.forEach((key, value) -> {
            if (value != null) {
                ctx.addParam(key, value);
            }
        });
    }

    private void executeInterceptVP(ActivityCxt ctx) {
        List<IVPoint> interceptVPoints = findVPoints(ctx, InterceptHandlerVP.CODE);
        if (CollectionUtils.isEmpty(interceptVPoints)) {
            return;
        }
        boolean finalIntercept = false;
        //多个拦截条件有一个命中就执行拦截
        for (IVPoint ivPoint : interceptVPoints) {
            InterceptHandlerVP<?> interceptHandlerVP = (InterceptHandlerVP) ivPoint;
            Boolean intercept = interceptHandlerVP.execute(ctx, InterceptHandlerVP.Param.builder()
                    .ctx(ctx)
                    .platform(ParamsUtil.getIntSafely(ctx, PmfConstants.Params.platform))
                    .build());
            finalIntercept = finalIntercept || (intercept != null && intercept);
        }
        if(finalIntercept){
            ShelfInterceptUtils.setInterceptContext(ctx);
        }
    }

    private CompletableFuture<Map<String, Object>> executeAsyncVP(ActivityCxt ctx) {
        List<IVPoint> asyncVPoints = findVPoints(ctx, PreAsyncHandlerVP.CODE);
        Map<String, CompletableFuture<Object>> asyncResult = Maps.newHashMap();
        for(IVPoint ivPoint : asyncVPoints) {
            PreAsyncHandlerVP<?> preAsyncHandlerVP = (PreAsyncHandlerVP) ivPoint;
            CompletableFuture<Object> execute = preAsyncHandlerVP.execute(ctx, PreAsyncHandlerVP.Param.builder()
                    .ctx(ctx)
                    .platform(ParamsUtil.getIntSafely(ctx, PmfConstants.Params.platform))
                    .build());
            if(execute != null) {
                asyncResult.put(preAsyncHandlerVP.getVPointOptionCode(), execute);
            }
        }
        return CompletableFutureUtil.each(asyncResult);
    }

    private void executeSyncVP(ActivityCxt ctx, ExtContextCfg contextCfg) {
        List<IVPoint> ivPoints = findVPoints(ctx, PreSyncHandlerVP.CODE);
        if(CollectionUtils.isEmpty(ivPoints) || CollectionUtils.isEmpty(contextCfg.getNeedFields())) {
            return;
        }
        List<IVPoint> ivPointsWithDependency = sortVPWitchDependency(ivPoints, contextCfg.getDependency());
        for(IVPoint ivPoint : ivPointsWithDependency) {
            PreSyncHandlerVP<?> preSyncHandlerVP = (PreSyncHandlerVP) ivPoint;
            Map<String, Object> execute = preSyncHandlerVP.execute(ctx, PreSyncHandlerVP.Param.builder()
                    .ctx(ctx)
                    .needFields(contextCfg.getNeedFields())
                    .platform(ParamsUtil.getIntSafely(ctx, PmfConstants.Params.platform))
                    .build());
            putResultIntoCtx(execute, ctx);
        }
    }

    //解决多个opt之间的依赖问题，人为规定多个opt按照配置的顺序执行
    private List<IVPoint> sortVPWitchDependency(List<IVPoint> ivPoints, List<String> dependency) {
        if (CollectionUtils.isEmpty(dependency)) {
            return ivPoints;
        }
        ivPoints.sort((a, b) -> sort(a, b, dependency));
        return ivPoints;
    }

    private int sort(IVPoint v1, IVPoint v2, List<String> orders) {
        PreSyncHandlerVP vp1 = (PreSyncHandlerVP)v1;
        PreSyncHandlerVP vp2 = (PreSyncHandlerVP)v2;
        if (CollectionUtils.isEmpty(orders)) {
            return 0;
        }
        if (!orders.contains(vp1.getVPointOptionCode())) {
            return 1;
        }
        if (!orders.contains(vp2.getVPointOptionCode())) {
            return -1;
        }
        int index1 = orders.indexOf(vp1.getVPointOptionCode());
        int index2 = orders.indexOf(vp2.getVPointOptionCode());
        return index1 - index2;
    }

}
