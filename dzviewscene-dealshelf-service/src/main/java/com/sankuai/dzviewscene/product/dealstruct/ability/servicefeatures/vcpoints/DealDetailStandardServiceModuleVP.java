package com.sankuai.dzviewscene.product.dealstruct.ability.servicefeatures.vcpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.servicefeatures.StandardServiceModuleBuilder;
import com.sankuai.dzviewscene.product.dealstruct.vo.StandardServiceVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/23 8:56 下午
 */
@VPoint(name = "团购详情模块标准服务流程组件变化点", description = "团购详情模块标准服务流程组件变化点",code = DealDetailStandardServiceModuleVP.CODE, ability = StandardServiceModuleBuilder.CODE)
public abstract class DealDetailStandardServiceModuleVP<T> extends PmfVPoint<StandardServiceVO, DealDetailStandardServiceModuleVP.Param, T> {

    public static final String CODE = "DealDetailStandardServiceModuleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private List<AttrM> dealAttrs;
    }
}
