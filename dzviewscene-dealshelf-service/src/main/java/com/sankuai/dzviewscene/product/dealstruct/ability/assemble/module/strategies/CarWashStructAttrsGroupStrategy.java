package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.DealDetailSkuGroupsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailConstants;
import com.sankuai.dzviewscene.product.dealstruct.model.*;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import jodd.util.StringUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * created by wangxinyuan in 2021/03/11
 */
@Component("carWashStructAttrsGroupStrategy")
public class CarWashStructAttrsGroupStrategy implements ModuleStrategy {

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.car.wash.strategy.content", defaultValue = "{}")
    private Config config;

    private static final int ATTR_MATA_ID_KEY = 2443;

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        int dealId = getDealId(activityCxt);
        int platform = getPlatform(activityCxt);
        List<DealDetailSkuUniModel> dealDetailSkuUniModels = activityCxt.getSource(DealDetailSkuGroupsBuilder.CODE);
        //1、获取需要的依赖数据
        SkuItemDto skuItemDto = getSkuItemDto(CollectUtils.firstValue(dealDetailSkuUniModels));
        //2、组装VOList
        List<DealSkuVO> dealSkuVOList = buildDealSkuVOList(skuItemDto);
        //3、后置处理：白名单内团单不展示"整车外表清洁"和"内饰清洁除尘"下的属性信息
        afterProcessingDealSkuVOList(dealSkuVOList, dealId, platform);
        //4、组装为结果VO
        return buildDealDetailModuleVO(dealSkuVOList);
    }

    private int getPlatform(ActivityCxt activityCxt) {
        ActivityContext activityContext = ActivityCtxtUtils.toActivityContext(activityCxt);
        return ParamsUtil.getIntSafely(activityContext, DealDetailConstants.Params.platform);
    }

    private void afterProcessingDealSkuVOList(List<DealSkuVO> dealSkuVOList, int dealId, int platform) {
        if (CollectionUtils.isEmpty(dealSkuVOList)) {
            return;
        }
        boolean isDealInWhiteList = isDealWithoutSkuAttrInfo(dealId, platform);
        if (!isDealInWhiteList) {
            return;
        }
        for(DealSkuVO dealSkuVO : dealSkuVOList) {
            if (dealSkuVO == null) {
                continue;
            }
            if (config != null && CollectionUtils.isNotEmpty(config.getSkuTitleWithoutSkuAttrInfo()) && config.getSkuTitleWithoutSkuAttrInfo().contains(dealSkuVO.getTitle())) {
                dealSkuVO.setItems(null);
            }
        }
    }

    //白名单内的团单不展示"整车外表清洁"和"内饰清洁除尘"下的属性信息
    private boolean isDealWithoutSkuAttrInfo(int dealId, int platform) {
        if (config == null) {
            return false;
        }
        List<Integer> dealIdInWhiteList = config.getDpDealIdWhiteList();
        if (PlatformUtil.isMT(platform)) {
            dealIdInWhiteList = config.getMtDealIdWhiteList();
        }
        if (CollectionUtils.isEmpty(dealIdInWhiteList)) {
            return false;
        }
        return dealIdInWhiteList.contains(dealId);
    }

    private int getDealId(ActivityCxt activityCxt) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        DealDetailInfoModel dealDetailInfoModel = CollectUtils.firstValue(dealDetailInfoModels);
        if (dealDetailInfoModel == null) {
            return -1;
        }
        return dealDetailInfoModel.getDealId();
    }

    private SkuItemDto getSkuItemDto(DealDetailSkuUniModel dealDetailSkuUniModel) {
        if(dealDetailSkuUniModel == null || CollectionUtils.isEmpty(dealDetailSkuUniModel.getMustGroups())) {
            return null;
        }
        DealDetailSkuSetModel dealDetailSkuSetModel = getDealDetailSkuSetModel(dealDetailSkuUniModel.getMustGroups().get(0));
        if(dealDetailSkuSetModel == null) {
            return null;
        }
        return getMatchedSkuItemByConfig(dealDetailSkuSetModel.getSkuItems());
    }

    private DealDetailSkuSetModel getDealDetailSkuSetModel(DealDetailSkuGroupModel dealDetailSkuGroupModel) {
        if(dealDetailSkuGroupModel == null || CollectionUtils.isEmpty(dealDetailSkuGroupModel.getSkuSetModels())) {
            return null;
        }
        return dealDetailSkuGroupModel.getSkuSetModels().get(0);
    }

    private SkuItemDto getMatchedSkuItemByConfig(List<SkuItemModel> skuItems) {
        if(CollectionUtils.isEmpty(skuItems)) {
            return null;
        }
        SkuItemModel skuItemModel = skuItems.stream()
                .filter(skuItem -> checkProductCategory(skuItem)).findFirst().orElse(null);
        return skuItemModel == null ? null : skuItemModel.getSkuItemDto();
    }

    private boolean checkProductCategory(SkuItemModel skuItem) {
        if(config == null || CollectionUtils.isEmpty(config.getProductCategoryList())) {
            return true;
        }
        if(skuItem == null || skuItem.getProductCategory() <= 0) {
            return false;
        }
        return config.getProductCategoryList().contains(String.valueOf(skuItem.getProductCategory()));
    }

    private List<DealSkuVO> buildDealSkuVOList(SkuItemDto skuItemDto) {
        if(skuItemDto == null) {
            return Lists.newArrayList();
        }
        //当团单的服务项目含有基础服务内容属性时（即metaAttrId为2443的服务项目属性），展示基础服务内容
        List<String> attrValues = buildAttrValues(skuItemDto.getAttrItems(), ATTR_MATA_ID_KEY);
        //当团单的服务项目不含有基础服务内容属性时，返回为空
        if (CollectionUtils.isEmpty(attrValues)) {
            return null;
        }
        return buildDealSkuGroupModuleVO(skuItemDto, attrValues);
    }

    private List<String> buildAttrValues(List<SkuAttrItemDto> skuAttrItems, int mataId) {
        if(CollectionUtils.isEmpty(skuAttrItems)) {
            return Lists.newArrayList();
        }
        String serviceContent = getMatchedMataAttr(skuAttrItems, mataId);
        if(StringUtil.isEmpty(serviceContent)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(serviceContent.split("、"));
    }

    private String getMatchedMataAttr(List<SkuAttrItemDto> skuAttrItems, int mataId) {
        return skuAttrItems.stream()
                .filter(skuAttrItem -> StringUtil.isNotEmpty(skuAttrItem.getAttrName()) && skuAttrItem.getMetaAttrId() == mataId)
                .map(SkuAttrItemDto::getAttrValue).findFirst().orElse(null);
    }

    private List<DealSkuVO> buildDealSkuGroupModuleVO(SkuItemDto skuItemDto, List<String> attrValues) {
        if(CollectionUtils.isEmpty(attrValues) || config == null) {
            return null;
        }
        return attrValues.stream()
                .filter(Objects::nonNull)
                .map(skuItem -> buildSkuVO(skuItemDto.getProductCategory(), skuItemDto.getCopies(), skuItem, config))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    private DealSkuVO buildSkuVO(long productCategory, int copies, String skuItemValue, Config config) {
        AttrVO attrValue = getAttrVO(productCategory, skuItemValue);
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(skuItemValue);
        dealSkuVO.setIcon(buildIcon(attrValue, skuItemValue,  config.getDefaultIcon()));
        dealSkuVO.setCopies(buildCopies(copies));
        List<DealSkuItemVO> dealSkuItems = buildItems(attrValue);
        if(CollectionUtils.isNotEmpty(dealSkuItems)) {
            dealSkuVO.setItems(dealSkuItems);
        }
        return dealSkuVO;
    }

    private String buildCopies(int copies) {
        if(copies <= 0) {
            return "";
        }
        return copies + "份";
    }

    private List<DealSkuItemVO> buildItems(AttrVO attrValue) {
        if(attrValue == null || StringUtil.isEmpty(attrValue.getDesc())) {
            return Lists.newArrayList();
        }
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(attrValue.getDesc());
        return Lists.newArrayList(dealSkuItemVO);
    }

    private AttrVO getAttrVO(long productCategory, String skuItemValue) {
        List<AttrVO> attrValues = getCategoryId2VO(String.valueOf(productCategory), config);
        if(CollectionUtils.isEmpty(attrValues)) {
            return null;
        }
        return attrValues.stream()
                .filter(attrVO -> attrVO.getName().equals(skuItemValue))
                .findFirst().orElse(null);
    }

    private List<AttrVO> getCategoryId2VO(String productCategory, Config config) {
        if(config == null) {
            return Lists.newArrayList();
        }
        if(CollectionUtils.isEmpty(config.getProductCategoryList()) || !config.getProductCategoryList().contains(productCategory)) {
            return Lists.newArrayList();
        }
        if(MapUtils.isEmpty(config.getCategoryId2VO()) || CollectionUtils.isEmpty(config.getCategoryId2VO().get(productCategory))) {
            return Lists.newArrayList();
        }
        return config.getCategoryId2VO().get(productCategory);
    }

    private String buildIcon(AttrVO attrValue, String skuItemValue, String defaultIcon) {
        if(attrValue == null || StringUtil.isEmpty(skuItemValue)) {
            return defaultIcon;
        }
        return StringUtil.isEmpty(attrValue.getIcon()) ? defaultIcon : attrValue.getIcon();
    }

    private DealDetailModuleVO buildDealDetailModuleVO(List<DealSkuVO> dealSkuVOList) {
        if (CollectionUtils.isEmpty(dealSkuVOList)) {
            return null;
        }
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        List<DealSkuGroupModuleVO> skuGroupsModel1 = Lists.newArrayList();
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(dealSkuVOList);
        dealSkuGroupModuleVO.setTitle(getConfigTitle());
        skuGroupsModel1.add(dealSkuGroupModuleVO);
        dealDetailModuleVO.setSkuGroupsModel1(skuGroupsModel1);
        return dealDetailModuleVO;
    }

    private String getConfigTitle() {
        if(config == null || StringUtil.isEmpty(config.getTitle())) {
            return "";
        }
        return config.getTitle();
    }

    @Data
    public static class Config {
        private List<String> productCategoryList;
        private String defaultIcon;
        private Map<String, List<AttrVO>> categoryId2VO;
        private String title;
        //白名单内的团单不展示"整车外表清洁"和"内饰清洁除尘"下的属性信息
        private List<Integer> dpDealIdWhiteList;
        private List<Integer> mtDealIdWhiteList;
        private List<String> skuTitleWithoutSkuAttrInfo;
        //精洗团单sku名对应展示的sku属性名Map
        private Map<String, List<String>> cleanDealSkuName2SkuAttrChnNamesMap;
    }

    @Data
    public static class AttrVO {
        private String name;
        private String icon;
        private String desc;
    }
}
