package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.dianping.cat.Cat;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 2. 猜喜路径
 * 召回规则：召回商户内全部在线且非货架隐藏团单
 * <p>
 * 排序规则：
 * 首先展示当前团单，其他团单排序规则如下，
 * 整体：团单价格 > 团单销量
 */

@VPointOption(name = "教育相似团购",
        description = "教育相似团购",
        code = "EduSimilarFilterAcsOpt")
public class EduSimilarGuessJoyFilterAcsOpt extends ProductListVP<EduSimilarGuessJoyFilterAcsOpt.Config> {

    private static final int defaultPromoType = PromoTypeEnum.DIRECT_PROMO.getType();

    @Override
    public List<ProductM> compute(ActivityCxt context, Param param, Config config) {
        if ("close".equals(config.getShowSwitch())) {
            return null;
        }
        List<ProductM> productMS = param.getProductMS();
        //获取当前团单信息
        ProductM currentProduct = productMS.stream()
                .filter(productM -> productM.getProductId() == ParamsUtil.getIntSafely(context, PmfConstants.Params.entityId))
                .findFirst().orElse(productMS.get(0));
        //货架途径pagesource=shelf  猜喜途径pagesource=guess
        String pageSource = ParamsUtil.getStringSafely(context, PmfConstants.Params.pageSource);
        if (productMS.size() < 2 || checkShowType(config, pageSource)) {
            return null;
        }
        productMS = productMS.stream()
                .filter(e -> isValid(e, currentProduct))
                .sorted(
                        Comparator.comparing((ProductM product) -> getProductPrice(product, config), Comparator.nullsLast(BigDecimal::compareTo))
                                .thenComparing((ProductM product) -> getSaleNum(product), Comparator.reverseOrder())
                )
                .collect(Collectors.toList());
        if (config.getLimit() > 0) {
            productMS = productMS.stream()
                    .limit(config.getLimit())
                    .collect(Collectors.toList());
        }
        productMS.add(0, currentProduct);
        if (CollectionUtils.isEmpty(productMS) || productMS.size() < 2) {
            return null;
        }
        return productMS;
    }

    private boolean checkShowType(Config config, String pageSource) {
        return  !isAllType(config) && !isPageSourceMatched(config, pageSource);
    }

    private boolean isAllType(Config config) {
        return "allType".equals(config.getShowSwitch());
    }

    private boolean isPageSourceMatched(Config config, String pageSource) {
        return StringUtils.equals(pageSource, config.getShowSwitch());
    }


    private boolean isValid(ProductM productM, ProductM currentProduct) {
        Boolean isHidden = Boolean.parseBoolean(productM.getAttr("attr_search_hidden_status"));
        return !productM.equals(currentProduct) //过滤当前团单
                && !isHidden;// 过滤隐藏;
    }

    /**
     * 获取销量
     **/
    private int getSaleNum(ProductM productM) {
        ProductSaleM sale = productM.getSale();
        return sale != null ? sale.getSale() : 0;
    }

    //团单价格
    private BigDecimal getProductPrice(ProductM productM, Config config) {
        BigDecimal productPrice;
        if (isDuringPerfectActivity(productM)) {
            productPrice = getPerfectActivityPrice(productM);
        } else {
            productPrice = getPromoOrBasePrice(productM, config);
        }
        return productPrice;
    }

    private boolean isDuringPerfectActivity(ProductM productM) {
        String perfectActivityPrice = PerfectActivityBuildUtils.getPerfectActivityPrice(productM);
        return PerfectActivityBuildUtils.isDuringPerfectActivity(productM) && StringUtils.isNotEmpty(perfectActivityPrice);
    }

    private BigDecimal getPerfectActivityPrice(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.EduSimilarGuessJoyFilterAcsOpt.getPerfectActivityPrice(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        return productM.getPromo(8).getPromoPrice();
    }

    private BigDecimal getPromoOrBasePrice(ProductM productM, Config config) {
        ProductPromoPriceM productPromoPriceM = productM.getPromo(config.getPromoType());
        return productPromoPriceM != null ? productPromoPriceM.getPromoPrice() : productM.getBasePrice();
    }

    @VPointCfg
    @Data
    public static class Config {
        //默认不限制，pmf平台配置优先于代码配置
        private int limit = 0;
        //close关闭，guess猜喜，shelf货架,allType所有情况
        private String showSwitch = "guess";
        private int promoType = defaultPromoType;
    }

}
