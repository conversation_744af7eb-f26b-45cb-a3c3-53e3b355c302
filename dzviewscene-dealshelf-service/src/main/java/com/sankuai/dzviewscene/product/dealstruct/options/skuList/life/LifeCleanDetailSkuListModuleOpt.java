package com.sankuai.dzviewscene.product.dealstruct.options.skuList.life;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.LifeCleanAttrVOListOpt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.product.filterlist.utils.LifeCleanUtils;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/***
 * <AUTHOR>
 * @date 2023/4/20
 * @apiNote
 */
@VPointOption(name = "生活服务保洁团详sku列表组变化点", description = "生活服务保洁团详sku列表组变化点", code = LifeCleanDetailSkuListModuleOpt.CODE)
public class LifeCleanDetailSkuListModuleOpt extends SkuListModuleVP<LifeCleanDetailSkuListModuleOpt.Config> {

    public static final String CODE = "LifeCleanDealDetailSkuListModuleOpt";

    @Override
    public List<DealDetailSkuListModuleGroupModel> compute(ActivityCxt activityCxt, Param param, Config config) {
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        //获取数据源
        List<SkuAttrItemDto> skuAttrItemDtos = getSkuAttrList(dealDetailInfoModel);
        //判断是否为保洁自营
        //如果是自营门店下的团单，需要和Config中的数据做对比。判断是否展示
        boolean isSelfSupport = LifeCleanUtils.isLifeClearSelf(param.getDealDetailInfoModel().getDealAttrs());
        //构造服务项目列表组
        ProductSkuCategoryModel firstProductCategory = getFirstProductCategory(dealDetailInfoModel);
        List<DealSkuGroupModuleVO> dealSkuGroupModuleVOS = buildDealSkuGroupModuleVOs(skuAttrItemDtos, config, firstProductCategory,isSelfSupport);
        String groupSubTitle = buildDealSubTitle(skuAttrItemDtos, config, firstProductCategory);
        //装配结果
        return buildDealDetailSkuListModuleGroupModelList(dealSkuGroupModuleVOS, config, groupSubTitle);
    }

    private List<DealDetailSkuListModuleGroupModel> buildDealDetailSkuListModuleGroupModelList(List<DealSkuGroupModuleVO> dealSkuGroupModuleVOS, Config config, String groupSubTitle) {
        if (CollectionUtils.isEmpty(dealSkuGroupModuleVOS)) {
            return null;
        }
        DealDetailSkuListModuleGroupModel dealDetailSkuListModuleGroupModel = new DealDetailSkuListModuleGroupModel();
        if (config != null) {
            dealDetailSkuListModuleGroupModel.setGroupName(config.getGroupName());
        }
        dealDetailSkuListModuleGroupModel.setGroupSubtitle(groupSubTitle);
        dealDetailSkuListModuleGroupModel.setDealSkuGroupModuleVOS(dealSkuGroupModuleVOS);
        return Lists.newArrayList(dealDetailSkuListModuleGroupModel);
    }

    public List<DealSkuGroupModuleVO> buildDealSkuGroupModuleVOs(List<SkuAttrItemDto> skuAttrItemDtos, Config config, ProductSkuCategoryModel firstProductCategory,Boolean isSelfSupport) {
        if (CollectionUtils.isEmpty(skuAttrItemDtos) || config == null
                || firstProductCategory == null || MapUtils.isEmpty(config.getLifeCleanProductCategory2SkuAttrIconMap())) {
            return null;
        }
        List<DealSkuGroupModuleVO> dealSkuGroupModuleList = Lists.newArrayList();
        DealSkuGroupModuleVO dealSkuGroupModuleVO = buildDealSkuGroupModuleVO(skuAttrItemDtos, config.getLifeCleanProductCategory2SkuAttrIconMap(), firstProductCategory,isSelfSupport);
        if (dealSkuGroupModuleVO != null && CollectionUtils.isNotEmpty(dealSkuGroupModuleVO.getDealSkuList())) {
            dealSkuGroupModuleList.add(dealSkuGroupModuleVO);
        }
        return dealSkuGroupModuleList;
    }

    private String buildDealSubTitle(List<SkuAttrItemDto> skuAttrItemDtos, Config config, ProductSkuCategoryModel firstProductCategory) {
        if (CollectionUtils.isEmpty(skuAttrItemDtos) || config == null || MapUtils.isEmpty(config.getLifeCleanProductCategory2SkuAttrIconMap())
            || firstProductCategory == null || firstProductCategory.getProductCategoryId() == null) {
            return null;
        }
        Map<String, SkuAttrIconConfig> skuAttrIconConfigMap = config.getLifeCleanProductCategory2SkuAttrIconMap();
        SkuAttrIconConfig skuAttrIconConfig = skuAttrIconConfigMap.get(String.valueOf(firstProductCategory.getProductCategoryId()));
        if (skuAttrIconConfig == null) {
            return null;
        }
        return skuAttrIconConfig.getSubTitle();
    }

    public DealSkuGroupModuleVO buildDealSkuGroupModuleVO(List<SkuAttrItemDto> skuAttrItemDtos, Map<String, SkuAttrIconConfig> skuAttrIconConfigMap, ProductSkuCategoryModel firstProductCategory,Boolean isSelfSupport) {
        if (CollectionUtils.isEmpty(skuAttrItemDtos) || firstProductCategory == null || firstProductCategory.getProductCategoryId() == null) {
            return null;
        }
        SkuAttrIconConfig skuAttrIconConfig = skuAttrIconConfigMap.get(String.valueOf(firstProductCategory.getProductCategoryId()));
        if (skuAttrIconConfig == null) {
            return null;
        }
        //获取配置文件中，自营信息与。团单自营信息做对比
        boolean configSelfSupport = skuAttrIconConfig.isSelfSupport();
        if (isSelfSupport && !configSelfSupport) {
            return null;
        }
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(buildDealSkuVOList(skuAttrItemDtos, skuAttrIconConfig.getSkuAttrName2IconList()));
        if (CollectionUtils.isEmpty(dealSkuGroupModuleVO.getDealSkuList())) {
            return null;
        }
        return dealSkuGroupModuleVO;
    }

    /**
     * 构造sku列表组
     *
     * @return
     */
    private List<DealSkuVO> buildDealSkuVOList(List<SkuAttrItemDto> skuAttrItemDtos, List<AttrConfigModel> skuAttrName2IconList) {
        if (CollectionUtils.isEmpty(skuAttrName2IconList)) {
            return null;
        }
        return skuAttrName2IconList.stream()
                .map(entry -> {
                    List<DealSkuItemVO> items = getSkuItems(skuAttrItemDtos, entry.getSkuAttrList());
                    return buildDealSkuVO(entry.getIcon(), entry.getTitle(), items);
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取sku描述
     *
     * @return
     */
    private List<DealSkuItemVO> getSkuItems(List<SkuAttrItemDto> skuAttrItemDtos, List<String> attrKeyList) {
        if (CollectionUtils.isEmpty(attrKeyList)) {
            return Lists.newArrayList();
        }
        String attrValue = attrKeyList.stream().map(attrKey -> DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItemDtos, attrKey)).filter(StringUtils::isNotEmpty).collect(Collectors.joining("，"));
        if (StringUtils.isEmpty(attrValue)) {
            return Lists.newArrayList();
        }
        List<DealSkuItemVO> skuItemVOS = Lists.newArrayList();
        DealSkuItemVO skuItemVO = new DealSkuItemVO();
        skuItemVO.setType(0);
        skuItemVO.setName(attrValue);
        skuItemVOS.add(skuItemVO);
        return skuItemVOS;
    }

    private DealSkuVO buildDealSkuVO(String icon, String title, List<DealSkuItemVO> items) {
        if (StringUtils.isEmpty(title) || CollectionUtils.isEmpty(items)) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(title);
        dealSkuVO.setIcon(icon);
        dealSkuVO.setItems(items);
        return dealSkuVO;
    }

    @Data
    @VPointCfg
    public static class Config {
        private String groupName;
        //生活服务保洁sku icon map
        private Map<String, SkuAttrIconConfig> lifeCleanProductCategory2SkuAttrIconMap;
    }

    @Data
    public static class SkuAttrIconConfig {
        //副标题
        private String subTitle;
        //自营团单是否展示
        private boolean selfSupport;
        //sku属性列表
        private List<AttrConfigModel> skuAttrName2IconList;

    }

    /**
     * 属性配置模型
     *
     * @return
     */
    @Data
    public static class AttrConfigModel {
        //属性展示标题
        public String title;
        //属性列表
        public List<String> skuAttrList;
        //icon
        public String icon;
    }

    private List<SkuAttrItemDto> getSkuAttrList(DealDetailInfoModel dealDetailInfoModel) {
        if (dealDetailInfoModel == null) {
            return null;
        }
        return DealDetailUtils.getFirstMustGroupFirstSkuAttrList(dealDetailInfoModel.getDealDetailDtoModel());
    }

    private ProductSkuCategoryModel getFirstProductCategory(DealDetailInfoModel dealDetailInfoModel) {
        if (dealDetailInfoModel == null || CollectionUtils.isEmpty(dealDetailInfoModel.getProductCategories())) {
            return null;
        }
        return dealDetailInfoModel.getProductCategories().get(0);
    }
}
