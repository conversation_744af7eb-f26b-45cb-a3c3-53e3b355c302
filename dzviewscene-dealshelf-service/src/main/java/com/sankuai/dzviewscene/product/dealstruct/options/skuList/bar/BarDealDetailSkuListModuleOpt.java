package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar;

import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.OptionalSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.AbstractBarSkuListCreator;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.BarSkuListCreatorFactory;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealItemTag;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.ExtraExplainVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.JumpUrlVO;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/2/8 8:31 下午
 */
@VPointOption(name = "酒吧团详sku列表组变化点", description = "酒吧团详sku列表组变化点", code = BarDealDetailSkuListModuleOpt.CODE)
public class BarDealDetailSkuListModuleOpt extends SkuListModuleVP<BarDealDetailSkuListModuleOpt.Config> {

    public static final String CODE = "BarDealDetailSkuListModuleOpt";

    @Override
    public List<DealDetailSkuListModuleGroupModel> compute(ActivityCxt activityCxt, Param param, Config config) {
        boolean hitDouHu = DouHuUtils.isHitDouHu(param.getDouhuResultModels(), config.getHitDouHuSks());
        //获取数据源
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        //构造服务项目列表组
        List<DealSkuGroupModuleVO> dealSkuGroupModuleVOS = buildDealSkuGroupModuleVOs(dealDetailInfoModel, config, hitDouHu);
        //装配结果
        return buildDealDetailSkuListModuleGroupModelList(dealSkuGroupModuleVOS, config, dealDetailInfoModel.getDealAttrs());
    }

    private List<DealSkuGroupModuleVO> buildDealSkuGroupModuleVOs(DealDetailInfoModel dealDetailInfoModel, Config config, boolean hitDouHu) {
        if (dealDetailInfoModel == null) {
            return null;
        }
        List<DealSkuGroupModuleVO> result = new ArrayList<>();
        //添加全部可享服务项目列表
        addMustSkuListGroup(result, dealDetailInfoModel.getDealDetailDtoModel(), config, hitDouHu);
        //添加几选几服务项目列表
        addOptionalSkuListGroups(result, dealDetailInfoModel.getDealDetailDtoModel(), config, hitDouHu);
        return decorateSkuCommonField(result, dealDetailInfoModel);
    }

    private DealItemTag getSkuTag(DealDetailUtils.DealSkuItemInfoModel recommendTag) {
        if (recommendTag == null || StringUtils.isEmpty(recommendTag.getProductTag())) {
            return null;
        }
        DealItemTag dealItemTag = new DealItemTag();
        dealItemTag.setTitle(recommendTag.getProductTag());
        dealItemTag.setTextColor(recommendTag.getProductTag().startsWith("网友") ? "#FF4B10" : "#B65120");
        dealItemTag.setTextBgColor(recommendTag.getProductTag().startsWith("网友") ? "#FFF1EC" : "#FFF0E0");
        return dealItemTag;
    }

    private List<DealSkuGroupModuleVO> decorateSkuCommonField(List<DealSkuGroupModuleVO> result, DealDetailInfoModel dealDetailInfoModel) {
        String value = DealDetailUtils.getAttrSingleValueByAttrName(dealDetailInfoModel.getDealAttrs(), "DealSkuRelateTags");
        if (StringUtils.isEmpty(value)) {
            return result;
        }
        Map<String, DealDetailUtils.DealSkuItemInfoModel> name2RecommendTag = DealDetailUtils.getRecommendTag(value);
        //后置填充标签
        result.stream().map(DealSkuGroupModuleVO::getDealSkuList).flatMap(Collection::stream).forEach(sku -> {
            DealDetailUtils.DealSkuItemInfoModel dealSkuItemInfoModel = name2RecommendTag.get(sku.getTitle());
            if (dealSkuItemInfoModel == null) {
                return;
            }
            DealItemTag skuTag = getSkuTag(dealSkuItemInfoModel);
            if (skuTag == null) {
                return;
            }
            sku.setTag(skuTag);
            sku.setJumpUrl(buildJumpUrl(dealSkuItemInfoModel));
        });
        return result;
    }

    private JumpUrlVO buildJumpUrl(DealDetailUtils.DealSkuItemInfoModel dealSkuItemInfoModel) {
        JumpUrlVO jumpUrlVO = new JumpUrlVO();
        jumpUrlVO.setUrl(dealSkuItemInfoModel.getProductUrl());
        return jumpUrlVO;
    }

    private void addOptionalSkuListGroups(List<DealSkuGroupModuleVO> result, DealDetailDtoModel dealDetailDtoModel, Config config, boolean hitDouHu) {
        if (dealDetailDtoModel == null || dealDetailDtoModel.getSkuUniStructuredDto() == null || CollectionUtils.isEmpty(dealDetailDtoModel.getSkuUniStructuredDto().getOptionalGroups())) {
            return;
        }
        for (OptionalSkuItemsGroupDto optionalGroup : dealDetailDtoModel.getSkuUniStructuredDto().getOptionalGroups()) {
            addSkuListGroup(optionalGroup.getSkuItems(), false, config, optionalGroup.getOptionalCount(), result, hitDouHu);
        }
    }

    private void addMustSkuListGroup(List<DealSkuGroupModuleVO> result, DealDetailDtoModel dealDetailDtoModel, Config config, boolean hitDouHu) {
        if (dealDetailDtoModel == null || dealDetailDtoModel.getSkuUniStructuredDto() == null || CollectionUtils.isEmpty(dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups())) {
            return;
        }
        for (MustSkuItemsGroupDto mustGroup : dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups()) {
            addSkuListGroup(mustGroup.getSkuItems(), true, config, 0, result, hitDouHu);
        }
    }

    private void addSkuListGroup(List<SkuItemDto> skuList, boolean isMustGroup, Config config, int optionalnum, List<DealSkuGroupModuleVO> result, boolean hitDouHu) {
        if (CollectionUtils.isEmpty(skuList) || result == null) {
            return;
        }
        //获取对应的构造器
        AbstractBarSkuListCreator barSkuListCreator = BarSkuListCreatorFactory.creadSkuListBuilder(isMustGroup, skuList, config);
        if (barSkuListCreator == null) {
            return;
        }
        //进行构造
        List<DealSkuGroupModuleVO> dealSkuGroupModuleVOS = barSkuListCreator.buildSkuListModules(skuList, config, optionalnum, hitDouHu);
        //添加到result
        addDealSkuGroupModuleVO(result, dealSkuGroupModuleVOS);
    }

    private List<DealDetailSkuListModuleGroupModel> buildDealDetailSkuListModuleGroupModelList(List<DealSkuGroupModuleVO> dealSkuGroupModuleVOS, Config config, List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealSkuGroupModuleVOS)) {
            return null;
        }
        DealDetailSkuListModuleGroupModel dealDetailSkuListModuleGroupModel = new DealDetailSkuListModuleGroupModel();
        dealDetailSkuListModuleGroupModel.setGroupName("酒吧服务项目列表");
        dealDetailSkuListModuleGroupModel.setDealSkuGroupModuleVOS(dealSkuGroupModuleVOS);
        dealDetailSkuListModuleGroupModel.setExtraExplain(buildExtraExplain(dealAttrs, config));
        dealDetailSkuListModuleGroupModel.setGroupTitle(TimesDealUtil.getTimesTitle(dealAttrs));
        return Lists.newArrayList(dealDetailSkuListModuleGroupModel);
    }

    private ExtraExplainVO buildExtraExplain(List<AttrM> dealAttrs, Config config) {
        String serviceType = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "service_type");
        return Lists.newArrayList("酒水", "酒水小食套餐").contains(serviceType) ? config.getExtraExplain() : null;
    }

    private void addDealSkuGroupModuleVO(List<DealSkuGroupModuleVO> result, List<DealSkuGroupModuleVO> dealSkuGroupModuleVOS) {
        if (result == null || CollectionUtils.isEmpty(dealSkuGroupModuleVOS)) {
            return;
        }
        result.addAll(dealSkuGroupModuleVOS);
    }

    @Data
    @VPointCfg
    public static class Config {
        //酒水sku类别id列表
        private List<Long> drinksSkuCateIds;
        //可乐sku类别id
        private List<Long> colaSkuCateIds;
        //餐食sku类别id列表
        private List<Long> mealsSkuCateIds;
        //全部可享酒水sku列表组名format
        private String mustDrinksSkusGroupNameFormat;
        //全部可享餐食sku列表组名format
        private String mustMealsSkusGroupNameFormat;
        //部分可享酒水sku列表组名format
        private String optionalDrinksSkusGroupNameFormat;
        //部分可享酒水数量一致时sku列表组名format
        private String optionalDrinksSkusWithSameNumGroupNameFormat;
        //部分可享餐食sku列表组名format
        private String optionalMealsSkusGroupNameFormat;
        //部分可享餐食数量一致时sku列表组名format
        private String optionalMealsSkusWithSameNumGroupNameFormat;
        //部分可享混合酒水餐食sku列表组名format
        private String optionalDrinksMealsSkusGroupNameFormat;
        //以下 M选N
        private String optionalFormat;
        //可乐副标题属性名 to 属性format 映射map
        private Map<String, String> colaSubtitlesAttrName2FormatMap;
        //非可乐饮品副标题属性名 to 属性format 映射map
        private Map<String, String> drinksSubtitlesAttrName2FormatMap;
        //餐食副标题属性名 to 属性format 映射map
        private Map<String, String> mealsSubtitlesAttrName2FormatMap;
        //酒水非可乐弹窗属性配置列表
        private List<AttrConfigModel> drinksPopupAttrConfigModels;
        //可乐弹窗属性配置列表
        private List<AttrConfigModel> colaPopupAttrConfigModels;
        //酒水弹窗展示属性名 to 属性标题 映射map
        private Map<String, String> drinksPopupAttrName2TitleMap;
        //常见酒水酒精度区间配置（从低到高区间边界上酒水度数 to 酒水名称 映射map）
        private LinkedHashMap<Double, String> alcoholByVolumeRange2DocMap;
        //常见酒精度说明
        private ExtraExplainVO extraExplain;

        private List<String> hitDouHuSks;
    }

    /**
     * 属性配置模型
     *
     * @param
     * @return
     */
    @Data
    public static class AttrConfigModel {
        //属性名
        public String attrName;
        //属性展示标题
        public String attrTitle;
        //属性值展示format
        public String attrFormat;
    }
}
