package com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.sankuai.dzviewscene.product.dealstruct.vo.CommonAttrsVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/18 10:59 上午
 */
@MobileDo(id = 0xca4b)
public class HealthExaminationDealDetailVO implements Serializable {
    /**
     * 商家补充说明信息
     */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
     * 团单属性信息
     */
    @MobileDo.MobileField(key = 0xf672)
    private List<DealDetailStructAttrModuleVO> dealAttrs;

    /**
     * 检查项目组
     */
    @MobileDo.MobileField(key = 0x7463)
    private HealthExaminationItemsGroupVO examinationItemsGroup;

    /**
     * 检查须知
     */
    @MobileDo.MobileField(key = 0x782f)
    private CommonAttrsVO examinationNotice;

    /**
     * 筛选项目列表
     */
    @MobileDo.MobileField(key = 0xbc9e)
    private List<FilterItemListVO> filterItemListVOs;

    /**
     * 获取发票
     */
    @MobileDo.MobileField(key = 0xebf9)
    private ProcessModuleVO invoiceModule;

    /**
     * 服务流程
     */
    @MobileDo.MobileField(key = 0x9df1)
    private ProcessModuleListVO serviceProcess;

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<DealDetailStructAttrModuleVO> getDealAttrs() {
        return dealAttrs;
    }

    public void setDealAttrs(List<DealDetailStructAttrModuleVO> dealAttrs) {
        this.dealAttrs = dealAttrs;
    }

    public HealthExaminationItemsGroupVO getExaminationItemsGroup() {
        return examinationItemsGroup;
    }

    public void setExaminationItemsGroup(
            HealthExaminationItemsGroupVO examinationItemsGroup) {
        this.examinationItemsGroup = examinationItemsGroup;
    }

    public CommonAttrsVO getExaminationNotice() {
        return examinationNotice;
    }

    public void setExaminationNotice(CommonAttrsVO examinationNotice) {
        this.examinationNotice = examinationNotice;
    }

    public List<FilterItemListVO> getFilterItemListVOs() {
        return filterItemListVOs;
    }

    public void setFilterItemListVOs(List<FilterItemListVO> filterItemListVOs) {
        this.filterItemListVOs = filterItemListVOs;
    }

    public ProcessModuleVO getInvoiceModule() {
        return invoiceModule;
    }

    public void setInvoiceModule(ProcessModuleVO invoiceModule) {
        this.invoiceModule = invoiceModule;
    }

    public ProcessModuleListVO getServiceProcess() {
        return serviceProcess;
    }

    public void setServiceProcess(ProcessModuleListVO serviceProcess) {
        this.serviceProcess = serviceProcess;
    }
}