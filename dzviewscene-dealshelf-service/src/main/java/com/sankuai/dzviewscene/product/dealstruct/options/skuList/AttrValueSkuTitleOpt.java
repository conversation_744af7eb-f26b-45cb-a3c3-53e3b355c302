package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuTitleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@VPointOption(name = "取sku属性或行业属性和sku名称拼接作为sku标题的变化点", description = "取sku属性或行业属性和sku名称拼接作为sku标题的变化点",code = AttrValueSkuTitleOpt.CODE)
public class AttrValueSkuTitleOpt extends SkuTitleVP<AttrValueSkuTitleOpt.Config> {
    public static final String CODE = "AttrValueSkuTitleOpt";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        SkuItemDto skuItemDto = param.getSkuItemDto();
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems()) || config == null || CollectionUtils.isEmpty(config.getProductCategoryList()) || MapUtils.isEmpty(config.getProductCategory2AttrName())) {
            return param.getSkuTitle();
        }
        long productCategory = param.getSkuItemDto().getProductCategory();
        if(config.getProductCategoryList().contains(productCategory)){
            return buildSkuTitle(config.getProductCategory2AttrName().get(productCategory), param.getSkuTitle(), skuItemDto.getAttrItems(), param.getDealAttrs(), config.getNeedSkuTitle());
        }
        return param.getSkuTitle();
    }

    private String buildSkuTitle(ConcatModel concatModel, String skuTitle, List<SkuAttrItemDto> skuAttrItems, List<AttrM> dealAttrs, boolean needSkuTitle){
        if(concatModel == null || StringUtils.isEmpty(concatModel.getAttrName())){
            return skuTitle;
        }
        String attrValue = getAttrFromAttrIfSkuAttrIsEmpty(concatModel.getAttrName(), skuAttrItems, dealAttrs);
        if(StringUtils.isEmpty(attrValue)){
            return skuTitle;
        }
        if(StringUtils.isEmpty(concatModel.getReplaceStr()) || StringUtils.isEmpty(concatModel.getOldStr())){
            return StringUtils.isEmpty(concatModel.getJoinStr()) ? attrValue + skuTitle : attrValue + concatModel.getJoinStr() + skuTitle;
        }
        List<String> split = Lists.newArrayList(attrValue.replace(concatModel.getOldStr(), concatModel.getReplaceStr()));
        if(needSkuTitle){
            split.add(skuTitle);
        }
        return StringUtils.join(split, concatModel.getJoinStr());
    }

    private String getAttrFromAttrIfSkuAttrIsEmpty(String attrName, List<SkuAttrItemDto> skuAttrItems, List<AttrM> dealAttrs){
        String attrValue = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, attrName);
        if(StringUtils.isEmpty(attrValue)){
            return DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, attrName);
        }
        return attrValue;
    }


    @Data
    @VPointCfg
    public static class Config {
        /**
         * 作用到的服务项目id列表
         */
        private List<Long> productCategoryList;

        /**
         * 服务项目id和货属性名映射关系
         */
        private Map<Long, ConcatModel> productCategory2AttrName;

        /**
         * 拼接标题是否需要服务项目标题
         */
        private Boolean needSkuTitle = true;

    }

    @Data
    private static class ConcatModel {

        /**
         * 属性名
         */
        private String attrName;

        /**
         * 替换字符串
         */
        private String oldStr;

        /**
         * 替换字符串
         */
        private String replaceStr;

        /**
         * 拼接字符串
         */
        private String joinStr;
    }
}
