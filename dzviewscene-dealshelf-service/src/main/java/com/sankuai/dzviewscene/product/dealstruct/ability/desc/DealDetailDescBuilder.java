package com.sankuai.dzviewscene.product.dealstruct.ability.desc;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.desc.vpoints.DealDetailDescVP;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDescModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/28 12:17 下午
 */
@Ability(code = DealDetailDescBuilder.CODE,
        name = "团购详情模块补充描述组件构造能力",
        description = "团购详情模块补充描述组件构造能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE
        }
)
public class DealDetailDescBuilder extends PmfAbility<List<DealDetailDescModel>, DealDetailDescParam, DealDetailDescCfg> {

    public static final String CODE = "dealDetailDescBuilder";

    @Override
    public CompletableFuture<List<DealDetailDescModel>> build(ActivityCxt activityCxt, DealDetailDescParam descParam, DealDetailDescCfg descCfg) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        if (CollectionUtils.isEmpty(dealDetailInfoModels)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        List<DealDetailDescModel> descList = dealDetailInfoModels.stream().map(detailModel -> {
            DealDetailDescVP<?> dealDetailDescVP = findVPoint(activityCxt, DealDetailDescVP.CODE);
            if (detailModel == null) {
                return null;
            }
            String desc = dealDetailDescVP.execute(activityCxt, DealDetailDescVP.Param.builder()
                    .dealDetailInfoModel(detailModel)
                    .desc(detailModel.getDesc())
                    .dealDetailDtoModel(detailModel.getDealDetailDtoModel())
                    .productCategories(detailModel.getProductCategories())
                    .build());
            return buildDealDetailModuleVO(detailModel.getDealId(), desc);
        }).filter(desc -> desc != null).collect(Collectors.toList());

        return CompletableFuture.completedFuture(descList);
    }

    private DealDetailDescModel buildDealDetailModuleVO(int dealId, String desc) {
        if(StringUtils.isEmpty(desc)) {
            return null;
        }
        DealDetailDescModel dealDetailDescModel = new DealDetailDescModel();
        dealDetailDescModel.setDealId(dealId);
        dealDetailDescModel.setDesc(desc);
        return dealDetailDescModel;

    }
}
