package com.sankuai.dzviewscene.product.filterlist.option.builder.product.promo;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPromosVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzPromoVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/15
 */
@VPointOption(name = "空变化点",
        description = "",
        code = "NullProductPromosOpt")
public class NullProductPromosOpt extends ProductPromosVP<Void> {
    @Override
    public List<DzPromoVO> compute(ActivityCxt context, Param param, Void unused) {
        return null;
    }
}
