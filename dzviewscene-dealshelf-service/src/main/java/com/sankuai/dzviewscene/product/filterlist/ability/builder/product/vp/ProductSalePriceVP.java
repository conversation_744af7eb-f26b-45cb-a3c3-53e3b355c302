package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

@VPoint(name = "商品售价", description = "商品-salePrice", code = ProductSalePriceVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductSalePriceVP <T> extends PmfVPoint<String, ProductSalePriceVP.Param, T> {

    public static final String CODE = "ProductSalePriceVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;
        private CardM cardM;
    }
}
