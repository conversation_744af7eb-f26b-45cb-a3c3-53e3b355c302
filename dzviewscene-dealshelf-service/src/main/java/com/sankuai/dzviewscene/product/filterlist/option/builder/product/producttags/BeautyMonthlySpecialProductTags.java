package com.sankuai.dzviewscene.product.filterlist.option.builder.product.producttags;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTagsVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/29 12:06 下午
 */
@VPointOption(name = "商品-ProductTags",
        description = "【丽人】美容美体频道页“每月精选特惠”会场",
        code = "BeautyMonthlySpecialProductTags")
public class BeautyMonthlySpecialProductTags extends ProductTagsVP<BeautyMonthlySpecialProductTags.Config> {


    private static final String RETURN_ANY_TIME = "随时退";
    private static final String LIMIT_BUY = "限时抢购";
    private static final String SHOP_AMOUNT = "店通用";

    private static final String FIELD_NAME = "dealApplyShopNumberAttr";

    @Override
    public List<String> compute(ActivityCxt activityCxt, Param param, BeautyMonthlySpecialProductTags.Config config) {
        List<AttrM> extAttrs = param.getProductM().getExtAttrs();
        List<String> tagLists = Lists.newArrayList();
        tagLists.add(LIMIT_BUY);
        tagLists.add(RETURN_ANY_TIME);
        if (CollectionUtils.isEmpty(extAttrs)) {
            return tagLists;
        }
        for (AttrM extAttr : extAttrs) {
            if (extAttr.getName().equals(FIELD_NAME)) {
                tagLists.add((extAttr.getValue().equals("1") ? "本" : extAttr.getValue()) + SHOP_AMOUNT);
            }
//            if (extAttr.getName().equals(SupplyCommandMatchDealHandler.SHOP_TYPE_VALUE_HAVE_KEY)) {
//                tagLists.add(0, LIMIT_BUY);
//            }
        }
        return tagLists;
    }

    @VPointCfg
    @Data
    public static class Config {
    }
}
