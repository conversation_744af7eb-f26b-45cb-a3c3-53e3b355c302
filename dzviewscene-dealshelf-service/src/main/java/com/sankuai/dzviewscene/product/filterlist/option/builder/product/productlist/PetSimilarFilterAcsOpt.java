package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.enums.DealCategoryEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealStructModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.MustSkusGroupModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.SkuModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/10/30 16:20
 */
@VPointOption(name = "宠物相似团购",
        description = "宠物相似团购",
        code = "PetSimilarFilterAcsOpt")
public class PetSimilarFilterAcsOpt extends ProductListVP<PetSimilarFilterAcsOpt.Config> {

    private static final String SERVICE_TYPE = "service_type";

    /**
     * 适用宠物对应的属性值
     */
    private static final List<String> PET_TYPE_NAMES = Lists.newArrayList("apply_pets_in_wash_beauty", "apply_pets", "apply_pet", "pet_type");

    public static final long VACCINE = 1174L;
    public static final long ANTIBODY_DETECTION = 1165L;
    public static final List<Long> EXPELLING_INSECTS = Lists.newArrayList(1175L, 1176L, 1177L);
    private static final String DEAL_STRUCT_CONTENT_ATTR_NAME = "dealStructContent";

    @Override
    public List<ProductM> compute(ActivityCxt context, Param param, Config config) {
        List<ProductM> productMS = param.getProductMS();
        int entityId = ParamsUtil.getIntSafely(context, PmfConstants.Params.entityId);
        int firstTopIndex = findTopProductIndex(productMS, entityId);
        if (firstTopIndex < 0) {
            return null;
        }
        ProductM topProduct = productMS.get(firstTopIndex);
        //先过滤出目标
        List<ProductM> result = productMS.stream()
                .filter(this::isValidProduct)
                .filter(productM -> getAvailableProduct(productM, topProduct, productMS, config))
                .filter(productM -> StringUtils.isNotEmpty(productM.getTitle()))
                .sorted(Comparator.comparing((ProductM product) -> getOrderValue(product, topProduct))
                        .thenComparing((ProductM product) -> getProductPrice(product, topProduct)))
                .limit(config.getLimit())
                .collect(Collectors.toList());
        return topAndFilterResult(result, entityId);
    }

    private List<ProductM> topAndFilterResult(List<ProductM> result, int entityId) {
        if (CollectionUtils.isEmpty(result) || result.size() <= 1) {
            return null;
        }
        int finalTopIndex = findTopProductIndex(result, entityId);
        if (finalTopIndex < 0) {
            return null;
        }
        Collections.swap(result, 0, finalTopIndex);
        return result;
    }

    private int findTopProductIndex(List<ProductM> productMS, int entityId) {
        int topIndex = -1;
        for (int index = 0; index < productMS.size(); index++) {
            if (productMS.get(index).getProductId() != entityId || !isValidProduct(productMS.get(index))) {
                continue;
            }
            topIndex = index;
            break;
        }
        return topIndex;
    }

    private boolean isValidProduct(ProductM productM) {
        int categoryId = productM.getCategoryId();
        String serviceType = productM.getAttr(SERVICE_TYPE);
        return categoryId != 0 && StringUtils.isNotEmpty(serviceType);
    }

    /**
     * 与当前团单三级目录相同则为-1,交换
     **/
    private int getOrderValue(ProductM productM, ProductM topProduct) {
        String serviceType = productM.getAttr(SERVICE_TYPE);
        String topServiceType = topProduct.getAttr(SERVICE_TYPE);
        if (StringUtils.isEmpty(serviceType) || StringUtils.isEmpty(topServiceType)) {
            return 0;
        }
        if (StringUtils.equals(serviceType, topServiceType)) {
            return -1;
        } else {
            return 0;
        }
    }

    //价格差
    private Double getProductPrice(ProductM productM, ProductM currentProductM) {
        String diff = getSalePrice(productM).subtract(getSalePrice(currentProductM)).stripTrailingZeros().toPlainString();
        return Math.abs(Double.parseDouble(diff));
    }

    private BigDecimal getSalePrice(ProductM productM) {
        //立减
        ProductPromoPriceM productPromoPriceM = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
        BigDecimal salePrice = Optional.ofNullable(productPromoPriceM)
                .map(ProductPromoPriceM::getPromoPrice)
                .filter(price -> Objects.nonNull(price))
                .filter(price -> price.compareTo(BigDecimal.ZERO) > 0)
                .orElse(productM.getBasePrice());
        return salePrice == null ? BigDecimal.ZERO : salePrice;
    }

    public boolean getAvailableProduct(ProductM productM, ProductM topProduct, List<ProductM> productMS, Config config) {
        if (productMS.size() == 1) {
            return false;
        }
        if (productM.getProductId() == topProduct.getProductId()) {
            return true;
        }
        int topCategoryId = topProduct.getCategoryId();
        String topServiceType = topProduct.getAttr(SERVICE_TYPE);
        if (topCategoryId == DealCategoryEnum.PET_HOSPITAL.getCode()) {
            switch (topServiceType) {
                case "疫苗":
                    return isAvailableVaccineProduct(productM, topProduct, config);
                case "体检":
                    return isAvailablePhysicalExaminationProduct(productM, topProduct, config);
                case "驱虫":
                    return isAvailableExpellingInsectsProduct(productM, topProduct);
                case "洗澡":
                    return isAvailableWashingProduct(productM, topProduct);
                case "美容":
                    return isAvailableBeautifyProduct(productM, topProduct);
                default:
                    break;
            }
        } else if (topCategoryId == DealCategoryEnum.PET_SHOP.getCode()) {
            switch (topServiceType) {
                case "洗澡":
                    return isAvailableWashingProduct(productM, topProduct);
                case "美容":
                    return isAvailableBeautifyProduct(productM, topProduct);
                default:
                    break;
            }
        }
        return false;
    }

    //疫苗关联的团单
    private boolean isAvailableVaccineProduct(ProductM productM, ProductM topProduct, Config config) {
        int categoryId = productM.getCategoryId();
        String serviceType = productM.getAttr(SERVICE_TYPE);
        //服务项目
        List<ProductSkuCategoryModel> productCategories = getProductCategories(productM);
        List<Long> productCategory = productCategories.stream().map(ProductSkuCategoryModel::getProductCategoryId).collect(Collectors.toList());
        //适用宠物
        List<String> applyPets = getApplyPetTypes(productM);
        List<String> topApplyPets = getApplyPetTypes(topProduct);
        if (categoryId == DealCategoryEnum.PET_HOSPITAL.getCode()) {
            switch (serviceType) {
                //两个case会走一样的逻辑
                case "疫苗":
                case "服务组合":
                    return productCategory.contains(VACCINE) && CollectionUtils.containsAny(applyPets, topApplyPets);
                case "体检":
                    //同一门店」 且「团购类目为宠物-宠物医院-体检」且 「服务项目包括抗体检测，但不包含传染病筛查、血液检查、影像检查、尿液检查、粪便检查、皮肤检查、人宠共患病检测、过敏检查、基因检测
                    boolean containsAnti = productCategory.contains(ANTIBODY_DETECTION);
                    boolean containsBlackList = CollectionUtils.isNotEmpty(config.getMedicalCheckBlackList()) && CollectionUtils.containsAny(productCategory, config.getMedicalCheckBlackList());
                    return containsAnti && !containsBlackList;
                case "驱虫":
                    return CollectionUtils.containsAny(productCategory, EXPELLING_INSECTS) && CollectionUtils.containsAny(applyPets, topApplyPets);
                default:
                    break;
            }
        }
        return false;
    }

    /**
     * 「宠物-宠物医院-体检」且服务项目包含「抗体检测」的团单
     *
     * @param productM
     * @param topProduct
     * @param config
     * @return
     */
    private boolean isAvailablePhysicalExaminationProduct(ProductM productM, ProductM topProduct, Config config) {
        //同一门店」 且「团购类目为宠物-宠物医院-体检」且 「服务项目包括抗体检测，但不包含传染病筛查、血液检查、影像检查、尿液检查、粪便检查、皮肤检查、人宠共患病检测、过敏检查、基因检测
        int categoryId = productM.getCategoryId();
        String serviceType = productM.getAttr(SERVICE_TYPE);
        //当前团单服务项目
        List<ProductSkuCategoryModel> topProductCategories = getProductCategories(topProduct);
        List<Long> topProductCategory = topProductCategories.stream().map(ProductSkuCategoryModel::getProductCategoryId).collect(Collectors.toList());
        //1.当前团单不包含抗体检测，也不匹配
        //2.如果当前团单包含传染病筛查、血液检查、影像检查、尿液检查、粪便检查、皮肤检查、人宠共患病检测、过敏检查、基因检测，也不匹配
        if (!topProductCategory.contains(ANTIBODY_DETECTION) || CollectionUtils.containsAny(topProductCategory, config.getMedicalCheckBlackList())) {
            return false;
        }
        //服务项目
        List<ProductSkuCategoryModel> productCategories = getProductCategories(productM);
        List<Long> productCategory = productCategories.stream().map(ProductSkuCategoryModel::getProductCategoryId).collect(Collectors.toList());
        //适应宠物
        List<String> applyPets = getApplyPetTypes(productM);
        List<String> topApplyPets = getApplyPetTypes(topProduct);
        if (categoryId == DealCategoryEnum.PET_HOSPITAL.getCode()) {
            if ("疫苗".equals(serviceType)) {
                return productCategory.contains(VACCINE) && CollectionUtils.containsAny(applyPets, topApplyPets);
            }
        }
        return false;
    }

    private boolean isAvailableExpellingInsectsProduct(ProductM productM, ProductM topProduct) {
        String serviceType = productM.getAttr(SERVICE_TYPE);
        //服务项目
        List<ProductSkuCategoryModel> productCategories = getProductCategories(productM);
        List<Long> productCategory = productCategories.stream().map(ProductSkuCategoryModel::getProductCategoryId).collect(Collectors.toList());
        //适应宠物
        List<String> applyPets = getApplyPetTypes(productM);
        List<String> topApplyPets = getApplyPetTypes(topProduct);
        if (productM.getCategoryId() == DealCategoryEnum.PET_HOSPITAL.getCode()) {
            switch (serviceType) {
                case "疫苗":
                    return productCategory.contains(VACCINE) && CollectionUtils.containsAny(applyPets, topApplyPets);
                case "服务组合":
                case "驱虫":
                    return CollectionUtils.containsAny(productCategory, EXPELLING_INSECTS) && CollectionUtils.containsAny(applyPets, topApplyPets);
                default:
                    break;
            }
        }
        return false;
    }

    private boolean isAvailableWashingProduct(ProductM productM, ProductM topProduct) {
        //适用宠物
        List<String> applyPetsInWashBeauty = getApplyPetTypes(productM);
        List<String> topApplyPetsInWashBeauty = getApplyPetTypes(topProduct);
        //匹配逻辑:「同一门店」 且 「团购类目为宠物-宠物店-洗澡」 且 「行业属性-适用宠物包含当前团单的适用宠物」的团单 且「字段-基础洗浴的值必须包含 洗浴清洁
        boolean categoryMatch = productM.getCategoryId() == topProduct.getCategoryId();
        boolean serviceTypeMatch = "洗澡".equals(topProduct.getAttr(SERVICE_TYPE));
        boolean petTypeMatch = CollectionUtils.containsAny(applyPetsInWashBeauty, topApplyPetsInWashBeauty);
        boolean containsBasicWash = judgeIfContainsBasicWash(productM);
        return categoryMatch && serviceTypeMatch && petTypeMatch && containsBasicWash;
    }

    private boolean judgeIfContainsBasicWash(ProductM productM) {
        String dealDetail = productM.getAttr(DEAL_STRUCT_CONTENT_ATTR_NAME);
        DealStructModel dealStructModel = getDealStructModel(dealDetail);
        if (dealStructModel == null || dealStructModel.getDealDetailStructuredData() == null) {
            return false;
        }
        if (dealStructModel.getDealDetailStructuredData().getDealDetailSkuUniStructuredModel() == null) {
            return false;
        }
        List<MustSkusGroupModel> mustGroups = dealStructModel.getDealDetailStructuredData().getDealDetailSkuUniStructuredModel().getMustGroups();
        if (CollectionUtils.isEmpty(mustGroups)) {
            return false;
        }
        return mustGroups.stream()
                .map(MustSkusGroupModel::getSkus)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .map(SkuModel::getSkuAttrs)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .anyMatch(attr -> "基础洗浴".equals(attr.getChnName()) && Optional.ofNullable(attr.getAttrValue()).orElse("").contains("洗浴清洁"));
    }

    private boolean isAvailableBeautifyProduct(ProductM productM, ProductM topProduct) {
        String serviceType = productM.getAttr(SERVICE_TYPE);
        //适应宠物
        List<String> applyPetsInWashBeauty = getApplyPetTypes(productM);
        List<String> topApplyPetsInWashBeauty = getApplyPetTypes(topProduct);
        if (productM.getCategoryId() == topProduct.getCategoryId()) {
            if ("美容".equals(serviceType)) {
                return CollectionUtils.containsAny(applyPetsInWashBeauty, topApplyPetsInWashBeauty);
            }
        }
        return false;
    }

    private List<ProductSkuCategoryModel> getProductCategories(ProductM productM) {
        List<ProductSkuCategoryModel> defaultList = Lists.newArrayList();
        if (productM == null || StringUtils.isEmpty(productM.getAttr(DEAL_STRUCT_CONTENT_ATTR_NAME))) {
            return defaultList;
        }
        String dealDetail = productM.getAttr(DEAL_STRUCT_CONTENT_ATTR_NAME);
        DealStructModel dealStructModel = getDealStructModel(dealDetail);
        if (dealStructModel == null) {
            return defaultList;
        }
        return CollectionUtils.isEmpty(dealStructModel.getProductCategories()) ? defaultList : dealStructModel.getProductCategories();
    }

    private DealStructModel getDealStructModel(String dealDetail) {
        if (org.apache.commons.lang.StringUtils.isEmpty(dealDetail)) {
            return null;
        }
        DealStructModel dealStructModel = JsonCodec.decode(dealDetail, DealStructModel.class);
        if (dealStructModel == null || org.apache.commons.lang.StringUtils.isEmpty(dealStructModel.getStract())) {
            return null;
        }
        return dealStructModel;
    }

    /**
     * 获取适用宠物
     *
     * @param productM
     * @return
     */
    private List<String> getApplyPetTypes(ProductM productM) {
        for (String petTypeName : PET_TYPE_NAMES) {
            String attrRawValue = productM.getAttr(petTypeName);
            if (StringUtils.isEmpty(attrRawValue)) {
                continue;
            }
            return Arrays.asList(StringUtils.split(attrRawValue, ","));
        }
        return new ArrayList<>();
    }


    @VPointCfg
    @Data
    public static class Config {
        private int limit = 5;
        // 体检类黑名单列表
        private List<Long> medicalCheckBlackList;
    }
}
