package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuPriceVP;
import com.sankuai.dzviewscene.product.filterlist.utils.FitnessCrossUtils;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

@VPointOption(name = "健身行业sku价格变化点", description = "健身行业sku价格变化点", code = FitnessSkuPriceOpt.CODE)
public class FitnessSkuPriceOpt extends SkuPriceVP<FitnessSkuPriceOpt.Config> {

    public static final String CODE = "FitnessSkuPriceOpt";

    private static final String DEFAULT_FORMAT = "%s元";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (param.getPrice() == null) {
            return null;
        }

        // 健身通商品不展示价格
        if (FitnessCrossUtils.isFitnessCrossDeal(context)) {
            return null;
        }

        String format = StringUtils.isEmpty(config.getFormat()) ? DEFAULT_FORMAT : config.getFormat();
        return String.format(format, param.getPrice().stripTrailingZeros().toPlainString());
    }

    @Data
    @VPointCfg
    public static class Config {

        private String format;

    }

}
