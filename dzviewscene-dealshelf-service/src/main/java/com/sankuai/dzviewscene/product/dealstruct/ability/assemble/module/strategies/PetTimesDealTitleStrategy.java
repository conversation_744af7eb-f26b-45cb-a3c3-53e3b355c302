package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 车宠dealmodule.bin 团详多次卡标题
 * created by wb_<PERSON><PERSON><PERSON><PERSON> in 2024/04/18
 */
@Component("petTimesDealTitleStrategy")
public class PetTimesDealTitleStrategy implements ModuleStrategy {
    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        DealDetailInfoModel dealDetailInfoModel = CollectUtils.firstValue(dealDetailInfoModels);
        if(!TimesDealUtil.isTimesDeal(dealDetailInfoModel)) {
            return null;
        }
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        dealDetailModuleVO.setTitleModel(dealDetailInfoModel.getDealTitle());
        dealDetailModuleVO.setName(TimesDealUtil.getTimesTitle(dealDetailInfoModel.getDealAttrs()));
        return dealDetailModuleVO;
    }
}
