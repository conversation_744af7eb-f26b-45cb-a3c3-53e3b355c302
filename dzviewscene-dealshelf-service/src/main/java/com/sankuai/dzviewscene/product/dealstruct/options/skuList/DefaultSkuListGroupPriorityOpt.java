package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListGroupPriorityVP;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/9/8 5:29 下午
 */
@VPointOption(name = "sku列表组优先级默认变化点", description = "sku列表组优先级默认变化点",code = DefaultSkuListGroupPriorityOpt.CODE, isDefault = true)
public class DefaultSkuListGroupPriorityOpt extends SkuListGroupPriorityVP<DefaultSkuListGroupPriorityOpt.Config> {

    public static final String CODE = "DefaultSkuListGroupPriorityOpt";

    @Override
    public Integer compute(ActivityCxt context, Param param, Config config) {
        return Integer.MAX_VALUE;
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
