package com.sankuai.dzviewscene.product.dealstruct.ability.desc.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.desc.DealDetailDescBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/28 4:59 下午
 */
@VPoint(name = "团购详情补充描述组件", description = "团购详情补充描述组件",code = DealDetailDescVP.CODE, ability = DealDetailDescBuilder.CODE)
public abstract class DealDetailDescVP <T> extends PmfVPoint<String, DealDetailDescVP.Param, T> {

    public static final String CODE = "DealDetailDescVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private DealDetailInfoModel dealDetailInfoModel;

        private String desc;

        private DealDetailDtoModel dealDetailDtoModel;

        private List<ProductSkuCategoryModel> productCategories;
    }
}
