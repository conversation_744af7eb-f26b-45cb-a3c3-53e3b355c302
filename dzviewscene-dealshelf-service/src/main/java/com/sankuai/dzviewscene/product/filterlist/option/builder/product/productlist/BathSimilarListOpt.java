package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.google.common.collect.Sets;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.AvailableDateM;
import com.sankuai.dzviewscene.shelf.platform.common.model.CycleAvailableDateM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: created by hang.yu on 2024/4/2 16:11
 */
@VPointOption(name = "洗浴相似团购筛选列表过滤与排序",
        description = "洗浴相似团购筛选列表过滤与排序",
        code = "BathSimilarListOpt")
public class BathSimilarListOpt extends AbstractTimesDealListOpt {

    @Override
    protected List<ProductM> getSimilarDealList(ActivityCxt context, ProductM currentProduct, List<ProductM> list) {
        String pageSource = context.getParam(ShelfActivityConstants.Params.pageSource);
        if (GUESS.equals(pageSource)) {
            return getGuessList(list, currentProduct);
        } else if (SHELF.equals(pageSource)) {
            return getShelfList(list, currentProduct);
        }
        return Lists.newArrayList();
    }

    public List<ProductM> getGuessList(List<ProductM> list, ProductM currentProduct) {
        String currentServiceType = DealDetailUtils.getAttrSingleValueByAttrName(currentProduct.getExtAttrs(), SERVICE_TYPE);
        if (ServiceTypeConstants.IN_STORE_SERVICE_TYPE.equals(currentServiceType)) {
            return sortProductBySale(list);
        } else if (ServiceTypeConstants.BATH_FEE_SERVICE_TYPE.equals(currentServiceType) || ServiceTypeConstants.BATH_FEE_IN_STORE_SERVICE_TYPE.equals(currentServiceType)) {
            return list.stream().filter(this::filterFeeServiceType).sorted(sortSale()).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    public List<ProductM> getShelfList(List<ProductM> list, ProductM currentProduct) {
        String currentServiceType = DealDetailUtils.getAttrSingleValueByAttrName(currentProduct.getExtAttrs(), SERVICE_TYPE);
        AvailableDateM currentAvailableDateM = getAvailableDateM(currentProduct);
        List<Long> productCategoryList = getProductCategoryList(currentProduct);
        if (ServiceTypeConstants.BATH_FEE_SERVICE_TYPE.equals(currentServiceType)) {
            // 浴资票
            List<ProductM> filterList = list.stream().filter(this::filterFeeServiceType).filter(e -> filterAvailableDay(currentAvailableDateM, e)).collect(Collectors.toList());
            return sortFeeList(filterList);
        } else if (ServiceTypeConstants.BATH_FEE_IN_STORE_SERVICE_TYPE.equals(currentServiceType)) {
            // 浴资票和店内服务
            List<ProductM> filterList = list.stream().filter(e -> filterAvailableDay(currentAvailableDateM, e)).collect(Collectors.toList());
            return sortFeeAndInStoreList(filterList, productCategoryList);
        } else if (ServiceTypeConstants.IN_STORE_SERVICE_TYPE.equals(currentServiceType)) {
            // 店内服务
            List<ProductM> filterList = list.stream().filter(this::filterInStoreServiceType).filter(e -> filterAvailableDay(currentAvailableDateM, e)).collect(Collectors.toList());
            return sortInStoreList(filterList, productCategoryList);
        }
        return Lists.newArrayList();
    }

    public boolean filterFeeServiceType(ProductM productM) {
        String serviceType = DealDetailUtils.getAttrSingleValueByAttrName(productM.getExtAttrs(), "service_type");
        return ServiceTypeConstants.BATH_FEE_SERVICE_TYPE.equals(serviceType) || ServiceTypeConstants.BATH_FEE_IN_STORE_SERVICE_TYPE.equals(serviceType);
    }

    public boolean filterInStoreServiceType(ProductM productM) {
        String serviceType = DealDetailUtils.getAttrSingleValueByAttrName(productM.getExtAttrs(), "service_type");
        return ServiceTypeConstants.IN_STORE_SERVICE_TYPE.equals(serviceType) || ServiceTypeConstants.BATH_FEE_IN_STORE_SERVICE_TYPE.equals(serviceType);
    }

    public boolean filterAvailableDay(AvailableDateM currentAvailableDateM, ProductM productM) {
        AvailableDateM availableDateM = getAvailableDateM(productM);
        if (currentAvailableDateM == null || availableDateM == null) {
            return false;
        }
        if (currentAvailableDateM.getAvailableType() == 1 || weekAllDaysAvailable(currentAvailableDateM)) {
            // 指定日期可用 或  “周期适用可用”条件下“周一到周日都可用”
            return availableDateM.getAvailableType() == 1 || weekAllDaysAvailable(availableDateM);
        }
        return availableDateM.getAvailableType() == 1 || repeatDayAvailable(currentAvailableDateM, availableDateM);
    }

    public boolean weekAllDaysAvailable(AvailableDateM availableDateM) {
        return availableDateM.getAvailableType() == 0 && WeekConstants.ALL_DAYS.equals(getAvailableDays(availableDateM.getCycleAvailableDateList()));
    }

    public boolean repeatDayAvailable(AvailableDateM currentAvailableDateM, AvailableDateM availableDateM) {
        Set<Integer> currentAvailableDays = getAvailableDays(currentAvailableDateM.getCycleAvailableDateList());
        Set<Integer> availableDays = getAvailableDays(availableDateM.getCycleAvailableDateList());
        return !Collections.disjoint(currentAvailableDays, availableDays);
    }

    public Set<Integer> getAvailableDays(List<CycleAvailableDateM> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Sets.newHashSet();
        }
        return list.stream().filter(e -> CollectionUtils.isNotEmpty(e.getAvailableDays())).flatMap(e -> e.getAvailableDays().stream())
                .filter(WeekConstants.ALL_DAYS::contains).collect(Collectors.toSet());
    }

    public AvailableDateM getAvailableDateM(ProductM productM) {
        if (productM.getUseRuleM() == null || productM.getUseRuleM().getAvailableDate() == null) {
            return null;
        }
        return productM.getUseRuleM().getAvailableDate();
    }

    public List<ProductM> sortFeeList(List<ProductM> list) {
        List<ProductM> result = Lists.newArrayList();
        List<ProductM> feeList = list.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.BATH_FEE_SERVICE_TYPE))
                .sorted(sortSale()).collect(Collectors.toList());
        result.addAll(feeList);
        List<ProductM> feeAndInStoreList = list.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.BATH_FEE_IN_STORE_SERVICE_TYPE))
                .sorted(sortSale()).collect(Collectors.toList());
        result.addAll(feeAndInStoreList);
        return result;
    }

    public List<ProductM> sortFeeAndInStoreList(List<ProductM> list, List<Long> productCategoryList) {
        List<ProductM> result = Lists.newArrayList();
        // 浴资票和店内服务
        List<ProductM> feeAndInStoreList = list.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.BATH_FEE_IN_STORE_SERVICE_TYPE))
                .sorted(sortByProjectCategoryAndSale(productCategoryList)).collect(Collectors.toList());
        result.addAll(feeAndInStoreList);
        // 浴资票
        List<ProductM> feeList = list.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.BATH_FEE_SERVICE_TYPE))
                .sorted(sortByProjectCategoryAndSale(productCategoryList)).collect(Collectors.toList());
        result.addAll(feeList);
        // 店内服务
        List<ProductM> storeList = list.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.IN_STORE_SERVICE_TYPE))
                .sorted(sortByProjectCategoryAndSale(productCategoryList)).collect(Collectors.toList());
        result.addAll(storeList);
        return result;
    }

    public List<ProductM> sortInStoreList(List<ProductM> list, List<Long> productCategoryList) {
        List<ProductM> result = Lists.newArrayList();
        // 店内服务
        List<ProductM> storeList = list.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.IN_STORE_SERVICE_TYPE))
                .sorted(sortByProjectCategoryAndSale(productCategoryList)).collect(Collectors.toList());
        result.addAll(storeList);
        // 浴资票和店内服务
        List<ProductM> feeAndInStoreList = list.stream().filter(productM -> compareServiceType(productM, ServiceTypeConstants.BATH_FEE_IN_STORE_SERVICE_TYPE))
                .sorted(sortByProjectCategoryAndSale(productCategoryList)).collect(Collectors.toList());
        result.addAll(feeAndInStoreList);
        return result;
    }

    /**
     * 先按照服务项目重复率排序 再按照销量排序
     */
    public Comparator<ProductM> sortByProjectCategoryAndSale(List<Long> productCategoryList) {
        return (o1, o2) -> {
            // 取出 当前团单的项目id
            List<Long> o1ProductCategoryList = getProductCategoryList(o1);
            List<Long> o2ProductCategoryList = getProductCategoryList(o2);
            // 取交集
            o1ProductCategoryList.retainAll(productCategoryList);
            o2ProductCategoryList.retainAll(productCategoryList);
            int compareResult = o2ProductCategoryList.size() - o1ProductCategoryList.size();
            if (compareResult == 0) {
                // 按销量排序
                return o2.getSale().getSale() - o1.getSale().getSale();
            }
            return compareResult;
        };
    }


    static class ServiceTypeConstants {

        public static final String BATH_FEE_SERVICE_TYPE = "浴资票";

        public static final String IN_STORE_SERVICE_TYPE = "店内服务";

        public static final String BATH_FEE_IN_STORE_SERVICE_TYPE = "浴资票和店内服务";

    }

}
