package com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.DealDetailSkuProductsGroupsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/27
 */
@VPoint(name = "非结构化团购详情货模块构造",
        description = "当结构化数据构造无结果后 && 能力配置开启非结构化兼容时，从旧团购详情构造",
        code = NoStructDealSkuGroupBuildVP.CODE,
        ability = DealDetailSkuProductsGroupsBuilder.CODE)
public abstract class NoStructDealSkuGroupBuildVP <T> extends PmfVPoint<List<DealSkuGroupModuleVO>, NoStructDealSkuGroupBuildVP.Param, T> {
    public static final String CODE = "NoStructDealSkuGroupBuildVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private DealDetailInfoModel dealDetailInfo;
    }
}
