package com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.DealDetailSkuProductsGroupsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Builder;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/6/8 6:02 下午
 */
@VPoint(name = "团购详情sku货列表组列表后置处理", description = "团购详情sku货列表组列表后置处理",code = DealDetailSkusGroupListAfterProcessingVP.CODE, ability = DealDetailSkuProductsGroupsBuilder.CODE)
public abstract class DealDetailSkusGroupListAfterProcessingVP <T> extends PmfVPoint<List<DealSkuGroupModuleVO>, DealDetailSkusGroupListAfterProcessingVP.Param, T> {

    public static final String CODE = "DealDetailSkusGroupListAfterProcessingVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private List<DealSkuGroupModuleVO> skuGroups;
        private List<AttrM> dealAttrs;
        private String dealTitle;
        private String marketPrice;
    }
}
