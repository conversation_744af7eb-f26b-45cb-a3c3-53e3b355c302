package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListTitleVP;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/3/14
 */
@VPointOption(name = "剧本杀标品拼接标题，数据来源于前端传递",
        description = "如'《声声慢》精选商品'",
        code = "ScriptKillTitleOpt")
public class ScriptKillTitleOpt extends DealFilterListTitleVP<ScriptKillTitleOpt.Config> {

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if(config == null || StringUtils.isEmpty(config.getDefaultTitle())) {
            return "";
        }
        String scriptTitle = param.getScriptKillTitle();
        if(StringUtils.isEmpty(scriptTitle)) {
            return config.getDefaultTitle();
        }
        StringBuilder stringBuilder = new StringBuilder();
        if(StringUtils.isNotEmpty(config.getPrefix())) {
            stringBuilder.append(config.getPrefix());
            stringBuilder.append(config.getSuffix());
        }
        if(StringUtils.isNotEmpty(config.getSuffix())) {
            stringBuilder.append(config.getSuffix());
        }
        return stringBuilder.toString();
    }

    @Data
    @VPointCfg
    public static class Config {
        private String defaultTitle;
        private String prefix;
        private String suffix;
    }
}
