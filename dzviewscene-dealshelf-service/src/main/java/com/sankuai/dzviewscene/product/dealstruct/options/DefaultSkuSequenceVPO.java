package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.SkuSequenceVP;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/27 10:45 下午
 */
@VPointOption(name = "团购详情sku货顺序默认变化点", description = "团购详情sku货顺序默认变化点",code = DefaultSkuSequenceVPO.CODE, isDefault = true)
public class DefaultSkuSequenceVPO extends SkuSequenceVP<DefaultSkuSequenceVPO.Config> {

    public static final String CODE = "DefaultSkuSequenceVPO";

    @Override
    public Integer compute(ActivityCxt context, Param param, Config config) {
        Map<String, Integer> skuCategory2SequenceIdMap = config.getSkuCategory2SequenceIdMap();
        if (MapUtils.isEmpty(skuCategory2SequenceIdMap)) {
            return Integer.MAX_VALUE;
        }
        SkuItemDto skuItemDto = param.getSkuItemDto();
        List<ProductSkuCategoryModel> productCategories = param.getProductCategories();
        if (skuItemDto == null || CollectionUtils.isEmpty(productCategories)) {
            return Integer.MAX_VALUE;
        }
        String skuCategory = DealDetailUtils.getSkuCategoryBySkuCategoryId(skuItemDto.getProductCategory(), productCategories);
        return skuCategory2SequenceIdMap.get(skuCategory);
    }

    @Data
    @VPointCfg
    public static class Config {
        private Map<String, Integer> skuCategory2SequenceIdMap;
    }
}
