package com.sankuai.dzviewscene.product.dealstruct.model;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import lombok.Data;

import java.util.List;

/**
 * created by zhangzhiyuan04 in 2021/12/30
 */
@Data
public class SkuItemModel {

    /**
     * 泛商品SkuId
     */
    private long skuId;

    /**
     * 泛商品商品分类
     */
    private long productCategory;

    /**
     * 商品名
     */
    private String name;

    /**
     * 价格
     */
    private String price;

    /**
     * 数量
     */
    private String copies;

    /**
     * 优先级(数字越小优先级越高)
     */
    private int priority = Integer.MAX_VALUE;


    /**
     * Sku一级属性列表
     */
    private List<SkuAttrModel> AttrItems;

    /**
     * SkuItem原获取内容
     */
    private SkuItemDto skuItemDto;
}
