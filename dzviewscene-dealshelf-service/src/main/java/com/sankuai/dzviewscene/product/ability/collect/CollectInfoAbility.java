package com.sankuai.dzviewscene.product.ability.collect;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.productdetail.activity.SpuDetailActivity;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivity;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.user.collection.client.CollTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

@Ability(code = CollectInfoAbility.CODE,
        name = "收藏信息获取能力",
        description = "适用于需要查询商品详情等页面是否被用户收藏的场景使用",
        activities = {DealShelfActivity.CODE, SpuDetailActivity.CODE}
)
public class CollectInfoAbility extends PmfAbility<Boolean, CollectInfoQueryParam, CollectInfoQueryCfg> {

    public static final String CODE = "CollectInfoAbility";
    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<Boolean> build(ActivityCxt activityCxt,
                                            CollectInfoQueryParam param,
                                            CollectInfoQueryCfg cfg) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.ability.collect.CollectInfoAbility.build(ActivityCxt,CollectInfoQueryParam,CollectInfoQueryCfg)");
        if (cfg == null) {
            return CompletableFuture.completedFuture(false);
        }
        int platform = ParamsUtil.getIntSafely(activityCxt, PmfConstants.Params.platform);
        long dpUserId = ParamsUtil.getLongSafely(activityCxt, PmfConstants.Params.dpUserId);
        long mtUserId = ParamsUtil.getLongSafely(activityCxt, PmfConstants.Params.mtUserId);

        if (platform == VCPlatformEnum.MT.getType() || VCClientTypeEnum.isMtClientTypeByCode(platform)) {
            return queryMtCollectStatus(mtUserId, loadMtBizId(cfg, activityCxt), cfg.getMtBizType(), cfg.getMtSrcType());
        }
        return queryDpCollectStatus(dpUserId, loadDpBizId(cfg, activityCxt), cfg.getDpBizType());
    }

    private CompletableFuture<Boolean> queryMtCollectStatus(long mtUserId, long bizId, int bizType, short srcType) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.ability.collect.CollectInfoAbility.queryMtCollectStatus(long,long,int,short)");
        return compositeAtomService.getMtCollectedFromGivenSets(mtUserId, loadMtCollType(bizType), Lists.newArrayList(bizId), srcType)
                .thenApply(list -> CollectionUtils.isNotEmpty(list) && list.contains(0L));
    }

    private CollTypeEnum loadMtCollType(int bizType) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.ability.collect.CollectInfoAbility.loadMtCollType(int)");
        if (bizType == CollTypeEnum.POI_COLL.getValue()) {
            return CollTypeEnum.POI_COLL;
        }
        if (bizType == CollTypeEnum.DEAL_COLL.getValue()) {
            return CollTypeEnum.DEAL_COLL;
        }
        if (bizType == CollTypeEnum.TOUTIAO_COLL.getValue()) {
            return CollTypeEnum.TOUTIAO_COLL;
        }
        return CollTypeEnum.ANY;
    }

    private CompletableFuture<Boolean> queryDpCollectStatus(long dpUserId, String bizId, int bizType) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.ability.collect.CollectInfoAbility.queryDpCollectStatus(long,java.lang.String,int)");
        return compositeAtomService.getDpFavorStatus(Lists.newArrayList(bizId), bizType, dpUserId)
                .thenApply(resultMap -> MapUtils.isNotEmpty(resultMap) && BooleanUtils.isTrue(resultMap.get(bizId)));
    }

    private String loadDpBizId(CollectInfoQueryCfg cfg, ActivityCxt activityCxt) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.ability.collect.CollectInfoAbility.loadDpBizId(com.sankuai.dzviewscene.product.ability.collect.CollectInfoQueryCfg,com.sankuai.athena.viewscene.framework.ActivityCxt)");
        long productId = ParamsUtil.getLongSafely(activityCxt, PmfConstants.Params.productIdL);
        return productId <= 0 ? cfg.getDpBizId() : String.valueOf(productId);
    }

    private long loadMtBizId(CollectInfoQueryCfg cfg, ActivityCxt activityCxt) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.ability.collect.CollectInfoAbility.loadMtBizId(com.sankuai.dzviewscene.product.ability.collect.CollectInfoQueryCfg,com.sankuai.athena.viewscene.framework.ActivityCxt)");
        long productId = ParamsUtil.getLongSafely(activityCxt, PmfConstants.Params.productIdL);
        return productId <= 0 ? NumberUtils.toLong(cfg.getMtBizId()) : productId;
    }
}
