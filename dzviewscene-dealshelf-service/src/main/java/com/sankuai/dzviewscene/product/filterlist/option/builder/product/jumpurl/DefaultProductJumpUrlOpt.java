package com.sankuai.dzviewscene.product.filterlist.option.builder.product.jumpurl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductJumpUrlVP;

@VPointOption(name = "默认商品筛选列表跳链Opt",
        description = "默认商品筛选列表跳链Opt",
        code = "DefaultProductJumpUrlOpt",
        isDefault = true)
public class DefaultProductJumpUrlOpt extends ProductJumpUrlVP<Void> {
    @Override
    public String compute(ActivityCxt context, Param param, Void unused) {
        if (param.getProductM() != null) {
            return param.getProductM().getJumpUrl();
        } else {
            return "";
        }
    }
}
