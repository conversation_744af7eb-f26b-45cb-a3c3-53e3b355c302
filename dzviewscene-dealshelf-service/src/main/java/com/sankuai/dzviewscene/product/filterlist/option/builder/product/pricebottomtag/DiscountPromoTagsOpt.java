package com.sankuai.dzviewscene.product.filterlist.option.builder.product.pricebottomtag;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceBottomTagVP;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;

@Slf4j
@VPointOption(name = "多次卡单次价格",
        description = "",
        code = "DiscountPromoTagsOpt")
public class DiscountPromoTagsOpt extends ProductPriceBottomTagVP<DiscountPromoTagsOpt.Config> {

    private static final String TIMES_DEAL_SALE_NUMBER = "sys_multi_sale_number";

    @Override
    public List<DzTagVO> compute(ActivityCxt context, Param param, DiscountPromoTagsOpt.Config config) {
        if (!param.getProductM().isTimesDeal()) {
            return null;
        }
        return getTimesDealPromoTag(param, config.getTimesDealTagPrefix());
    }

    /**
     * 团购次卡的标签输出：单份价格
     */
    public List<DzTagVO> getTimesDealPromoTag(ProductPriceBottomTagVP.Param param, String timesDealTagPrefix) {
        String singlePrice = getSinglePrice(param.getProductM(), param.getSalePrice());
        if (StringUtils.isBlank(singlePrice) || StringUtils.isBlank(timesDealTagPrefix)) {
            return null;
        }
        DzTagVO dzTagVO = new DzTagVO();
        dzTagVO.setName(String.format(timesDealTagPrefix, singlePrice));
        dzTagVO.setHasBorder(true);
        dzTagVO.setBorderColor(ColorUtils.colorFF4B10);
        dzTagVO.setText(String.format(timesDealTagPrefix, singlePrice));
        dzTagVO.setTextColor(ColorUtils.colorFF4B10);
        return Collections.singletonList(dzTagVO);
    }

    public String getSinglePrice(ProductM productM, String salePrice) {
        if (StringUtils.isBlank(salePrice)) {
            return null;
        }
        String attr = productM.getAttr(TIMES_DEAL_SALE_NUMBER);
        if (StringUtils.isBlank(attr) || !NumberUtils.isDigits(attr)) {
            return null;
        }
        try {
            // 取到手价/可用次数  向上取整
            return new BigDecimal(salePrice).divide(new BigDecimal(attr), 2, RoundingMode.UP).stripTrailingZeros().toPlainString();
        } catch (Exception e) {
            log.error("DiscountPromoTagsOpt.getSinglePrice error, salePrice is {}, e is ", salePrice, e);
            return null;
        }
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 团购次卡的tag前缀
         */
        private String timesDealTagPrefix = "单次¥%s";

    }

}
