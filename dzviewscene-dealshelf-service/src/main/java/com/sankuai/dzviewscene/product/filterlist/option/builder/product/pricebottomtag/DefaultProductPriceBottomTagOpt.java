package com.sankuai.dzviewscene.product.filterlist.option.builder.product.pricebottomtag;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceBottomTagVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/6 00:12
 */
@VPointOption(name = "通用-不填充",
        description = "",
        code = "DefaultProductPriceBottomTagOpt",
        isDefault = true)
public class DefaultProductPriceBottomTagOpt extends ProductPriceBottomTagVP<Void> {
    @Override
    public List<DzTagVO> compute(ActivityCxt context, Param param, Void unused) {
        return null;
    }
}
