package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCombineBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.combine.CombineItemDTO;
import com.sankuai.general.product.query.center.client.enums.CombineTypeEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName PetSimilarAndTimesDealFilterListOpt.java
 * @Description 宠物相似团购和多次卡团购过滤
 * @createTime 2024/04/10 17:02
 */
@VPointOption(name = "宠物相似团购和多次卡",
        description = "宠物相似团购和多次卡",
        code = "PetSimilarAndTimesDealFilterListOpt")
public class PetSimilarAndTimesDealFilterListOpt extends ProductListVP<PetSimilarAndTimesDealFilterListOpt.Config> {
    private static final String ATTR_SEARCH_HIDDEN_STATUS = "attr_search_hidden_status";

    private static final String DEAL_STATUS_ATTR = "dealStatusAttr";

    private static final String PRODUCT_TYPE = "productType";
    @Resource
    private CompositeAtomService compositeAtomService;

    @Resource
    private PetSimilarFilterAcsOpt petSimilarFilterAcsOpt;

    @Override
    public List<ProductM> compute(ActivityCxt context, Param param, Config config) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.PetSimilarAndTimesDealFilterListOpt.compute(ActivityCxt,ProductListVP$Param,PetSimilarAndTimesDealFilterListOpt$Config)");
        // 获取多次卡关联团购商品列表
        List<ProductM> products = combineProduct(getTimesDealRelationProduct(context, param, config), param.getProductMS());
        if (CollectionUtils.isEmpty(products)) {
            return Lists.newArrayList();
        }
        List<ProductM> filterProductList = getFilterList(products);
        // 单个商品不展示
        if (CollectionUtils.isEmpty(filterProductList) || filterProductList.size() == 1) {
            return Lists.newArrayList();
        }
        int currentDealId = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.entityId);
        // 列表必须包含当前商品
        ProductM currentProduct = getCurrentProductM(filterProductList, currentDealId);
        if (currentProduct == null) {
            return Lists.newArrayList();
        }
        // 去除当前团单
        List<ProductM> result = Lists.newArrayList();
        // 当前商品置顶
        result.add(currentProduct);
        // 多次卡团单
        result.addAll(getTimesDealList(filterProductList, currentDealId));
        // 相似团购团单
        List<ProductM> otherDeals = filterProductList.stream().filter(productM -> productM.getProductId() != currentDealId)
                .filter(productM -> !productM.isTimesDealQueryFlag())
                .collect(Collectors.toList());
        result.addAll(getSimilarDealList(context, currentProduct, otherDeals, config));
        if (result.size() <= 1) {
            return Lists.newArrayList();
        }
        return result;
    }

    private List<ProductM> getSimilarDealList(ActivityCxt context, ProductM currentProduct, List<ProductM> list, Config config) {
        Cat.logEvent("INVALID_METHOD_3", "c.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.PetSimilarAndTimesDealFilterListOpt.getSimilarDealList(ActivityCxt,ProductM,List,PetSimilarAndTimesDealFilterListOpt$Config)");
        List<ProductM> similarDealList = Lists.newArrayList(list);
        similarDealList.add(currentProduct);

        PetSimilarFilterAcsOpt.Config similarFilterConfig = new PetSimilarFilterAcsOpt.Config();
        BeanUtils.copyProperties(config, similarFilterConfig);

        List<ProductM> similarFilterList = petSimilarFilterAcsOpt.compute(context, Param.builder().productMS(similarDealList).build(), similarFilterConfig);
        return similarFilterList.stream().filter(productM -> productM.getProductId() != currentProduct.getProductId()).collect(Collectors.toList());
    }

    private List<ProductM> combineProduct(List<ProductM> timesDealRes, List<ProductM> petSimilarRes) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.PetSimilarAndTimesDealFilterListOpt.combineProduct(java.util.List,java.util.List)");
        if (empty(timesDealRes) && empty(petSimilarRes)) {
            return Lists.newArrayList();
        } else if (!empty(timesDealRes) && empty(petSimilarRes)) {
            return timesDealRes;
        } else if (empty(timesDealRes)) {
            return petSimilarRes;
        } else {
            return combine(timesDealRes, petSimilarRes);
        }
    }

    public List<ProductM> combine(List<ProductM> timesDealRes, List<ProductM> petSimilarRes) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.PetSimilarAndTimesDealFilterListOpt.combine(java.util.List,java.util.List)");
        // res1 都是 次卡的  res2是全部的，将 res1 和 res2 的去重合并
        Map<Integer, ProductM> mergedMap = Maps.newLinkedHashMap();
        timesDealRes.forEach(productM -> mergedMap.put(productM.getProductId(), productM));
        petSimilarRes.forEach(productM -> {
            if (!mergedMap.containsKey(productM.getProductId())) {
                mergedMap.put(productM.getProductId(), productM);
            }
        });
        return Lists.newArrayList(mergedMap.values());
    }

    private boolean empty(List<ProductM> groupM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.PetSimilarAndTimesDealFilterListOpt.empty(java.util.List)");
        return CollectionUtils.isEmpty(groupM);
    }

    private List<ProductM> getTimesDealRelationProduct(ActivityCxt context, Param param, Config config) {
        Cat.logEvent("INVALID_METHOD_4", "c.s.d.product.filterlist.option.builder.product.productlist.PetSimilarAndTimesDealFilterListOpt.getTimesDealRelationProduct(ActivityCxt,ProductListVP$Param,PetSimilarAndTimesDealFilterListOpt$Config)");
        // 获取查询的团单
        long entityId = ParamsUtil.getLongSafely(context.getParameters(), "entityId");
        if (entityId <= 0) {
            return Lists.newArrayList();
        }
        // 调用查询中心接口召回有关联关系的团单id
        return compositeAtomService.queryByDealGroupIds(buildRequest(entityId, context)).thenApply(res -> {
            if (res == null || CollectionUtils.isEmpty(res.getList())) {
                return null;
            }
            DealGroupDTO dealGroupDTO = res.getList().get(0);
            if (CollectionUtils.isEmpty(dealGroupDTO.getCombines())) {
                return null;
            }
            Set<Long> combineDealIds = dealGroupDTO.getCombines().stream().filter(combineDTO -> CollectionUtils.isNotEmpty(combineDTO.getCombineItems()))
                    .flatMap(combineDTO -> combineDTO.getCombineItems().stream()).map(CombineItemDTO::getCombineItemId)
                    .collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(combineDealIds)) {
                return null;
            }
            combineDealIds.add(entityId);
            return buildProductM(combineDealIds);
        }).join();
    }


    private QueryByDealGroupIdRequest buildRequest(long dealId, ActivityCxt context) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.PetSimilarAndTimesDealFilterListOpt.buildRequest(long,com.sankuai.athena.viewscene.framework.ActivityCxt)");
        int platform = context.getParam("platform");
        IdTypeEnum idTypeEnum = PlatformUtil.isMT(platform) ? IdTypeEnum.MT : IdTypeEnum.DP;
        return QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(dealId), idTypeEnum)
                .combine(DealGroupCombineBuilder
                        .builder(idTypeEnum)
                        .combineTypeEnum(CombineTypeEnum.COUNT_CARD_RELATION)
                        .shopId(getShopIdFromParams(context)))
                .build();
    }

    private long getShopIdFromParams(ActivityCxt context) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.PetSimilarAndTimesDealFilterListOpt.getShopIdFromParams(com.sankuai.athena.viewscene.framework.ActivityCxt)");
        int platform = context.getParam("platform");
        if (PlatformUtil.isMT(platform)) {
            return NumberUtils.toLong(context.getParam("mtPoiIdL") + "", 0L);
        }
        return NumberUtils.toLong(context.getParam("dpPoiIdL") + "", 0L);
    }

    private List<ProductM> buildProductM(Set<Long> dealIds) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.PetSimilarAndTimesDealFilterListOpt.buildProductM(java.util.Set)");
        if (CollectionUtils.isEmpty(dealIds)) {
            return null;
        }
        return dealIds.stream().map(dealId -> {
            ProductM productM = new ProductM();
            productM.setProductId(dealId.intValue());
            // 多次卡召回标识 用于后续排序、构建数据使用
            productM.setTimesDealQueryFlag(true);
            return productM;
        }).collect(Collectors.toList());
    }

    private boolean isValid(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.PetSimilarAndTimesDealFilterListOpt.isValid(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        return productM != null && productM.getSalePrice() != null
                && "deal".equals(productM.getAttr(PRODUCT_TYPE))
                && "true".equals(productM.getAttr(DEAL_STATUS_ATTR))
                && "false".equals(productM.getAttr(ATTR_SEARCH_HIDDEN_STATUS))
                && productM.getSale() != null;
    }

    private ProductM getCurrentProductM(List<ProductM> list, int currentDealId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.PetSimilarAndTimesDealFilterListOpt.getCurrentProductM(java.util.List,int)");
        return list.stream().filter(productM -> productM.getProductId() == currentDealId).findAny().orElse(null);
    }

    private List<ProductM> getTimesDealList(List<ProductM> list, int currentDealId) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.PetSimilarAndTimesDealFilterListOpt.getTimesDealList(java.util.List,int)");
        List<ProductM> timesDealQueryList = list.stream().filter(ProductM::isTimesDealQueryFlag).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(timesDealQueryList)) {
            return Lists.newArrayList();
        }
        // 列表中必须有多次卡团单
        if (timesDealQueryList.stream().noneMatch(ProductM::isTimesDeal)) {
            return Lists.newArrayList();
        }
        // 列表中必须有单次卡团单
        if (timesDealQueryList.stream().allMatch(ProductM::isTimesDeal)) {
            return Lists.newArrayList();
        }
        return timesDealQueryList.stream()
                .filter(productM -> productM.getProductId() != currentDealId)
                .sorted(Comparator.comparing(ProductM::getSalePrice))
                .collect(Collectors.toList());
    }

    private List<ProductM> getFilterList(List<ProductM> list) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist.PetSimilarAndTimesDealFilterListOpt.getFilterList(java.util.List)");
        return list.stream().filter(this::isValid).collect(Collectors.toList());
    }

    @VPointCfg
    @Data
    public static class Config {
        private int limit = 5;
        // 体检类黑名单列表
        private List<Long> medicalCheckBlackList;
    }
}
