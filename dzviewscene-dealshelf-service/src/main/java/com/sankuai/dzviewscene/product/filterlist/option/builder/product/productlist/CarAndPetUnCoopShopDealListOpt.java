package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.dianping.cat.util.StringUtils;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@VPointOption(name = "养车用车、宠物非合作商户的团购列表组装", 
        description = "养车用车、宠物非合作商户的团购列表组装", 
        code = CarAndPetUnCoopShopDealListOpt.CODE)
public class CarAndPetUnCoopShopDealListOpt extends ProductListVP<CarAndPetUnCoopShopDealListOpt.Config> {
    public static final String CODE = "carAndPetUnCoopShopDealListOpt";

    @Override
    public List<ProductM> compute(ActivityCxt activityCxt, Param param, Config config) {
        String distanceFormat = getValidDistanceFormat(config.getDistanceFormat());
        return param.getProductMS().stream().filter(this::isValidProduct)
                .peek(productM -> updateDistance(productM, distanceFormat)).collect(Collectors.toList());
    }

    private String getValidDistanceFormat(String distanceFormat) {
        return StringUtils.isEmpty(distanceFormat) ? "距商户%s" : distanceFormat;
    }

    private boolean isValidProduct(ProductM productM) {
        return Objects.nonNull(productM)
                && CollectionUtils.isNotEmpty(productM.getShopMs())
                && Objects.nonNull(productM.getShopMs().get(0))
                && StringUtils.isNotEmpty(productM.getShopMs().get(0).getDistance());
    }

    private void updateDistance(ProductM productM, String distanceFormat) {
        productM.getShopMs().get(0)
                .setDistance(String.format(distanceFormat, productM.getShopMs().get(0).getDistance()));
    }

    @VPointCfg
    @Data
    public static class Config {
        private String distanceFormat = "距商户%s";
    }
}
