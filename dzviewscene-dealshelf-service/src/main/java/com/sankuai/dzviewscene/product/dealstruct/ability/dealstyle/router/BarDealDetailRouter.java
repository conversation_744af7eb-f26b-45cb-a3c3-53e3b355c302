package com.sankuai.dzviewscene.product.dealstruct.ability.dealstyle.router;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealstyle.DealStyleSwitchCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.detailSwitch.DouhuConfigModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.detailSwitch.SwitchModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.detailSwitch.SwitchModuleDouhuConfigModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.detailSwitch.SwitchModuleModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealDetailModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealStructModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/19 5:53 下午
 */
@Component
public class BarDealDetailRouter implements DealCategoryRouter {
    
    @Override
    public boolean identify(DealStyleSwitchCfg cfg, int dealCategory) {
        return Lists.newArrayList(312).contains(dealCategory);
    }

    @Override
    public SwitchModel compute(ActivityCxt context, DealCategoryRouter.Param param, DealStyleSwitchCfg config) {
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        List<DouhuResultModel> hitModels = findHitDouHuModels(param.getDouHuResultModels(), config.getBarExpId());
        SwitchModuleDouhuConfigModel douHuConfigModel = buildModuleAbConfig(param.getModuleKey(), hitModels);
        SwitchModuleModel switchModule = buildModule(param, hitModels, dealDetailInfoModel);
        return buildSwitchModel(switchModule, douHuConfigModel);
    }

    private List<DouhuResultModel> findHitDouHuModels(List<DouhuResultModel> douHuResultModels, List<String> barExpId) {
        if (CollectionUtils.isEmpty(douHuResultModels) || CollectionUtils.isEmpty(barExpId)) {
            return null;
        }
        return douHuResultModels.stream()
                .filter(douHuResultModel -> StringUtils.isNotEmpty(douHuResultModel.getExpId()))
                .filter(douHuResultModel -> barExpId.contains(douHuResultModel.getExpId()))
                .collect(Collectors.toList());
    }

    private SwitchModuleDouhuConfigModel buildModuleAbConfig(String key, List<DouhuResultModel> hitModels) {
        if (CollectionUtils.isEmpty(hitModels)) {
            return null;
        }
        List<DouhuConfigModel> douHuConfigModels = hitModels.stream()
                .filter(Objects::nonNull)
                .map(hitModel -> buildDouHuConfigModel(hitModel.getExpId(), hitModel.getSk(), buildExpBiInfo(hitModel)))
                .collect(Collectors.toList());
        SwitchModuleDouhuConfigModel switchModuleDouhuConfigModel = new SwitchModuleDouhuConfigModel();
        switchModuleDouhuConfigModel.setKey(key);
        switchModuleDouhuConfigModel.setAbConfigs(douHuConfigModels);
        return switchModuleDouhuConfigModel;
    }

    private DouhuConfigModel buildDouHuConfigModel(String expId, String expResult, String expBiInfo) {
        DouhuConfigModel douhuConfigModel = new DouhuConfigModel();
        douhuConfigModel.setExpId(expId);
        douhuConfigModel.setExpResult(expResult);
        douhuConfigModel.setExpBiInfo(expBiInfo);
        return douhuConfigModel;
    }

    private String buildExpBiInfo(DouhuResultModel douHuM) {
        Map<String, String> expBiInfoMap = new HashMap<String, String>() {{
            put("query_id", douHuM.getAbQueryId());
            put("ab_id", douHuM.getSk());
        }};
        return JsonCodec.encode(expBiInfoMap);
    }

    private SwitchModuleModel buildModule(Param param, List<DouhuResultModel> hitModels, DealDetailInfoModel dealDetailInfoModel) {
        if (dealDetailInfoModel == null) {
            return null;
        }
        if (!goDealModule(param, hitModels, dealDetailInfoModel.getDealAttrs())) {
            return null;
        }
        SwitchModuleModel switchModuleModel = new SwitchModuleModel();
        switchModuleModel.setModuleValue(CUSTOM_STRUCTURE_MODULE);
        switchModuleModel.setModuleKey(param.getModuleKey());
        return switchModuleModel;
    }

    private boolean goDealModule(Param param, List<DouhuResultModel> hitModels, List<AttrM> dealAttrs) {
        //改版前的-线上逻辑，有图走dealmoudle,无图走dealstruct
        boolean onlineLogic = isBarDealContainPicSkuAttr(dealAttrs);
        //没命中实验 || 是小程序, 走线上
        if (CollectionUtils.isEmpty(hitModels) || PlatformUtil.isAnyXcx(param.getMpSource())) {
            return onlineLogic;
        }
        //只有先命中团详改版实验2860，再命中2588实验，才走最新的服务项目实验样式，其余情况都走线上逻辑
        boolean goNewestStyle = hitModels.stream().allMatch(DouhuResultModel::isHitSk);
        return goNewestStyle || onlineLogic;
    }

    private boolean isBarDealContainPicSkuAttr(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return false;
        }
        DealDetailModel dealDetailModel = getDealDetailModel(DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "dealStructContent"));
        List<SkuItemDto> skuItemDtos = getSkuItemDtoList(dealDetailModel);
        if (CollectionUtils.isEmpty(skuItemDtos)) {
            return false;
        }
        List<SkuItemDto> skuLists = skuItemDtos.stream().filter(sku -> StringUtils.isNotEmpty(DealDetailUtils.getSkuAttrValueBySkuAttrName(sku.getAttrItems(), "headpic"))).collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(skuLists) && skuLists.size() == skuItemDtos.size();
    }

    private DealDetailModel getDealDetailModel(String dealDetail) {
        DealStructModel dealStructModel = getDealStructModel(dealDetail);
        if (dealStructModel == null || StringUtils.isEmpty(dealStructModel.getStract())) {
            return null;
        }
        DealDetailModel dealDetailModel = JsonCodec.decode(dealStructModel.getStract(), DealDetailModel.class);
        if (dealDetailModel == null || dealDetailModel.getSkuUniStructuredDto() == null) {
            return null;
        }
        return dealDetailModel;
    }

    private DealStructModel getDealStructModel(String dealDetail) {
        if (StringUtils.isEmpty(dealDetail)) {
            return null;
        }
        DealStructModel dealStructModel = JsonCodec.decode(dealDetail, DealStructModel.class);
        if (dealStructModel == null || StringUtils.isEmpty(dealStructModel.getStract())) {
            return null;
        }
        return dealStructModel;
    }

    private List<SkuItemDto> getSkuItemDtoList(DealDetailModel dealDetailModel) {
        if (dealDetailModel == null || dealDetailModel.getSkuUniStructuredDto() == null) {
            return null;
        }
        List<SkuItemDto> skuItemDtos = new ArrayList<>();
        List<SkuItemDto> mustGroupSkuItems = dealDetailModel.getSkuUniStructuredDto().getMustGroups().stream().flatMap(mustGroup -> mustGroup.getSkuItems().stream()).filter(item -> item != null).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(mustGroupSkuItems)) {
            skuItemDtos.addAll(mustGroupSkuItems);
        }
        List<SkuItemDto> optionalGroupSkuItems = dealDetailModel.getSkuUniStructuredDto().getOptionalGroups().stream().flatMap(optionalGroup -> optionalGroup.getSkuItems().stream()).filter(item -> item != null).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(optionalGroupSkuItems)) {
            skuItemDtos.addAll(optionalGroupSkuItems);
        }
        return skuItemDtos;
    }
}
