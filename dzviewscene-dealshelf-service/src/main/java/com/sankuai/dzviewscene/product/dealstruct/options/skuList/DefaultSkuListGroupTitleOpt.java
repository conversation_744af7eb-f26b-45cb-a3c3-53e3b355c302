package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListGroupTitleVP;
import lombok.Data;
import org.codehaus.plexus.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/9/8 5:29 下午
 */
@VPointOption(name = "sku列表组标题默认变化点", description = "sku列表组标题默认变化点",code = DefaultSkuListGroupTitleOpt.CODE, isDefault = true)
public class DefaultSkuListGroupTitleOpt extends SkuListGroupTitleVP<DefaultSkuListGroupTitleOpt.Config> {

    public static final String CODE = "DefaultSkuGroupTitleOpt";

    private static final String DEFAULT_FORMAT = "以下%s选%s";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (param.isMustGroup()) {
            return null;
        }
        String format = StringUtils.isEmpty(config.getFormat()) ? DEFAULT_FORMAT : config.getFormat();
        return String.format(format, param.getTotalNum(), param.getOptionalNum());
    }

    @Data
    @VPointCfg
    public static class Config {
        private String format;
    }
}
