package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.price.vpoints.DealDetailPriceVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailPriceModel;
import lombok.Data;
import org.apache.commons.lang.StringUtils;


@VPointOption(name = "团购详框架改版情价格组件变化点", description = "团购详情框架改版价格组件变化点，支持配置", code = DefaultDealDetailPriceV2VPO.CODE, isDefault = false)
public class DefaultDealDetailPriceV2VPO extends DealDetailPriceVP<DefaultDealDetailPriceV2VPO.Config> {

    public static final String CODE = "DefaultDealDetailPriceV2VPO";

    private static final String DEFAULT_SALE_PRICE_TITLE = "团购价";

    private static final String DEFAULT_SALE_PRICE_FORMAT = "%s元";

    private static final String DEFAULT_MARKET_PRICE_TITLE = "总价";

    private static final String DEFAULT_MARKET_PRICE_FORMAT = "%s元";

    @Override
    public DealDetailPriceModel compute(ActivityCxt context, Param param, Config config) {
        String salePrice = param.getSalePrice();
        String marketPrice = param.getMarketPrice();
        int dealId = param.getDealId();
        String salePriceTag = null;
        String salePriceTitle = null;
        if (StringUtils.isNotBlank(config.getSalePriceFormat())){
            String salePriceFormat = config.getSalePriceFormat();
            salePriceTag = StringUtils.isEmpty(salePrice) ? null : String.format(salePriceFormat, salePrice);
            salePriceTitle = StringUtils.isEmpty(config.getSalePriceTitle()) ? DEFAULT_SALE_PRICE_TITLE : config.getSalePriceTitle();
        }
        String marketriceTag = null;
        String marketPriceTitle = null;
        if (StringUtils.isNotBlank(config.getMarketPriceFormat())){
            String marketPriceFormat = config.getMarketPriceFormat();
            marketriceTag = StringUtils.isEmpty(marketPrice) ? null : String.format(marketPriceFormat, marketPrice);
            marketPriceTitle = StringUtils.isEmpty(config.getMarketPriceTitle()) ? DEFAULT_MARKET_PRICE_TITLE : config.getMarketPriceTitle();
        }
        return buildDealDetailPriceModuleVO(dealId, salePriceTag, salePriceTitle, marketriceTag, marketPriceTitle);
    }

    private DealDetailPriceModel buildDealDetailPriceModuleVO(int dealId, String salePrice, String salePriceTitle, String marketPrice, String marketPriceTitle) {
        if (StringUtils.isEmpty(salePrice) && StringUtils.isEmpty(marketPrice)) {
            return null;
        }
        DealDetailPriceModel dealDetailPriceModel = new DealDetailPriceModel();
        dealDetailPriceModel.setDealId(dealId);
        if (StringUtils.isNotEmpty(salePrice)) {
            dealDetailPriceModel.setSalePrice(salePrice);
            dealDetailPriceModel.setSalePriceTitle(salePriceTitle);
        }
        if (StringUtils.isNotEmpty(marketPrice)) {
            dealDetailPriceModel.setOriginalPrice(marketPrice);
            dealDetailPriceModel.setOriginalPriceTitle(marketPriceTitle);
        }
        return dealDetailPriceModel;
    }

    @Data
    @VPointCfg
    public static class Config {
        private String salePriceTitle;
        private String salePriceFormat;
        private String marketPriceTitle;
        private String marketPriceFormat;
    }
}
