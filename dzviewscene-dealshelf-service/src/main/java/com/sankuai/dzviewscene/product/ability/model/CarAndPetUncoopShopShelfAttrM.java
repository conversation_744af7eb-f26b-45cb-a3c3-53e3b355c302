package com.sankuai.dzviewscene.product.ability.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class CarAndPetUncoopShopShelfAttrM {

    private String backCategoryId;

    private String viewType;

    private String rule;

    private String clientType;

    private String moduleName;

    private String jumpUrl;

    private String jumpDesc;

    private String expId;

    private String keyWord;

    private String dzgmStartVersion;

    private String dzgmEndVersion;

    /**
     * 对应内层的数据。dataList大小为内层的配置条数
     */
    private List<Map<String, Object>> dataList;
}
