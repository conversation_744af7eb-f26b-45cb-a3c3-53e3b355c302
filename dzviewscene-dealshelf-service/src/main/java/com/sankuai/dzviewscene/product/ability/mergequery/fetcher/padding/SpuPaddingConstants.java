package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding;

public class SpuPaddingConstants {
    public interface Params {
        /**
         * 平台，1-点评，2美团
         */
        String PLATFORM = "platform";

        /**
         * 设备类型，点评APP-100，美团APP-200，点评H5-101，美团H5-201
         */
        String UA_CODE ="uaCode";

        /**
         * 城市id
         */
        String CITY_ID = "cityId";

        /**
         * 城市ID,点评侧
         */
        String DP_CITY_ID = "dpCityId";

        /**
         * 城市ID,美团侧
         */
        String MT_CITY_ID = "mtCityId";

        /**
         * 设备ID，点评dpid，美团uuid
         */
       String DEVICE_ID = "deviceId";

        /**
         * 统一设备ID
         */
        String UNION_ID = "unionId";

        /**
         * 纬度
         */
        String LAT = "lat";

        /**
         * 经度
         */
        String LNG = "lng";

        /**
         * 用户ID，区分平台
         */
        String USER_ID = "userId";

        /**
         * 点评用户ID
         */
        String DP_USER_ID = "dpUserId";

        /**
         * 美团用户ID
         */
       String MT_USER_ID = "mtUserId";

        /**
         * app版本
         */
        String APP_VERSION = "appVersion";

        /**
         * 门店UUID
         */
        String SHOP_UUID = "shopUuid";

        /**
         * 标品类型
         */
        String PRODUCT_TYPE = "spuType";

        /**
         * 门店ID，区分平台
         */
        String POI_ID = "poiId";

        /**
         * 美团门店ID
         */
        String MT_POI_ID = "mtPoiId";

        /**
         * 点评门店ID
         */
        String DP_POI_ID = "dpPoiId";

        /**
         * 门店城市ID
         */
        String SHOP_CITY_ID = "shopCityId";

        /**
         * 门店点评门店ID
         */
        String SHOP_DP_CITY_ID = "shopDpCityId";

        /**
         * 门店美团门店ID
         */
        String SHOP_MT_CITY_ID = "shopMtCityId";

        /**
         * 标品基础信息查询策略
         */
        String QUERY_SPU_STRATEGIES = "querySpuStrategies";

        /**
         * 标品价格信息查询策略
         */
        String QUERY_PRICE_SCENE = "queryPriceScene";
    }
}
