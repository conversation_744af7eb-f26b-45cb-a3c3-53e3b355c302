package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.ability.videoModule.vpoints.DealDetailVideoModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.VideoModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/27 9:17 下午
 */
@VPointOption(name = "教培-运动培训-游泳团购详情模块视频组件变化点", description = "游泳团购详情模块视频组件变化点",code = EduSportSwimmingDealDetailVideoModuleVPO.CODE, isDefault = false)
public class EduSportSwimmingDealDetailVideoModuleVPO extends DealDetailVideoModuleVP<EduSportSwimmingDealDetailVideoModuleVPO.Config> {

    public static final String CODE = "eduSportSwimmingDealDetailVideoModuleVPO";

    private static final String COUSE_TARGET_DETAIL_ATTR_NAME = "course_target_detail";

    private static final String CUSTOMIZE_COUSE_TARGET_DETAIL_ATTR_NAME = "customize_course_target_detail";

    private static final String COUSE_TARGET_ATTR_NAME = "course_target";

    private static final String CUSTOMIZE_OUSE_TARGET_ATTR_VALUE = "自定义课程目标";

    private static final String SWIMMING_STROKE_ATTR_NAME = "swimming_stroke";

    @Override
    public VideoModuleVO compute(ActivityCxt context, Param param, Config config) {
        List<AttrM> dealAttrs = param.getDealAttrs();
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        String courseTargetDesc = getCourseTargetDesc(param.getDealAttrs());
        String courseTarget = DealDetailUtils.getAttrSingleValueByAttrName(param.getDealAttrs(), COUSE_TARGET_ATTR_NAME);
        VideoModuleVO videoModuleVO = new VideoModuleVO();
        videoModuleVO.setContent(courseTargetDesc);
        if (!isShowVideo(courseTarget) || !isHitDouhu(param.getDouhuResultModels(), config)) {
            return videoModuleVO;
        }
        String swimmingStroke = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, SWIMMING_STROKE_ATTR_NAME);
        VideoUrlModel videoUrlModel = getVideoUrlModel(swimmingStroke, config);
        if (videoUrlModel == null) {
            return videoModuleVO;
        }
        videoModuleVO.setUrl(videoUrlModel.getVidelUrl());
        videoModuleVO.setThumbnailURL(videoUrlModel.getThumbnailURL());
        if (config == null || StringUtils.isEmpty(config.getVideoDesc())) {
            return videoModuleVO;
        }
        videoModuleVO.setDesc(config.getVideoDesc());
        return videoModuleVO;
     }

     private boolean isShowVideo(String courseTarget) {
         return StringUtils.isNotEmpty(courseTarget) && !CUSTOMIZE_OUSE_TARGET_ATTR_VALUE.equals(courseTarget);
     }

     private String getCourseTargetDesc(List<AttrM> dealAttrs) {
         String courseTargetDesc = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, COUSE_TARGET_DETAIL_ATTR_NAME);
         String customizeCourseTargetDesc = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, CUSTOMIZE_COUSE_TARGET_DETAIL_ATTR_NAME);
         return StringUtils.isEmpty(courseTargetDesc) ? customizeCourseTargetDesc : courseTargetDesc;
     }

     private boolean isHitDouhu(List<DouhuResultModel> douhuResultModels, Config config) {
        if (CollectionUtils.isEmpty(douhuResultModels) || config == null || CollectionUtils.isEmpty(config.getDouhuExpIds())) {
            return false;
        }
        DouhuResultModel douhuResultModel = douhuResultModels.stream().filter(douhu -> config.getDouhuExpIds().contains(douhu.getExpId())).findFirst().orElse(null);
        return douhuResultModel != null && douhuResultModel.isHitSk();
     }

    private VideoUrlModel getVideoUrlModel(String swimmingStroke, Config config) {
        if (StringUtils.isEmpty(swimmingStroke) || config == null || CollectionUtils.isEmpty(config.getVideoUrlModels())) {
            return null;
        }
        return config.getVideoUrlModels().stream().filter(model -> swimmingStroke.equals(model.getSwimminggStroke())).findFirst().orElse(null);
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<String> douhuExpIds;
        private String videoDesc;
        private List<VideoUrlModel> videoUrlModels;
    }

    @Data
    public static class VideoUrlModel {
        private String swimminggStroke;
        private String videlUrl;
        private String thumbnailURL;
    }
}
