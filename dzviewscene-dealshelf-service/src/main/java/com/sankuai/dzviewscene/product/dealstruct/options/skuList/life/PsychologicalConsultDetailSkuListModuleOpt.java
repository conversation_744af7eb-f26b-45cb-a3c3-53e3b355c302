package com.sankuai.dzviewscene.product.dealstruct.options.skuList.life;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 心理咨询团详sku列表模块
 */
@Slf4j
@VPointOption(
        name = "心理咨询团详sku列表组变化点", description = "心理咨询团详sku列表组变化点",
        code = PsychologicalConsultDetailSkuListModuleOpt.CODE
)
public class PsychologicalConsultDetailSkuListModuleOpt extends SkuListModuleVP<PsychologicalConsultDetailSkuListModuleOpt.Config> {

    public static final String CODE = "PsychologicalConsultDetailSkuListModuleOpt";
    /**
     * sku属性类型，
     * 0时属性默认取name和value，为1时属性默认取name和picValue，2时参考美容团购详情模块【服务步骤部分】
     */
    private static final int SKU_ITEM_TYPE_ATTR_LIST = 2;

    // 取数key
    private static final String ATTR_KEY_PSYCHOLOGICAL_SERVICES = "PsychologicalService";

    private static final String ATTR_KEY_SERVICE_MODE = "psychological_consultant_service_mode";

    private static final String ATTR_KEY_CONCERN_TYPE = "ConcernType";

    private static final String ATTR_KEY_CONSULT_LEVEL = "ConsultantLevel";
    // 服务内容name
    private static final String SKU_ITEM_NAME_SERVICE_CONTENT = "服务内容";

    // 困扰类型到服务方向映射
    @ConfigValue(key = "com.sankuai.dztheme.dealgroup.document.productTags.life.psychological.concern.type2service_direction", defaultValue = "{}")
    private Map<String, String> concernType2ServiceDirectionMaps;

    @Override
    public List<DealDetailSkuListModuleGroupModel> compute(ActivityCxt activityCxt, Param param, Config config) {
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        // 获取团单属性列表
        List<AttrM> dealAttrs = DealDetailUtils.extractDealAttrsFromDealDetailInfoModel(dealDetailInfoModel);
        // 解析服务内容列表
        List<PsychologicalServiceItem> psychologicalServiceItems = parsePsychologicalServiceItems(dealAttrs, config);
        if (CollectionUtils.isEmpty(psychologicalServiceItems)) {
            return Lists.newArrayList();
        }
        // 构造Sku模块列表
        List<DealSkuVO> dealSkuList = buildDealSkuVOs(psychologicalServiceItems, dealAttrs);
        // 装配结果
        return buildDealDetailSkuListModuleGroupModelList(dealSkuList);
    }

    private List<DealDetailSkuListModuleGroupModel> buildDealDetailSkuListModuleGroupModelList(List<DealSkuVO> dealSkuList) {
        if (CollectionUtils.isEmpty(dealSkuList)) {
            return null;
        }
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(dealSkuList);
        DealDetailSkuListModuleGroupModel dealDetailSkuListModuleGroupModel = new DealDetailSkuListModuleGroupModel();
        dealDetailSkuListModuleGroupModel.setDealSkuGroupModuleVOS(Lists.newArrayList(dealSkuGroupModuleVO));
        return Lists.newArrayList(dealDetailSkuListModuleGroupModel);
    }

    /**
     * 构造sku列表组
     *
     * @return
     */
    private List<DealSkuVO> buildDealSkuVOs(List<PsychologicalServiceItem> serviceItems, List<AttrM> dealAttrs) {
        // 获取通用咨询师等级
        String consultantLevel = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, ATTR_KEY_CONSULT_LEVEL);
        // 获取通用服务方式
        String serviceMode = getServiceMode(dealAttrs, ATTR_KEY_SERVICE_MODE);
        // 获取通用服务方向
        String serviceDirection = getServiceDirection(dealAttrs);
        // 构建skuItem列表组
        return serviceItems.stream().map(serviceItem -> {
            DealSkuVO dealSkuVO = new DealSkuVO();
            dealSkuVO.setTitle(serviceItem.getServiceName());
            dealSkuVO.setItems(
                    buildSkuItems(serviceItem.getServiceContents(), serviceDirection, serviceMode, consultantLevel));
            dealSkuVO.setSubTitle(serviceItem.getSalePrice());
            return dealSkuVO;
        }).collect(Collectors.toList());
    }

    private String getServiceMode (List<AttrM> dealAttrs, String attrName) {
        List<String> serviceModes = DealDetailUtils.parseAttrValueByAttrName(dealAttrs, attrName);
        return joinList(serviceModes);
    }

    private String joinList(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return StringUtils.join(list, "、");
    }

    private String getServiceDirection(List<AttrM> dealAttrs) {
        String serviceDirection = getServiceDirectionByConcernType(dealAttrs);
        List<String>  serviceDirections = DealDetailUtils.parseAttrValueByAttrName(dealAttrs, serviceDirection);
        return joinList(serviceDirections);
    }

    private String getServiceDirectionByConcernType(List<AttrM> dealAttrs) {
        String concernType = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, ATTR_KEY_CONCERN_TYPE);
        if (StringUtils.isBlank(concernType)) {
            return null;
        }
        if (Objects.isNull(concernType2ServiceDirectionMaps) || concernType2ServiceDirectionMaps.isEmpty()) {
            return null;
        }
        return concernType2ServiceDirectionMaps.get(concernType);
    }

    /**
     * 构建SkuItem服务项目列表
     *
     * @return
     */
    private List<DealSkuItemVO> buildSkuItems(List<ServiceContentModel> psychologicalServices, String serviceDirection,
            String serviceMode, String consultantLevel) {
        List<DealSkuItemVO> skuItems = Lists.newArrayList();
        // 构建咨询师等级
        skuItems.add(buildSkuItem("服务等级", consultantLevel));
        // 构建服务方式
        skuItems.add(buildSkuItem("服务方式", serviceMode));
        // 构建服务方向
        skuItems.add(buildSkuItem("服务方向", serviceDirection));
        // 构建服务内容
        skuItems.add(buildServiceContent(psychologicalServices));
        return skuItems.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private DealSkuItemVO buildSkuItem(String name, String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setValue(value);
        return dealSkuItemVO;
    }

    private DealSkuItemVO buildServiceContent(List<ServiceContentModel> psychologicalServices) {
        if (CollectionUtils.isEmpty(psychologicalServices)) {
            return null;
        }
        // 构建服务内容二级属性列表
        List<SkuAttrAttrItemVO> serviceItems = buildSkuAttAttrsItems(psychologicalServices);
        return buildSkuItem(SKU_ITEM_NAME_SERVICE_CONTENT, serviceItems);
    }

    private DealSkuItemVO buildSkuItem(String name, List<SkuAttrAttrItemVO> skuValueAttrs) {
        if (CollectionUtils.isEmpty(skuValueAttrs)) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setType(SKU_ITEM_TYPE_ATTR_LIST);
        dealSkuItemVO.setValueAttrs(skuValueAttrs);
        return dealSkuItemVO;
    }

    private List<SkuAttrAttrItemVO> buildSkuAttAttrsItems(List<ServiceContentModel> psychologicalServices) {
        return psychologicalServices.stream().filter(
                service -> Objects.nonNull(service) && StringUtils.isNotBlank(service.getConsultingServicesContent()))
                .map(this::buildSkuAttrAttrItemVO).collect(Collectors.toList());
    }

    // Sku二级属性
    private SkuAttrAttrItemVO buildSkuAttrAttrItemVO(ServiceContentModel service) {
        SkuAttrAttrItemVO attrItemVO = new SkuAttrAttrItemVO();
        if (Objects.nonNull(service.getSingleServiceTime())) {
            attrItemVO.setInfo(Lists.newArrayList(formatServiceTime(service.getSingleServiceTime())));
        }
        attrItemVO.setName(service.getConsultingServicesContent());
        return attrItemVO;
    }

    // 服务时长格式化
    private String formatServiceTime(BigDecimal serviceTime) {
        return serviceTime.stripTrailingZeros().toPlainString() + "分钟";
    }

    // 解析服务内容
    private List<PsychologicalServiceItem> parsePsychologicalServiceItems(List<AttrM> attrs, Config config) {
        AttrM dealDetailInfo = attrs.stream().filter(attr -> "detailInfo".equals(attr.getName())).findFirst()
                .orElse(null);
        if (Objects.isNull(dealDetailInfo) || StringUtils.isBlank(dealDetailInfo.getValue())) {
            return null;
        }
        List<PsychologicalServiceItem> psychologicalServiceModels = Lists.newArrayList();
        try {
            Optional.ofNullable(JSON.parseObject(dealDetailInfo.getValue()))
                    .map(obj -> obj.getJSONArray("content"))
                    .filter(arr -> !arr.isEmpty())
                    .map(arr -> getDetailInfoContentByType(arr, "serviceItem-structure-table"))
                    .map(obj -> obj.getJSONObject("data"))
                    .map(obj -> obj.getJSONArray("groups"))
                    .filter(arr -> !arr.isEmpty())
                    .orElse(new JSONArray()).stream().map(JSONObject.class::cast)
                    .filter(obj -> obj.containsKey("units"))
                    .map(obj -> {
                        JSONArray units = obj.getJSONArray("units");
                        return units == null ? new JSONArray() : units;
                    })
                    .flatMap(Collection::stream)
                    .map(JSONObject.class::cast)
                    .forEach(obj -> {
                        if (obj.containsKey("serviceItemName") && obj.containsKey("serviceItemValue")) {
                            String serviceItemName = obj.getString("serviceItemName");
                            String serviceItemPrice = buildServiceItemPrice(obj.getBigDecimal("price"), config);
                            List<ServiceContentModel> serviceContents = parsePsychologicalServices(
                                    obj.getJSONObject("serviceItemValue"));
                            psychologicalServiceModels.add(
                                    buildPsychologicalServiceItem(serviceItemName, serviceItemPrice, serviceContents));
                        }
                    });
            return psychologicalServiceModels;
        } catch (Exception e) {
            log.error("PsychologicalConsultDetailSkuListModuleOpt.parseDealDetailInfoAttrsFromAttrs error:{}", JSON.toJSON(attrs), e);
            return Lists.newArrayList();
        }
    }

    String buildServiceItemPrice(BigDecimal salePrice, Config config) {
        if (salePrice == null) {
            return null;
        }
        return config.getRMB_SIGN() + salePrice.setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
    }

    private PsychologicalServiceItem buildPsychologicalServiceItem(String itemName, String salePrice, List<ServiceContentModel> serviceContents) {
        PsychologicalServiceItem serviceItem = new PsychologicalServiceItem();
        serviceItem.setServiceName(itemName);
        serviceItem.setSalePrice(salePrice);
        serviceItem.setServiceContents(serviceContents);
        return serviceItem;
    }

    private List<ServiceContentModel> parsePsychologicalServices(JSONObject serviceItemJSON) {
        try {
            if (serviceItemJSON.containsKey("objectValues")) {
                JSONObject objectValues = serviceItemJSON.getJSONObject("objectValues");
                String psychologicalServiceJson = objectValues.getString(ATTR_KEY_PSYCHOLOGICAL_SERVICES);
                return parsePsychologicalServiceArray(psychologicalServiceJson);
            }
            return Lists.newArrayList();
        } catch (Exception e) {
            log.error("PsychologicalConsultDetailSkuListModuleOpt.parsePsychologicalServices, error:{}",
                    JSON.toJSON(serviceItemJSON), e);
            return Lists.newArrayList();
        }
    }

    private List<ServiceContentModel> parsePsychologicalServiceArray(String values) {
        try {
            if (StringUtils.isBlank(values)) {
                return Lists.newArrayList();
            }
            if (isInvalidArrayJson(values)) {
                values = "[" + values + "]";
            }
            return JSON.parseArray(values, ServiceContentModel.class);
        } catch (Exception e) {
            log.error("PsychologicalConsultDetailSkuListModuleOpt.parsePsychologicalServices error, {}", values, e);
            return Lists.newArrayList();
        }
    }

    private boolean isInvalidArrayJson(String values) {
        return !values.contains("[") && !values.contains("]");
    }

    private JSONObject getDetailInfoContentByType(JSONArray array, String type) {
        Optional<JSONObject> opt = array.stream().map(JSONObject.class::cast)
                .filter(obj -> type.equals(obj.getString("type"))).findFirst();
        return opt.orElse(null);
    }

    @Data
    @VPointCfg
    public static class Config {
        private String RMB_SIGN = "¥";
    }

    /**
     * 属性配置模型
     *
     * @return
     */
    @Data
    public static class AttrConfigModel {
        // 属性展示标题
        public String title;
        // 属性列表
        public List<String> skuAttrList;
        // icon
        public String icon;
    }

    /**
     * 服务项目模型
     */
    @Data
    public static class PsychologicalServiceItem {
        private String serviceName;
        private String salePrice;
        private List<ServiceContentModel> serviceContents;
    }

    /**
     * 服务内容模型
     *
     * @return
     */
    @Data
    public static class ServiceContentModel {
        // 服务内容
        public String consultingServicesContent;
        // 服务时长
        public BigDecimal singleServiceTime;
    }
}
