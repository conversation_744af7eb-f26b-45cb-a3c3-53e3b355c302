package com.sankuai.dzviewscene.product.dealstruct.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/14 8:31 下午
 */
@MobileDo(id = 0xb939)
public class SkuAttrAttrItemVO implements Serializable {
    /**
     * 属性相关信息
     */
    @MobileDo.MobileField(key = 0x649f)
    private List<String> info;

    /**
     * 属性值
     */
    @MobileDo.MobileField(key = 0x53c7)
    private List<CommonAttrVO> values;


    /**
     * 属性名
     */
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;

    /**
     * 属性名
     */
    @MobileDo.MobileField(key = 0xa1cc)
    private PopUpWindowVO popup;

    public List<String> getInfo() {
        return info;
    }

    public void setInfo(List<String> info) {
        this.info = info;
    }

    public List<CommonAttrVO> getValues() {
        return values;
    }

    public void setValues(List<CommonAttrVO> values) {
        this.values = values;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public PopUpWindowVO getPopup() {
        return popup;
    }

    public void setPopup(PopUpWindowVO popup) {
        this.popup = popup;
    }
}
