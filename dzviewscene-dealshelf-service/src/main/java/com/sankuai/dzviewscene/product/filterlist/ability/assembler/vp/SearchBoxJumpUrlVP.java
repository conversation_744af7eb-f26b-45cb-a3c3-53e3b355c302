package com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp;


import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.DealListResponseAssembler;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@VPoint(name = "搜索框跳转链接", description = "搜索框跳转链接变化点", code = SearchBoxJumpUrlVP.CODE, ability = DealListResponseAssembler.CODE)
public abstract class SearchBoxJumpUrlVP<T> extends PmfVPoint<String, SearchBoxJumpUrlVP.Param, T> {
    public static final String CODE = "SearchBoxJumpUrlVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {

        /**
         * 平台
         */
        private int platform;
        /**
         * 商户分类
         */
        private int shopCategory;
        /**
         * 美团侧门店ID
         */
        private int mtPoiId;
        private long mtPoiIdL;
        /**
         * 点评侧门店ID
         */
        private int dpPoiId;
        private long dpPoiIdL;
    }
}
