package com.sankuai.dzviewscene.product.filterlist.option.builder.product.saleprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePriceVP;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.card.CardFetcher;
import com.sankuai.dzviewscene.product.utils.CardPromoUtils;
import com.sankuai.dzviewscene.product.utils.MerchantMemberPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.MerchantMemberProductPromoData;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.utils.ShelfErrorUtils;
import org.apache.commons.lang.StringUtils;

@VPointOption(name = "商家会员会员价",
        description = "享受会员价或者新会员价商品的售卖价格处理",
        code = "MerchantMemberProductSalePriceOpt")
public class MerchantMemberProductSalePriceOpt extends ProductSalePriceVP<Void> {
    @Override
    public String compute(ActivityCxt activityCxt, Param param, Void unused) {
        ProductM productM = param.getProductM();
        try {
            CardM cardM = activityCxt.getSource(CardFetcher.CODE);
            ProductPromoPriceM cardPromo = CardPromoUtils.getFirstUserHoldCardPromo(productM.getPromoPrices(), cardM);
            if (cardPromo != null && StringUtils.isNotEmpty(cardPromo.getPromoPriceTag())) {
                //有卡优惠 且 卡优惠没有倒挂，才展示卡价
                if (!CardPromoUtils.isCardDaoGuaPromo(productM.getPromoPrices())) {
                    return cardPromo.getPromoPriceTag();
                }
            }
            //会员价
            MerchantMemberProductPromoData merchantMemberPromoPriceM = MerchantMemberPromoUtils.getMerchantMemberPromo(productM);
            if(merchantMemberPromoPriceM.getProductPromoPrice() != null && StringUtils.isNotEmpty(merchantMemberPromoPriceM.getProductPromoPrice().getPromoPriceTag())){
                return merchantMemberPromoPriceM.getProductPromoPrice().getPromoPriceTag();
            }
            //立减
            ProductPromoPriceM productPromoPriceM = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
            if (productPromoPriceM != null && StringUtils.isNotEmpty(productPromoPriceM.getPromoPriceTag())) {
                return productPromoPriceM.getPromoPriceTag();
            }
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, productM, e);
        }
        //团购
        return productM.getBasePriceTag();
    }
}
