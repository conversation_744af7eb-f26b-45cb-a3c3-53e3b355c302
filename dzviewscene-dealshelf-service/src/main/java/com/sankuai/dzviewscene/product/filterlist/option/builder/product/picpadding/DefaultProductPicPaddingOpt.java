package com.sankuai.dzviewscene.product.filterlist.option.builder.product.picpadding;

import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPicPaddingVP;
import com.sankuai.dzviewscene.product.filterlist.utils.ImageUtils;
import com.sankuai.dzviewscene.product.filterlist.vo.DzActivityTagVO;
import com.sankuai.dzviewscene.product.filterlist.vo.DzProductVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/5/1
 */
@VPointOption(name = "通用-填充默认图片",
        description = "",
        code = "DefaultProductPicPaddingOpt",
        isDefault = true)
public class DefaultProductPicPaddingOpt extends ProductPicPaddingVP<DefaultProductPicPaddingOpt.Config> {

    @Override
    public Void compute(ActivityCxt context, Param param, Config config) {
        if (StringUtils.isEmpty(param.getProductM().getPicUrl())) {
            return null;
        }
        DzProductVO paddingProduct = param.getDzProductVO();
        if (Objects.isNull(config.getDoublePicScale()) || Double.compare(config.getDoublePicScale(), 0) <= 0) {
            paddingProduct.setPicScale(config.getHeaderPicAspectRadio());
        } else {
            paddingProduct.setPicScale(config.getDoublePicScale());
        }

        String picUrl = PictureURLBuilders.toHttpsUrl(param.getProductM().getPicUrl(), config.getPicWidth(), config.getPicHeight(), PictureURLBuilders.ScaleType.Cut);
        if (config.isUseWebpSuffix()) {
            picUrl = ImageUtils.transferImg2Webp(picUrl);
        }
        paddingProduct.setPic(picUrl);

        //可选的塞入角标
        if (CollectionUtils.isNotEmpty(config.getFloatTags())) {
            paddingProduct.setActivityTags(config.getFloatTags());
        }
        return null;
    }

    @VPointCfg
    @Data
    public static class Config {

        private int headerPicAspectRadio = 1;

        private Double doublePicScale;

        private int picWidth = 300;

        private int picHeight = 300;

        // 活动角标后续改为ProductActivityTagsVP实现
        @Deprecated
        private List<DzActivityTagVO> floatTags;
        // 是否追加webp格式, 使用WebP格式能够优化图片大小25-50%， 清晰度基本没有影响 https://km.sankuai.com/page/28284841
        private boolean useWebpSuffix = false;
    }
}
