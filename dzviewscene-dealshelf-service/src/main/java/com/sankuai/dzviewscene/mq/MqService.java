package com.sankuai.dzviewscene.mq;

import com.alibaba.fastjson.JSON;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;

import com.sankuai.dzviewscene.mq.request.MqRequest;
import com.sankuai.dzviewscene.shelf.gateways.utils.CompressUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Properties;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MqService implements InitializingBean  {

    private static IProducerProcessor producer;

    public void sendMsgData(MqRequest mqRequest) {

        if (mqRequest == null) {
            return;
        }

        final String jsonString = JSON.toJSONString(mqRequest);

        final byte[] bytes = CompressUtils.gzipCompress(jsonString);

        try {
            if (bytes == null || bytes.length == 0) {
                return;
            }
            final ProducerResult producerResult = producer.sendMessage(bytes);
            if (producerResult.getProducerStatus().equals(ProducerStatus.SEND_FAILURE)) {
                log.error("sendMsgData failed,data:{}, res:{}", jsonString,JSON.toJSONString(producerResult));
            }
        } catch (Exception e) {
            log.error("sendMsgData exception",e);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        // 设置业务所在BG的namespace，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        // 设置生产者appkey，此参数必须配置且请按照demo正确配置
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "deal-style-service");

        // 创建topic对应的producer对象（注意每次build调用会产生一个新的实例），此处配置topic名称，请按照demo正确配置
        // 请注意：若调用MafkaClient.buildProduceFactory()创建实例抛出有异常，请重点关注并排查异常原因，不可频繁调用该方法给服务端带来压力。
        producer = MafkaClient.buildProduceFactory(properties, "DealRCFNativeSnapshot");
    }
}
