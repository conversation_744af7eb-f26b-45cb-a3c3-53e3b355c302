package com.sankuai.dzviewscene.nr.atom.cache;

public enum CacheMethodEnum {
    SHOP_ID_MT_2_DP("getDpByMtPoiIdL", "shop_id_mt2dp","美团门店ID转点评门店ID"),
    SHOP_ID_DP_2_MT("getMtByDpPoiIdL", "shop_id_dp2mt","点评门店ID转美团门店ID"),
    CITY_ID_MT_2_DP("getDpCityIdByMt", "city_id_mt2dp","美团城市ID转点评城市ID"),
    CITY_ID_DP_2_MT("getMtCityIdByDp", "city_id_dp2mt","点评城市ID转美团城市ID"),
    SHOP_INFO_DP("findShopsByDpShopId", "shop_model", "点评门店信息"),
    ;

    private String code;

    private String category;

    private String desc;

    CacheMethodEnum(String code, String category, String desc){
        this.code = code;
        this.desc = desc;
        this.category = category;
    }

    public String getCode() {
        return code;
    }

    public String getCategory() {
        return category;
    }
}
