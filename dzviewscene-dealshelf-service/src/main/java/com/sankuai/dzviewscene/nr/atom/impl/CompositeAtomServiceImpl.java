package com.sankuai.dzviewscene.nr.atom.impl;

import com.dianping.account.MeituanUserService;
import com.dianping.account.UserAccountService;
import com.dianping.account.dto.MeituanUserInfoDTO;
import com.dianping.account.dto.UserAccountDTO;
import com.dianping.baby.timeLimitTg.api.BabyTimeLimitTgSpikeService;
import com.dianping.baby.timeLimitTg.dto.BabyTimeLimeTgSpikeDTO;
import com.dianping.cat.Cat;
import com.dianping.deal.attribute.dto.DealGroupAttributeDTO;
import com.dianping.deal.attribute.dto.DealGroupAttributeResult;
import com.dianping.deal.attribute.service.DealGroupAttributeGetService;
import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.deal.publishcategory.DealGroupPublishCategoryQueryService;
import com.dianping.deal.shop.DealGroupShopService;
import com.dianping.deal.shop.DealShopQueryService;
import com.dianping.deal.shop.ShopOnlineDealGroupService;
import com.dianping.deal.shop.dto.DealGroupShop;
import com.dianping.deal.shop.dto.DealGroupShopSearchRequest;
import com.dianping.deal.shop.dto.ShopOnlineDealGroup;
import com.dianping.deal.shop.dto.ShopOnlineDealGroupRequest;
import com.dianping.deal.struct.query.api.DealDetailStructuredQueryService;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailDto;
import com.dianping.dztrade.order.business.api.service.OrderRuleRemoteService;
import com.dianping.dztrade.refund.api.bean.ShopRefundConfigDTO;
import com.dianping.dztrade.refund.api.request.ShopRefundConfigRequest;
import com.dianping.dztrade.refund.api.service.DzTradeShopRefundConfigRemoteService;
import com.dianping.general.unified.search.api.generalsearchv2.DealGroupSearchService;
import com.dianping.general.unified.search.api.generalsearchv2.ProductSearchServiceV2;
import com.dianping.general.unified.search.api.generalsearchv2.dto.DealGroupDto;
import com.dianping.general.unified.search.api.generalsearchv2.dto.ProductSearchIdDto;
import com.dianping.general.unified.search.api.generalsearchv2.request.DealGroupSearchRequest;
import com.dianping.general.unified.search.api.generalsearchv2.request.ProductSearchRequestV2;
import com.dianping.general.unified.search.api.generalsearchv2.response.DealGroupSearchResponse;
import com.dianping.general.unified.search.api.sku.ProductSearchRequest;
import com.dianping.general.unified.search.api.sku.ProductSearchResponse;
import com.dianping.general.unified.search.api.sku.ProductSearchService;
import com.dianping.gis.remote.dto.HotRegionStationDTO;
import com.dianping.gis.remote.dto.RegionInfoDTO;
import com.dianping.gis.remote.enums.RegionType;
import com.dianping.gis.remote.service.HotRegionStationService;
import com.dianping.gis.remote.service.RegionInfoService;
import com.dianping.gm.marketing.times.card.api.dto.CardResponse;
import com.dianping.gm.marketing.times.card.api.dto.TimesCardDetailExposureDTO;
import com.dianping.gmkt.activity.api.dto.BatchQueryShopActivityRequest;
import com.dianping.gmkt.activity.api.dto.BatchQueryShopActivityResponse;
import com.dianping.gmkt.activity.api.request.QuerySecKillDealByPoiRequest;
import com.dianping.gmkt.activity.api.response.QuerySecKillDealByPoiResponse;
import com.dianping.gmkt.activity.api.service.DealActivityQueryService;
import com.dianping.gmkt.activity.api.service.SecKillService;
import com.dianping.gmkt.event.api.distribution.dto.DistributorActivityDTO;
import com.dianping.gmkt.event.api.distribution.dto.DistributorActivityRequest;
import com.dianping.gmkt.event.api.distribution.service.DistributorActivityService;
import com.dianping.gmkt.event.api.model.CommonResponse;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeResponse;
import com.dianping.gmkt.event.api.promoqrcode.dto.staffcode.StaffCodeDTO;
import com.dianping.gmkt.event.api.promoqrcode.service.PromoQRCodeCService;
import com.dianping.haima.client.HaimaClient;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.home.service.api.ServiceResponse;
import com.dianping.home.shop.fusion.api.interest.ShopInterestService;
import com.dianping.joy.booking.api.generalpool.dto.GeneralPoolInfoDTO;
import com.dianping.joy.booking.api.generalpool.dto.PageInfo;
import com.dianping.joy.booking.api.generalpool.request.GeneralQueryPoolInfoRequest;
import com.dianping.joy.booking.api.generalpool.service.GeneralQueryPoolInfoService;
import com.dianping.joy.order.dto.soldcount.QuerySpuSalesReqDTO;
import com.dianping.joy.order.service.SoldCountService;
import com.dianping.joygeneral.api.sharerelation.MarketBonusService;
import com.dianping.joygeneral.api.sharerelation.ShopBlockService;
import com.dianping.joygeneral.api.sharerelation.dto.ResponseDTO;
import com.dianping.joygeneral.api.thirdpart.SelfHelpBilliardService;
import com.dianping.joygeneral.api.thirdpart.dto.QueryAutoOpenTableReqDTO;
import com.dianping.ktv.shop.api.protocol.ShopResponse;
import com.dianping.ktv.shop.coop.dto.CoopShopConfigDTO;
import com.dianping.ktv.shop.coop.request.CoopShopConfigQueryRequest;
import com.dianping.ktv.shop.coop.service.CoopShopConfigRemoteService;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Maps;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.service.RecommendService;
import com.dianping.pay.order.service.query.GetUnifiedOrderService;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.dianping.pay.promo.display.api.PromoDisplayService;
import com.dianping.pay.promo.display.api.dto.BatchQueryPromoDisplayRequest;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.poi.areacommon.AreaCommonService;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.product.shelf.common.dto.*;
import com.dianping.product.shelf.common.dto.activity.ActivityShelfToCDTO;
import com.dianping.product.shelf.common.request.*;
import com.dianping.product.shelf.common.request.activities.ProductQueryByShopIdToCRequest;
import com.dianping.product.shelf.query.api.ActivityShelfQueryService;
import com.dianping.product.shelf.query.api.GeneralShelfQueryService;
import com.dianping.reviewremote.remote.ReviewServiceV2;
import com.dianping.shopremote.remote.ShopService;
import com.dianping.shopremote.remote.ShopUuidService;
import com.dianping.shopremote.remote.dto.ShopDTO;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.dianping.tpfun.deal.transform.DealGroupRelationService;
import com.dianping.tpfun.deal.transform.entity.TransformTaskEntity;
import com.dianping.tpfun.product.api.sku.ProductService;
import com.dianping.tpfun.product.api.sku.abstracts.AbstractService;
import com.dianping.tpfun.product.api.sku.abstracts.dto.SpuTypeAbstractDTO;
import com.dianping.tpfun.product.api.sku.aggregate.ProductDetailService;
import com.dianping.tpfun.product.api.sku.aggregate.ProductStaticDetailBySpuService;
import com.dianping.tpfun.product.api.sku.aggregate.dto.SimpleShopDTO;
import com.dianping.tpfun.product.api.sku.aggregate.dto.StandardProductDTO;
import com.dianping.tpfun.product.api.sku.aggregate.dto.StandardProductPageDTO;
import com.dianping.tpfun.product.api.sku.aggregate.request.GetProductNearestShopReq;
import com.dianping.tpfun.product.api.sku.aggregate.request.GetStandardProductPriceRequest;
import com.dianping.tpfun.product.api.sku.model.Product;
import com.dianping.tpfun.product.api.sku.model.ProductItem;
import com.dianping.tpfun.product.api.sku.platform.resource.ResourceQueryService;
import com.dianping.tpfun.product.api.sku.platform.resource.dto.ResourceInfoDTO;
import com.dianping.tpfun.product.api.sku.platform.resource.request.BatchQueryResourceInfoRequest;
import com.dianping.tpfun.product.api.sku.request.GetProductItemByProductRequest;
import com.dianping.tpfun.product.api.sku.request.QueryShopProductRequest;
import com.dianping.ugc.proxyService.remote.dp.DpReviewUserInfoService;
import com.dianping.ugc.proxyService.remote.dto.AnonymousUserInfo;
import com.dianping.ugc.proxyService.remote.dto.UserRequestPara;
import com.dianping.userremote.base.dto.UserDTO;
import com.dianping.userremote.base.service.UserService;
import com.dianping.userremote.service.collection.FavorService;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.concurrent.threadpool.ExecutorServices;
import com.dianping.vc.sdk.data.Converters;
import com.dianping.wed.business.weddingshopbrief.api.WeddingShopBriefInfoService;
import com.dianping.wed.business.weddingshopbrief.dto.WeddingShopBriefInfoDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.maoyan.shplatform.content.api.mthomepage.MtHomePageService;
import com.maoyan.shplatform.content.api.mthomepage.base.ProjectContentDTO;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.mpproduct.general.trade.api.dto.SpuProductDTO;
import com.meituan.mpproduct.general.trade.api.request.CitySpuIdQueryRequest;
import com.meituan.mpproduct.general.trade.api.request.PoiSpuIdQueryRequest;
import com.meituan.mpproduct.general.trade.api.request.QuerySpuProductRequest;
import com.meituan.mpproduct.general.trade.api.service.SpuQueryService;
import com.meituan.mtrace.Tracer;
import com.meituan.service.deal.dealbasic.thrift.DealBasicInfo;
import com.meituan.service.deal.dealbasic.thrift.DealBasicInfoService;
import com.meituan.service.mobile.group.geo.bean.AreaInfo;
import com.meituan.service.mobile.group.geo.bean.SubwayLine;
import com.meituan.service.mobile.group.geo.bean.SubwayStation;
import com.meituan.service.mobile.group.geo.service.AreaService;
import com.meituan.service.mobile.group.geo.service.SubwayService;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.athena.biz.annotation.AtomService;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.athena.stability.faulttolerance.tracer.ExceptionTracer;
import com.sankuai.beautycontent.beautylaunchapi.model.dto.drama.DramaPageReviewDetailDto;
import com.sankuai.beautycontent.beautylaunchapi.model.dto.gw.AggOverallReviewModel;
import com.sankuai.beautycontent.beautylaunchapi.model.dto.gw.request.ContentBaseRequest;
import com.sankuai.beautycontent.beautylaunchapi.model.request.BeautyGoodsReviewRequest;
import com.sankuai.beautycontent.beautylaunchapi.model.service.gw.AggReviewService;
import com.sankuai.beautycontent.beautylaunchapi.model.service.reviewfromugc.DramaDimensionReviewService;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.dto.EduTechnicianVideoDTO;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.facade.EduTechnicianVideoQueryFacade;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.request.QueryEduTechnicianVideoForCRequest;
import com.sankuai.beautycontent.intention.api.IntentionService;
import com.sankuai.beautycontent.intention.dto.IntentionResultResponse;
import com.sankuai.beautycontent.intention.request.IntentionRequest;
import com.sankuai.carnation.distribution.promocode.privilege.service.QROfflineDealGroupApplicationService;
import com.sankuai.clr.content.process.thrift.api.LeadsQueryService;
import com.sankuai.clr.content.process.thrift.dto.leads.req.BatchQueryLeadsInfoReqDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.resp.BatchQueryLeadsInfoRespDTO;
import com.sankuai.coll.idl.CollectedUserEchoV2;
import com.sankuai.dzcard.fulfill.api.MemberCardQueryService;
import com.sankuai.dzcard.fulfill.api.dto.MemberCardDTO;
import com.sankuai.dzcard.fulfill.api.request.QueryMemberCardRequest;
import com.sankuai.dzcard.fulfill.api.response.MemberCardResponse;
import com.sankuai.dzcard.joycard.navigation.api.JoyCardProductShelfService;
import com.sankuai.dzcard.joycard.navigation.api.dto.LoadProductShelfJoyCardReqDTO;
import com.sankuai.dzcard.joycard.navigation.api.dto.ProductShelfJoyCardDTO;
import com.sankuai.dzcard.navigation.api.DzCardExposureService;
import com.sankuai.dzcard.navigation.api.dto.CardHoldStatusDTO;
import com.sankuai.dzcard.navigation.api.dto.FindCardHoldStatusReqDTO;
import com.sankuai.dzim.cliententry.ClientEntryService;
import com.sankuai.dzim.cliententry.dto.ClientEntryDTO;
import com.sankuai.dzim.cliententry.dto.ClientEntryReqDTO;
import com.sankuai.dztheme.deal.DealProductService;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dztheme.generalproduct.GeneralProductService;
import com.sankuai.dztheme.generalproduct.req.GeneralProductRequest;
import com.sankuai.dztheme.generalproduct.res.GeneralProductResult;
import com.sankuai.dztheme.shop.service.DzThemeShopService;
import com.sankuai.dztheme.shop.vo.ShopCardDTO;
import com.sankuai.dztheme.shop.vo.ShopThemePlanRequest;
import com.sankuai.dztheme.shop.vo.v1.ShopThemeResponse;
import com.sankuai.dztheme.spuproduct.SpuThemeQueryService;
import com.sankuai.dztheme.spuproduct.req.SpuRequest;
import com.sankuai.dztheme.spuproduct.res.SpuDTO;
import com.sankuai.dzviewscene.intention.service.api.IntentionRecognizeService;
import com.sankuai.dzviewscene.intention.service.req.IntentionRecognizeRequest;
import com.sankuai.dzviewscene.intention.service.res.IntentionRecognizeResult;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.req.OperatorShelfConfigFlashRequest;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.service.UnifiedShelfOperatorMService;
import com.sankuai.fbi.faas.wed.api.CrossCatRecommendService;
import com.sankuai.fbi.lifeevent.reserverpcapi.dto.NewReserveSubmissionPageWhiteShopCheckRespDTO;
import com.sankuai.fbi.lifeevent.reserverpcapi.request.NewReserveSubmissionPageWhiteShopCheckRequest;
import com.sankuai.fbi.lifeevent.reserverpcapi.request.StrictReserveShopSupportCheckRequest;
import com.sankuai.fbi.lifeevent.reserverpcapi.service.ReserveConfigQueryService;
import com.sankuai.fbi.merchantminiprogramregistrationrpc.api.MiniprogramLinkService;
import com.sankuai.fbi.merchantminiprogramregistrationrpc.dto.ApiResponse;
import com.sankuai.feitianplus.data.onedata.api.thrift.domain.KeyQueryParamSync;
import com.sankuai.feitianplus.data.onedata.api.thrift.domain.QueryResultSync;
import com.sankuai.feitianplus.data.onedata.api.thrift.service.QueryDataSyncService;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.IBNPLAccessThriftService;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.request.BNPLExposureRequest;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.response.BNPLExposureResponse;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.enums.ResponseCodeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.haima.admin.common.util.GsonUtils;
import com.sankuai.health.sc.api.thrift.DrugPoiFoodIfaceService;
import com.sankuai.health.sc.api.thrift.SpuTagProductParam;
import com.sankuai.health.sc.api.thrift.SpuTagResponse;
import com.sankuai.interest.core.thrift.remote.api.InterestCalculateService;
import com.sankuai.interest.core.thrift.remote.dto.InterestBatchCalculateResponse;
import com.sankuai.interest.core.thrift.remote.dto.InterestCalculateRequest;
import com.sankuai.interest.core.thrift.remote.dto.InterestCalculateResponse;
import com.sankuai.joynav.rb.spi.ListingRBService;
import com.sankuai.leads.count.thrift.api.NewLeadsCountService;
import com.sankuai.leads.count.thrift.dto.LeadsCountADTO;
import com.sankuai.leads.count.thrift.dto.LeadsCountRespDTO;
import com.sankuai.leads.process.thrift.api.leads.LeadsOrderQueryService;
import com.sankuai.leads.process.thrift.dto.leads.LeadsCountReqDTO;
import com.sankuai.map.open.platform.api.MapOpenApiService;
import com.sankuai.map.open.platform.api.transformcitytype.TransformCityTypeRequest;
import com.sankuai.map.open.platform.api.transformcitytype.TransformCityTypeResponse;
import com.sankuai.mdp.dzshoplist.rank.api.RankEntityInfoQuery;
import com.sankuai.mdp.dzshoplist.rank.api.request.EntityScenesRankInfoRequest;
import com.sankuai.mdp.dzshoplist.rank.api.response.dto.RankEntityScenesDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.ShopAutoVerifyQueryRequest;
import com.sankuai.medicalcosmetology.offline.code.api.service.PromoCodeOrderAutoVerifyService;
import com.sankuai.merchantcard.timescard.exposure.TimesCardQueryService;
import com.sankuai.merchantcard.timescard.exposure.req.QueryTimesCardByProductIdRequest;
import com.sankuai.merchantcard.timescard.exposure.req.QueryTimesCardByShopRequest;
import com.sankuai.mpcontent.feeds.thrift.api.HorizonVideoLiveService;
import com.sankuai.mpcontent.feeds.thrift.dto.request.QueryIsLivingReqDTO;
import com.sankuai.mpcontent.feeds.thrift.dto.response.QueryIsLivingRespDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.api.NibMemberProductQryThriftService;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.req.ListAllEffectMemberGrouponByTenantIdReqDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.resp.ListAllEffectMemberGrouponByTenantIdRespDTO;
import com.sankuai.mppack.api.client.request.CombineProductInfoRequest;
import com.sankuai.mppack.api.client.request.ProductInfoRequest;
import com.sankuai.mppack.api.client.response.CombineProductInfoResponse;
import com.sankuai.mppack.api.client.response.ProductInfoResponse;
import com.sankuai.mppack.api.client.service.CombinationProductQueryService;
import com.sankuai.mppack.api.client.service.ProductQueryService;
import com.sankuai.mppack.product.client.query.dto.ProductRelationDTO;
import com.sankuai.mppack.product.client.query.request.PackProductByIdRequest;
import com.sankuai.mpproduct.idservice.sdk.MtDpCommonConverter;
import com.sankuai.mpproduct.trade.api.model.ProductSkuDTO;
import com.sankuai.mpproduct.trade.api.request.ProductSkuBatchQueryRequest;
import com.sankuai.mpproduct.trade.api.request.ResourceBatchQueryRequest;
import com.sankuai.mpproduct.trade.api.response.ResourceBatchQueryResponse;
import com.sankuai.mpproduct.trade.api.service.ProductCacheQueryService;
import com.sankuai.mpproduct.trade.api.service.ResourceCacheQueryService;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.dto.DpPoiUuidRequest;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.service.DpPoiService;
import com.sankuai.sinai.data.api.service.MtPoiService;
import com.sankuai.swan.udqs.api.*;
import com.sankuai.technician.trade.api.product.dto.TechStandardShelfModule;
import com.sankuai.technician.trade.api.product.request.TechProductShelfRequest;
import com.sankuai.technician.trade.api.product.service.TechProductQueryService;
import com.sankuai.user.collection.client.CollTypeEnum;
import com.sankuai.user.collection.client.UserCollectionClient;
import com.sankuai.web.deal.deallistapi.thrift.dealinfo.DealRequest;
import com.sankuai.web.deal.deallistapi.thrift.dealinfo.DealResponse;
import com.sankuai.web.deal.deallistapi.thrift.dealinfo.DealService;
import com.sankuai.wpt.user.merge.query.thrift.message.BindRelationResp;
import com.sankuai.wpt.user.merge.query.thrift.message.FlattedBindRelation;
import com.sankuai.wpt.user.merge.query.thrift.message.UserMergeQueryService;
import com.sankuai.wpt.user.retrieve.thrift.message.RpcUserBatchRetrieveService;
import com.sankuai.zdc.tag.apply.api.PoiTagDisplayRPCService;
import com.sankuai.zdc.tag.apply.api.PoiTagGroupDisplayRPCService;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import com.sankuai.zdc.tag.apply.dto.FindSceneDisplayTagRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 场景通用AtomService
 */
@Slf4j
@AtomService(name = "CompositeAtomServiceImpl", description = "场景通用AtomService")
public class CompositeAtomServiceImpl implements CompositeAtomService {
    private static final int corePoolSize = 50;
    private static final int maximumPoolSize = 100;
    private static final int PRODUCT_BATCH_SIZE = 100;
    // 场景原子服务线程池
    public static final ExecutorService sceneAtomServiceExecutorService = ExecutorServices.forThreadPoolExecutor("sceneAtomServicePool", corePoolSize, maximumPoolSize);

    @RpcClient(url = "http://service.dianping.com/shopService/shopService_2.0.0")
    private ShopService shopService;

    @RpcClient(url = "http://service.dianping.com/shopService/shopUuidService_1.0.0")
    private ShopUuidService shopUuidService;

    @RpcClient(url = "com.dianping.poi.relation.service.api.PoiRelationService")
    private PoiRelationService poiRelationService;

    @RpcClient(url = "com.dianping.product.shelf.query.api.GeneralShelfQueryService")
    private GeneralShelfQueryService generalShelfQueryService;

    @RpcClient(url = "http://service.dianping.com/tpfunService/skuProductService_1.0.0")
    private ProductService productService;

    @RpcClient(url = "com.sankuai.dztheme.generalproduct.GeneralProductService")
    private GeneralProductService generalProductService;

    @RpcClient(url = "com.sankuai.dztheme.dealproduct.DealProductService")
    private DealProductService dealProductService;

    @RpcClient(url = "com.dianping.deal.shop.ShopOnlineDealGroupService")
    private ShopOnlineDealGroupService shopOnlineDealGroupService;

    @RpcClient(url = "http://service.dianping.com/dealIdMapperService/dealIdMapperService_1.0.0")
    private DealIdMapperService dealIdMapperService;

    @RpcClient(url = "com.sankuai.dzcard.joycard.navigation.api.JoyCardProductShelfService")
    private JoyCardProductShelfService joyCardProductShelfService;

    @RpcClient(url = "http://service.dianping.com/gmkt_activity_service/DealActivityQueryService_0.0.1")
    private DealActivityQueryService dealActivityQueryService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.web.deal.dealbasic", timeout = 500, testTimeout = 5000)
    private DealBasicInfoService.Iface dealBasicThriftClient;

    @RpcClient(url = "http://service.dianping.com/joy-order-service/SoldCountService_1.0.0")
    private SoldCountService soldCountService;

    @RpcClient(url = "com.dianping.dztrade.order.business.api.service.OrderRuleRemoteService")
    private OrderRuleRemoteService orderConfigService;

    @RpcClient(url = "com.dianping.dztrade.refund.api.service.DzTradeShopRefundConfigRemoteService")
    private DzTradeShopRefundConfigRemoteService refundConfigService;

    @RpcClient(url = "http://service.dianping.com/com.dianping.poi.areacommon.AreaCommonService_1.0.0")
    private AreaCommonService areaCommonService;

    @RpcClient(url = "com.dianping.home.shop.fusion.api.interest.ShopInterestService")
    private ShopInterestService shopInterestService;

    @RpcClient(url = "http://service.dianping.com/generalSearchService/ProductSearchService_1.0.0")
    private ProductSearchService productSearchService;

    @RpcClient(url = "com.sankuai.dzcard.navigation.api.DzCardExposureService")
    private DzCardExposureService dzCardExposureService;

    @RpcClient(url = "http://service.dianping.com/generalSearchServiceV2/ProductSearchServiceV2_1.0.0")
    private ProductSearchServiceV2 productSearchServiceV2;

    @RpcClient(url = "http://service.dianping.com/userService/favorService_1.0.0")
    private FavorService favorService;

    @RpcClient(url = "ReviewService.ReviewService")
    private ReviewServiceV2 reviewServiceV2;

    @RpcClient(url = "http://service.dianping.com/userBaseService/userService_2.0.0")
    private UserService userService;

    @RpcClient(url = "UGCProxyService.DpReviewUserInfoService")
    private DpReviewUserInfoService dpReviewUserInfoService;

    @RpcClient(url = "com.sankuai.dzim.cliententry.ClientEntryService")
    private ClientEntryService clientEntryService;

    @RpcClient(url = "http://service.dianping.com/tpfunService/productdetailservice_1.0.0")
    private ProductDetailService productDetailService;

    @RpcClient(url = "http://service.dianping.com/tpfunService/abstractService_1.0.0")
    private AbstractService abstractService;

    @RpcClient(url = "com.dianping.ktv.shop.coop.service.CoopShopConfigRemoteService")
    private CoopShopConfigRemoteService coopShopConfigRemoteService;

    @RpcClient(url = "com.dianping.joy.booking.api.generalpool.service.GeneralQueryPoolInfoService")
    private GeneralQueryPoolInfoService generalQueryPoolInfoService;

    @RpcClient(url = "http://service.dianping.com/gisService/regionInfoService_1.0.0")
    private RegionInfoService regionInfoService;

    @RpcClient(url = "http://service.dianping.com/gisService/hotRegionStationService_1.0.0")
    private HotRegionStationService hotRegionStationService;

    @RpcClient(remoteAppkey = "com.sankuai.apigw.map.facadecenter", timeout = 5000)
    private MapOpenApiService.Iface mapOpenApiService;

    @RpcClient(url = "http://service.dianping.com/payPromoDisplayService/PromoDisplayService_1.0.0")
    private PromoDisplayService promoDisplayService;


    @RpcClient(url = "http://service.dianping.com/martgeneral/recommend_1.0.0", timeout = 300)
    private RecommendService recommendService;

    @RpcClient(url = "com.maoyan.shplatform.content.api.mthomepage.MtHomePageService", timeout = 300)
    private MtHomePageService mtHomePageService;

    @RpcClient(url = "http://service.dianping.com/gmkt_activity_service/SecKillService_0.0.1")
    private SecKillService secKillService;

    @RpcClient(url = "com.sankuai.deal.gateways.rpc.DpNearbyHotDealListRBService")
    private ListingRBService dpNearbyHotDealListRBService;

    @Resource
    private HaimaClient haimaClient;

    @RpcClient(url = "http://service.dianping.com/tpfunService/productstaticdetailbyspuservice_1.0.0")
    private ProductStaticDetailBySpuService productStaticDetailBySpuService;

    @RpcClient(url = "com.sankuai.swan.udqs.api.SwanQueryService")
    private SwanQueryService swanQueryService;

    private final SwanUniteQueryService swanUniteQueryService = new SwanUniteQueryService();

    @RpcClient(url = "http://service.dianping.com/sku/transform/dealGroupRelationService_1.0.0")
    private DealGroupRelationService dealGroupRelationService;

    @RpcClient(remoteAppkey = "com.sankuai.mtusercenter.merge.query")
    private UserMergeQueryService.Iface userMergeQueryService;

    @RpcClient(url = "http://service.dianping.com/deal-publish-category-service/dealGroupPublishCategoryQueryService_1.0.0")
    private DealGroupPublishCategoryQueryService dealGroupPublishCategoryQueryService;

    @RpcClient(url = "http://service.dianping.com/tuangou/dealShopService/dealGroupShopService_1.0.0")
    private DealGroupShopService dealGroupShopService;

    @RpcClient(url = "com.dianping.product.shelf.query.api.ActivityShelfQueryService")
    private ActivityShelfQueryService activityShelfQueryService;

    @RpcClient(remoteAppkey = "com.sankuai.mpproduct.query")
    private ResourceCacheQueryService resourceCacheQueryService;

    @RpcClient(url = "com.sankuai.beautycontent.beautylaunchapi.gw.AggReviewService")
    private AggReviewService aggReviewService;

    @RpcClient(url = "com.sankuai.dztheme.shop.service.DzThemeShopService")
    private DzThemeShopService dzThemeShopService;

    @RpcClient(remoteAppkey = "com.sankuai.mtusercenter.info.batchretrieve")
    private RpcUserBatchRetrieveService.Iface rpcUserRetrieveService;

    @Resource
    private UserCollectionClient userCollectionClient;

    @RpcClient(url = "http://service.dianping.com/wedBusinessService/wedBusinessShopBriefInfoService_1.0.0")
    private WeddingShopBriefInfoService weddingShopBriefInfoService;

    @RpcClient(url = "http://service.dianping.com/baby/timelimit/babyTimeLimitTgSpikeService_1.0.0")
    private BabyTimeLimitTgSpikeService babyTimeLimitTgSpikeService;

    @Resource
    private AreaService areaService;

    @RpcClient(url = "com.sankuai.mdp.dzshoplist.rank.api.RankEntityInfoQuery")
    private RankEntityInfoQuery rankEntityInfoQuery;

    @RpcClient(url = "com.sankuai.joygeneral.share.relation.ShopBlockService")
    private ShopBlockService shopBlockService;

    @RpcClient(url = "com.sankuai.dztheme.spuproduct.SpuThemeQueryService")
    private SpuThemeQueryService spuThemeQueryService;

    @RpcClient(url = "com.sankuai.joygeneral.share.relation.MarketBonusService")
    private MarketBonusService marketBonusService;

    @RpcClient(remoteAppkey = "com.sankuai.lifeevent.faas.wed")
    private CrossCatRecommendService crossCatRecommendService;

    @Resource
    private SpuQueryService spuQueryService;

    @Resource
    private DrugPoiFoodIfaceService.Iface drugPoiFoodService;

    @Resource
    private SubwayService subwayService;

    @RpcClient(remoteAppkey = "com.sankuai.sinai.data.query")
    private DpPoiService dpPoiService;

    @RpcClient(url = "com.sankuai.beautycontent.beautylaunchapi.model.service.reviewfromugc.DramaDimensionReviewService")
    private DramaDimensionReviewService dramaDimensionReviewService;

    @RpcClient(url = "http://service.dianping.com/dealAttributeService/dealGroupAttributeGetService_1.0.0")
    private DealGroupAttributeGetService dealGroupAttributeGetService;

    @RpcClient(url = "com.dianping.general.unified.search.api.generalsearchv2.DealGroupSearchService")
    private DealGroupSearchService dealGroupSearchService;

    @RpcClient(url = "http://service.dianping.com/sku/platform/resource/resourceQueryService_1.0.0")
    private ResourceQueryService resourceQueryService;

    @RpcClient(url = "com.dianping.deal.struct.query.api.DealDetailStructuredQueryService")
    private DealDetailStructuredQueryService dealDetailStructuredQueryService;

    @RpcClient(remoteAppkey = "com.sankuai.mpmctmember.process")
    private NibMemberProductQryThriftService nibMemberQryThriftService;

    @RpcClient(url = "http://service.dianping.com/tuangou/dealShopService/dealShopQueryService_1.0.0")
    private DealShopQueryService dealShopQueryService;

    @RpcClient(remoteAppkey = "com.sankuai.beautycontent.function")
    private IntentionService intentionService;

    @RpcClient(url = "http://service.dianping.com/orderService/query/getUnifiedOrderService_1.0.0")
    private GetUnifiedOrderService getUnifiedOrderService;

    @RpcClient(url = "com.sankuai.carnation.distribution.promocode.privilege.service.QROfflineDealGroupApplicationService")
    private QROfflineDealGroupApplicationService qrOfflineDealGroupApplicationService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.pack.api", timeout = 1000, testTimeout = 2000)
    private ProductQueryService productQueryService;

    @RpcClient(url = "http://service.dianping.com/TimesCardQueryService/TimesCardQueryService_1.0.0")
    private TimesCardQueryService timesCardQueryService;

    @RpcClient(url = "http://service.dianping.com/ZDCTagApplyService/poiTagGroupDisplayRPCService_1.0.0")
    private PoiTagGroupDisplayRPCService poiTagGroupDisplayRPCService;

    @RpcClient(url = "http://service.dianping.com/ZDCTagApplyService/poiTagDisplayRPCService_1.0.0")
    private PoiTagDisplayRPCService poiTagDisplayRPCService;

    @RpcClient(remoteAppkey = "com.sankuai.mpcontent.feeds")
    private HorizonVideoLiveService horizonVideoLiveService;

    @RpcClient(url = "com.sankuai.beautycontent.creator.application.edu.technicianVideo.facade.EduTechnicianVideoQueryFacade")
    private EduTechnicianVideoQueryFacade eduTechnicianVideoQueryFacade;

    @RpcClient(remoteAppkey = "com.sankuai.mpproduct.query")
    private ProductCacheQueryService productCacheQueryService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.productuser.query.center", timeout = 500, testTimeout = 5000)
    private DealGroupQueryService dealGroupQueryService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.productuser.query.center", timeout = 500, testTimeout = 5000, async = true)
    private DealGroupQueryService dealGroupQueryServiceAsync;

    @MdpThriftClient(remoteAppKey = "com.sankuai.web.deal.deallistapi", timeout = 500, testTimeout = 5000)
    private DealService.Iface dealService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.sinai.data.query", timeout = 500, testTimeout = 5000)
    private MtPoiService mtPoiService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.leads.content.process", timeout = 3000)
    private LeadsQueryService leadsQueryService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.pack.product", remoteServerPort = 9000, timeout = 300)
    private com.sankuai.mppack.product.client.query.service.ProductQueryService packProductQueryService;

    @MdpThriftClient(
            remoteAppKey = "com.sankuai.pack.api",
            remoteServerPort = 9100,
            timeout = 1000,
            testTimeout = 5000
    )
    private CombinationProductQueryService combinationProductQueryService;

    @MdpThriftClient(
            remoteAppKey = "com.sankuai.leads.count",
            remoteServerPort = 9001,
            timeout = 1000
    )
    private NewLeadsCountService newLeadsCountService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.leads.process",remoteServerPort = 9001,timeout = 1500)
    private LeadsOrderQueryService leadsOrderQueryService;

    @MdpThriftClient(
            remoteAppKey = "com.sankuai.interest.core",
            remoteServerPort = 8710,
            timeout = 500
    )
    private InterestCalculateService interestCalculateService;

    @RpcClient(url = "com.sankuai.dzviewscene.intention.service.IntentionRecognizeService")
    private IntentionRecognizeService intentionRecognizeService;

    @Resource
    @Qualifier("memberCardQueryService")
    private MemberCardQueryService memberCardQueryService;

    @Resource
    @Qualifier("userAccountService")
    private UserAccountService userAccountService;

    @Resource
    @Qualifier("meituanUserServiceNew")
    private MeituanUserService meituanUserServiceNew;

    @RpcClient(url = "com.sankuai.technician.trade.api.product.service.TechProductQueryService")
    private TechProductQueryService techProductQueryService;

    @RpcClient(url = "http://service.dianping.com/gmkt_event_manage_service/promoQRCodeCService_0.0.1")
    private PromoQRCodeCService promoQRCodeCService;
    @RpcClient(url = "http://service.dianping.com/gmkt_event_manage_service/distributorActivityService_0.0.1")
    private DistributorActivityService distributorActivityService;
    @RpcClient(url = "com.dianping.joygeneral.api.thirdpart.SelfHelpBilliardService", timeout = 1000)
    private SelfHelpBilliardService selfHelpBilliardService;

    @RpcClient(remoteAppkey = "com.sankuai.lifeevent.faas.life", timeout = 1000)
    private ReserveConfigQueryService reserveConfigQueryService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.feitianplus.data.onedata", remoteServerPort = 9001, timeout = 1000)
    private QueryDataSyncService.Iface queryDataSyncService;

    @RpcClient(url = "com.sankuai.dzviewscene.unifiedshelf.operator.api.service.UnifiedShelfOperatorMService")
    private UnifiedShelfOperatorMService unifiedShelfOperatorMService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.lifeevent.faas.wed", timeout = 1000)
    private MiniprogramLinkService miniprogramLinkService;
    @MdpPigeonClient(timeout = 1000, url = "com.sankuai.medicalcosmetology.offline.code.api.service.PromoCodeOrderAutoVerifyService")
    private PromoCodeOrderAutoVerifyService promoCodeOrderAutoVerifyService;

    @Resource
    private IBNPLAccessThriftService bnplAccessThriftService;

    @Override
    public CompletableFuture<List<ResourceInfoDTO>> batchQueryResourceInfo(BatchQueryResourceInfoRequest batchQueryResourceInfoRequest) {
        return AthenaInf.getRpcCompletableFuture(resourceQueryService.batchQueryResourceInfo(batchQueryResourceInfoRequest))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "batchQueryResourceInfo")
                            .message(String.format("batchQueryResourceInfo error, batchQueryResourceInfoRequest: %s", JsonCodec.encode(batchQueryResourceInfoRequest))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchQueryResourceInfo"), e);
                    return null;
                }).thenApply(response -> {
                    if (response == null || !response.isSuccess()) {
                        return new ArrayList();
                    }
                    return response.getResult();
                });
    }

    @Override
    public CompletableFuture<Map<Integer, DealGroupAttributeDTO>> batchGetDealAttribute(List<Integer> dealGroupIds, List<String> attributes) {
        return AthenaInf.getRpcCompletableFuture(dealGroupAttributeGetService.getDealAttribute(dealGroupIds, attributes))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getDealAttribute")
                            .message(String.format("getDealAttribute error, req: %s", JsonCodec.encode(dealGroupIds))));
                    ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "getDealAttribute"), e);
                    return null;
                })
                .thenApply(response -> {
                    if (response == null || response.getCode() != DealGroupAttributeResult.SUCCESS || CollectionUtils.isEmpty(response.getContent())) {
                        return Maps.newHashMap();
                    }
                    return Converters.newMapByPropertyNameConverter("dealGroupId", response.getContent());
                });
    }

    @Override
    public CompletableFuture<List<ShopM>> multiShopMListNew(ShopThemePlanRequest request) {
        return multiGetShopThemeDtoNew(request).thenApply(response -> buildShopMListNew(response.getShopCardDTOMap()));
    }

    private CompletableFuture<ShopThemeResponse> multiGetShopThemeDtoNew(ShopThemePlanRequest request) {
        return AthenaInf.getRpcCompletableFuture(dzThemeShopService.queryShopTheme(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "planForMap")
                            .message(String.format("商户主题异常, request : %s", JsonCodec.encodeWithUTF8(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "multiGetShopThemeDto"), e);
                    return new ShopThemeResponse();
                });
    }

    private List<ShopM> buildShopMListNew(Map<Long, ShopCardDTO> shopIdThemeMap) {
        if (MapUtils.isEmpty(shopIdThemeMap)) {
            return Lists.newArrayList();
        }
        return shopIdThemeMap.values().stream().map(shop -> convertShopCardDTO2ShopM(shop)).collect(Collectors.toList());
    }

    private List<ShopM> buildShopMList(Map<Integer, ShopCardDTO> shopIdThemeMap) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.buildShopMList(java.util.Map)");
        if (MapUtils.isEmpty(shopIdThemeMap)) {
            return Lists.newArrayList();
        }
        return shopIdThemeMap.values().stream().map(shop -> convertShopCardDTO2ShopM(shop)).collect(Collectors.toList());
    }

    private ShopM convertShopCardDTO2ShopM(ShopCardDTO shopCardDTO) {
        if (shopCardDTO == null) {
            return null;
        }
        ShopM shopM = new ShopM();
        shopM.setShopId(shopCardDTO.getShopid());
        shopM.setLongShopId(shopCardDTO.getLongShopid());
        shopM.setShopName(shopCardDTO.getShopName());
        shopM.setDistance(shopCardDTO.getDistanceStr());
        shopM.setDistanceNum(Double.MAX_VALUE);
        if (shopCardDTO.getDistanceValue() != null) {
            shopM.setDistanceNum(shopCardDTO.getDistanceValue());
        }
        shopM.setStarStr(shopCardDTO.getStarVal());
        shopM.setScoreTag(shopCardDTO.getStarStr());
        if (shopCardDTO.getReview() != null) {
            shopM.setReviewCount(shopCardDTO.getReview().getReviewCount());
        }
        if (shopCardDTO.getCity() != null) {
            shopM.setCityId(shopCardDTO.getCity().getId());
        }
        shopM.setDetailUrl(shopCardDTO.getShopUrl());
        shopM.setMainRegionName(shopCardDTO.getBareaName());
        return shopM;
    }

    @Override
    public CompletableFuture<Response<ActivityShelfToCDTO>> getActivityShelfResponseByShopId(ProductQueryByShopIdToCRequest request) {
        return AthenaInf.getRpcCompletableFuture(activityShelfQueryService.queryProductListByShopIdToC(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getActivityShelfResponseByShopId")
                            .message(String.format("getActivityShelfResponseByShopId error, req: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "getActivityShelfResponseByShopId"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<DealGroupShop> getDealNearestShopResult(DealGroupShopSearchRequest request) {
        if (request == null) {
            return CompletableFuture.completedFuture(null);
        }
        return AthenaInf.getRpcCompletableFuture(dealGroupShopService.getDealGroupNearestShop(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getDealNearestShopResult")
                            .message(String.format("getDealGroupShops error, req: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "getDealNearestShopResult"), e);
                    return null;
                }).thenApply(map -> {
                    if (MapUtils.isEmpty(map)) {
                        return null;
                    }
                    return CollectUtils.firstValue(map);
                });
    }

    @Override
    public CompletableFuture<Map<Integer, Integer>> batchGetDealIdCategoryIdMap(List<Integer> dealIds) {
        if (CollectionUtils.isEmpty(dealIds)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        Set<Integer> dealIdSet = Sets.newHashSet(dealIds);
        return AthenaInf.getRpcCompletableFuture(dealGroupPublishCategoryQueryService.batchGetPublishCategory(dealIdSet))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "batchGetDealIdCategoryIdMap")
                            .message(String.format("batchGetDealIdCategoryIdMap error, req: %s", JsonCodec.encode(dealIds))));
                    ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "batchGetDealIdCategoryIdMap"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<HaimaResponse> getHaiMaResponse(HaimaRequest haimaRequest) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return haimaClient.query(haimaRequest);
            } catch (Exception e) {
                ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "getHaiMaResponse"), e);
                return null;
            }
        }, sceneAtomServiceExecutorService).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "getHaiMaResponse")
                    .message(String.format("getHaiMaResponse error, req: %s", JsonCodec.encode(haimaRequest))));
            ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "getHaiMaResponse"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<ServiceResponse<Boolean>> getIsCooperativeResponse(int shopId) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.getIsCooperativeResponse(int)");
        return AthenaInf.getRpcCompletableFuture(shopInterestService.isCooperative(shopId))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getIsCooperativeResponse")
                            .message(String.format("getIsCooperativeResponse error, req: %d", shopId)));
                    ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "getIsCooperativeResponse"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<ServiceResponse<Boolean>> getIsCooperativeResponseL(Long shopId) {
        return AthenaInf.getRpcCompletableFuture(shopInterestService.isCooperative(shopId))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getIsCooperativeResponseL")
                            .message(String.format("getIsCooperativeResponseL error, req: %d", shopId)));
                    ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "getIsCooperativeResponseL"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<ShopDTO> loadShop(int shopId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.loadShop(int)");
        return AthenaInf.getRpcCompletableFuture(shopService.loadShop(shopId)).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "loadShop")
                    .message(String.format("loadShop error, shopid: %d", shopId)));
            ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "loadShop"), e);
            return null;
        });
    }

    /**
     * 通过ShopUuid查询门店信息
     *
     * @param shopUuid
     * @return
     */
    @Override
    public CompletableFuture<ShopDTO> loadShop(String shopUuid) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.loadShop(java.lang.String)");
        return AthenaInf.getRpcCompletableFuture(shopUuidService.loadShopByUuid(shopUuid))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "loadShop")
                            .message(String.format("loadShop error, req: %s", shopUuid)));
                    ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "loadShopByUuid"), e);
                    return null;
                });
    }

    /**
     * 通过ShopUuid查询门店信息
     *
     * @return
     */
    @Override
    public CompletableFuture<List<DpPoiDTO>> findShopsByUuids(DpPoiUuidRequest dpPoiUuidRequest) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.findShopsByUuids(com.sankuai.sinai.data.api.dto.DpPoiUuidRequest)");
        try {
            return AthenaInf.getRpcCompletableFuture(dpPoiService.findShopsByUuids(dpPoiUuidRequest))
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("scene", "compositeAtomService")
                                .putTag("method", "findShopsByUuids")
                                .message(String.format("findShopsByUuids error, request: %s", JsonCodec.encode(dpPoiUuidRequest))));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "findShopsByUuids"), e);
                        return Lists.newArrayList();
                    });
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "findShopsByUuids"), e);
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
    }


    @Override
    public CompletableFuture<List<Integer>> batchGetDpByMtId(int mtShopId) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.batchGetDpByMtId(int)");
        if (mtShopId <= 0) {
            return CompletableFuture.completedFuture(null);
        }
        CompletableFuture<List<Integer>> future = null;
        try {
            future = AthenaInf.getRpcCompletableFuture(poiRelationService.queryDpByMtId(mtShopId));
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "batchGetDpByMtId"), e);
            return CompletableFuture.completedFuture(null);
        }
        return future.exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "batchGetDpByMtId")
                    .message(String.format("batchGetDpByMtId error, mtShopid: %d", mtShopId)));
            ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "batchGetDpByMtId"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<List<Long>> batchGetDpByMtIdL(Long mtShopId) {
        if (mtShopId <= 0) {
            return CompletableFuture.completedFuture(null);
        }
        CompletableFuture<List<Long>> future = null;
        try {
            future = AthenaInf.getRpcCompletableFuture(poiRelationService.queryDpByMtIdL(mtShopId));
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "batchGetDpByMtIdL"), e);
            return CompletableFuture.completedFuture(null);
        }
        return future.exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "batchGetDpByMtIdL")
                    .message(String.format("batchGetDpByMtIdL error, mtShopid: %d", mtShopId)));
            ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "batchGetDpByMtIdL"), e);
            return null;
        });
    }

    /**
     * 根据美团ID获取对应的点评ID;如果是多个，第一个是主门店ID
     *
     * @param dpShopId
     * @return
     */
    @Override
    public CompletableFuture<List<Integer>> batchGetMtShopIdByDp(int dpShopId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.batchGetMtShopIdByDp(int)");
        try {
            return AthenaInf.getRpcCompletableFuture(poiRelationService.queryMtByDpId(dpShopId))
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("scene", "compositeAtomService")
                                .putTag("method", "batchGetMtShopIdByDp")
                                .message(String.format("batchGetMtShopIdByDp error, req: %d", dpShopId)));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetMtShopIdByDp"), e);
                        return null;
                    });
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetMtShopIdByDp"), e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<List<Long>> batchGetMtShopIdByDpL(Long dpShopId) {
        try {
            return AthenaInf.getRpcCompletableFuture(poiRelationService.queryMtByDpIdL(dpShopId))
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("scene", "compositeAtomService")
                                .putTag("method", "batchGetMtShopIdByDpL")
                                .message(String.format("batchGetMtShopIdByDpL error, req: %d", dpShopId)));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetMtShopIdByDpL"), e);
                        return null;
                    });
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetMtShopIdByDpL"), e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<Long> getMtByDpPoiIdL(long dpPoiId) {
        try {
            return AthenaInf.getRpcCompletableFuture(poiRelationService.queryPoiPairByDpIdL(dpPoiId))
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("scene", "compositeAtomService")
                                .putTag("method", "getMtByDpPoiIdL")
                                .message(String.format("getMtByDpPoiIdL error, request: %s", JsonCodec.encode(dpPoiId))));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getMtByDpPoiIdL"), e);
                        return null;
                    })
                    .thenApply(result -> {
                        if (result == null) {
                            return 0L;
                        }
                        return Optional.ofNullable(result.getMtId()).orElse(0L);
                    });
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getMtByDpPoiIdL"), e);
            return CompletableFuture.completedFuture(0L);
        }
    }

    @Override
    public CompletableFuture<Long> getDpByMtPoiIdL(long mtPoiId) {
        try {
            return AthenaInf.getRpcCompletableFuture(poiRelationService.queryPoiPairByMtIdL(mtPoiId))
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("scene", "compositeAtomService")
                                .putTag("method", "getDpByMtPoiIdL")
                                .message(String.format("getDpByMtPoiIdL error, request: %s", JsonCodec.encode(mtPoiId))));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getDpByMtPoiIdL"), e);
                        return null;
                    })
                    .thenApply(result -> {
                        if (result == null) {
                            return 0L;
                        }
                        return Optional.ofNullable(result.getDpId()).orElse(0L);
                    });
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getDpByMtPoiIdL"), e);
            return CompletableFuture.completedFuture(0L);
        }
    }

    @Override
    public CompletableFuture<List<DpPoiDTO>> findShopsByDpShopIds(DpPoiRequest dpPoiRequest) {
        try {
            return AthenaInf.getRpcCompletableFuture(dpPoiService.findShopsByShopIds(dpPoiRequest))
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("scene", "compositeAtomService")
                                .putTag("method", "findShopsByDpShopIds")
                                .message(String.format("findShopsByDpShopIds error, request: %s", JsonCodec.encode(dpPoiRequest))));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "findShopsByDpShopIds"), e);
                        return Lists.newArrayList();
                    });
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "findShopsByDpShopIds"), e);
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
    }

    @Override
    public CompletableFuture<Response<ShelfDTO>> multiGetShelfNav(ShelfRequest navRequest) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.multiGetShelfNav(com.dianping.product.shelf.common.request.ShelfRequest)");
        if (navRequest != null) {
            navRequest.setUseCache(true);
        }
        return AthenaInf.getRpcCompletableFuture(generalShelfQueryService.queryShelfNav(navRequest)).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "multiGetShelfNav")
                    .message(String.format("multiGetShelfNav error, request: %s", JsonCodec.encode(navRequest))));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "multiGetShelfNav"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<Response<ShelfNavTabProductList>> multiGetProductList(ShelfNavTabProductRequest request) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.multiGetProductList(com.dianping.product.shelf.common.request.ShelfNavTabProductRequest)");
        if (request != null) {
            request.setUseCache(true);
        }
        return AthenaInf.getRpcCompletableFuture(generalShelfQueryService.queryShelfNavTabProductList(request)).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "multiGetProductList")
                    .message(String.format("multiGetProductList error, request: %s", JsonCodec.encode(request))));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "multiGetProductList"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<Map<Integer, List<Product>>> mGetBaseProductsByShop(QueryShopProductRequest request) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.mGetBaseProductsByShop(com.dianping.tpfun.product.api.sku.request.QueryShopProductRequest)");
        return AthenaInf.getRpcCompletableFuture(productService.mGetBaseProductsByShop(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "mGetBaseProductsByShop")
                            .message(String.format("根据门店召回商品信息异常, request : %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "mGetBaseProductsByShop"), e);
                    return Maps.newHashMap();
                });
    }

    @Override
    public CompletableFuture<Map<Long, List<Product>>> mGetBaseProductsByLongShop(QueryShopProductRequest request) {
        // 这个被废弃掉了
        return AthenaInf.getRpcCompletableFuture(productService.mGetBaseProductsByLongShop(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "mGetBaseProductsByLongShop")
                            .message(String.format("根据门店召回商品信息异常, request : %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "mGetBaseProductsByLongShop"), e);
                    return Maps.newHashMap();
                });
    }

    @Override
    public CompletableFuture<Map<Integer, List<ProductItem>>> mGetProductItemsByProducts(GetProductItemByProductRequest request) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.mGetProductItemsByProducts(com.dianping.tpfun.product.api.sku.request.GetProductItemByProductRequest)");
        return AthenaInf.getRpcCompletableFuture(productService.mGetItemsByProductV2(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "mGetProductItemsByProducts")
                            .message(String.format("根据商品查询sku信息异常, request : %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "mGetProductItemsByProducts"), e);
                    return Maps.newHashMap();
                });
    }

    @Override
    public CompletableFuture<GeneralProductResult> queryGeneralProductTheme(GeneralProductRequest generalProductRequest) {
        return AthenaInf.getRpcCompletableFuture(generalProductService.query(generalProductRequest))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "queryGeneralProductTheme")
                            .message(String.format("查询泛商品主题异常，request: %s", JsonCodec.encode(generalProductRequest))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryGeneralProductTheme"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Map<Integer, List<Integer>>> batchGetDpByMtIds(List<Integer> mtShopIds) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.batchGetDpByMtIds(java.util.List)");
        try {
            return AthenaInf.getRpcCompletableFuture(poiRelationService.queryDpByMtIds(mtShopIds)).exceptionally(e -> {
                log.error(XMDLogFormat.build()
                        .putTag("scene", "compositeAtomService")
                        .putTag("method", "batchGetDpByMtIds")
                        .message(String.format("batchGetDpByMtIds error，request: %s", JsonCodec.encode(mtShopIds))));
                ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "batchGetDpByMtIds"), e);
                return null;
            });
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetDpByMtIds"), e);
        }
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<Map<Long, List<Long>>> batchGetDpByMtIdsL(List<Long> mtShopIds) {
        try {
            return AthenaInf.getRpcCompletableFuture(poiRelationService.queryDpByMtIdsL(mtShopIds)).exceptionally(e -> {
                log.error(XMDLogFormat.build()
                        .putTag("scene", "compositeAtomService")
                        .putTag("method", "batchGetDpByMtIdsL")
                        .message(String.format("batchGetDpByMtIdsL error，request: %s", JsonCodec.encode(mtShopIds))));
                ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "batchGetDpByMtIdsL"), e);
                return null;
            });
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetDpByMtIdsL"), e);
        }
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<DealProductResult> queryDealProductTheme(DealProductRequest dealProductRequest) {
        return AthenaInf.getRpcCompletableFuture(dealProductService.query(dealProductRequest)).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "queryDealProductTheme")
                    .message(String.format("dealProductService.query error，request: %s", JsonCodec.encode(dealProductRequest))));
            ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "queryDealProductTheme"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<Map<Integer, ShopOnlineDealGroup>> batchGetShopOnlineDealGroups(ShopOnlineDealGroupRequest shopOnlineDealGroupRequest) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.batchGetShopOnlineDealGroups(com.dianping.deal.shop.dto.ShopOnlineDealGroupRequest)");
        return AthenaInf.getRpcCompletableFuture(shopOnlineDealGroupService.queryShopOnlineDealGroups(shopOnlineDealGroupRequest)).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "batchGetShopOnlineDealGroups")
                    .message(String.format("queryShopOnlineDealGroups error，request: %s", JsonCodec.encode(shopOnlineDealGroupRequest))));
            ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "batchGetShopOnlineDealGroups"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<Map<Long, ShopOnlineDealGroup>> batchGetLongShopOnlineDealGroups(ShopOnlineDealGroupRequest shopOnlineDealGroupRequest) {
        return AthenaInf.getRpcCompletableFuture(shopOnlineDealGroupService.queryLongShopOnlineDealGroups(shopOnlineDealGroupRequest)).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "batchGetLongShopOnlineDealGroups")
                    .message(String.format("queryLongShopOnlineDealGroups error，request: %s", JsonCodec.encode(shopOnlineDealGroupRequest))));
            ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "batchGetLongShopOnlineDealGroups"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<Response<List<ShelfShopRecProduct>>> multiGetShopRecommendProducts(ShelfShopRecProductRequest request) {
        return AthenaInf.getRpcCompletableFuture(generalShelfQueryService.queryShopRecommendProduct(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "multiGetShopRecommendProducts")
                            .message(String.format("multiGetShopRecommendProducts error, req: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "multiGetShopRecommendProducts"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<List<IdMapper>> batchGetDealIdByDpId(List<Integer> dpDealIds) {
        return AthenaInf.getRpcCompletableFuture(dealIdMapperService.queryByDpDealGroupIds(dpDealIds))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "batchGetDealIdByDpId")
                            .message(String.format("batchGetDealIdByDpId error, req: %s", JsonCodec.encode(dpDealIds))));
                    ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "queryByDpDealGroupIds"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Map<Long, Long>> getMtDealIdByDp(List<Long> dpDealIds){
        if(CollectionUtils.isEmpty(dpDealIds)){
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        return CompletableFuture.supplyAsync(() -> {
            try {
                return MtDpCommonConverter.convertDpGroupIdsToMtGroupIds(dpDealIds);
            } catch (TException e) {
                log.error(XMDLogFormat.build()
                        .putTag("scene", "getMtDealIdByDp")
                        .putTag("method", "getMtDealIdByDp")
                        .message(String.format("getMtDealIdByDp error, dpDealIds: %s", JsonCodec.encode(dpDealIds))));
                ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getMtDealIdByDp"), e);
            }
            return null;
        },sceneAtomServiceExecutorService).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "getMtDealIdByDp")
                    .message(String.format("getMtDealIdByDp error, dpDealIds: %s", JsonCodec.encode(dpDealIds))));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getMtDealIdByDp"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<List<IdMapper>> batchGetDealIdByMtId(List<Integer> mtDealIds) {
        return AthenaInf.getRpcCompletableFuture(dealIdMapperService.queryByMtDealGroupIds(mtDealIds))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "batchGetDealIdByMtId")
                            .message(String.format("batchGetDealIdByMtId error, req: %s", JsonCodec.encode(mtDealIds))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetDealIdByMtId"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<ProductShelfJoyCardDTO> getCardModuleForProductShelf(LoadProductShelfJoyCardReqDTO request) {
        return AthenaInf.getRpcCompletableFuture(joyCardProductShelfService.loadCardModuleForProductShelf(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getCardModuleForProductShelf")
                            .message(String.format("getCardModuleForProductShelf error, req: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getCardModuleForProductShelf"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<CardHoldStatusDTO> getCardHoldStatus(FindCardHoldStatusReqDTO request) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.getCardHoldStatus(com.sankuai.dzcard.navigation.api.dto.FindCardHoldStatusReqDTO)");
        return AthenaInf.getRpcCompletableFuture(dzCardExposureService.findShopAndUserCardHoldStatus(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getCardHoldStatus")
                            .message(String.format("getCardHoldStatus error, req: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getCardHoldStatus"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<BatchQueryShopActivityResponse> getShopActivity(BatchQueryShopActivityRequest request) {
        return AthenaInf.getRpcCompletableFuture(dealActivityQueryService.batchQueryShopActivity(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getShopActivity")
                            .message(String.format("getShopActivity error, req: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getShopActivity"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Map<Integer, List<Long>>> findShopIdsByProductIdsPoiMigrate(List<Integer> productIds) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.findShopIdsByProductIdsPoiMigrate(java.util.List)");
        if (CollectionUtils.isEmpty(productIds)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        return AthenaInf.getRpcCompletableFuture(productService.mGetLongShopIds(productIds))
                .exceptionally(e -> {
                    log.info(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "findShopIdsByProductIdsPoiMigrate")
                            .message("查询商品对应门店失败，req: " + JsonCodec.encode(productIds)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "findShopIdsByProductIdsPoiMigrate"), e);
                    return Maps.newHashMap();
                }).thenApply(result -> {
                    if (MapUtils.isEmpty(result)) {
                        return Maps.newHashMap();
                    }
                    return result.entrySet().stream().filter(e -> Objects.nonNull(e.getValue()))
                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (a, b) -> a));
                });
    }

    @Override
    public CompletableFuture<ShopRefundConfigDTO> loadShopRefundConfig(ShopRefundConfigRequest shopRefundConfigRequest) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.loadShopRefundConfig(com.dianping.dztrade.refund.api.request.ShopRefundConfigRequest)");
        return AthenaInf.getRpcCompletableFuture(refundConfigService.loadShopRefundConfig(shopRefundConfigRequest))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "loadShopRefundConfig")
                            .message(String.format("loadShopRefundConfig error, req: %s", JsonCodec.encode(shopRefundConfigRequest))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "loadShopRefundConfig"), e);
                    return null;
                })
                .thenApply(resp -> {
                    if (resp == null || !resp.isSuccess()) {
                        return null;
                    }
                    return resp.getResult();
                });
    }

    @Override
    public CompletableFuture<Integer> loadDpShopId(int shopId, int platform) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.loadDpShopId(int,int)");
        if (platform == VCPlatformEnum.DP.getType()) {
            return CompletableFuture.completedFuture(shopId);
        }
        try {
            return AthenaInf.getRpcCompletableFuture(poiRelationService.queryPoiPairByMtId(shopId))
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("scene", "compositeAtomService")
                                .putTag("method", "loadDpShopId")
                                .message(String.format("loadDpShopId error, shopid: %d, platform: %d", shopId, platform)));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "loadDpShopId"), e);
                        return null;
                    })
                    .thenApply(pairDto -> {
                        if (pairDto == null || pairDto.getDpId() == null) {
                            return 0;
                        }
                        return pairDto.getDpId();
                    });
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "loadDpShopId"), e);
            return CompletableFuture.completedFuture(0);
        }
    }

    @Override
    public CompletableFuture<Long> loadDpShopIdPoiMigrate(long shopId, int platform) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.loadDpShopIdPoiMigrate(long,int)");
        if (platform == VCPlatformEnum.DP.getType()) {
            return CompletableFuture.completedFuture(shopId);
        }
        try {
            return AthenaInf.getRpcCompletableFuture(poiRelationService.queryPoiPairByMtIdL(shopId))
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("scene", "compositeAtomService")
                                .putTag("method", "loadDpShopIdPoiMigrate")
                                .message(String.format("loadDpShopIdPoiMigrate error, shopid: %d, platform: %d", shopId, platform)));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "loadDpShopIdPoiMigrate"), e);
                        return null;
                    })
                    .thenApply(pairDto -> {
                        if (pairDto == null || pairDto.getDpId() == null) {
                            return 0L;
                        }
                        return pairDto.getDpId();
                    });
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "loadDpShopIdPoiMigrate"), e);
            return CompletableFuture.completedFuture(0L);
        }
    }

    @Override
    public CompletableFuture<Long> loadMtShopId(long shopId, int platform) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.loadMtShopId(long,int)");
        if (platform == VCPlatformEnum.MT.getType()) {
            return CompletableFuture.completedFuture(shopId);
        }
        try {
            return AthenaInf.getRpcCompletableFuture(poiRelationService.queryPoiPairByDpId(((int) (shopId))))
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("method", "loadMtShopId")
                                .putTag("scene", "compositeAtomService")
                                .message(String.format("loadMtShopId error, mtshopid : %s", JsonCodec.encode(shopId))));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "loadMtShopId"), e);
                        return null;
                    })
                    .thenApply(pairDto -> {
                        if (pairDto == null || pairDto.getMtId() == null) {
                            return 0L;
                        }
                        return pairDto.getMtId().longValue();
                    });
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "loadMtShopId"), e);
            return CompletableFuture.completedFuture(0L);
        }
    }

    @Override
    public CompletableFuture<Long> loadMtShopIdPoiMigrate(long shopId, int platform) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.loadMtShopIdPoiMigrate(long,int)");
        if (platform == VCPlatformEnum.MT.getType()) {
            return CompletableFuture.completedFuture(shopId);
        }
        try {
            return AthenaInf.getRpcCompletableFuture(poiRelationService.queryPoiPairByDpIdL(shopId))
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("method", "loadMtShopIdPoiMigrate")
                                .putTag("scene", "compositeAtomService")
                                .message(String.format("loadMtShopIdPoiMigrate error, mtshopid : %s", JsonCodec.encode(shopId))));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "loadMtShopIdPoiMigrate"), e);
                        return null;
                    })
                    .thenApply(pairDto -> {
                        if (pairDto == null || pairDto.getMtId() == null) {
                            return 0L;
                        }
                        return pairDto.getMtId().longValue();
                    });
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "loadMtShopIdPoiMigrate"), e);
            return CompletableFuture.completedFuture(0L);
        }
    }

    @Override
    public CompletableFuture<Integer> getDpCityIdByMt(Integer mtCityId) {
        return AthenaInf.getRpcCompletableFuture(areaCommonService.getDpCityByMtCity(Optional.ofNullable(mtCityId).orElse(0)))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getDpCityIdByMt")
                            .message(String.format("美团转dp城市id异常,mtCityId: %s", JsonCodec.encode(mtCityId))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getDpCityIdByMt"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Integer> getMtCityIdByDp(int dpCityId) {
        return AthenaInf.getRpcCompletableFuture(areaCommonService.getMtCityByDpCity(dpCityId))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getMtCityIdByDp")
                            .message(String.format("点评转美团城市id失败, request : %d", dpCityId)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getMtCityIdByDp"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<ProductSearchResponse<com.dianping.general.unified.search.api.sku.model.Product>> commonSearchProduct(ProductSearchRequest request) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.commonSearchProduct(com.dianping.general.unified.search.api.sku.ProductSearchRequest)");
        return AthenaInf.getRpcCompletableFuture(productSearchService.commonSearchProduct(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "commonSearchProduct")
                            .message(String.format("commonSearchProduct error, req: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "commonSearchProduct"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<ProductSearchResponse<ProductSearchIdDto>> searchProductServiceV2(ProductSearchRequestV2 productSearchRequestV2) {
        return AthenaInf.getRpcCompletableFuture(productSearchServiceV2.searchProductIds(productSearchRequestV2))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "searchProductServiceV2")
                            .message(String.format("searchProductServiceV2 error, req: %s", JsonCodec.encode(productSearchRequestV2))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "searchProductServiceV2"), e);
                    return null;
                });
    }

    /**
     * 获取dp侧ugc
     *
     * @param shopId
     * @param stars
     * @param page
     * @param pageSize
     * @return
     */
    @Override
    public CompletableFuture<Object> multiGetDpReviewByShopId(int shopId, List<Integer> stars, int page, int pageSize) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.multiGetDpReviewByShopId(int,java.util.List,int,int)");
        return AthenaInf.getRpcCompletableFuture(reviewServiceV2.paginateShopReviewsAllByStars(shopId, stars, page, pageSize))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "multiGetDpReviewByShopId")
                            .message(String.format("multiGetDpReviewByShopId error, shopId:%d, stars:%s, page:%d, pageSize:%d",
                                    shopId, JsonCodec.encode(stars), page, pageSize)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "multiGetDpReviewByShopId"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Object> multiGetDpReviewByShopIdPoiMigrate(long shopId, List<Integer> stars, int page, int pageSize) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.multiGetDpReviewByShopIdPoiMigrate(long,java.util.List,int,int)");
        return AthenaInf.getRpcCompletableFuture(reviewServiceV2.paginateShopReviewsAllByStars(shopId, stars, page, pageSize))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "multiGetDpReviewByShopIdPoiMigrate")
                            .message(String.format("multiGetDpReviewByShopIdPoiMigrate error, shopId:%d, stars:%s, page:%d, pageSize:%d",
                                    shopId, JsonCodec.encode(stars), page, pageSize)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "multiGetDpReviewByShopIdPoiMigrate"), e);
                    return null;
                });
    }

    /**
     * 获取点评侧Ugc的数量
     *
     * @param dpShopId
     * @return
     */
    @Override
    public CompletableFuture<Map<String, Integer>> findDpReviewCountAllByStar(int dpShopId) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.findDpReviewCountAllByStar(int)");
        return AthenaInf.getRpcCompletableFuture(reviewServiceV2.findReviewCountAllByStar(dpShopId))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "findDpReviewCountAllByStar")
                            .message(String.format("findDpReviewCountAllByStar error, dpshopid:%d", dpShopId)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "findDpReviewCountAllByStar"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Map<String, Integer>> findDpReviewCountAllByStarPoiMigrate(Long dpShopId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.findDpReviewCountAllByStarPoiMigrate(java.lang.Long)");
        return AthenaInf.getRpcCompletableFuture(reviewServiceV2.findReviewCountAllByStar(dpShopId))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "findDpReviewCountAllByStarPoiMigrate")
                            .message(String.format("findDpReviewCountAllByStarPoiMigrate error, dpshopid:%d", dpShopId)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "findDpReviewCountAllByStarPoiMigrate"), e);
                    return null;
                });
    }

    /**
     * 获取点评侧用户信息
     *
     * @param dpUserIds
     * @return
     */
    @Override
    public CompletableFuture<Map<Long, UserDTO>> batchGetUserMap(List<Long> dpUserIds) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.batchGetUserMap(java.util.List)");
        return AthenaInf.getRpcCompletableFuture(userService.getUserMap(dpUserIds))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "batchGetUserMap")
                            .message(String.format("batchGetUserMap error, req: %s", JsonCodec.encode(dpUserIds))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetUserMap"), e);
                    return null;
                });
    }

    /**
     * 点评侧，根据reviewId查询匿名评价的用户信息，如果不是匿名评价不返回
     *
     * @param mainIds         匿名评价的reviewId
     * @param userRequestPara 查询匿名评价作者信息的参数
     * @return
     */
    @Override
    public CompletableFuture<Map<Integer, AnonymousUserInfo>> batchGetAnonymousUserInfo(List<Integer> mainIds, UserRequestPara userRequestPara) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.batchGetAnonymousUserInfo(java.util.List,com.dianping.ugc.proxyService.remote.dto.UserRequestPara)");
        return AthenaInf.getRpcCompletableFuture(dpReviewUserInfoService.findAnonymousUserInfo(mainIds, userRequestPara))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "batchQueryRelationOrder")
                            .message(String.format("batchQueryRelationOrder error, mainIds: %s, userRequestPara:%s", JsonCodec.encode(mainIds), JsonCodec.encode(userRequestPara))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetAnonymousUserInfo"), e);
                    return null;
                });
    }

    /**
     * 获取泛商品的适用商户列表
     *
     * @param productIds
     * @return
     */
    @Override
    public CompletableFuture<Map<Integer, List<Integer>>> batchGetAvailableShopIds(List<Integer> productIds) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.batchGetAvailableShopIds(java.util.List)");
        return AthenaInf.getRpcCompletableFuture(productService.mGetShopIds(productIds))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "batchGetAvailableShopIds")
                            .message(String.format("batchGetAvailableShopIds error, req: %s", JsonCodec.encode(productIds))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetAvailableShopIds"), e);
                    return null;
                });
    }

    /**
     * 获取商户IM的C端入口是否展示以及跳转链接
     *
     * @param reqDTO
     * @return
     */
    @Override
    public CompletableFuture<ClientEntryDTO> getImInfo(ClientEntryReqDTO reqDTO) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.getImInfo(com.sankuai.dzim.cliententry.dto.ClientEntryReqDTO)");
        return AthenaInf.getRpcCompletableFuture(clientEntryService.getClientEntry(reqDTO))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getImInfo")
                            .message(String.format("getImInfo error, req: %s", JsonCodec.encode(reqDTO))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getImInfo"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Map<Integer, SimpleShopDTO>> batchGetProductNearestShop(GetProductNearestShopReq req) {
        return AthenaInf.getRpcCompletableFuture(productDetailService.getProductNearestShop(req))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getProductNearestShop")
                            .message(String.format("getProductNearestShop error, req: %s", JsonCodec.encode(req))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetProductNearestShop"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Map<Long, List<ShelfShopRecProduct>>> multiGetShopRecommendProducts(BatchShelfShopRecommendRequest batchShelfShopRecommendRequest) {
        CompletableFuture<Response<Map<Long, List<ShelfShopRecProduct>>>> shopId2RecProductsFuture = AthenaInf.getRpcCompletableFuture(generalShelfQueryService.batchQueryShopRecommend(batchShelfShopRecommendRequest));
        return shopId2RecProductsFuture
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "multiGetShopRecommendProducts")
                            .message(String.format("multiGetShopRecommendProducts error, req: %s", JsonCodec.encode(batchShelfShopRecommendRequest))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "multiGetShopRecommendProducts"), e);
                    return null;
                })
                .thenApply(response -> {
                    if (response == null || response.getContent() == null) {
                        return Collections.emptyMap();
                    }

                    return response.getContent();
                });
    }

    @Override
    public CompletableFuture<Map<Integer, List<SpuTypeAbstractDTO>>> batchGetSpuTypeAbstractsByShopIds(List<Integer> shopIds) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.batchGetSpuTypeAbstractsByShopIds(java.util.List)");
        return AthenaInf.getRpcCompletableFuture(abstractService.getSpuTypeAbstractsByShopIds(shopIds))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "batchGetSpuTypeAbstractsByShopIds")
                            .message(String.format("batchGetSpuTypeAbstractsByShopIds error, req: %s", JsonCodec.encode(shopIds))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetSpuTypeAbstractsByShopIds"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Map<Long, List<SpuTypeAbstractDTO>>> batchGetSpuTypeAbstractsByLongShopIds(List<Long> shopIds) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.batchGetSpuTypeAbstractsByLongShopIds(java.util.List)");
        return AthenaInf.getRpcCompletableFuture(abstractService.getSpuTypeAbstractsByLongShopIds(shopIds))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "batchGetSpuTypeAbstractsByLongShopIds")
                            .message(String.format("batchGetSpuTypeAbstractsByLongShopIds error, req: %s", JsonCodec.encode(shopIds))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetSpuTypeAbstractsByLongShopIds"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<ShopResponse<List<CoopShopConfigDTO>>> findShopConfigList(CoopShopConfigQueryRequest request) {
        return AthenaInf.getRpcCompletableFuture(coopShopConfigRemoteService.findShopConfigList(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "findShopConfigList")
                            .message(String.format("findShopConfigList error, req: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "findShopConfigList"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<List<ShopDTO>> findShops(List<Integer> shopIds) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.findShops(java.util.List)");
        return AthenaInf.getRpcCompletableFuture(shopService.findShops(shopIds))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "findShopConfigList")
                            .message(String.format("findShopConfigList error, req: %s", JsonCodec.encode(shopIds))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "findShops"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<CardResponse<Map<Integer, TimesCardDetailExposureDTO>>> batchQueryTimesCards(QueryTimesCardByProductIdRequest timesCardByProductIdRequest) {
        return AthenaInf.getRpcCompletableFuture(timesCardQueryService.findTimesCardDetailExposureByProductId(timesCardByProductIdRequest))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "batchQueryTimesCards")
                            .message(String.format("batchQueryTimesCards error, req: %s", JsonCodec.encode(timesCardByProductIdRequest))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchQueryTimesCards"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<CardResponse<Map<Long, List<TimesCardDetailExposureDTO>>>> batchQueryTimesCardsByShop(QueryTimesCardByShopRequest timesCardByShopRequest) {
        return AthenaInf.getRpcCompletableFuture(timesCardQueryService.findTimesCardDetailExposureThroughLongShopId(timesCardByShopRequest))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "batchQueryTimesCards")
                            .message(String.format("batchQueryTimesCards error, req: %s", JsonCodec.encode(timesCardByShopRequest))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchQueryTimesCardsByShop"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Map<com.dianping.pay.promo.display.api.dto.Product, List<PromoDisplayDTO>>> batchQueryPromoDisplay(BatchQueryPromoDisplayRequest request) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.batchQueryPromoDisplay(com.dianping.pay.promo.display.api.dto.BatchQueryPromoDisplayRequest)");
        return AthenaInf.getRpcCompletableFuture(promoDisplayService.batchQueryPromoDisplay(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "batchQueryPromoDisplay")
                            .message(String.format("批量查询立减信息失败，request: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchQueryPromoDisplay"), e);
                    return null;
                }).thenApply(result -> {
                    if (result == null || !result.isSuccess()) {
                        return Maps.newHashMap();
                    }
                    return result.getResult();
                });
    }

    @Override
    public CompletableFuture<PageInfo<GeneralPoolInfoDTO>> pageFindPoolInfo(GeneralQueryPoolInfoRequest request) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.pageFindPoolInfo(com.dianping.joy.booking.api.generalpool.request.GeneralQueryPoolInfoRequest)");
        return AthenaInf.getRpcCompletableFuture(generalQueryPoolInfoService.generalQueryPoolInfo(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "pageFindPoolInfo")
                            .message(String.format("pageFindPoolInfo error, req: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "pageFindPoolInfo"), e);
                    return null;
                })
                .thenApply(result -> {
                    if (result == null || !result.isSuccess() || result.getResult() == null) {
                        return null;
                    }
                    return result.getResult();
                });
    }

    @Override
    public CompletableFuture<List<RegionInfoDTO>> multiGetDpChildRegionListByCityId(int cityId, RegionType regionType, int skip, int limit) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.multiGetDpChildRegionListByCityId(int,com.dianping.gis.remote.enums.RegionType,int,int)");
        return AthenaInf.getRpcCompletableFuture(regionInfoService.findRegionListByCityId(cityId, regionType, skip, limit))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "multiGetDpChildRegionListByCityId")
                            .message(String.format("根据城市id批量获取点评商区信息, cityId: %d, regionType:%d, skip:%d, limit:%d",
                                    cityId, regionType.value, skip, limit)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "multiGetDpChildRegionListByCityId"), e);
                    return null;
                });
    }

    /**
     * 获取点评侧热门商圈信息
     *
     * @param dpCityId
     * @return
     */
    @Override
    public CompletableFuture<HotRegionStationDTO> multiGetDpHotRegionList(int dpCityId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.multiGetDpHotRegionList(int)");
        return AthenaInf.getRpcCompletableFuture(hotRegionStationService.getHotRegionAndStation(dpCityId))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "multiGetDpHotRegionList")
                            .message(String.format("multiGetDpHotRegionList error, dpCityId: %d", dpCityId)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "multiGetDpHotRegionList"), e);
                    return null;
                });
    }


    /**
     * 获取美团侧商圈信息
     *
     * @param mtCityId
     * @return
     */
    @Override
    public CompletableFuture<List<AreaInfo>> multiGetMtAreaInfoByCityId(int mtCityId) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.multiGetMtAreaInfoByCityId(int)");
        return CompletableFuture.supplyAsync(() -> areaService.listByCity(mtCityId), sceneAtomServiceExecutorService)
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "multiGetMtAreaInfoByCityId")
                            .message(String.format("multiGetMtAreaInfoByCityId error, req: %s", JsonCodec.encode(mtCityId))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "multiGetMtAreaInfoByCityId"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<List<RegionInfoDTO>> batchGetDpRegionInfoList(List<Integer> dpRegionIdList) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.batchGetDpRegionInfoList(java.util.List)");
        return AthenaInf.getRpcCompletableFuture(regionInfoService.findRegions(dpRegionIdList))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "batchGetDpRegionInfoList")
                            .message(String.format("batchGetDpRegionInfoList error, req: %s", JsonCodec.encode(dpRegionIdList))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetDpRegionInfoList"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<UnifiedOrderWithId> getUnifiedOrder(String unifiedOrderId) {
        if (Objects.isNull(unifiedOrderId)) {
            return CompletableFuture.completedFuture(null);
        }
        return AthenaInf.getRpcCompletableFuture(getUnifiedOrderService.getByUnifiedOrderId(unifiedOrderId))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getUnifiedOrder")
                            .message(String.format("getUnifiedOrder error, unifiedOrderId: %s", unifiedOrderId)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getUnifiedOrder"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<RecommendResult<RecommendDTO>> getRecommendResult(RecommendParameters recommendParameters) {
        return AthenaInf.getRpcCompletableFuture(recommendService.recommend(recommendParameters, RecommendDTO.class))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getRecommendResult")
                            .message(String.format("getRecommendResult error, req: %s", JsonCodec.encode(recommendParameters))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getRecommendResult"), e);
                    return null;
                })
                .thenApply(response -> {
                    if (response == null || !response.isSuccess() || response.getResult() == null) {
                        return null;
                    }
                    return JsonCodec.decode(JsonCodec.encode(response.getResult()), new TypeReference<RecommendResult<RecommendDTO>>() {
                    });
                });
    }

    @Override
    public CompletableFuture<List<com.sankuai.wpt.user.retrieve.thrift.message.UserModel>> findUserInfoByMtUserIds(List<Long> mtUserIds, com.sankuai.wpt.user.retrieve.thrift.message.UserFields fields) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.findUserInfoByMtUserIds(java.util.List,com.sankuai.wpt.user.retrieve.thrift.message.UserFields)");
        try {
            return AthenaInf.getRpcCompletableFuture(rpcUserRetrieveService.batchGetUserByIds(mtUserIds, fields))
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("scene", "compositeAtomService")
                                .putTag("method", "findUserInfoByMtUserIds")
                                .message(String.format("findUserInfoByMtUserIds error, mtuserids: %s", JsonCodec.encode(mtUserIds))));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "findUserInfoByMtUserIds"), e);
                        return null;
                    });
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "findUserInfoByMtUserIds"), e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<List<ProjectContentDTO>> mGetMaoyanProductDetail(List<Integer> projectIdList) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.mGetMaoyanProductDetail(java.util.List)");
        return AthenaInf.getRpcCompletableFuture(mtHomePageService.queryProjectListById(projectIdList))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "mGetMaoyanProductDetail")
                            .message(String.format("mGetMaoyanProductDetail error, request : %s", JsonCodec.encode(projectIdList))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "mGetMaoyanProductDetail"), e);
                    return null;
                })
                .thenApply(result -> {
                    if (result == null || !result.isSuccess() || result.getData() == null) {
                        return null;
                    }
                    return result.getData();
                });
    }

    @Override
    public CompletableFuture<StandardProductPageDTO> getSpuById(long spuId) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.getSpuById(long)");
        return AthenaInf.getRpcCompletableFuture(productStaticDetailBySpuService.queryProduct(spuId))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getSpuById")
                            .message(String.format("getSpuById error, request : %d", spuId)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getSpuById"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<StandardProductDTO> getSpuShop(long spuId, int cityId) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.getSpuShop(long,int)");
        return AthenaInf.getRpcCompletableFuture(productStaticDetailBySpuService.getSpuShop(spuId, cityId))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getSpuShop")
                            .message(String.format("getSpuShop error, spuId: %d, cityId:%d", spuId, cityId)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getSpuShop"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<StandardProductDTO> getSpuPrice(GetStandardProductPriceRequest request) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.getSpuPrice(com.dianping.tpfun.product.api.sku.aggregate.request.GetStandardProductPriceRequest)");
        return AthenaInf.getRpcCompletableFuture(productStaticDetailBySpuService.getSpuPrice(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getSpuPrice")
                            .message(String.format("getSpuPrice error, request : %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getSpuPrice"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Result<QueryData>> getSwanDataByKey(Integer bizTypeId, String queryKey, SwanParam swanParam) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.getSwanDataByKey(java.lang.Integer,java.lang.String,com.sankuai.swan.udqs.api.SwanParam)");
        return AthenaInf.getRpcCompletableFuture(swanQueryService.queryByKey(bizTypeId, queryKey, swanParam))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getSwanDataByKey")
                            .message(String.format("getSwanDataByKey error, bizTypeId : %d, querykey:%s, swanparam:%s",
                                    bizTypeId, queryKey, JsonCodec.encode(swanParam))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getSwanDataByKey"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Result<QueryData>> getSwanDataByQueryKey(Integer bizTypeId, String queryKey, SwanParam swanParam) {
        return CompletableFuture.supplyAsync(() -> swanUniteQueryService.swanQuery(bizTypeId, queryKey, swanParam), sceneAtomServiceExecutorService)
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getSwanDataByQueryKey")
                            .message(String.format("getSwanDataByQueryKey error, bizTypeId:%s, queryKey:%s, swanParam:%s",
                                    bizTypeId, queryKey, JsonCodec.encode(swanParam))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getSwanDataByQueryKey"), e);
                    return null;
                });

    }

    @Override
    public CompletableFuture<List<Integer>> getTransformProductId(List<Integer> dealIds) {
        List<Long> dealIdLongs = dealIds.stream().map(Long::valueOf).collect(Collectors.toList());
        return AthenaInf.getRpcCompletableFuture(dealGroupRelationService.queryProductByDealGroupId(dealIdLongs))
                .exceptionally(throwable -> {
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getTransformProductId"), throwable);
                    return Maps.newHashMap();
                })
                .thenApply(resultMap -> {
                    if (MapUtils.isEmpty(resultMap)) {
                        return Lists.newArrayList();
                    }
                    List<Integer> transformDealIDs = Lists.newArrayList();
                    for (Map.Entry<Long, TransformTaskEntity> entry : resultMap.entrySet()) {
                        if (entry.getValue() != null && entry.getValue().getProductStatus() == 1) {
                            transformDealIDs.add(entry.getKey().intValue());
                        }
                    }
                    return transformDealIDs;
                });
    }

    @Override
    public CompletableFuture<Response<ShelfNavTabProductList>> getMultiSelectedShelfProducts(QueryProductsRequest request) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.getMultiSelectedShelfProducts(com.dianping.product.shelf.common.request.QueryProductsRequest)");
        if (request != null) {
            request.setUseCache(true);
        }
        return AthenaInf.getRpcCompletableFuture(generalShelfQueryService.queryProducts(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getMultiSelectedShelfProducts")
                            .message(String.format("查询平台通用货架商品异常，request: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getMultiSelectedShelfProducts"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Response<ShelfFilterComponent>> getMultiSelectedShelfFilter(QueryFiltersRequest request) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.getMultiSelectedShelfFilter(com.dianping.product.shelf.common.request.QueryFiltersRequest)");
        if (request != null) {
            request.setUseCache(true);
        }
        return AthenaInf.getRpcCompletableFuture(generalShelfQueryService.queryFilters(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getMultiSelectedShelfFilter")
                            .message(String.format("查询平台通用货架筛选异常，request: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getMultiSelectedShelfFilter"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Boolean> getIsGreyWhiteShop(Long shopId, String greyScene) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.getIsGreyWhiteShop(java.lang.Long,java.lang.String)");
        return AthenaInf.getRpcCompletableFuture(generalShelfQueryService.inWhiteList(shopId, greyScene))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("interface", "compositeAtomService")
                            .putTag("method", "getIsGreyWhiteShop")
                            .message(String.format("查询门店是否在灰度门店中失败，失败的门店 id: %d", shopId)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getIsGreyWhiteShop"), e);
                    return null;
                }).thenApply(result -> result != null && BooleanUtils.isTrue(result.getContent()));
    }

    @Override
    public CompletableFuture<BindRelationResp> getVirtualDPUserIdByRealMTUserId(long realMtUserId) throws TException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.getVirtualDPUserIdByRealMTUserId(long)");
        return AthenaInf.getRpcCompletableFuture(userMergeQueryService.getVirtualBindByMtUserId(realMtUserId))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getVirtualDPUserIdByRealMTUserId")
                            .message(String.format("根据美团userId 查询绑定关系失败, mtUserId: %d", realMtUserId)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getVirtualDPUserIdByRealMTUserId"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<QueryIsLivingRespDTO> queryIsLiving(QueryIsLivingReqDTO request) throws TException {
        return AthenaInf.getRpcCompletableFuture(horizonVideoLiveService.queryIsLiving(request)).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "queryIsLiving")
                    .message(String.format("queryIsLiving error, req: %s", JsonCodec.encode(request))));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryIsLiving"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<ResourceBatchQueryResponse> batchQueryResource(ResourceBatchQueryRequest request) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.batchQueryResource(com.sankuai.mpproduct.trade.api.request.ResourceBatchQueryRequest)");
        String sourceCell = Tracer.getCell();
        try {
            Tracer.setCell("gray-release-nibpp-general-nontrade");
            return AthenaInf.getRpcCompletableFuture(resourceCacheQueryService.batchQueryResource(request))
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("scene", "compositeAtomService")
                                .putTag("method", "batchQueryResource")
                                .message(String.format("batchQueryResource error, request : %s", JsonCodec.encode(request))));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchQueryResource"), e);
                        return null;
                    });
        } catch (TException e) {
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchQueryResource"), e);
            return CompletableFuture.completedFuture(null);
        } finally {
            if (sourceCell == null) {
                // 清空此次调用请求中的LiteSet标识，一定要调用Tracer.clearContext(Tracer.CELL)才能清空流量标识，其他方法清不了！
                Tracer.clearContext(Tracer.CELL);
            } else {
                //设置回原来LiteSet标识
                Tracer.setCell(sourceCell);
            }
        }
    }

    @Override
    public CompletableFuture<Map<Long, AggOverallReviewModel>> queryReview(ContentBaseRequest request) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.queryReview(com.sankuai.beautycontent.beautylaunchapi.model.dto.gw.request.ContentBaseRequest)");
        return AthenaInf.getRpcCompletableFuture(aggReviewService.queryAggOverall(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "queryReview")
                            .message(String.format("queryReview error, request : %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryReview"), e);
                    return null;
                }).thenApply(res -> {
                    if (res == null || !res.isSuccess()) {
                        return Maps.newHashMap();
                    }
                    return res.getData();
                });
    }

    @Override
    public CompletableFuture<Map<String, Boolean>> getDpFavorStatus(List<String> bizIds, int bizType, long userId) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.getDpFavorStatus(java.util.List,int,long)");
        return AthenaInf.getRpcCompletableFuture(favorService.findFavorStatus(bizIds, bizType, userId))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getDpFavorStatus")
                            .message(String.format("getDpFavorStatus error, bizIds:%s, biztype:%d, userid:%d",
                                    JsonCodec.encode(bizIds), bizType, userId)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getDpFavorStatus"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Map<String, Integer>> getDpFavorCount(List<String> bizIds, int bizType) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.getDpFavorCount(java.util.List,int)");
        return AthenaInf.getRpcCompletableFuture(favorService.getBizFavorCountsByStr(bizIds, bizType))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getDpFavorCount")
                            .message(String.format("getDpFavorCount error, bizIds : %s, biztype:%d",
                                    JsonCodec.encode(bizIds), bizType)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getDpFavorCount"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<List<Long>> getMtCollectedFromGivenSets(long userId, CollTypeEnum collType, List<Long> ids, short srcType) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.getMtCollectedFromGivenSets(long,com.sankuai.user.collection.client.CollTypeEnum,java.util.List,short)");
        return CompletableFuture.supplyAsync(() -> {
            try {
                return userCollectionClient.getCollectedFromGivenSetsV2(userId, collType, ids, srcType);
            } catch (Exception e) {
                ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getMtCollectedFromGivenSets"), e);
                return null;
            }
        }, sceneAtomServiceExecutorService).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "getMtCollectedFromGivenSets")
                    .message(String.format("getMtCollectedFromGivenSets error, userId : %d, collType:%d, ids:%s, srctype:%d",
                            userId, collType.getValue(), JsonCodec.encode(ids), srcType)));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getMtCollectedFromGivenSets"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<Map<Long, Integer>> getMtCollectedCount(CollTypeEnum collType, List<Long> collIds, short srcType) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.getMtCollectedCount(com.sankuai.user.collection.client.CollTypeEnum,java.util.List,short)");
        //接口不支持future调用，只能自己启线程池
        Map<CompletableFuture<CollectedUserEchoV2>, Long> futureMap = new HashMap<>();
        collIds.forEach(collId -> {
            futureMap.put(CompletableFuture.supplyAsync(() -> {
                try {
                    return userCollectionClient.getOneBeCollectedUsersV2(collId, collType, srcType, (short) 0, (short) 1, (short) 0);
                } catch (Exception e) {
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getMtCollectedCount"), e);
                    return null;
                }
            }, sceneAtomServiceExecutorService).exceptionally(e -> {
                log.error(XMDLogFormat.build()
                        .putTag("scene", "compositeAtomService")
                        .putTag("method", "getMtCollectedCount")
                        .message(String.format("getMtCollectedCount error, collType : %d, collIds:%s, srcType:%d",
                                collType.getValue(), JsonCodec.encode(collIds), srcType)));
                ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getMtCollectedCount"), e);
                return null;
            }), collId);
        });
        return CompletableFuture.allOf(futureMap.keySet().toArray(new CompletableFuture[0])).thenApply(aVoid -> {
            Map<Long, Integer> result = com.google.common.collect.Maps.newHashMap();
            futureMap.forEach((k, v) -> {
                CollectedUserEchoV2 userEchoV2 = k.join();
                if (userEchoV2 != null) {
                    result.put(v, userEchoV2.getTotal());
                }
            });
            return result;
        });
    }

    @Override
    public CompletableFuture<Map<Long, Integer>> querySpuSaleByResourceId(long resourceId) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.querySpuSaleByResourceId(long)");
        if (resourceId <= 0) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        QuerySpuSalesReqDTO reqDTO = new QuerySpuSalesReqDTO();
        reqDTO.setResourceId(resourceId);
        return AthenaInf.getRpcCompletableFuture(soldCountService.querySpuSalesByResourceId(reqDTO))
                .exceptionally(e -> {
                    log.info(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "querySpuSalesByResourceId")
                            .message("查询资源销量失败，resourceId: " + resourceId));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "querySpuSaleByResourceId"), e);
                    return null;
                })
                .thenApply(result -> result == null ? Maps.newHashMap() : new HashMap<Long, Integer>(1) {{
                    put(resourceId, result.getSpuSoldCount());
                }});
    }

    @Override
    public CompletableFuture<List<Product>> findProductInfoByIds(List<Integer> productIds) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.findProductInfoByIds(java.util.List)");
        return AthenaInf.getRpcCompletableFuture(productService.mGetBaseProductByIds(productIds))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "findProductInfoByIds")
                            .message(String.format("查询商品信息列表异常,request: %s", JsonCodec.encode(productIds))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "findProductInfoByIds"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<WeddingShopBriefInfoDTO> loadShopBriefInfoByShopId(long shopId) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.loadShopBriefInfoByShopId(long)");
        return AthenaInf.getRpcCompletableFuture(weddingShopBriefInfoService.loadShopBriefInfoByShopIdL(shopId))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "loadShopBriefInfoByShopId")
                            .message(String.format("根据ShopID获取商户简介DTO异常,shopId: %s", shopId)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "loadShopBriefInfoByShopId"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<List<BabyTimeLimeTgSpikeDTO>> loadBabyTimeLimitTgListByShopId(long dpShopId, int limit) {
        return AthenaInf.getRpcCompletableFuture(babyTimeLimitTgSpikeService.loadBabyDpSkuListByShopId(dpShopId, limit))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "loadBabyTimeLimitTgListByShopId")
                            .message(String.format("根据shopid获取所有的团单, dpShopId: %s, limit: %s", dpShopId, limit)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "loadBabyTimeLimitTgListByShopId"), e);
                    return null;
                }).thenApply(resp -> {
                    if (resp == null || !resp.isSuccess() || CollectionUtils.isEmpty(resp.getData())) {
                        return new ArrayList<>();
                    }
                    return resp.getData();
                });
    }

    @Override
    public CompletableFuture<Map<Integer, RankEntityScenesDTO>> loadRankEntityInfoDTO(EntityScenesRankInfoRequest request) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.loadRankEntityInfoDTO(com.sankuai.mdp.dzshoplist.rank.api.request.EntityScenesRankInfoRequest)");
        return AthenaInf.getRpcCompletableFuture(rankEntityInfoQuery.batchGetScenesRankInfo(request)).thenApply(response -> {
            if (Objects.isNull(response) || !response.isSuccess()) {
                return null;
            }
            return response.getResult();
        }).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "loadRankEntityInfoDTO")
                    .message(String.format("查询商品榜单信息异常, request=%s", JsonCodec.encode(request))));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "loadRankEntityInfoDTO"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<List<SubwayLine>> multiGetMtSubwayLineByCity(int mtCityId) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.multiGetMtSubwayLineByCity(int)");
        return CompletableFuture.supplyAsync(() -> subwayService.listSubwayLineByCity(mtCityId), sceneAtomServiceExecutorService)
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "multiGetMtSubwayLineByCity")
                            .message(String.format("multiGetMtSubwayLineByCity error, req: %s", JsonCodec.encode(mtCityId))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "multiGetMtSubwayLineByCity"), e);
                    return Lists.newArrayList();
                });
    }

    @Override
    public CompletableFuture<List<SubwayStation>> multiGetMtSubwayStationByLineId(int mtSubwayLineId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.multiGetMtSubwayStationByLineId(int)");
        return CompletableFuture.supplyAsync(() -> subwayService.listSubwayStation(mtSubwayLineId), sceneAtomServiceExecutorService)
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "multiGetMtSubwayStationByLineId")
                            .message(String.format("multiGetMtSubwayStationByLineId error, req: %s", JsonCodec.encode(mtSubwayLineId))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "multiGetMtSubwayStationByLineId"), e);
                    return Lists.newArrayList();
                });
    }

    @Override
    public CompletableFuture<ResponseDTO<Boolean>> getGroupPinShopBlock(Long dpShopId) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.getGroupPinShopBlock(java.lang.Long)");
        return AthenaInf.getRpcCompletableFuture(shopBlockService.queryShopBlock(dpShopId)).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "getGroupPinShopBlock")
                    .message(String.format("判断门店是否在组队拼场黑名单里失败，dpShopId : %s", JsonCodec.encode(dpShopId))));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getGroupPinShopBlock"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<List<SpuDTO>> querySpuTheme(SpuRequest request) {
        return AthenaInf.getRpcCompletableFuture(spuThemeQueryService.query(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "querySpuTheme")
                            .message(String.format("获取标品主题信息失败，请求:%s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "querySpuTheme"), e);
                    return null;
                }).thenApply(result -> {
                    if (result == null || CollectionUtils.isEmpty(result.getSpuList())) {
                        return Lists.newArrayList();
                    }
                    return result.getSpuList();
                });
    }

    @Override
    public CompletableFuture<List<Long>> querySpuIdsByCity(CitySpuIdQueryRequest request) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.querySpuIdsByCity(com.meituan.mpproduct.general.trade.api.request.CitySpuIdQueryRequest)");
        try {
            return CompletableFuture.supplyAsync(() -> {
                        String sourceCell = Tracer.getCell();
                        try {
                            Tracer.setCell("gray-release-nibpp-general-nontrade");
                            return spuQueryService.queryCitySpuId(request);
                        } finally {
                            clearAndSetNewCell(sourceCell);
                        }
                    }, sceneAtomServiceExecutorService)
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("scene", "compositeAtomService")
                                .putTag("method", "querySpuIdsByCity")
                                .message(String.format("根据城市获取标品id列表失败，请求:%s", JsonCodec.encode(request))));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "querySpuIdsByCity"), e);
                        return null;
                    }).thenApply(resp -> {
                        if (resp == null || !resp.isSuccess() || resp.getSpuIds() == null) {
                            return Lists.newArrayList();
                        }
                        return resp.getSpuIds();
                    });
        } catch (Exception e) {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "querySpuIdsByCity")
                    .message(String.format("根据城市获取标品id列表失败，请求:%s", JsonCodec.encode(request))));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "querySpuIdsByCity"), e);
        }
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<List<Long>> querySpuIdsByPoi(PoiSpuIdQueryRequest request) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.querySpuIdsByPoi(com.meituan.mpproduct.general.trade.api.request.PoiSpuIdQueryRequest)");
        try {
            return CompletableFuture.supplyAsync(() -> {
                        String sourceCell = Tracer.getCell();
                        try {
                            Tracer.setCell("gray-release-nibpp-general-nontrade");
                            return spuQueryService.queryPoiSpuId(request);
                        } finally {
                            clearAndSetNewCell(sourceCell);
                        }
                    }, sceneAtomServiceExecutorService)
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("scene", "compositeAtomService")
                                .putTag("method", "querySpuIdsByCity")
                                .message(String.format("根据门店获取标品id列表失败，请求:%s", JsonCodec.encode(request))));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "querySpuIdsByPoi"), e);
                        return null;
                    }).thenApply(resp -> {
                        if (resp == null || !resp.isSuccess() || resp.getSpuIds() == null) {
                            return Lists.newArrayList();
                        }
                        return resp.getSpuIds();
                    });
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "querySpuIdsByPoi"), e);
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
    }

    @Override
    public CompletableFuture<SpuTagResponse> batchGetMedicineShelfProducts(SpuTagProductParam spuTagProductParam) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.batchGetMedicineShelfProducts(com.sankuai.health.sc.api.thrift.SpuTagProductParam)");
        return CompletableFuture.supplyAsync(() -> {
            try {
                return drugPoiFoodService.spuTagProduct(spuTagProductParam);
            } catch (Exception e) {
                log.error(XMDLogFormat.build()
                        .putTag("scene", "compositeAtomService")
                        .putTag("method", "batchGetMedicineShelfProducts")
                        .message(String.format("batchGetMedicineShelfProducts error, req: %s", spuTagProductParam)));
                ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetMedicineShelfProducts"), e);
                return null;
            }
        }, sceneAtomServiceExecutorService).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "batchGetMedicineShelfProducts")
                    .message(String.format("batchGetMedicineShelfProducts error, req: %s", spuTagProductParam)));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetMedicineShelfProducts"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<Boolean> checkGroupPinCouponStatus(int activityType) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.checkGroupPinCouponStatus(int)");
        return AthenaInf.getRpcCompletableFuture(marketBonusService.checkCouponActivityStatus(activityType)).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "checkGroupPinCouponStatus")
                    .message(String.format("checkGroupPinCouponStatus error, req: %s", JsonCodec.encode(activityType))));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "checkGroupPinCouponStatus"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<QuerySecKillDealByPoiResponse> querySecKillDealByPoi(QuerySecKillDealByPoiRequest request) {
        return AthenaInf.getRpcCompletableFuture(secKillService.querySecKillDealByPoi(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "querySecKillDealByPoi")
                            .message(String.format("querySecKillDealByPoi error, req: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "querySecKillDealByPoi"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<DealGroupSearchResponse<DealGroupDto>> searchDealGroupBySearchTerm(DealGroupSearchRequest request) {
        return AthenaInf.getRpcCompletableFuture(dealGroupSearchService.searchDealGroup(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "searchDealGroup")
                            .message(String.format("searchDealGroup error, req: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "searchDealGroup"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<Map<Integer, DealDetailDto>> batchQueryDealDetailInfo(List<Integer> dealGroupIds) {
        return AthenaInf.getRpcCompletableFuture(dealDetailStructuredQueryService.batchQueryDealDetailInfo(dealGroupIds))
                .exceptionally(ex -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "batchQueryDealDetailInfo")
                            .message(String.format("batchQueryDealDetailInfo error, req: %s", JsonCodec.encode(dealGroupIds))));
                    ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "batchQueryDealDetailInfo"), ex);
                    return null;
                }).thenApply(res -> {
                    if (res == null || !res.isSuccess()) {
                        return null;
                    }
                    return res.getContent();
                });
    }

    private void clearAndSetNewCell(String cellName) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.clearAndSetNewCell(java.lang.String)");
        if (cellName == null) {
            Tracer.clearContext(Tracer.CELL);
        } else {
            Tracer.setCell(cellName);
        }
    }

    @Override
    public CompletableFuture<DramaPageReviewDetailDto> getStandardDetailReviewByResourceId(BeautyGoodsReviewRequest beautyGoodsReviewRequest) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.getStandardDetailReviewByResourceId(com.sankuai.beautycontent.beautylaunchapi.model.request.BeautyGoodsReviewRequest)");
        return AthenaInf.getRpcCompletableFuture(dramaDimensionReviewService.getLatestReviewByReferIdUserIdAndReferType(beautyGoodsReviewRequest))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getStandardDetailReviewByResourceId")
                            .message(String.format("getStandardDetailReviewByResourceId error, req: %s", JsonCodec.encodeWithUTF8(beautyGoodsReviewRequest))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getStandardDetailReviewByResourceId"), e);
                    return null;
                }).thenApply(response -> {
                    if (Objects.isNull(response) || !response.isSuccess() || Objects.isNull(response.getData())) {
                        return null;
                    }
                    return response.getData();
                });
    }

    @Override
    public CompletableFuture<String> getRecommendFlowId(RecommendParameters recommendParameters) {
        return AthenaInf.getRpcCompletableFuture(recommendService.recommend(recommendParameters, RecommendDTO.class))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getRecommendFlowId")
                            .message(String.format("getRecommendFlowId error, req: %s", JsonCodec.encode(recommendParameters))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getRecommendFlowId"), e);
                    return null;
                })
                .thenApply(response -> {
                    if (response == null || !response.isSuccess() || response.getResult() == null) {
                        return null;
                    }
                    return response.getResult().getFlowId();
                });
    }

    @Override
    public CompletableFuture<ListAllEffectMemberGrouponByTenantIdRespDTO> listAllEffectMemberGrouponByTenantId(ListAllEffectMemberGrouponByTenantIdReqDTO tenantIdReqDTO) {

        CompletableFuture<ListAllEffectMemberGrouponByTenantIdRespDTO> future = null;
        try {
            future = AthenaInf.getRpcCompletableFuture(nibMemberQryThriftService.listAllEffectMemberGrouponByTenantId(tenantIdReqDTO));
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "listAllEffectMemberGrouponByTenantId"), e);
            return CompletableFuture.completedFuture(null);
        }
        return future.exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "listAllEffectMemberGrouponByTenantId")
                    .message(String.format("listAllEffectMemberGrouponByTenantId error, tenantIdReqDTO: %s", tenantIdReqDTO.toString())));
            ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "listAllEffectMemberGrouponByTenantId"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<List<Long>> queryMallFilteredDealGroups(List<Long> dealGroupIds, int platform) {
        return AthenaInf.getRpcCompletableFuture(dealShopQueryService.queryMallFilteredDealGroups(dealGroupIds, platform))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "queryMallFilteredDealGroups")
                            .message(String.format("queryMallFilteredDealGroups error, req: %s", JsonCodec.encode(dealGroupIds))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryMallFilteredDealGroups"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<IntentionResultResponse> recognizeIntention(IntentionRequest request) throws TException {
        return AthenaInf.getRpcCompletableFuture(intentionService.recognizeIntention(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "recognizeIntention")
                            .message(String.format("recognizeIntention error, req: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "recognizeIntention"), e);
                    return null;
                })
                .thenApply(response -> {
                    if (Objects.isNull(response) || response.getSuccess() == null || !response.getSuccess() || Objects.isNull(response.getData())) {
                        return null;
                    }
                    return response;
                });
    }

    @Override
    public CompletableFuture<ProductInfoResponse> batchQueryProductInfo(ProductInfoRequest request) {
        return CompletableFuture.supplyAsync(() -> productQueryService.batchQueryProductInfo(request),
                        sceneAtomServiceExecutorService)
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "batchQueryProductInfo")
                            .message(String.format("batchQueryProductInfo error, req: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchQueryProductInfo"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<List<Long>> queryShopSaleDealIds(long shopId, int platform) {
        //走批量接口获取全量在线售卖团单，分页参数实际不生效
        return AthenaInf.getRpcCompletableFuture(dealShopQueryService.batchQuerySaleDealGroupId(Sets.newHashSet(shopId), PlatformUtil.isMT(platform) ? 200 : 100, 1, 100))
                .thenApply(shopDealIdMap -> {
                    if (MapUtils.isEmpty(shopDealIdMap)) {
                        return null;
                    }
                    return shopDealIdMap.get(shopId);
                })
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "multiGetShopSaleDealIds")
                            .message(String.format("multiGetShopSaleDealIds error, shopId: %s, platform: %s", JsonCodec.encode(shopId), platform)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryShopSaleDealIds"), e);
                    return null;
                });
    }

    public CompletableFuture<List<Long>> queryShopSaleDealGroupIds(long shopId, int platform, int start, int limit) {
        return AthenaInf.getRpcCompletableFuture(dealShopQueryService.querySaleDealGroupId(shopId, platform, start, limit))
                .thenApply(response -> response)
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "queryShopSaleDealGroupIds")
                            .message(String.format("queryShopSaleDealGroupIds error, shopId: %s, platform: %s, start: %s, limit: %s", shopId, platform, start, limit)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryShopSaleDealGroupIds"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<List<DealGroupDTO>> batchGetMtDealIdByDp(List<Long> dpDealIds) {
        if (CollectionUtils.isEmpty(dpDealIds)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        QueryByDealGroupIdRequest request = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(dpDealIds), IdTypeEnum.DP)
                .dealGroupId(DealGroupIdBuilder.builder().mtDealGroupId())
                .build();
        return CompletableFuture.supplyAsync(() -> {
            try {
                QueryDealGroupListResponse response = dealGroupQueryService.queryByDealGroupIds(request);
                if (response != null && response.getCode() == ResponseCodeEnum.SUCCESS.getCode() && response.getData() != null) {
                    return response.getData().getList();
                }
            } catch (TException e) {
                log.error(XMDLogFormat.build()
                        .putTag("scene", "compositeAtomService")
                        .putTag("method", "batchGetMtDealIdByDp")
                        .message(String.format("batchGetMtDealIdByDp error, dpDealIds: %s", JsonCodec.encode(dpDealIds))));
                ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetMtDealIdByDp"), e);
            }
            return null;
        }, sceneAtomServiceExecutorService).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "batchGetMtDealIdByDp")
                    .message(String.format("batchGetMtDealIdByDp error, dpDealIds: %s", JsonCodec.encode(dpDealIds))));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetMtDealIdByDp"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<DealResponse> getFoodDealInfos(DealRequest request) {
        if (Objects.isNull(request)) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.supplyAsync(() -> {
            try {
                return dealService.getDealInfos(request);
            } catch (Exception e) {
                log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "getDealInfos")
                        .message(String.format("getFoodDealInfos Error，request: %s", GsonUtils.toJson(request))));
                ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getFoodDealInfos"), e);
                return null;
            }
        }, sceneAtomServiceExecutorService).exceptionally(e -> {
            log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "getDealInfos")
                    .message(String.format("getFoodDealInfos Error，request: %s", GsonUtils.toJson(request))));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getFoodDealInfos"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<Map<Long, List<Long>>> batchGetMtByDpIds(List<Long> dpShopIds) {
        if (CollectionUtils.isEmpty(dpShopIds)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        try {
            return AthenaInf.getRpcCompletableFuture(poiRelationService.queryMtByDpIdsL(dpShopIds)).exceptionally(e -> {
                log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService")
                        .putTag("method", "queryMtByDpIdsL")
                        .message(String.format("queryMtByDpIdsL error，request: %s", JsonCodec.encode(dpShopIds))));
                ExceptionTracer.addException(String.format("%s_%s", "compositeAtomService", "queryMtByDpIdsL"), e);
                return Maps.newHashMap();
            });
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryMtByDpIdsL"), e);
        }
        return CompletableFuture.completedFuture(Maps.newHashMap());
    }

    @Override
    public CompletableFuture<Map<Long, MtPoiDTO>> batchGetPoiByMtShopIds(List<Long> mtShopIds, List<String> fields) {
        if (CollectionUtils.isEmpty(mtShopIds) || CollectionUtils.isEmpty(fields)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        return CompletableFuture.supplyAsync(() -> {
            try {
                return mtPoiService.findPoisById(mtShopIds, fields);
            } catch (Exception e) {
                log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService")
                        .putTag("method", "batchGetPoiByMtShopIds").message(String.format(
                                "batchGetPoiByMtShopIds Error，request: mtShopId=%s,fields=%s", mtShopIds, fields)));
                ExceptionTracer
                        .addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetPoiByMtShopIds"), e);
                return new HashMap<Long, MtPoiDTO>();
            }
        }, sceneAtomServiceExecutorService).exceptionally(e -> {
            log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService")
                    .putTag("method", "batchGetPoiByMtShopIds").message(String
                            .format("batchGetPoiByMtShopIds Error，request: mtShopId=%s,fields=%s", mtShopIds, fields)));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetPoiByMtShopIds"),
                    e);
            return Maps.newHashMap();
        });
    }

    @Override
    public CompletableFuture<Map<Long, DealBasicInfo>> getDealBasicInfo(List<Long> longDealIds) {
        if (CollectionUtils.isEmpty(longDealIds)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        List<Integer> dealIds = longDealIds.stream().map(Long::intValue).collect(Collectors.toList());
        return CompletableFuture.supplyAsync(() -> {
            try {
                Map<Integer, DealBasicInfo> responseMap = dealBasicThriftClient.multiGetDealBasicInfo(dealIds);
                return responseMap.entrySet().stream().collect(Collectors.toMap(a -> a.getKey().longValue(), Map.Entry::getValue));
            } catch (Exception e) {
                log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "getDealBasicInfo").message(String.format("getDealBasicInfo error, req: %s", JsonCodec.encode(longDealIds))));
                return new HashMap<Long, DealBasicInfo>();
            }
        }, sceneAtomServiceExecutorService).exceptionally(e -> {
            log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "getDealBasicInfo").message(String.format("getDealBasicInfo Error，req: %s", JsonCodec.encode(longDealIds))));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getDealBasicInfo"), e);
            return new HashMap<>();
        });
    }

    public CompletableFuture<RemoteResponse<List<Long>>> queryPromoCodeDealGroups(Long dpShopId) {
        return AthenaInf.getRpcCompletableFuture(qrOfflineDealGroupApplicationService.queryBoundedDpDealGroupIds(dpShopId))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "queryPromoCodeDealGroups")
                            .message(String.format("queryPromoCodeDealGroups error, req: %s", JsonCodec.encode(dpShopId))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryPromoCodeDealGroups"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<List<DealGroupDTO>> batchGetServiceProjectByDealIds(QueryByDealGroupIdRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                QueryDealGroupListResponse response = dealGroupQueryService.queryByDealGroupIds(request);
                if (response != null && response.getCode() == ResponseCodeEnum.SUCCESS.getCode() && response.getData() != null) {
                    return response.getData().getList();
                }
            } catch (Exception e) {
                log.error(XMDLogFormat.build()
                        .putTag("scene", "compositeAtomService")
                        .putTag("method", "batchGetServiceProjectByDealIds")
                        .message(String.format("batchGetServiceProjectByDealIds error, request: %s", JsonCodec.encode(request))));
                ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetServiceProjectByDealIds"), e);
            }
            return null;
        }, sceneAtomServiceExecutorService).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "batchGetServiceProjectByDealIds")
                    .message(String.format("batchGetServiceProjectByDealIds error, request: %s", JsonCodec.encode(request))));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchGetServiceProjectByDealIds"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<Result<QueryData>> batchQuerySupplyCommandMatchDeals(int backCityId, int pageNo, int pageSize) {
        SwanParam swanParam = buildSupplyCommandSwanParam(backCityId, pageNo, pageSize);
        return AthenaInf.getRpcCompletableFuture(swanQueryService.queryByKey(1096, "dpapp_sndiagnosis_notsatisfy_finish_product", swanParam))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getSwanDataByKey")
                            .message(String.format("getSwanDataByKey error, bizTypeId : %d, querykey:%s, swanparam:%s",
                                    1096, "dpapp_sndiagnosis_notsatisfy_detail_d", JsonCodec.encode(swanParam))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getSwanDataByKey"), e);
                    return null;
                });

    }

    private SwanParam buildSupplyCommandSwanParam(int backCityId, int pageNo, int pageSize) {
        SwanParam swanParam = new SwanParam();
        swanParam.setRequestParams(buildSupplyCommandRequestParams(backCityId));
        com.sankuai.swan.udqs.api.PageInfo pageInfo = new com.sankuai.swan.udqs.api.PageInfo();
        pageInfo.setPageNo(pageNo);
        pageInfo.setPageSize(pageSize);
        swanParam.setPageInfo(pageInfo);
        return swanParam;
    }

    private List<Map<String, Object>> buildSupplyCommandRequestParams(int backCityId) {
        List<Map<String, Object>> list = Lists.newArrayList();
        Map<String, Object> params = Maps.newHashMap();
        //筛选参数为list
        params.put("dim_city_id", Lists.newArrayList(backCityId));
        //筛选参数为list
//        params.put("district_id_shop", List数据类型);
        //筛选参数为list
//        params.put("region_id_shop", List数据类型);
        //筛选参数为list
//        params.put("shop_type", List数据类型);
        //筛选参数为list
        // todo 配置化改造
        params.put("spu_type", Lists.newArrayList("肩颈按摩", "修护护理", "脱毛", "补水保湿", "全身SPA",
                "美胸美臀", "眼部护理", "养生调理", "祛痘清痘", "头疗", "深层清洁", "美白嫩肤", "瘦身纤体", "紧致提拉"));
        list.add(params);
        return list;
    }

    @Override
    public CompletableFuture<TransformCityTypeResponse> queryBackCityId(String sourceCityId, boolean isMt) {
        TransformCityTypeRequest request = new TransformCityTypeRequest();
        request.setKey(MdpContextUtils.isProdEnv() ? "m9926ef7f55841b6adc08432529d669t" : "mce69a77528d4b1c8b40213c9387603t");
        request.setSource_city_type(isMt ? "mt_front" : "dp");
        request.setTarget_city_type("mt_back");
        if (isMt) {
            request.setMt_front_city_id(String.valueOf(sourceCityId));
        } else {
            request.setDp_city_id(String.valueOf(sourceCityId));
        }
        try {
            return AthenaInf.getRpcCompletableFuture(mapOpenApiService.transformcitytype(request))
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("scene", "compositeAtomService")
                                .putTag("method", "batchQuerySupplyCommandMatchDeals")
                                .message(String.format("queryBackCityId error, sourceCityId : %d, isMt:%s",
                                        sourceCityId, isMt)));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryBackCityId"), e);
                        return null;
                    });
        } catch (TException e) {
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<CombineProductInfoResponse> batchQueryCombinationProductInfo(CombineProductInfoRequest request) {
        if (Objects.isNull(request)) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.supplyAsync(() -> {
            try {
                CombineProductInfoResponse response = combinationProductQueryService.queryCombineProductInfo(request);
                return response;
            } catch (Exception e) {
                log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "batchQueryCombinationProductInfo")
                        .message(String.format("batchQueryCombinationProductInfo Error，request: %s", GsonUtils.toJson(request))));
                ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchQueryCombinationProductInfo"), e);
                return null;
            }
        }, sceneAtomServiceExecutorService).exceptionally(e -> {
            log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "batchQueryCombinationProductInfo")
                    .message(String.format("batchQueryCombinationProductInfo Error，request: %s", GsonUtils.toJson(request))));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchQueryCombinationProductInfo"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<FlattedBindRelation> getUserInfoByDpUserId(long dpUserId) {
        try {
            return AthenaInf.getRpcCompletableFuture(userMergeQueryService.getFlattedBindAggregateByDpUserId(dpUserId))
                    .thenApply(res -> {
                        if (res == null || !res.isSuccess() || res.getFlattedAggregateData() == null) {
                            return null;
                        }
                        return res.getFlattedAggregateData();
                    });
        } catch (Exception exception) {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "getRealBindByDpUserId")
                    .message(String.format("getRealBindByDpUserId error, shopId : %d", dpUserId)));
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<Boolean> checkZdcTagByDpShopId(long dpShopId, long zdcTag) {
        return AthenaInf.getRpcCompletableFuture(poiTagGroupDisplayRPCService.checkTagsByDPShopIds(zdcTag, Sets.newHashSet(dpShopId)))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "checkZdcTagByDpShopId")
                            .message(String.format("checkZdcTagByDpShopId error, dpShopId : %d, zdcTag:%s",
                                    dpShopId, zdcTag)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "checkZdcTagByDpShopId"), e);
                    return null;
                }).thenApply(resp -> {
                    if (resp == null || !resp.isSuccess() || resp.getData() == null) {
                        return false;
                    }
                    return resp.getData().get(dpShopId);
                });
    }

    @Override
    public CompletableFuture<List<Long>> findZdcTagByDpShopId(long dpShopId, String bizCode) {
        FindSceneDisplayTagRequest request = new FindSceneDisplayTagRequest();
        request.setShopIds(Lists.newArrayList(dpShopId));
        request.setBizCode(bizCode);
        return AthenaInf.getRpcCompletableFuture(poiTagDisplayRPCService.findSceneDisplayTagOfDp(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "findZdcTagByDpShopId")
                            .message(String.format("findZdcTagByDpShopId error, dpShopId : %d, bizCode:%s",
                                    dpShopId, bizCode)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "findZdcTagByDpShopId"), e);
                    return null;
                }).thenApply(resp -> {
                    if (resp == null || !resp.isSuccess() || resp.getData() == null) {
                        return Collections.emptyList();
                    }
                    List<DisplayTagDto> displayTagDtoList = resp.getData().get(dpShopId);
                    if (CollectionUtils.isEmpty(displayTagDtoList)) {
                        return Collections.emptyList();
                    } else {
                        return displayTagDtoList.stream().map(t -> t.getTagId()).collect(Collectors.toList());
                    }
                });
    }

    @Override
    public CompletableFuture<List<EduTechnicianVideoDTO>> batchQueryEduTechnicianVideoInfo(QueryEduTechnicianVideoForCRequest request) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl.batchQueryEduTechnicianVideoInfo(QueryEduTechnicianVideoForCRequest)");
        return AthenaInf.getRpcCompletableFuture(eduTechnicianVideoQueryFacade.queryData(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "batchQueryEduTechnicianVideoInfo")
                            .message(String.format("batchQueryEduTechnicianVideoInfo error, request : %s",
                                    JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchQueryEduTechnicianVideoInfo"), e);
                    return null;
                }).thenApply(resp -> {
                    if (resp == null || !resp.isSuccess() || resp.getData() == null) {
                        return null;
                    }
                    return resp.getData().getVideos();
                });
    }

    @Override
    public CompletableFuture<QueryDealGroupListResult> queryByDealGroupIds(QueryByDealGroupIdRequest request) {
        try {
            dealGroupQueryServiceAsync.queryByDealGroupIds(request);
            CompletableFuture cf = new CompletableFuture();
            Futures.addCallback(ContextStore.getSettableFuture(), new FutureCallback<QueryDealGroupListResponse>() {
                @Override
                public void onSuccess(@Nullable QueryDealGroupListResponse result) {
                    if (result == null || result.getData() == null) {
                        cf.complete(null);
                        return;
                    }
                    cf.complete(result.getData());
                }

                @Override
                public void onFailure(Throwable t) {
                    log.error("queryByDealGroupIds error", t);
                    cf.complete(null);
                }
            });
            return cf;
        } catch (Exception e) {
            log.error("queryByDealGroupIds error", e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<BatchQueryLeadsInfoRespDTO> batchQueryLeadsInfo(BatchQueryLeadsInfoReqDTO request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return leadsQueryService.batchQueryLeadsInfo(request);
            } catch (Exception e) {
                log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "batchQueryLeadsInfo")
                        .message(String.format("batchQueryLeadsInfo Error，request: %s", GsonUtils.toJson(request))));
                ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchQueryCombinationProductInfo"), e);
                return null;
            }
        }, sceneAtomServiceExecutorService).exceptionally(e -> {
            log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "batchQueryLeadsInfo")
                    .message(String.format("batchQueryLeadsInfo Error，request: %s", GsonUtils.toJson(request))));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchQueryLeadsInfo"), e);
            return null;
        });
    }

    public CompletableFuture<LeadsCountRespDTO> queryShopReservationCount(LeadsCountADTO request) {
        if (Objects.isNull(request)) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.supplyAsync(() -> {
            try {
                return newLeadsCountService.queryCountByCache(request);
            } catch (Exception e) {
                log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "queryShopReservationCount")
                        .message(String.format("queryCountByCache Error，request: %s", GsonUtils.toJson(request))));
                ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryShopReservationCount"), e);
                return null;
            }
        }, sceneAtomServiceExecutorService).exceptionally(e -> {
            log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "queryShopReservationCount")
                    .message(String.format("queryCountByCache Error，request: %s", GsonUtils.toJson(request))));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryShopReservationCount"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<List<SpuProductDTO>> batchQuerySpuRelatedProductIds(QuerySpuProductRequest request) {
        if (Objects.isNull(request)) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.supplyAsync(() -> {
                    try {
                        return spuQueryService.batchQuerySpuProduct(request);
                    } catch (Exception e) {
                        log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "batchQuerySpuRelatedProductIds")
                                .message(String.format("batchQueryProductIds Error，request: %s", GsonUtils.toJson(request))));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchQuerySpuRelatedProductIds"), e);
                        return null;
                    }
                }, sceneAtomServiceExecutorService)
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "batchQuerySpuRelatedProductIds")
                            .message(String.format("batchQueryProductIds Error，request: %s", GsonUtils.toJson(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchQuerySpuRelatedProductIds"), e);
                    return null;
                }).thenApply(result -> {
                    if (result == null || CollectionUtils.isEmpty(result.getProducts())) {
                        return Lists.newArrayList();
                    }
                    return Lists.newArrayList(result.getProducts());
                });
    }

    /**
     * 查询用户手机号码
     *
     * @param userId   用户id
     * @param platform 平台：1点评；2美团
     * @return 手机号码
     */
    @Override
    public CompletableFuture<String> queryMobile(long userId, int platform) {
        if (platform == VCPlatformEnum.DP.getType()) {
            UserAccountDTO userAccountDTO = userAccountService.loadById(userId);
            return userAccountDTO == null ? CompletableFuture.completedFuture(null) : CompletableFuture.completedFuture(userAccountDTO.getMobile());
        } else if (platform == VCPlatformEnum.MT.getType()) {
            MeituanUserInfoDTO meituanUserInfoDTO = meituanUserServiceNew.loadUserByMTUid(userId);
            return meituanUserInfoDTO == null ? CompletableFuture.completedFuture(null) : CompletableFuture.completedFuture(meituanUserInfoDTO.getMobileNo());
        } else {
            return CompletableFuture.completedFuture(null);
        }
    }

    /**
     * 查询用户所有开卡信息
     *
     * @param mobile           手机号码
     * @param platform         平台，点评=1，美团=2
     * @param statusSet        要查询的卡状态
     * @param rightPackageType 要查询的卡类型
     * @return                 卡信息列表
     */
    @Override
    public CompletableFuture<List<MemberCardDTO>> queryFitnessCrossCardListByUserId(String mobile, int platform, Set<Integer> statusSet, int rightPackageType) {
        if (StringUtils.isEmpty(mobile) || (platform != VCPlatformEnum.DP.getType() && platform != VCPlatformEnum.MT.getType())) {
            return CompletableFuture.completedFuture(Collections.emptyList());
        }

        QueryMemberCardRequest request = new QueryMemberCardRequest();
        request.setPhoneNo(mobile);
        request.setPlatform(platform);
        request.setStatus(Lists.newArrayList(statusSet));
        request.setCardTemplateTypeList(Collections.singletonList(rightPackageType));

        MemberCardResponse<List<MemberCardDTO>> response;
        try {
            response = memberCardQueryService.queryMemberCard(request);
        } catch (Exception e) {
            log.error("查询是否开卡失败 mobile is [{}], e is ", mobile, e);
            return null;
        }
        if (!response.isSuccess()) {
            log.error("查询是否开卡失败 mobile is [{}], msg is [{}]", mobile, response.getResultMessage());
            return null;
        }
        if (CollectionUtils.isEmpty(response.getResult())) {
            return CompletableFuture.completedFuture(Collections.emptyList());
        }
        return Optional.of(CompletableFuture.completedFuture(response.getResult())).orElse(CompletableFuture.completedFuture(Collections.emptyList()));
    }

    @Override
    public CompletableFuture<List<ProductSkuDTO>> queryPlatformProducts(ProductSkuBatchQueryRequest request) {
        if (Objects.isNull(request)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        try {
            return AthenaInf.getRpcCompletableFuture(productCacheQueryService.batchQueryProductSku(request))
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "queryPlatformProducts")
                                .message(String.format("queryPlatformProducts Error，request: %s", GsonUtils.toJson(request))));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryPlatformProducts"), e);
                        return null;
                    }).thenApply(result -> {
                        if (result == null || CollectionUtils.isEmpty(result.getProductSkus())) {
                            return Lists.newArrayList();
                        }
                        return Lists.newArrayList(result.getProductSkus());
                    });
        } catch (Exception e) {
            log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "queryPlatformProducts")
                    .message(String.format("queryPlatformProducts Error，request: %s", GsonUtils.toJson(request))));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryPlatformProducts"), e);
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
    }

    @Override
    public CompletableFuture<List<ProductSkuDTO>> batchQueryPlatformProducts(ProductSkuBatchQueryRequest request) {
        if (Objects.isNull(request)) {
            return CompletableFuture.completedFuture(null);
        }
        List<CompletableFuture<List<ProductSkuDTO>>> futureList = Lists.newArrayList();
        List<Long> productIds = request.getProductIds();
        List<List<Long>> productIdsPart = Lists.partition(productIds, PRODUCT_BATCH_SIZE);
        for (List<Long> productIdList : productIdsPart) {
            ProductSkuBatchQueryRequest partRequest = new ProductSkuBatchQueryRequest();
            BeanUtils.copyProperties(request, partRequest);
            partRequest.setProductIds(productIdList);
            CompletableFuture<List<ProductSkuDTO>> future = queryPlatformProducts(partRequest);
            futureList.add(future);
        }
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[]{}));
        List<ProductSkuDTO> result = Lists.newArrayList();
        return allFutures.thenApply(v -> {
            for (CompletableFuture<List<ProductSkuDTO>> future : futureList) {
                result.addAll(future.join());
            }
            return result;
        });
    }

    @Override
    public CompletableFuture<Integer> queryLeadsCount(LeadsCountReqDTO reqDTO) {
        if (Objects.isNull(reqDTO)) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.supplyAsync(() -> {
            try {
                com.sankuai.leads.process.thrift.dto.leads.LeadsCountRespDTO respDTO = leadsOrderQueryService.leadsCount(reqDTO);
                return Objects.isNull(respDTO) ? 0 : respDTO.getCount();
            } catch (Exception e) {
                log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "queryLeadsCount")
                        .message(String.format("queryLeadsCount Error，request: %s", GsonUtils.toJson(reqDTO))));
                ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryLeadsCount"), e);
                return null;
            }
        }, sceneAtomServiceExecutorService)
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "queryLeadsCount")
                            .message(String.format("queryLeadsCount Error，request: %s", GsonUtils.toJson(reqDTO))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryLeadsCount"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<List<ProductRelationDTO>> queryPackProductIdByShop(PackProductByIdRequest request) {
        if (Objects.isNull(request)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        return CompletableFuture.supplyAsync(() -> {
                    try {
                        return packProductQueryService.queryPackProductRelationByIds(request);
                    } catch (Exception e) {
                        log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "queryPackProductIdByShop")
                                .message(String.format("queryPackProductIdByShop Error，request: %s", GsonUtils.toJson(request))));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryPackProductIdByShop"), e);
                        return null;
                    }
                }, sceneAtomServiceExecutorService)
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "queryPackProductIdByShop")
                            .message(String.format("queryPackProductIdByShop error, req: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryPackProductIdByShop"), e);
                    return null;
                }).thenApply(result -> {
                    if (result == null || CollectionUtils.isEmpty(result.getData())) {
                        return Lists.newArrayList();
                    }
                    return result.getData();
                });
    }

    @Override
    public CompletableFuture<IntentionRecognizeResult> recognize(IntentionRecognizeRequest request) {
        if (Objects.isNull(request)) {
            return CompletableFuture.completedFuture(null);
        }
        return AthenaInf.getRpcCompletableFuture(intentionRecognizeService.recognize(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "recognize")
                            .message(String.format("recognize error, req: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "recognize"), e);
                    return null;
                }).thenApply(response -> {
                    if (Objects.isNull(response) || Objects.isNull(response.getData())) {
                        return null;
                    }
                    return response.getData();
                });
    }

    @Override
    public CompletableFuture<TechnicianResp<TechStandardShelfModule>> queryStaffShelf(TechProductShelfRequest reqDTO) {
        if (Objects.isNull(reqDTO)) {
            return CompletableFuture.completedFuture(null);
        }
        return AthenaInf.getRpcCompletableFuture(techProductQueryService.queryProductShelf(reqDTO))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "queryProductShelf")
                            .message(String.format("queryPackProductIdByShop error, req: %s", JsonCodec.encode(reqDTO))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryProductShelf"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<PromoQRCodeResponse<StaffCodeDTO>> getStaffCodeDTOByCodeId(Long codeId) {
        if (Objects.isNull(codeId)) {
            return CompletableFuture.completedFuture(null);
        }
        return AthenaInf.getRpcCompletableFuture(promoQRCodeCService.getStaffCodeDTOByCodeId(codeId))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getStaffCodeDTOByCodeId")
                            .message(String.format("getStaffCodeDTOByCodeId error, req: %s", JsonCodec.encode(codeId))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "getStaffCodeDTOByCodeId"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<CommonResponse<List<DistributorActivityDTO>>> queryJoinDistributorActivity(DistributorActivityRequest request) {
        if (Objects.isNull(request)) {
            return CompletableFuture.completedFuture(null);
        }
        return AthenaInf.getRpcCompletableFuture(distributorActivityService.queryJoinDistributorActivity(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "queryJoinDistributorActivity")
                            .message(String.format("queryJoinDistributorActivity error, req: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryJoinDistributorActivity"), e);
                    return null;
                });
    }

    @Override
    public CompletableFuture<InterestCalculateResponse> interestCalculate(InterestCalculateRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            Tracer.putContext("INTEREST_LITESET_NAME", "DAOZONG");
            try {
                return interestCalculateService.calculate(request);
            } catch (Exception e) {
                log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "interestCalculate")
                        .message(String.format("interestCalculate Error，requestList: %s", GsonUtils.toJson(request))));
                ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "interestCalculate"), e);
                return null;
            }
        }, sceneAtomServiceExecutorService).exceptionally(e -> {
            log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "interestCalculate")
                    .message(String.format("interestCalculate Error，request: %s", GsonUtils.toJson(request))));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "interestCalculate"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<List<InterestBatchCalculateResponse>> batchInterestCalculate(List<InterestCalculateRequest> requestList) {
        if(CollectionUtils.isEmpty(requestList)){
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        return CompletableFuture.supplyAsync(() -> {
            Tracer.putContext("INTEREST_LITESET_NAME", "DAOZONG");
            try {
                return interestCalculateService.batchCalculate(requestList);
            } catch (Exception e) {
                log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "batchInterestCalculate")
                        .message(String.format("batchInterestCalculate Error，requestList: %s", GsonUtils.toJson(requestList))));
                ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchInterestCalculate"), e);
                return null;
            }
        }, sceneAtomServiceExecutorService).exceptionally(e -> {
            log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "batchInterestCalculate")
                    .message(String.format("batchInterestCalculate Error，request: %s", GsonUtils.toJson(requestList))));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "batchInterestCalculate"), e);
            return null;
        });
    }

    public CompletableFuture<String> queryCrossTitleByBackCat(List<Integer> backCatIds) {
        if (CollectionUtils.isEmpty(backCatIds) || backCatIds.size() < 2) {
            return CompletableFuture.completedFuture(StringUtils.EMPTY);
        }
        try {
            return AthenaInf.getRpcCompletableFuture(crossCatRecommendService.getEduCrossRecSubjectsBybackCatIds(backCatIds))
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("scene", "compositeAtomService")
                                .putTag("method", "queryCrossTitleByBackCat")
                                .message(String.format("queryCrossTitleByBackCat error, req: %s", JsonCodec.encode(backCatIds))));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryCrossTitleByBackCat"), e);
                        return null;
                    }).thenApply(response -> {
                        if (response == null
                                || !response.isSuccess()
                                || response.getCode() != 200
                                || CollectionUtils.isEmpty(response.getContent())) {
                            return StringUtils.EMPTY;
                        }
                        return String.join("/", response.getContent());
                    });
        } catch (Exception e) {
            log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "queryCrossTitleByBackCat")
                            .message(String.format("queryCrossTitleByBackCat Error，request: %s", JsonCodec.encode(backCatIds)))
                    , e);
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryCrossTitleByBackCat"), e);
            return CompletableFuture.completedFuture(StringUtils.EMPTY);
        }
    }

    @Override
    public CompletableFuture<com.dianping.joygeneral.api.thirdpart.dto.Response<Boolean>> queryAutoOpenTable(QueryAutoOpenTableReqDTO request) {
        if (Objects.isNull(request)) {
            return CompletableFuture.completedFuture(null);
        }
        return AthenaInf.getRpcCompletableFuture(selfHelpBilliardService.queryAutoOpenTable(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "queryAutoOpenTable")
                            .message(String.format("queryAutoOpenTable error, req: %s", JsonCodec.encode(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryAutoOpenTable"), e);

                    return null;
                });
    }

    @Override
    public CompletableFuture<NewReserveSubmissionPageWhiteShopCheckRespDTO> querySelfOperatedCleaningShopInfo(Long dpShopId) {
        if (Objects.isNull(dpShopId) || dpShopId <= 0L) {
            return null;
        }
        NewReserveSubmissionPageWhiteShopCheckRequest request = new NewReserveSubmissionPageWhiteShopCheckRequest();
        request.setDpShopId(dpShopId);
        return AthenaInf.getRpcCompletableFuture(reserveConfigQueryService.checkIsNewReserveSubmissionPageWhiteShop(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "querySelfOperatedCleaningShopInfo")
                            .message(String.format("querySelfOperatedCleaningShopInfo error, dpShopId: %s", dpShopId)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "querySelfOperatedCleaningShopInfo"), e);
                    return null;
                }).thenApply(response -> response);
    }

    @Override
    public CompletableFuture<Boolean> queryIsSupportStrictReserve(Long dpShopId) {
        if (Objects.isNull(dpShopId) || dpShopId <= 0L) {
            return CompletableFuture.completedFuture(false);
        }
        StrictReserveShopSupportCheckRequest request = new StrictReserveShopSupportCheckRequest();
        request.setDpShopId(dpShopId);
        return AthenaInf.getRpcCompletableFuture(reserveConfigQueryService.checkShopIsSupportStrictReserve(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService")
                            .putTag("method", "queryIsSupportStrictReserve")
                            .message(String.format("queryIsSupportStrictReserve error, dpShopId: %s", dpShopId)));
                    ExceptionTracer.addException(
                            String.format("%s_%s", "CompositeAtomServiceImpl", "queryIsSupportStrictReserve"), e);
                    return null;
                }).thenApply(response -> response != null && response.getCheckRes());
    }

    @Override
    public CompletableFuture<QueryResultSync> queryByKey(KeyQueryParamSync param) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return queryDataSyncService.queryByKey(param);
            } catch (Exception e) {
                log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "queryByKey")
                        .message(String.format("queryByKey Error，requestList: %s", GsonUtils.toJson(param))));
                ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryByKey"), e);
                return null;
            }
        }, sceneAtomServiceExecutorService).exceptionally(e -> {
            log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "queryByKey")
                    .message(String.format("queryByKey Error，request: %s", GsonUtils.toJson(param))));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryByKey"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<Boolean> asyncFlashOperatorShelfConfig(OperatorShelfConfigFlashRequest request) {
        return AthenaInf.getRpcCompletableFuture(unifiedShelfOperatorMService.asyncFlashConfig(request))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "asyncFlashOperatorShelfConfig")
                            .message(String.format("asyncFlashOperatorShelfConfig error, dpShopId: %s", request)));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "asyncFlashOperatorShelfConfig"), e);
                    return null;
                }).thenApply(response -> true);
    }

    @Override
    public CompletableFuture<Boolean> queryIsMiniprogramShop(Long shopId, int platform) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                if (Objects.isNull(shopId) || platform <= 0) {
                    return false;
                }
                ApiResponse<Boolean> response = miniprogramLinkService.isMiniprogramShop(shopId, platform);
                if (response == null || response.getData() == null) {
                    return false;
                }
                return response.getData();
            } catch (Exception e) {
                log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "queryIsMiniprogramShop")
                        .message(String.format("queryIsMiniprogramShop Error, shopId: %s, platform: %s", shopId, platform)), e);
                ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryIsMiniprogramShop"), e);
                return false;
            }
        }, sceneAtomServiceExecutorService).exceptionally(e -> {
            log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "queryIsMiniprogramShop")
                    .message(String.format("batchInterestCalculate Error, shopId: %s, platform: %s", shopId, platform)));
            ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryIsMiniprogramShop"), e);
            return false;
        });
    }


    @Override
    public CompletableFuture<BNPLExposureResponse> queryIsAfterPay(BNPLExposureRequest request) {
        return CompletableFuture.supplyAsync(() -> {
                    try {
                        return bnplAccessThriftService.exposure(request);
                    } catch (Exception e) {
                        log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "queryIsAfterPay")
                                .message(String.format("queryIsAfterPay Error，request: %s", GsonUtils.toJson(request))));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryIsAfterPay"), e);
                        return null;
                    }
                }, sceneAtomServiceExecutorService)
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "queryIsAfterPay")
                            .message(String.format("queryIsAfterPay Error，request: %s", GsonUtils.toJson(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "queryIsAfterPay"), e);
                    return null;
                }).thenApply(result -> {
                    if (result == null || Objects.isNull(result.getData())) {
                        return null;
                    }
                    return result;
                });
    }

    @Override
    public CompletableFuture<Boolean> queryShopIsAutoVerify(ShopAutoVerifyQueryRequest request) {
        if (request == null) {
            return CompletableFuture.completedFuture(false);
        }

        return CompletableFuture.supplyAsync(() -> {
                    try {
                        RemoteResponse<Boolean> response = promoCodeOrderAutoVerifyService.isShopAutoVerify(request);
                        if (response == null || !response.isSuccess()) {
                            return false;
                        }
                        return response.getData();
                    } catch (Exception e) {
                        log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "isShopAutoVerify")
                                .message(String.format("isShopAutoVerify Error，request: %s", GsonUtils.toJson(request))));
                        ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "isShopAutoVerify"), e);
                        return false;
                    }
                }, sceneAtomServiceExecutorService)
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "isShopAutoVerify")
                            .message(String.format("isShopAutoVerify Error，request: %s", GsonUtils.toJson(request))));
                    ExceptionTracer.addException(String.format("%s_%s", "CompositeAtomServiceImpl", "isShopAutoVerify"), e);
                    return false;
                });
    }
}
