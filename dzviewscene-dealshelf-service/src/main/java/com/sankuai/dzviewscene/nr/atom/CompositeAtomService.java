package com.sankuai.dzviewscene.nr.atom;

import com.dianping.baby.timeLimitTg.dto.BabyTimeLimeTgSpikeDTO;
import com.dianping.deal.attribute.dto.DealGroupAttributeDTO;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.deal.shop.dto.DealGroupShop;
import com.dianping.deal.shop.dto.DealGroupShopSearchRequest;
import com.dianping.deal.shop.dto.ShopOnlineDealGroup;
import com.dianping.deal.shop.dto.ShopOnlineDealGroupRequest;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailDto;
import com.dianping.dztrade.refund.api.bean.ShopRefundConfigDTO;
import com.dianping.dztrade.refund.api.request.ShopRefundConfigRequest;
import com.dianping.general.unified.search.api.generalsearchv2.dto.DealGroupDto;
import com.dianping.general.unified.search.api.generalsearchv2.dto.ProductSearchIdDto;
import com.dianping.general.unified.search.api.generalsearchv2.request.DealGroupSearchRequest;
import com.dianping.general.unified.search.api.generalsearchv2.request.ProductSearchRequestV2;
import com.dianping.general.unified.search.api.generalsearchv2.response.DealGroupSearchResponse;
import com.dianping.general.unified.search.api.sku.ProductSearchRequest;
import com.dianping.general.unified.search.api.sku.ProductSearchResponse;
import com.dianping.gis.remote.dto.HotRegionStationDTO;
import com.dianping.gis.remote.dto.RegionInfoDTO;
import com.dianping.gis.remote.enums.RegionType;
import com.dianping.gm.marketing.times.card.api.dto.CardResponse;
import com.dianping.gm.marketing.times.card.api.dto.TimesCardDetailExposureDTO;
import com.dianping.gmkt.activity.api.dto.BatchQueryShopActivityRequest;
import com.dianping.gmkt.activity.api.dto.BatchQueryShopActivityResponse;
import com.dianping.gmkt.activity.api.request.QuerySecKillDealByPoiRequest;
import com.dianping.gmkt.activity.api.response.QuerySecKillDealByPoiResponse;
import com.dianping.gmkt.event.api.distribution.dto.DistributorActivityDTO;
import com.dianping.gmkt.event.api.distribution.dto.DistributorActivityRequest;
import com.dianping.gmkt.event.api.model.CommonResponse;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeResponse;
import com.dianping.gmkt.event.api.promoqrcode.dto.staffcode.StaffCodeDTO;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.home.service.api.ServiceResponse;
import com.dianping.joy.booking.api.generalpool.dto.GeneralPoolInfoDTO;
import com.dianping.joy.booking.api.generalpool.dto.PageInfo;
import com.dianping.joy.booking.api.generalpool.request.GeneralQueryPoolInfoRequest;
import com.dianping.joygeneral.api.sharerelation.dto.ResponseDTO;
import com.dianping.joygeneral.api.thirdpart.dto.QueryAutoOpenTableReqDTO;
import com.dianping.ktv.shop.api.protocol.ShopResponse;
import com.dianping.ktv.shop.coop.dto.CoopShopConfigDTO;
import com.dianping.ktv.shop.coop.request.CoopShopConfigQueryRequest;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.dianping.pay.promo.display.api.dto.BatchQueryPromoDisplayRequest;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.product.shelf.common.dto.*;
import com.dianping.product.shelf.common.dto.activity.ActivityShelfToCDTO;
import com.dianping.product.shelf.common.request.*;
import com.dianping.product.shelf.common.request.activities.ProductQueryByShopIdToCRequest;
import com.dianping.shopremote.remote.dto.ShopDTO;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.dianping.tpfun.product.api.sku.abstracts.dto.SpuTypeAbstractDTO;
import com.dianping.tpfun.product.api.sku.aggregate.dto.SimpleShopDTO;
import com.dianping.tpfun.product.api.sku.aggregate.dto.StandardProductDTO;
import com.dianping.tpfun.product.api.sku.aggregate.dto.StandardProductPageDTO;
import com.dianping.tpfun.product.api.sku.aggregate.request.GetProductNearestShopReq;
import com.dianping.tpfun.product.api.sku.aggregate.request.GetStandardProductPriceRequest;
import com.dianping.tpfun.product.api.sku.model.Product;
import com.dianping.tpfun.product.api.sku.model.ProductItem;
import com.dianping.tpfun.product.api.sku.platform.resource.dto.ResourceInfoDTO;
import com.dianping.tpfun.product.api.sku.platform.resource.request.BatchQueryResourceInfoRequest;
import com.dianping.tpfun.product.api.sku.request.GetProductItemByProductRequest;
import com.dianping.tpfun.product.api.sku.request.QueryShopProductRequest;
import com.dianping.ugc.proxyService.remote.dto.AnonymousUserInfo;
import com.dianping.ugc.proxyService.remote.dto.UserRequestPara;
import com.dianping.userremote.base.dto.UserDTO;
import com.dianping.wed.business.weddingshopbrief.dto.WeddingShopBriefInfoDTO;
import com.maoyan.shplatform.content.api.mthomepage.base.ProjectContentDTO;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mpproduct.general.trade.api.dto.SpuProductDTO;
import com.meituan.mpproduct.general.trade.api.request.CitySpuIdQueryRequest;
import com.meituan.mpproduct.general.trade.api.request.PoiSpuIdQueryRequest;
import com.meituan.mpproduct.general.trade.api.request.QuerySpuProductRequest;
import com.meituan.service.deal.dealbasic.thrift.DealBasicInfo;
import com.meituan.service.mobile.group.geo.bean.AreaInfo;
import com.meituan.service.mobile.group.geo.bean.SubwayLine;
import com.meituan.service.mobile.group.geo.bean.SubwayStation;
import com.sankuai.beautycontent.beautylaunchapi.model.dto.drama.DramaPageReviewDetailDto;
import com.sankuai.beautycontent.beautylaunchapi.model.dto.gw.AggOverallReviewModel;
import com.sankuai.beautycontent.beautylaunchapi.model.dto.gw.request.ContentBaseRequest;
import com.sankuai.beautycontent.beautylaunchapi.model.request.BeautyGoodsReviewRequest;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.dto.EduTechnicianVideoDTO;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.request.QueryEduTechnicianVideoForCRequest;
import com.sankuai.beautycontent.intention.dto.IntentionResultResponse;
import com.sankuai.beautycontent.intention.request.IntentionRequest;
import com.sankuai.clr.content.process.thrift.dto.leads.req.BatchQueryLeadsInfoReqDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.resp.BatchQueryLeadsInfoRespDTO;
import com.sankuai.dzcard.fulfill.api.dto.MemberCardDTO;
import com.sankuai.dzcard.joycard.navigation.api.dto.LoadProductShelfJoyCardReqDTO;
import com.sankuai.dzcard.joycard.navigation.api.dto.ProductShelfJoyCardDTO;
import com.sankuai.dzcard.navigation.api.dto.CardHoldStatusDTO;
import com.sankuai.dzcard.navigation.api.dto.FindCardHoldStatusReqDTO;
import com.sankuai.dzim.cliententry.dto.ClientEntryDTO;
import com.sankuai.dzim.cliententry.dto.ClientEntryReqDTO;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dztheme.generalproduct.req.GeneralProductRequest;
import com.sankuai.dztheme.generalproduct.res.GeneralProductResult;
import com.sankuai.dztheme.shop.vo.ShopThemePlanRequest;
import com.sankuai.dztheme.spuproduct.req.SpuRequest;
import com.sankuai.dztheme.spuproduct.res.SpuDTO;
import com.sankuai.dzviewscene.intention.service.req.IntentionRecognizeRequest;
import com.sankuai.dzviewscene.intention.service.res.IntentionRecognizeResult;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.req.OperatorShelfConfigFlashRequest;
import com.sankuai.fbi.lifeevent.reserverpcapi.dto.NewReserveSubmissionPageWhiteShopCheckRespDTO;
import com.sankuai.feitianplus.data.onedata.api.thrift.domain.KeyQueryParamSync;
import com.sankuai.feitianplus.data.onedata.api.thrift.domain.QueryResultSync;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.request.BNPLExposureRequest;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.response.BNPLExposureResponse;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.health.sc.api.thrift.SpuTagProductParam;
import com.sankuai.health.sc.api.thrift.SpuTagResponse;
import com.sankuai.interest.core.thrift.remote.dto.InterestBatchCalculateResponse;
import com.sankuai.interest.core.thrift.remote.dto.InterestCalculateRequest;
import com.sankuai.interest.core.thrift.remote.dto.InterestCalculateResponse;
import com.sankuai.leads.count.thrift.dto.LeadsCountADTO;
import com.sankuai.leads.count.thrift.dto.LeadsCountRespDTO;
import com.sankuai.leads.process.thrift.dto.leads.LeadsCountReqDTO;
import com.sankuai.map.open.platform.api.transformcitytype.TransformCityTypeResponse;
import com.sankuai.mdp.dzshoplist.rank.api.request.EntityScenesRankInfoRequest;
import com.sankuai.mdp.dzshoplist.rank.api.response.dto.RankEntityScenesDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.ShopAutoVerifyQueryRequest;
import com.sankuai.merchantcard.timescard.exposure.req.QueryTimesCardByProductIdRequest;
import com.sankuai.merchantcard.timescard.exposure.req.QueryTimesCardByShopRequest;
import com.sankuai.mpcontent.feeds.thrift.dto.request.QueryIsLivingReqDTO;
import com.sankuai.mpcontent.feeds.thrift.dto.response.QueryIsLivingRespDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.req.ListAllEffectMemberGrouponByTenantIdReqDTO;
import com.sankuai.mpmctmember.process.thrift.nib.user.dto.resp.ListAllEffectMemberGrouponByTenantIdRespDTO;
import com.sankuai.mppack.api.client.request.CombineProductInfoRequest;
import com.sankuai.mppack.api.client.request.ProductInfoRequest;
import com.sankuai.mppack.api.client.response.CombineProductInfoResponse;
import com.sankuai.mppack.api.client.response.ProductInfoResponse;
import com.sankuai.mppack.product.client.query.dto.ProductRelationDTO;
import com.sankuai.mppack.product.client.query.request.PackProductByIdRequest;
import com.sankuai.mpproduct.trade.api.model.ProductSkuDTO;
import com.sankuai.mpproduct.trade.api.request.ProductSkuBatchQueryRequest;
import com.sankuai.mpproduct.trade.api.request.ResourceBatchQueryRequest;
import com.sankuai.mpproduct.trade.api.response.ResourceBatchQueryResponse;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.dto.DpPoiUuidRequest;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.swan.udqs.api.QueryData;
import com.sankuai.swan.udqs.api.Result;
import com.sankuai.swan.udqs.api.SwanParam;
import com.sankuai.technician.trade.api.product.dto.TechStandardShelfModule;
import com.sankuai.technician.trade.api.product.request.TechProductShelfRequest;
import com.sankuai.user.collection.client.CollTypeEnum;
import com.sankuai.web.deal.deallistapi.thrift.dealinfo.DealRequest;
import com.sankuai.web.deal.deallistapi.thrift.dealinfo.DealResponse;
import com.sankuai.wpt.user.merge.query.thrift.message.BindRelationResp;
import com.sankuai.wpt.user.merge.query.thrift.message.FlattedBindRelation;
import org.apache.thrift.TException;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * 场景通用AtomService
 *
 * <AUTHOR>
 */
public interface CompositeAtomService {

    /**
     * 批量查询资源信息
     *
     * @param
     * @return
     */
    CompletableFuture<List<ResourceInfoDTO>> batchQueryResourceInfo(BatchQueryResourceInfoRequest batchQueryResourceInfoRequest);

    /**
     * 批量查询团单属性
     *
     * @param dealGroupIds 点评团单
     * @param attributes   属性key
     * @return
     */
    CompletableFuture<Map<Integer, DealGroupAttributeDTO>> batchGetDealAttribute(List<Integer> dealGroupIds, List<String> attributes);

    CompletableFuture<List<ShopM>> multiShopMListNew(ShopThemePlanRequest request);

    /**
     * 根据商户获取活动团单信息
     *
     * @param request
     * @return
     */
    CompletableFuture<Response<ActivityShelfToCDTO>> getActivityShelfResponseByShopId(ProductQueryByShopIdToCRequest request);

    /**
     * 获取海马信息
     *
     * @param haimaRequest
     * @return
     */
    CompletableFuture<HaimaResponse> getHaiMaResponse(HaimaRequest haimaRequest);

    /**
     * 获取团单最近适用门店信息
     *
     * @param request
     * @return
     */
    CompletableFuture<DealGroupShop> getDealNearestShopResult(DealGroupShopSearchRequest request);

    /**
     * 根据传入的shopId,判断是否是合作商户
     *
     * @param shopId 点评商户ID
     * @return
     */
    CompletableFuture<ServiceResponse<Boolean>> getIsCooperativeResponse(int shopId);

    CompletableFuture<ServiceResponse<Boolean>> getIsCooperativeResponseL(Long shopId);

    /**
     * 通过点评门店ID查询门店信息
     *
     * @param shopId
     * @return
     */
    CompletableFuture<ShopDTO> loadShop(int shopId);

    /**
     * 通过团单id查询团单二级类目id
     *
     * @param dealIds
     * @return
     */
    public CompletableFuture<Map<Integer, Integer>> batchGetDealIdCategoryIdMap(List<Integer> dealIds);

    /**
     * 通过ShopUuid查询门店信息
     *
     * @param shopUuid
     * @return
     */
    CompletableFuture<ShopDTO> loadShop(String shopUuid);

    CompletableFuture<List<DpPoiDTO>> findShopsByUuids(DpPoiUuidRequest dpPoiUuidRequest);

    /**
     * 根据美团ID获取对应的点评ID;如果是多个，第一个是主门店ID
     *
     * @param mtShopId
     * @return
     */
    CompletableFuture<List<Integer>> batchGetDpByMtId(int mtShopId);

    CompletableFuture<List<Long>> batchGetDpByMtIdL(Long mtShopId);

    /**
     * 根据点评ID获取对应的美团ID;如果是多个，第一个是主门店ID
     *
     * @param dpShopId
     * @return
     */
    CompletableFuture<List<Integer>> batchGetMtShopIdByDp(int dpShopId);

    CompletableFuture<List<Long>> batchGetMtShopIdByDpL(Long dpShopId);

    /**
     * dp转mt门店id
     *
     * @param dpPoiId
     * @return
     */
    CompletableFuture<Long> getMtByDpPoiIdL(long dpPoiId);

    /**
     * mt转dp门店id
     *
     * @param mtPoiId
     * @return
     */
    CompletableFuture<Long> getDpByMtPoiIdL(long mtPoiId);

    /**
     * 查询long类型点评poi信息
     *
     * @param dpPoiRequest
     * @return
     */
    CompletableFuture<List<DpPoiDTO>> findShopsByDpShopIds(DpPoiRequest dpPoiRequest);


    /**
     * 查询商品平台的导航信息
     *
     * @param navRequest
     * @return
     */
    CompletableFuture<Response<ShelfDTO>> multiGetShelfNav(ShelfRequest navRequest);

    /**
     * 查询商品平台的导航下挂的商品数据
     *
     * @param request
     * @return
     */
    CompletableFuture<Response<ShelfNavTabProductList>> multiGetProductList(ShelfNavTabProductRequest request);

    /**
     * 指定门店ID批量查询商品的基本信息
     *
     * @param request
     * @return
     */
    CompletableFuture<Map<Integer, List<Product>>> mGetBaseProductsByShop(QueryShopProductRequest request);

    CompletableFuture<Map<Long, List<Product>>> mGetBaseProductsByLongShop(QueryShopProductRequest request);

    /**
     * 批量查询指定商品的 sku 信息
     *
     * @param request
     * @return
     */
    CompletableFuture<Map<Integer, List<ProductItem>>> mGetProductItemsByProducts(GetProductItemByProductRequest request);

    /**
     * 泛商品主题通用查询
     *
     * @param generalProductRequest
     * @return
     */
    CompletableFuture<GeneralProductResult> queryGeneralProductTheme(GeneralProductRequest generalProductRequest);

    /**
     * 美团门店转点评门店 批量转换
     *
     * @param mtShopIds mtShopIds
     * @return
     */
    CompletableFuture<Map<Integer, List<Integer>>> batchGetDpByMtIds(List<Integer> mtShopIds);

    CompletableFuture<Map<Long, List<Long>>> batchGetDpByMtIdsL(List<Long> mtShopIds);

    /**
     * 团单主题查询
     *
     * @param dealProductRequest
     * @return
     */
    CompletableFuture<DealProductResult> queryDealProductTheme(DealProductRequest dealProductRequest);

    /**
     * 根据点评商户ID批量获取周期团单信息
     *
     * @param shopOnlineDealGroupRequest
     * @return 商户ID对应的团单信息 批量限制20 单个商户最多返回50个团单
     */
    CompletableFuture<Map<Integer, ShopOnlineDealGroup>> batchGetShopOnlineDealGroups(ShopOnlineDealGroupRequest shopOnlineDealGroupRequest);

    /**
     * 根据点评商户ID批量获取在线团单信息，Long类型的ShopId
     *
     * @param shopOnlineDealGroupRequest
     * @return 商户ID对应的团单信息 批量限制20 单个商户最多返回50个团单
     */
    CompletableFuture<Map<Long, ShopOnlineDealGroup>> batchGetLongShopOnlineDealGroups(ShopOnlineDealGroupRequest shopOnlineDealGroupRequest);

    /**
     * 查询货架的商户推荐商品
     *
     * @param request 只能传点评商户Id
     * @return 如果有，返回推荐商品列表
     */
    CompletableFuture<Response<List<ShelfShopRecProduct>>> multiGetShopRecommendProducts(ShelfShopRecProductRequest request);

    /**
     * 根据点评Id查询团单Id映射
     *
     * @param dpDealIds
     * @return
     */
    CompletableFuture<List<IdMapper>> batchGetDealIdByDpId(List<Integer> dpDealIds);

    /**
     * 根据美团Id查询团单Id映射
     *
     * @param mtDealIds
     * @return
     */
    CompletableFuture<List<IdMapper>> batchGetDealIdByMtId(List<Integer> mtDealIds);

    /**
     * 点评团单Id映射美团团单id
     * 使用sdk，性能更好，单次最多100个
     * @param dpDealIds
     * @return
     */
    CompletableFuture<Map<Long, Long>> getMtDealIdByDp(List<Long> dpDealIds);

    /**
     * 获取团购/预订玩乐卡模块信息
     *
     * @param request
     * @return
     */
    CompletableFuture<ProductShelfJoyCardDTO> getCardModuleForProductShelf(LoadProductShelfJoyCardReqDTO request);

    /**
     * 获取用户持卡信息
     *
     * @param request
     * @return
     */
    CompletableFuture<CardHoldStatusDTO> getCardHoldStatus(FindCardHoldStatusReqDTO request);

    /**
     * 获取商户关联的曝光的营销活动信息
     *
     * @param request
     * @return
     */
    CompletableFuture<BatchQueryShopActivityResponse> getShopActivity(BatchQueryShopActivityRequest request);

    CompletableFuture<Map<Integer, List<Long>>> findShopIdsByProductIdsPoiMigrate(List<Integer> productIds);

    /**
     * 获取门店退款配置
     *
     * @param shopRefundConfigRequest 请求对象
     * @return 门店退款配置信息
     */
    CompletableFuture<ShopRefundConfigDTO> loadShopRefundConfig(ShopRefundConfigRequest shopRefundConfigRequest);

    /**
     * 根据商户Id + 平台获取点评商户Id
     *
     * @param shopId
     * @return
     */
    CompletableFuture<Integer> loadDpShopId(int shopId, int platform);

    CompletableFuture<Long> loadDpShopIdPoiMigrate(long shopId, int platform);

    /**
     * 根据商户Id + 平台获取美团商户Id
     *
     * @param shopId
     * @return
     */
    CompletableFuture<Long> loadMtShopId(long shopId, int platform);

    CompletableFuture<Long> loadMtShopIdPoiMigrate(long shopId, int platform);

    /**
     * 根据美团城市Id获取点评城市Id
     *
     * @param mtCityId
     * @return
     */
    CompletableFuture<Integer> getDpCityIdByMt(Integer mtCityId);

    /**
     * 根据点评城市Id获取美团城市Id
     *
     * @param dpCityId
     * @return
     */
    CompletableFuture<Integer> getMtCityIdByDp(int dpCityId);

    /**
     * 搜索商品
     *
     * @param request
     * @return
     */
    CompletableFuture<ProductSearchResponse<com.dianping.general.unified.search.api.sku.model.Product>> commonSearchProduct(ProductSearchRequest request);

    /**
     * 按项目类型召回商品信息
     *
     * @param productSearchRequestV2
     * @return
     */
    CompletableFuture<ProductSearchResponse<ProductSearchIdDto>> searchProductServiceV2(ProductSearchRequestV2 productSearchRequestV2);

    /**
     * 获取dp侧ugc
     *
     * @param dpShopId
     * @param stars
     * @param page
     * @param pageSize
     * @return
     */
    CompletableFuture<Object> multiGetDpReviewByShopId(int dpShopId, List<Integer> stars, int page, int pageSize);

    CompletableFuture<Object> multiGetDpReviewByShopIdPoiMigrate(long dpShopId, List<Integer> stars, int page, int pageSize);

    /**
     * 获取点评侧Ugc的数量
     *
     * @param dpShopId
     * @return
     */
    CompletableFuture<Map<String, Integer>> findDpReviewCountAllByStar(int dpShopId);

    CompletableFuture<Map<String, Integer>> findDpReviewCountAllByStarPoiMigrate(Long dpShopId);

    /**
     * 获取点评侧用户信息
     *
     * @param dpUserIds
     * @return
     */
    CompletableFuture<Map<Long, UserDTO>> batchGetUserMap(List<Long> dpUserIds);

    /**
     * 点评侧，根据reviewId查询匿名评价的用户信息，如果不是匿名评价不返回
     *
     * @param mainIds         匿名评价的reviewId
     * @param userRequestPara 查询匿名评价作者信息的参数
     * @return
     */
    CompletableFuture<Map<Integer, AnonymousUserInfo>> batchGetAnonymousUserInfo(List<Integer> mainIds, UserRequestPara userRequestPara);

    /**
     * 获取泛商品的适用商户列表
     *
     * @param productIds
     * @return
     */
    CompletableFuture<Map<Integer, List<Integer>>> batchGetAvailableShopIds(List<Integer> productIds);

    /**
     * 获取商户IM的C端入口是否展示以及跳转链接
     *
     * @param reqDTO
     * @return
     */
    CompletableFuture<ClientEntryDTO> getImInfo(ClientEntryReqDTO reqDTO);

    /**
     * 查找商品最近门店
     *
     * @param req
     * @return
     */
    CompletableFuture<Map<Integer, SimpleShopDTO>> batchGetProductNearestShop(GetProductNearestShopReq req);

    /**
     * 批量获取商户推荐商品
     *
     * @param batchShelfShopRecommendRequest batchShelfShopRecommendRequest
     * @return
     */
    CompletableFuture<Map<Long, List<ShelfShopRecProduct>>> multiGetShopRecommendProducts(BatchShelfShopRecommendRequest batchShelfShopRecommendRequest);

    /**
     * 根据点评shopId获取商品摘要
     *
     * @param doShopIds
     * @return
     */
    CompletableFuture<Map<Integer, List<SpuTypeAbstractDTO>>> batchGetSpuTypeAbstractsByShopIds(List<Integer> doShopIds);

    CompletableFuture<Map<Long, List<SpuTypeAbstractDTO>>> batchGetSpuTypeAbstractsByLongShopIds(List<Long> doShopIds);

    /**
     * 查询预订规则
     *
     * @param request
     * @return
     */
    CompletableFuture<ShopResponse<List<CoopShopConfigDTO>>> findShopConfigList(CoopShopConfigQueryRequest request);

    /**
     * 批量查询商户信息
     *
     * @param shopIds shopIds
     * @return
     */
    CompletableFuture<List<ShopDTO>> findShops(List<Integer> shopIds);

    CompletableFuture<CardResponse<Map<Integer, TimesCardDetailExposureDTO>>> batchQueryTimesCards(QueryTimesCardByProductIdRequest queryTimesCardByProductIdRequest);

    CompletableFuture<CardResponse<Map<Long, List<TimesCardDetailExposureDTO>>>> batchQueryTimesCardsByShop(QueryTimesCardByShopRequest timesCardByShopRequest);

    /**
     * 批量实时查询立减优惠信息
     *
     * @param batchQueryPromoDisplayRequest
     * @return
     */
    CompletableFuture<Map<com.dianping.pay.promo.display.api.dto.Product, List<PromoDisplayDTO>>> batchQueryPromoDisplay(BatchQueryPromoDisplayRequest batchQueryPromoDisplayRequest);

    /**
     * 召回在拼场次
     *
     * @param request
     * @return
     */
    CompletableFuture<PageInfo<GeneralPoolInfoDTO>> pageFindPoolInfo(GeneralQueryPoolInfoRequest request);

    /**
     * 根据订单号获取订单
     *
     * @param unifiedOrderId
     * @return
     */
    CompletableFuture<UnifiedOrderWithId> getUnifiedOrder(String unifiedOrderId);

    CompletableFuture<RecommendResult<RecommendDTO>> getRecommendResult(RecommendParameters recommendParameters);

    /**
     * 通过美团UserId获取用户ID对象
     *
     * @param mtUserIds
     * @return
     */
    CompletableFuture<List<com.sankuai.wpt.user.retrieve.thrift.message.UserModel>> findUserInfoByMtUserIds(List<Long> mtUserIds, com.sankuai.wpt.user.retrieve.thrift.message.UserFields fields);

    /**
     * 根据城市ID获取点评侧子行政区/子商圈信息
     *
     * @param
     * @return
     */
    CompletableFuture<List<RegionInfoDTO>> multiGetDpChildRegionListByCityId(int cityId, RegionType regionType, int skip, int limit);

    /**
     * 获取点评侧热门商圈信息
     *
     * @param dpCityId
     * @return
     */
    CompletableFuture<HotRegionStationDTO> multiGetDpHotRegionList(int dpCityId);


    /**
     * 获取美团侧商圈信息
     *
     * @param mtCityId
     * @return
     */
    CompletableFuture<List<AreaInfo>> multiGetMtAreaInfoByCityId(int mtCityId);

    /**
     * 批量获取点评侧商圈信息
     *
     * @param dpRegionIdList
     * @return
     */
    CompletableFuture<List<RegionInfoDTO>> batchGetDpRegionInfoList(List<Integer> dpRegionIdList);

    /**
     * 批量获取猫眼-展览演出商品列表详细
     *
     * @param projectIdList 商品列表
     * @return 商品详细
     */
    CompletableFuture<List<ProjectContentDTO>> mGetMaoyanProductDetail(List<Integer> projectIdList);

    /**
     * 通过ID查询SPU
     *
     * @param spuId
     * @return
     */
    CompletableFuture<StandardProductPageDTO> getSpuById(long spuId);

    /**
     * 获取标品适用门店
     *
     * @param spuId
     * @param cityId
     * @return
     */
    CompletableFuture<StandardProductDTO> getSpuShop(long spuId, int cityId);

    /**
     * @param request
     * @return
     */
    CompletableFuture<StandardProductDTO> getSpuPrice(GetStandardProductPriceRequest request);

    /**
     * SPU标品销量查询
     *
     * @param bizTypeId
     * @param queryKey
     * @param swanParam
     * @return
     */
    CompletableFuture<Result<QueryData>> getSwanDataByKey(Integer bizTypeId, String queryKey, SwanParam swanParam);


    /**
     * swan数据查询，走swan进阶版接口
     *
     * @param bizTypeId
     * @param queryKey
     * @param swanParam
     * @return
     */
    CompletableFuture<Result<QueryData>> getSwanDataByQueryKey(Integer bizTypeId, String queryKey, SwanParam swanParam);

    /**
     * 查询搬单到电商的团购
     *
     * @param
     * @return
     */
    CompletableFuture<List<Integer>> getTransformProductId(List<Integer> dealIds);

    /**
     * 查询货架多选商品列表（搜索）
     *
     * @param request
     * @return
     */
    CompletableFuture<Response<ShelfNavTabProductList>> getMultiSelectedShelfProducts(QueryProductsRequest request);

    /**
     * 查询货架多选筛选（搜索）
     *
     * @param request
     * @return
     */
    CompletableFuture<Response<ShelfFilterComponent>> getMultiSelectedShelfFilter(QueryFiltersRequest request);

    /**
     * 校验是否是灰度门店
     *
     * @param shopId
     * @param greyScene
     * @return
     */
    CompletableFuture<Boolean> getIsGreyWhiteShop(Long shopId, String greyScene);

    /**
     * 根据美团实用户id获取映射的点评虚用户id
     *
     * @param realMtUserId
     * @return
     */
    CompletableFuture<BindRelationResp> getVirtualDPUserIdByRealMTUserId(long realMtUserId) throws TException;


    /**
     * 根据批量商品 id 查询商品信息
     *
     * @param productIds
     * @return
     */
    CompletableFuture<List<Product>> findProductInfoByIds(List<Integer> productIds);

    /*************************************标品查询接口***************************************/

    /**
     * 根据资源id查询资源缓存
     *
     * @param request
     * @return
     */
    CompletableFuture<ResourceBatchQueryResponse> batchQueryResource(ResourceBatchQueryRequest request);

    /**
     * 查询星级和评分数据
     *
     * @param request
     * @return
     */
    CompletableFuture<Map<Long, AggOverallReviewModel>> queryReview(ContentBaseRequest request);

    /**
     * 查询点评侧收藏状态
     *
     * @param bizIds
     * @param bizType
     * @param userId
     * @return
     */
    CompletableFuture<Map<String, Boolean>> getDpFavorStatus(List<String> bizIds, int bizType, long userId);

    /**
     * 查询点评侧收藏用户数
     *
     * @param bizIds
     * @param bizType
     * @return
     */
    CompletableFuture<Map<String, Integer>> getDpFavorCount(List<String> bizIds, int bizType);

    /**
     * 查询美团侧收藏状态
     *
     * @param userId
     * @param collType
     * @param ids
     * @return
     */
    CompletableFuture<List<Long>> getMtCollectedFromGivenSets(long userId, CollTypeEnum collType, List<Long> ids, short srcType);

    /**
     * 查询美团侧收藏用户数
     *
     * @param collType
     * @param collIds
     * @return
     */
    CompletableFuture<Map<Long, Integer>> getMtCollectedCount(CollTypeEnum collType, List<Long> collIds, short srcType);

    /**
     * 查询资源销量
     *
     * @param resourceId
     * @return
     */
    CompletableFuture<Map<Long, Integer>> querySpuSaleByResourceId(long resourceId);

    /**
     * 根据 shopId 获取店铺简介
     *
     * @param shopId
     * @return
     */
    CompletableFuture<WeddingShopBriefInfoDTO> loadShopBriefInfoByShopId(long shopId);

    /**
     * 从亲子运营后台 根据点评shopId 召回团单
     *
     * @param dpShopId
     * @param limit
     * @return
     */
    CompletableFuture<List<BabyTimeLimeTgSpikeDTO>> loadBabyTimeLimitTgListByShopId(long dpShopId, int limit);

    /**
     * 根据 wmpoi_id和拿到的tab信息获取外卖医药商户对应tab下的商品
     *
     * @param spuTagProductParam
     * @return
     */
    CompletableFuture<SpuTagResponse> batchGetMedicineShelfProducts(SpuTagProductParam spuTagProductParam);

    /**
     * 获取商户详情页、标品详情页的榜单信息
     *
     * @param entityScenesRankInfoRequest
     * @return
     */
    CompletableFuture<Map<Integer, RankEntityScenesDTO>> loadRankEntityInfoDTO(EntityScenesRankInfoRequest entityScenesRankInfoRequest);

    /**
     * 获取美团侧地铁线信息
     *
     * @param mtCityId
     * @return
     */
    CompletableFuture<List<SubwayLine>> multiGetMtSubwayLineByCity(int mtCityId);

    /**
     * 获取美团侧地铁站信息
     *
     * @param mtSubwayLineId
     * @return
     */
    CompletableFuture<List<SubwayStation>> multiGetMtSubwayStationByLineId(int mtSubwayLineId);

    /**
     * 判断门店是否在组队拼场黑名单里
     *
     * @param dpShopId
     * @return
     */
    CompletableFuture<ResponseDTO<Boolean>> getGroupPinShopBlock(Long dpShopId);

    /**
     * 根据标品ID获取标品详情页用户评价信息
     *
     * @param beautyGoodsReviewRequest
     */
    CompletableFuture<DramaPageReviewDetailDto> getStandardDetailReviewByResourceId(BeautyGoodsReviewRequest beautyGoodsReviewRequest);

    /**
     * 获取标品主题数据
     *
     * @param request
     * @return
     */
    CompletableFuture<List<SpuDTO>> querySpuTheme(SpuRequest request);

    /**
     * 通过城市召回标品id，需要美团侧id
     *
     * @param request
     * @return
     */
    CompletableFuture<List<Long>> querySpuIdsByCity(CitySpuIdQueryRequest request);

    /**
     * 通过门店召回标品id，需要美团侧id
     *
     * @param request
     * @return
     */
    CompletableFuture<List<Long>> querySpuIdsByPoi(PoiSpuIdQueryRequest request);

    /**
     * 根据密室/剧本杀类目获取组队拼场的优惠活动状态
     *
     * @param activityType
     * @return
     */
    CompletableFuture<Boolean> checkGroupPinCouponStatus(int activityType);

    /**
     * 根据门店查询该门店下的秒杀团单
     *
     * @param request
     * @return
     */
    CompletableFuture<QuerySecKillDealByPoiResponse> querySecKillDealByPoi(QuerySecKillDealByPoiRequest request);

    /**
     * 根据用户搜索词查询对应商品
     *
     * @param request
     * @return
     */
    CompletableFuture<DealGroupSearchResponse<DealGroupDto>> searchDealGroupBySearchTerm(DealGroupSearchRequest request);

    /**
     * 根据团单id查询团单详情
     *
     * @param dealGroupIds 团单id列表
     * @return 团单详情
     */
    CompletableFuture<Map<Integer, DealDetailDto>> batchQueryDealDetailInfo(List<Integer> dealGroupIds);

    /**
     * 获取推荐流程唯一Id
     *
     * @param recommendParameters 推荐接口请求参数
     * @return 推荐flowId
     */
    CompletableFuture<String> getRecommendFlowId(RecommendParameters recommendParameters);

    /**
     * 获取商场会员专属的团购
     *
     * @param tenantIdReqDTO
     * @return
     */
    CompletableFuture<ListAllEffectMemberGrouponByTenantIdRespDTO> listAllEffectMemberGrouponByTenantId(ListAllEffectMemberGrouponByTenantIdReqDTO tenantIdReqDTO);

    /**
     * 根据团购ID进行过滤
     * 商场会员团购列表使用
     *
     * @param dealGroupIds
     * @param platform
     * @return
     */
    CompletableFuture<List<Long>> queryMallFilteredDealGroups(List<Long> dealGroupIds, int platform);

    /**
     * 判断搜索词是否是商户词
     */
    CompletableFuture<IntentionResultResponse> recognizeIntention(IntentionRequest request) throws TException;

    /**
     * 查询是否有直播模块
     */
    CompletableFuture<QueryIsLivingRespDTO> queryIsLiving(QueryIsLivingReqDTO reqDTO) throws TException;

    /**
     * 查询特团商品
     */
    CompletableFuture<ProductInfoResponse> batchQueryProductInfo(ProductInfoRequest request);


    /**
     * 查询优惠码业务绑定团单
     */
    CompletableFuture<RemoteResponse<List<Long>>> queryPromoCodeDealGroups(Long dpShopId);

    /**
     * 获取商户全量在线售卖团单，入参shopId为点评侧id，出参为点评团单id
     *
     * @param shopId
     * @param platform
     * @return
     */
    CompletableFuture<List<Long>> queryShopSaleDealIds(long shopId, int platform);

    /**
     * 分页获取商户在线售卖团单
     * @param shopId 门店id
     * @param platform 平台
     * @param start 开始位置
     * @param limit 分页大小
     * @return 团单id列表
     */
    CompletableFuture<List<Long>> queryShopSaleDealGroupIds(long shopId, int platform, int start, int limit);

    /**
     * 根据点评团单id查团单映射信息，调用新的商品查询中心服务
     *
     * @param dpDealIds 点评团单id，限制最多20个
     * @return
     */
    CompletableFuture<List<DealGroupDTO>> batchGetMtDealIdByDp(List<Long> dpDealIds);

    /**
     * 根据dealId查询团单项目信息，调用新的商品查询中心服务
     *
     * @param request
     * @return
     */
    CompletableFuture<List<DealGroupDTO>> batchGetServiceProjectByDealIds(QueryByDealGroupIdRequest request);

    /**
     * 查询餐团单信息
     *
     * @param request
     * @return
     */
    CompletableFuture<DealResponse> getFoodDealInfos(DealRequest request);

    /**
     * 点评门店Id转美团门店Id批量转换
     *
     * @param dpShopIds
     * @return
     */
    CompletableFuture<Map<Long, List<Long>>> batchGetMtByDpIds(List<Long> dpShopIds);

    /**
     * 批量查询美团门店信息
     */
    CompletableFuture<Map<Long, MtPoiDTO>> batchGetPoiByMtShopIds(List<Long> mtShopIds, List<String> fields);

    /**
     * 查询团单的基本信息
     *
     * @param dealIds
     * @return
     */
    CompletableFuture<Map<Long, DealBasicInfo>> getDealBasicInfo(List<Long> dealIds);

    /**
     * 查询供需诊断任务团单
     *
     * @param backCityId
     * @param pageNo
     * @param pageSize
     * @return
     */
    CompletableFuture<Result<QueryData>> batchQuerySupplyCommandMatchDeals(int backCityId, int pageNo, int pageSize);

    /**
     * 查询美团后台城市id
     *
     * @param sourceCityId
     * @param isMt
     * @return
     */
    CompletableFuture<TransformCityTypeResponse> queryBackCityId(String sourceCityId, boolean isMt);

    /**
     * 批量查询组合购商品信息
     *
     * @param request
     * @return
     */
    CompletableFuture<CombineProductInfoResponse> batchQueryCombinationProductInfo(CombineProductInfoRequest request);

    /**
     * 根据点评实id查各种用户信息FlattedBindRelation
     *
     * @param dpUserId
     * @return
     */
    CompletableFuture<FlattedBindRelation> getUserInfoByDpUserId(long dpUserId);

    /**
     * 验证门店是否有某个zdc标签
     *
     * @param dpShopId
     * @param zdcTag
     * @return
     */
    CompletableFuture<Boolean> checkZdcTagByDpShopId(long dpShopId, long zdcTag);


    CompletableFuture<List<Long>> findZdcTagByDpShopId(long dpShopId, String bizCode);


    /**
     * 查询教育的试听课视频
     *
     * @param request
     * @return
     */
    CompletableFuture<List<EduTechnicianVideoDTO>> batchQueryEduTechnicianVideoInfo(QueryEduTechnicianVideoForCRequest request);

    /**
     * 查询中心接口
     */
    CompletableFuture<QueryDealGroupListResult> queryByDealGroupIds(QueryByDealGroupIdRequest request);

    /**
     * 查询了留资权益
     *
     * @param request
     * @return
     */
    CompletableFuture<BatchQueryLeadsInfoRespDTO> batchQueryLeadsInfo(BatchQueryLeadsInfoReqDTO request);


    /**
     * 查询
     *
     * @param request
     * @return
     */
    CompletableFuture<LeadsCountRespDTO> queryShopReservationCount(LeadsCountADTO request);

    /**
     * 查询标品下商品Id列表
     *
     * @param request
     * @return
     */
    CompletableFuture<List<SpuProductDTO>> batchQuerySpuRelatedProductIds(QuerySpuProductRequest request);

    /**
     * 查询平台商品信息（缓存），最多仅支持100条
     *
     * @param request
     * @return
     */
    CompletableFuture<List<ProductSkuDTO>> queryPlatformProducts(ProductSkuBatchQueryRequest request);

    /**
     * 批量查询平台商品信息（缓存），内部分页
     *
     * @param request
     * @return
     */
    CompletableFuture<List<ProductSkuDTO>> batchQueryPlatformProducts(ProductSkuBatchQueryRequest request);

    /**
     * 查询用户手机号码
     *
     * @param userId   用户id
     * @param platform 平台：1点评；2美团
     * @return 手机号码
     */
    CompletableFuture<String> queryMobile(long userId, int platform);

    /**
     * 查询用户所有开卡信息
     *
     * @param mobile           手机号码
     * @param platform         平台，点评=1，美团=2
     * @param statusSet        要查询的卡状态
     * @param rightPackageType 要查询的卡类型
     * @return 卡信息列表
     */
    CompletableFuture<List<MemberCardDTO>> queryFitnessCrossCardListByUserId(String mobile, int platform, Set<Integer> statusSet, int rightPackageType);

    /**
     * 查询订单留资总数
     * @param reqDTO   留资请求参数
     * @return         订单留资总数
     */
    CompletableFuture<Integer> queryLeadsCount(LeadsCountReqDTO reqDTO);

    /**
     * 查询门店下打包商品
     *
     * @param request
     * @return
     */
    CompletableFuture<List<ProductRelationDTO>> queryPackProductIdByShop(PackProductByIdRequest request);

    /**
     * 识别用户意图-分词
     * @param request
     * @return
     */
    CompletableFuture<IntentionRecognizeResult> recognize(IntentionRecognizeRequest request);

    /**
     * 查询对应的职人绑定货架商品
     */
    CompletableFuture<TechnicianResp<TechStandardShelfModule>> queryStaffShelf(TechProductShelfRequest reqDTO);

    CompletableFuture<PromoQRCodeResponse<StaffCodeDTO>> getStaffCodeDTOByCodeId(Long codeId);

    CompletableFuture<CommonResponse<List<DistributorActivityDTO>>> queryJoinDistributorActivity(DistributorActivityRequest request);

    CompletableFuture<InterestCalculateResponse> interestCalculate(InterestCalculateRequest request);

    CompletableFuture<List<InterestBatchCalculateResponse>> batchInterestCalculate(List<InterestCalculateRequest> requestList);

    CompletableFuture<String> queryCrossTitleByBackCat(List<Integer> backCatIds);

    /**
     * 是否支持自助开球
     * @param request
     * @return
     */
    CompletableFuture<com.dianping.joygeneral.api.thirdpart.dto.Response<Boolean>> queryAutoOpenTable(QueryAutoOpenTableReqDTO request);

    /**
     * 查询自营清洗门店信息
     * @param dpShopId 点评门店id
     * @return 门店信息
     */
    CompletableFuture<NewReserveSubmissionPageWhiteShopCheckRespDTO> querySelfOperatedCleaningShopInfo(Long dpShopId);

    /**
     * 查询是否支持预订
     * 
     * @param dpShopId 点评门店id
     * @return
     */
    CompletableFuture<Boolean> queryIsSupportStrictReserve(Long dpShopId);

    /**
     * 查询飞天平台
     *
     * @param param
     * @return
     */
    CompletableFuture<QueryResultSync> queryByKey(KeyQueryParamSync param);

    /**
     * 异步刷新运营货架配置
     * @param request
     * @return
     */
    CompletableFuture<Boolean> asyncFlashOperatorShelfConfig(OperatorShelfConfigFlashRequest request);

    /**
     * 判断是否是小程序门店
     * @param shopId
     * @param platform
     * @return
     */
    CompletableFuture<Boolean> queryIsMiniprogramShop(Long shopId, int platform);

    /**
     * 查询用户是否开启先用后付
     * @param request
     * @return
     */
    CompletableFuture<BNPLExposureResponse> queryIsAfterPay(BNPLExposureRequest request);


    CompletableFuture<Boolean> queryShopIsAutoVerify(ShopAutoVerifyQueryRequest request);
}
