package com.sankuai.dzviewscene.nr.atom.cache;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.athena.inf.cache2.CacheClientConfig;
import com.sankuai.athena.inf.cache2.CacheKey;
import com.sankuai.athena.inf.cache2.loader.DataLoader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Random;
import java.util.concurrent.CompletableFuture;

@Component
@Slf4j
public class RedisCacheComponent {

    private static final String CACHE_CLUSTER_NAME = "redis-dealgroup";

    private static CacheClientConfig cacheClientConfig = new CacheClientConfig(CACHE_CLUSTER_NAME);

    private static CacheClient cacheClient = AthenaInf.getCacheClient(cacheClientConfig);

    public <V> CompletableFuture<V> getCacheData(CacheConfig cacheConfig, CacheKey cacheKey, TypeReference<V> typeReference, DataLoader<V> dataLoader) {
        return cacheClient.asyncGetReadThrough(cacheKey, typeReference, dataLoader, getExpiredInSeconds(cacheConfig), getRefreshAfterInSeconds(cacheConfig));
    }

    public int getExpiredInSeconds(CacheConfig cacheConfig) {
        //过期时间附加300秒内随机数
        return cacheConfig.getExpiredTime() + new Random().nextInt(300);
    }

    public int getRefreshAfterInSeconds(CacheConfig cacheConfig) {
        return cacheConfig.getRefreshTime();
    }
}
