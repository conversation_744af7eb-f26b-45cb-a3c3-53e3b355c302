package com.sankuai.dzviewscene.nr.atom.cache;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.athena.inf.cache2.CacheKey;
import com.sankuai.athena.inf.cache2.loader.DataLoader;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopCacheM;
import com.sankuai.dzviewscene.shelf.platform.utils.ModelUtils;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.CompletableFuture;


@Service
@Slf4j
public class CacheCompositeAtomService {

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.method.cache.config", defaultValue = "{}")
    private Map<String, CacheConfig> cacheConfig;

    private static final String CAT_TYPE = "CacheCompositeAtomService";

    @Resource
    private RedisCacheComponent redisCache;

    @Autowired
    private CompositeAtomService compositeAtomService;

    /**
     * 美团门店id转点评门店id
     *
     * @param mtShopId
     * @return
     */
    public CompletableFuture<Long> getDpByMtPoiIdL(long mtShopId) {
        if (mtShopId == 0) {
            return CompletableFuture.completedFuture(null);
        }
        try {
            CacheConfig cacheConfig = getCacheConfig(CacheMethodEnum.SHOP_ID_MT_2_DP.getCode());
            CacheKey cacheKey = new CacheKey(CacheMethodEnum.SHOP_ID_MT_2_DP.getCategory(), mtShopId);
            DataLoader<Long> dataLoader = key -> compositeAtomService.getDpByMtPoiIdL(mtShopId).thenApply(dpShopId -> {
                if (dpShopId == null || dpShopId == 0) {
                    Cat.logEvent(CAT_TYPE, "getDpByMtPoiIdL.invalid");
                    //向外抛出异常，不缓存无效值
                    throw new IllegalStateException("dpShopId is invalid");
                }
                return dpShopId;
            });
            return redisCache.getCacheData(cacheConfig, cacheKey, new TypeReference<Long>() {
            }, dataLoader).exceptionally(e -> {
                if (e instanceof IllegalStateException) {
                    return null;
                }
                log.error("CacheCompositeAtomService.getDpByMtPoiIdL getData error, request: {}", mtShopId, e);
                return null;
            });
        } catch (Exception e) {
            log.error("CacheCompositeAtomService.getDpByMtPoiIdL error, request: {}", mtShopId, e);
            Cat.logEvent(CAT_TYPE, "getDpByMtPoiIdL.error");
            //缓存异常降级为实时
            return compositeAtomService.getDpByMtPoiIdL(mtShopId);
        }
    }

    /**
     * 点评门店id转美团门店id
     *
     * @param dpShopId
     * @return
     */
    public CompletableFuture<Long> getMtByDpPoiIdL(long dpShopId) {
        if (dpShopId == 0) {
            return CompletableFuture.completedFuture(null);
        }
        try {
            CacheConfig cacheConfig = getCacheConfig(CacheMethodEnum.SHOP_ID_DP_2_MT.getCode());
            CacheKey cacheKey = new CacheKey(CacheMethodEnum.SHOP_ID_DP_2_MT.getCategory(), dpShopId);
            DataLoader<Long> dataLoader = key -> compositeAtomService.getMtByDpPoiIdL(dpShopId).thenApply(mtShopId -> {
                if (mtShopId == null || mtShopId == 0) {
                    Cat.logEvent(CAT_TYPE, "getMtByDpPoiIdL.invalid");
                    //向外抛出异常，不缓存无效值
                    throw new IllegalStateException("mtShopId is invalid");
                }
                return mtShopId;
            });
            return redisCache.getCacheData(cacheConfig, cacheKey, new TypeReference<Long>() {
            }, dataLoader).exceptionally(e -> {
                if (e instanceof IllegalStateException) {
                    return null;
                }
                log.error("CacheCompositeAtomService.getMtByDpPoiIdL getData error, request: {}", dpShopId, e);
                return null;
            });
        } catch (Exception e) {
            log.error("CacheCompositeAtomService.getMtByDpPoiIdL error, request: {}", dpShopId, e);
            Cat.logEvent(CAT_TYPE, "getMtByDpPoiIdL.error");
            //缓存异常降级为实时
            return compositeAtomService.getMtByDpPoiIdL(dpShopId);
        }

    }

    /**
     * 美团城市id转点评城市id
     *
     * @param mtCityId
     * @return
     */
    public CompletableFuture<Integer> getDpCityIdByMt(Integer mtCityId) {
        if (mtCityId == null || mtCityId == 0) {
            return CompletableFuture.completedFuture(null);
        }
        try {
            CacheConfig cacheConfig = getCacheConfig(CacheMethodEnum.CITY_ID_MT_2_DP.getCode());
            CacheKey cacheKey = new CacheKey(CacheMethodEnum.CITY_ID_MT_2_DP.getCategory(), mtCityId);
            DataLoader<Integer> dataLoader = key -> compositeAtomService.getDpCityIdByMt(mtCityId).thenApply(dpCityId -> {
                if (dpCityId == null || dpCityId == 0) {
                    Cat.logEvent(CAT_TYPE, "getDpCityIdByMt.invalid");
                    //向外抛出异常，不缓存无效值
                    throw new IllegalStateException("dpCityId is invalid");
                }
                return dpCityId;
            });
            return redisCache.getCacheData(cacheConfig, cacheKey, new TypeReference<Integer>() {
            }, dataLoader).exceptionally(e -> {
                if (e instanceof IllegalStateException) {
                    return null;
                }
                log.error("CacheCompositeAtomService.getDpCityIdByMt getData error, request: {}", mtCityId, e);
                return null;
            });
        } catch (Exception e) {
            log.error("CacheCompositeAtomService.getDpCityIdByMt error, request: {}", mtCityId, e);
            Cat.logEvent(CAT_TYPE, "getDpCityIdByMt.error");
            //缓存异常降级为实时
            return compositeAtomService.getDpCityIdByMt(mtCityId);
        }
    }

    /**
     * 点评城市id转美团城市id
     *
     * @param dpCityId
     * @return
     */
    public CompletableFuture<Integer> getMtCityIdByDp(Integer dpCityId) {
        if (dpCityId == null || dpCityId == 0) {
            return CompletableFuture.completedFuture(null);
        }
        try {
            CacheConfig cacheConfig = getCacheConfig(CacheMethodEnum.CITY_ID_DP_2_MT.getCode());
            CacheKey cacheKey = new CacheKey(CacheMethodEnum.CITY_ID_DP_2_MT.getCategory(), dpCityId);
            DataLoader<Integer> dataLoader = key -> compositeAtomService.getMtCityIdByDp(dpCityId).thenApply(mtCityId -> {
                if (mtCityId == null || mtCityId == 0) {
                    Cat.logEvent(CAT_TYPE, "getMtCityIdByDp.invalid");
                    //向外抛出异常，不缓存无效值
                    throw new IllegalStateException("mtCityId is invalid");
                }
                return mtCityId;
            });
            return redisCache.getCacheData(cacheConfig, cacheKey, new TypeReference<Integer>() {
            }, dataLoader).exceptionally(e -> {
                if (e instanceof IllegalStateException) {
                    return null;
                }
                log.error("CacheCompositeAtomService.getMtCityIdByDp getData error, request: {}", dpCityId, e);
                return null;
            });
        } catch (Exception e) {
            log.error("CacheCompositeAtomService.getMtCityIdByDp error, request: {}", dpCityId, e);
            Cat.logEvent(CAT_TYPE, "getMtCityIdByDp.error");
            //缓存异常降级为实时
            return compositeAtomService.getMtCityIdByDp(dpCityId);
        }
    }

    /**
     * 单门店信息缓存
     *
     * @param dpPoiRequest
     * @return
     */
    public CompletableFuture<ShopCacheM> findShopsByDpShopId(DpPoiRequest dpPoiRequest) {
        if (dpPoiRequest == null || CollectionUtils.isEmpty(dpPoiRequest.getShopIds())) {
            return CompletableFuture.completedFuture(null);
        }
        try {
            CacheConfig cacheConfig = getCacheConfig(CacheMethodEnum.SHOP_INFO_DP.getCode());
            CacheKey cacheKey = new CacheKey(CacheMethodEnum.SHOP_INFO_DP.getCategory(), dpPoiRequest.getShopIds().get(0));
            DataLoader<ShopCacheM> dataLoader = key -> compositeAtomService.findShopsByDpShopIds(dpPoiRequest).thenApply(dpPoiDTOS -> {
                if (CollectionUtils.isEmpty(dpPoiDTOS) || dpPoiDTOS.get(0) == null) {
                    Cat.logEvent(CAT_TYPE, "findShopsByDpShopIds.invalid");
                    //向外抛出异常，不缓存无效值
                    throw new IllegalStateException("dpPoiDTOS is empty");
                }
                return convert2ShopCacheM(dpPoiDTOS.get(0));
            });
            return redisCache.getCacheData(cacheConfig, cacheKey, new TypeReference<ShopCacheM>() {
            }, dataLoader).exceptionally(e -> {
                if (e instanceof IllegalStateException) {
                    return null;
                }
                log.error("CacheCompositeAtomService.findShopsByDpShopIds getData error, request: {}", JsonCodec.encode(dpPoiRequest), e);
                return null;
            });
        } catch (Exception e) {
            log.error("CacheCompositeAtomService.findShopsByDpShopIds error, request: {}", JsonCodec.encode(dpPoiRequest), e);
            Cat.logEvent(CAT_TYPE, "findShopsByDpShopId.error");
            //缓存异常降级为实时
            return compositeAtomService.findShopsByDpShopIds(dpPoiRequest).thenApply(dpPoiDTOS -> {
                if (CollectionUtils.isEmpty(dpPoiDTOS) || dpPoiDTOS.get(0) == null) {
                    return null;
                }
                return convert2ShopCacheM(dpPoiDTOS.get(0));
            });
        }
    }

    private ShopCacheM convert2ShopCacheM(DpPoiDTO dpPoiDTO) {
        ShopCacheM shopM = new ShopCacheM();
        shopM.setShopId(dpPoiDTO.getShopId() == null ? 0 : dpPoiDTO.getShopId());
        shopM.setShopUuid(dpPoiDTO.getUuid() == null ? "" : dpPoiDTO.getUuid());
        shopM.setShopType(dpPoiDTO.getShopType() == null ? 0 : dpPoiDTO.getShopType());
        shopM.setCategory(dpPoiDTO.getMainCategoryId() == null ? 0 : dpPoiDTO.getMainCategoryId());
        shopM.setBackCategory(ModelUtils.extractBackCat(dpPoiDTO));
        shopM.setUseType(dpPoiDTO.getUseType() == null ? 0 : dpPoiDTO.getUseType());
        shopM.setShopName(dpPoiDTO.getShopName());
        shopM.setLat(dpPoiDTO.getLat() == null ? 0 : dpPoiDTO.getLat());
        shopM.setLng(dpPoiDTO.getLng() == null ? 0 : dpPoiDTO.getLng());
        shopM.setCityId(dpPoiDTO.getCityId() == null ? 0 : dpPoiDTO.getCityId());
        return shopM;
    }

    public CacheConfig getCacheConfig(String methodKey) {
        if (MapUtils.isEmpty(cacheConfig)) {
            return new CacheConfig();
        }
        CacheConfig config = cacheConfig.get(methodKey);
        if (config == null) {
            return new CacheConfig();
        }
        return config;
    }
}
