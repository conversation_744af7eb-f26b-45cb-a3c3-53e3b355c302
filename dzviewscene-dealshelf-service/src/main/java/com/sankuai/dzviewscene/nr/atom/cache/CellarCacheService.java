package com.sankuai.dzviewscene.nr.atom.cache;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.style.DealDetailStyleFlashService;
import com.dianping.deal.style.dto.flash.DealDetailStyleUpdateRequest;
import com.dianping.deal.style.dto.flash.DealFlashUpdateRequest;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealModuleDetailVO;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.vo.DealDetailStructModuleVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CellarCacheService {

    @Resource
    @Qualifier("dealDetailStyleFlashService")
    private DealDetailStyleFlashService dealDetailStyleFlashService;

    public String buildKey(int productId) {
        // 定制团购详情缓存维度：团单ID
        return String.format("dealmodule-%s", productId);
    }

    /**
     * 更新或写缓存
     * @param cacheKey
     * @param cacheValue
     * @return
     */
    public boolean saveOrUpdate(String cacheKey, Object cacheValue, String callService, String message) {
        try {
            if (Objects.nonNull(cacheValue) && cacheValue instanceof DealModuleDetailVO){
                DealFlashUpdateRequest request = buildSaveOrUpdateRequest(cacheKey, cacheValue, callService, message);
                // 缓存团购详情
                dealDetailStyleFlashService.saveOrUpdate(request);
                return Boolean.TRUE;
            }
            return Boolean.FALSE;
        } catch (Exception e) {
            log.error("CellarCacheComponent.put,cacheKey:{}", cacheKey, e);
            return Boolean.FALSE;
        }
    }

    private DealFlashUpdateRequest buildSaveOrUpdateRequest(String cacheKey, Object cacheValue, String callService, String message){
        DealFlashUpdateRequest request = new DealFlashUpdateRequest();
        request.setCacheKey(cacheKey);
        request.setCacheValue(JSON.toJSONString(cacheValue));
        request.setCallService(callService);
        request.setMessage(message);
        return request;
    }

}
