package com.sankuai.dzviewscene.nr.atom.cache;

import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class CacheGrayUtils {

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.rpc.cache.gray.config", defaultValue = "{}")
    public static Map<String, GrayConfig> grayConfigMap;

    public static boolean graySwitch(long shopId, int platform, String methodKey) {
        if (MapUtils.isEmpty(grayConfigMap) || !grayConfigMap.containsKey(methodKey)) {
            return false;
        }
        GrayConfig grayConfig = grayConfigMap.get(methodKey);
        // 根据平台类型获取白名单和黑名单
        List<Long> whiteShopIds = PlatformUtil.isMT(platform) ? grayConfig.getMtWhiteShopIds() : grayConfig.getDpWhiteShopIds();
        List<Long> blackShopIds = PlatformUtil.isMT(platform) ? grayConfig.getMtBlackShopIds() : grayConfig.getDpBlackShopIds();
        if (CollectionUtils.isNotEmpty(whiteShopIds) && whiteShopIds.contains(shopId)) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(blackShopIds) && blackShopIds.contains(shopId)) {
            return false;
        }
        // 开启总开关或灰度百分比
        return grayConfig.isAllOpen() || shopId % 100 < grayConfig.getGreyPercent();
    }
}
