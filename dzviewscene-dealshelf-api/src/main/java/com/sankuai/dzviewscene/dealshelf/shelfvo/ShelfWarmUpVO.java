package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

@Data
public class ShelfWarmUpVO implements Serializable {

    /**
     * 活动信息
     */
    @MobileDo.MobileField
    private ShelfItemActivityVO activity;

    /**
     * 按钮
     */
    @MobileDo.MobileField
    private ShelfButtonVO button;

    public ShelfWarmUpVO() {}

    public ShelfWarmUpVO(ShelfItemActivityVO activity, ShelfButtonVO button) {
        this.activity = activity;
        this.button = button;
    }
}
