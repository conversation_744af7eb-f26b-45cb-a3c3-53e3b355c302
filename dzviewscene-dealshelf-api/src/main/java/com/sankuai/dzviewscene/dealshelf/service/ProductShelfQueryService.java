package com.sankuai.dzviewscene.dealshelf.service;

import com.sankuai.dzviewscene.dealshelf.req.DealShelfRequest;
import com.sankuai.dzviewscene.dealshelf.res.DealShelfResponse;

/**
 * 货架通用查询服务
 * <AUTHOR>
 * @date 2023/3/30
 */
public interface ProductShelfQueryService {

    /**
     * 获取团单货架（首次加载），默认都可以走这个
     * 本质上是对 {com.sankuai.dzviewscene.productshelf.gateways.mapi.DealShelfApi} 的 RPC 封装
     * @param request
     * @return
     */
    DealShelfResponse getDealShelfForFirstLoad(DealShelfRequest request);

    /**
     * 获取团购列表（点击Tab获取List或无tab的全量List等）
     * 本质上是对 {com.sankuai.dzviewscene.productshelf.gateways.mapi.ShelfDealsApi} 的 RPC 封装
     * @param request
     * @return
     */
    DealShelfResponse getShelfDeals(DealShelfRequest request);
}
