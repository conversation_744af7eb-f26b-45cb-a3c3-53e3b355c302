/*
 * Create Author  : liyan<PERSON>
 * Create Date    : 2024-09-11
 * Project        :
 * File Name      : FloatTagModel.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-11
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
public class FloatTagModel implements Serializable {
    /**
     * 标签
     */
    @MobileDo.MobileField
    private IconRichLabelModel tag;

    /**
     * 位置，1左上，2右上，3右下，4左下
     */
    @MobileDo.MobileField
    private int position;

}
