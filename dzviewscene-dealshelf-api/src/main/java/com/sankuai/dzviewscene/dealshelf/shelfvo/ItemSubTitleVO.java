/*
 * Create Author  : liyan<PERSON>
 * Create Date    : 2024-09-10
 * Project        :
 * File Name      : ItemSubTitleVO.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-10
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
public class ItemSubTitleVO implements Serializable {
    /**
     * 分隔形式，0-竖线分隔，1-圆点分隔
     * @see com.sankuai.dzviewscene.product.unifiedshelf.enums.SubTitleJoinTypeEnum
     */
    @MobileDo.MobileField
    private int joinType;

    /**
     * 标签列表
     */
    @MobileDo.MobileField
    private List<StyleTextModel> tags;

    /**
     * 副标题前icon
     */
    @MobileDo.MobileField
    private IconRichLabelModel iconTag;

    /**
     * 前置副标题标签列表（用于安心练等特殊标签）
     */
    @MobileDo.MobileField
    private List<ShelfTagVO> preTags;

}
