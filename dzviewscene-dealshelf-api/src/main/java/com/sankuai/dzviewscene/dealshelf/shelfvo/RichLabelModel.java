/*
 * Create Author  : liyan<PERSON>
 * Create Date    : 2024-09-07
 * Project        :
 * File Name      : RichLabelModel.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-07
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
public class RichLabelModel implements Serializable {
    /**
     * 扩展样式配置
     */
    @MobileDo.MobileField
    private String extra;

    /**
     * 多文本列表
     */
    @MobileDo.MobileField
    private List<String> multiText;

    /**
     * 跳转链接
     */
    @MobileDo.MobileField
    private String jumpUrl;

    /**
     * 样式，0-默认，1-圆角，2-气泡
     */
    @MobileDo.MobileField
    private int style;

    /**
     * 背景色
     */
    @MobileDo.MobileField
    private String backgroundColor;

    /**
     * 文本颜色，16进制，示例：#FFEDDE
     */
    @MobileDo.MobileField
    private String textColor;

    /**
     * 文本
     */
    @MobileDo.MobileField
    private String text;

}
