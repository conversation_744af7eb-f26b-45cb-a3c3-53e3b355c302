/*
 * Create Author  : liyan<PERSON>
 * Create Date    : 2024-09-10
 * Project        :
 * File Name      : ShelfOceanEntryVO.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-10
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
public class ShelfOceanEntryVO implements Serializable {
    /**
     * 打点上报的表名
     */
    @MobileDo.MobileField
    private String category;

    /**
     * 额外打点数据，json格式
     */
    @MobileDo.MobileField
    private String labs;

    /**
     * 实验结果信息
     */
    @MobileDo.MobileField
    private String abtest;

    /**
     * 模块点击点
     */
    @MobileDo.MobileField
    private String bidClick;

    /**
     * 模块曝光点
     */
    @MobileDo.MobileField
    private String bidView;

}
