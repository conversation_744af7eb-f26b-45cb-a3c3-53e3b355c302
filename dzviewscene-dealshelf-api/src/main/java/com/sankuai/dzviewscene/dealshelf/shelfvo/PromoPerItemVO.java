package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

@Data
public class PromoPerItemVO implements Serializable {
    /**
     * 神券优惠信息
     */
    @MobileDo.MobileField
    private CouponPromoItemVO couponPromoItem;

    /**
     * 优惠条目标签
     */
    @MobileDo.MobileField
    private IconRichLabelModel tag;

    /**
     * 优惠金额
     */
    @MobileDo.MobileField
    private String promoPrice;

    /**
     * 优惠描述
     */
    @MobileDo.MobileField
    private String desc;

    /**
     * 优惠标题
     */
    @MobileDo.MobileField
    private String title;

    /**
     * 优惠类型，来自于营销
     */
    @MobileDo.MobileField
    private String promoType;

    /**
     * 优惠的ID，来自于营销
     */
    @MobileDo.MobileField
    private String promoId;
}