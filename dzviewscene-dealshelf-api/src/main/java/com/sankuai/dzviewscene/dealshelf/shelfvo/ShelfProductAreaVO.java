/*
 * Create Author  : liyanmin
 * Create Date    : 2024-09-07
 * Project        :
 * File Name      : ShelfProductAreaVO.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-07
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
public class ShelfProductAreaVO implements Serializable {
    /**
     * 商品列表
     */
    @MobileDo.MobileField
    private List<ShelfItemVO> items;

    /**
     * 折叠跳转链接
     */
    @MobileDo.MobileField
    private String moreJumpUrl;

    /**
     * 折叠文案
     */
    @MobileDo.MobileField
    private String moreText;

    /**
     * 默认显示商品数
     */
    @MobileDo.MobileField
    private int defaultShowNum;

    /**
     * 是否有下一页
     */
    @MobileDo.MobileField
    private boolean hasNext;

    /**
     * 商品区样式
     */
    @MobileDo.MobileField
    private int showType;

}
