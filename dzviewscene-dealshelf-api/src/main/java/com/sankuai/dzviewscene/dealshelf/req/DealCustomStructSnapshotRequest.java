package com.sankuai.dzviewscene.dealshelf.req;

import com.dianping.deal.common.enums.ClientTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-10-21
 * @desc 团单自定义团购详情快照请求
 */
@Data
public class DealCustomStructSnapshotRequest implements Serializable {
    /**
     * 团单ID
     */
    private Long dealGroupId;

    /**
     * 门店ID
     */
    private Long shopId;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 客户端类型
     */
    private ClientTypeEnum clientTypeEnum;

    /**
     * 请求来源
     * trade_snapshot-交易快照
     */
    private String requestSource;
}
