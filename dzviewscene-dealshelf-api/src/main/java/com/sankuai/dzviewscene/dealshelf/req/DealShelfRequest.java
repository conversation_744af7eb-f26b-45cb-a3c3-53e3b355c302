package com.sankuai.dzviewscene.dealshelf.req;

import java.io.Serializable;

/**
 * 团单货架请求
 * <AUTHOR>
 * @date 2023/3/30
 */
public class DealShelfRequest implements Serializable {

    /**
     * 统一商户ID
     */
    private String shopUuid;

    /**
     * 扩展字段
     */
    private String extra;

    /**
     * 商户Id
     */
    private long shopId;

    /**
     * 城市Id
     */
    private int cityId;

    /**
     * 来自后端分配@float.lu
     */
    private String sceneCode;

    /**
     * 定位的关键字
     */
    private String searchKeyword;

    /**
     * 经度
     */
    private double lng;

    /**
     * 纬度
     */
    private double lat;

    /**
     * 页起始号
     */
    private int pageNo = 1;

    /**
     * 页的大小
     */
    private int pageSize = 6;

    /**
     * 平台 {@link VCClientTypeEnum}
     * 100： dpapp
     * 101： m
     * 200： mtapp
     * 201： i
     */
    private int platform;

    /**
     * 客户端类型：ios | android | 空字符串
     */
    private String client = "android";

    /**
     * 版本号
     */
    private String version;

    /**
     * 设备ID，dpId or uuid
     */
    private String deviceId;

    /**
     * unionid
     */
    private String unionId;

    /**
     * 点评/美团 用户ID
     */
    private long userId;

    /**
     * 选择的导航ID，用于筛选
     * 可从此处获取 {@link com.sankuai.dzviewscene.productshelf.vu.vo.FilterButtonVO#filterBtnId}
     */
    private long filterBtnId;

    private String spaceKey ;

    public String getSpaceKey() {
        return spaceKey;
    }

    public void setSpaceKey(String spaceKey) {
        this.spaceKey = spaceKey;
    }

    public String getShopUuid() {
        return shopUuid;
    }

    public void setShopUuid(String shopUuid) {
        this.shopUuid = shopUuid;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public long getShopId() {
        return shopId;
    }

    public void setShopId(long shopId) {
        this.shopId = shopId;
    }

    public int getCityId() {
        return cityId;
    }

    public void setCityId(int cityId) {
        this.cityId = cityId;
    }

    public String getSceneCode() {
        return sceneCode;
    }

    public void setSceneCode(String sceneCode) {
        this.sceneCode = sceneCode;
    }

    public String getSearchKeyword() {
        return searchKeyword;
    }

    public void setSearchKeyword(String searchKeyword) {
        this.searchKeyword = searchKeyword;
    }

    public double getLng() {
        return lng;
    }

    public void setLng(double lng) {
        this.lng = lng;
    }

    public double getLat() {
        return lat;
    }

    public void setLat(double lat) {
        this.lat = lat;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPlatform() {
        return platform;
    }

    public void setPlatform(int platform) {
        this.platform = platform;
    }

    public String getClient() {
        return client;
    }

    public void setClient(String client) {
        this.client = client;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public long getFilterBtnId() {
        return filterBtnId;
    }

    public void setFilterBtnId(long filterBtnId) {
        this.filterBtnId = filterBtnId;
    }
}
