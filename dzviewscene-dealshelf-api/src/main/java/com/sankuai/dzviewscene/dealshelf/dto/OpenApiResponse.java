package com.sankuai.dzviewscene.dealshelf.dto;

import java.io.Serializable;

/**
 * 开放平台返回结果
 * <AUTHOR>
 * @date 2022/5/20
 */
public class OpenApiResponse<T> implements Serializable {

    private T data;

    /**
     * 枚举如下：(多了再找枚举)
     * SUCCESS：成功
     * PARAM_ERROR：参数错误，%s
     * SYSTEM_ERROR: 系统错误，%s
     */
    private String code;

    private String msg;

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public void setSuccess() {
        this.code = "SUCCESS";
        this.msg = "成功";
    }

    public void setParamError(String errorMsg) {
        this.code = "PARAM_ERROR";
        this.msg = String.format("参数错误 %s", errorMsg);
    }

    public void setSystemError(String errorMsg) {
        this.code = "SYSTEM_ERROR";
        this.msg = String.format("系统错误 %s", errorMsg);
    }

    @Override
    public String toString() {
        return "OpenApiResponse{" +
                "data=" + data +
                ", code='" + code + '\'' +
                ", msg='" + msg + '\'' +
                '}';
    }
}
