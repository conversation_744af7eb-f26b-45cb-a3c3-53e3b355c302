package com.sankuai.dzviewscene.dealshelf.res;

import com.sankuai.dzviewscene.productshelf.vu.vo.DzShelfResponseVO;

import java.io.Serializable;

/**
 * 团单货架返回结果
 * <AUTHOR>
 * @date 2023/3/30
 */
public class DealShelfResponse implements Serializable {

    /**
     * 返回结果
     */
    private DzShelfResponseVO data;

    /**
     * 错误信息
     */
    private String errorMessage;

    public DealShelfResponse() {

    }

    public DealShelfResponse(DzShelfResponseVO data, String errorMessage) {
        this.data = data;
        this.errorMessage = errorMessage;
    }

    public DzShelfResponseVO getData() {
        return data;
    }

    public void setData(DzShelfResponseVO data) {
        this.data = data;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
