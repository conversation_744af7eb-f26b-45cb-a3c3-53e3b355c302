/*
 * Create Author  : liyanmin
 * Create Date    : 2024-09-07
 * Project        :
 * File Name      : UnifiedShelfService.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.service;

import com.sankuai.dzviewscene.dealshelf.req.UnifiedShelfRequest;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfFilterProductAreaVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.UnifiedShelfResponse;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-07
 * @since dzviewscene-dealshelf-home 1.0
 */
public interface UnifiedShelfService {

    /**
     * POI货架统一服务,查询货架导航及商品列表
     */
    UnifiedShelfResponse queryShelfNavAndProduct(UnifiedShelfRequest shelfRequest);

    /**
     * POI货架统一服务，根据导航查询商品列表
     */
    ShelfFilterProductAreaVO queryShelfProductByFilterId(UnifiedShelfRequest shelfRequest);

}
