/*
 * Create Author  : liya<PERSON><PERSON>
 * Create Date    : 2024-09-07
 * Project        :
 * File Name      : CommonResponse.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.res;

import lombok.Data;

import java.io.Serializable;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-07
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
public class CommonResponse<T> implements Serializable {

    public static final int SUCCESS = 200;

    public static final int FAIL = 500;

    private int code;

    private T data;

    private String msg;

    private String traceId;

    public CommonResponse() {
    }

    public CommonResponse(int code, T data, String msg) {
        this.code = code;
        this.data = data;
        this.msg = msg;
    }

    public CommonResponse(int code, T data, String msg, String traceId) {
        this.code = code;
        this.data = data;
        this.msg = msg;
        this.traceId = traceId;
    }

    public static <T> CommonResponse<T> success(T data) {
        return new CommonResponse<>(SUCCESS, data, null);
    }

    public static <T> CommonResponse<T> fail(String msg) {
        return new CommonResponse<>(FAIL, null, msg);
    }
}
