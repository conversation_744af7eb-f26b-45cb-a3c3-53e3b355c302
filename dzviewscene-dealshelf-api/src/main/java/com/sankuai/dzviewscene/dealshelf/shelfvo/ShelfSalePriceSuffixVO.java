package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

@Data
public class ShelfSalePriceSuffixVO {

    /**
     * 售价后缀文案
     */
    @MobileDo.MobileField
    private String salePriceSuffix;

    /**
     * 字体大小
     */
    @MobileDo.MobileField
    private int textSize;

    public ShelfSalePriceSuffixVO() {}

    public ShelfSalePriceSuffixVO(String salePriceSuffix) {
        this.salePriceSuffix = salePriceSuffix;
    }

    public ShelfSalePriceSuffixVO(String salePriceSuffix, int textSize) {
        this.salePriceSuffix = salePriceSuffix;
        this.textSize = textSize;
    }
}
