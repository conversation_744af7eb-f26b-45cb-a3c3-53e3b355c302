package com.sankuai.dzviewscene.dealshelf.service;

import com.sankuai.dzviewscene.dealshelf.req.DealCustomStructSnapshotRequest;
import com.sankuai.dzviewscene.dealshelf.res.CommonResponse;

/**
 * <AUTHOR>
 * @date 2024-10-21
 * @desc 团单自定义团购详情快照服务
 */
public interface DealCustomStructSnapshotService {
    /**
     * 查询团单自定义团购详情快照
     * @param request 请求参数
     * @return 团单自定义团购详情快照
     */
    CommonResponse<String> queryDealCustomStructSnapshot(DealCustomStructSnapshotRequest request);
}
