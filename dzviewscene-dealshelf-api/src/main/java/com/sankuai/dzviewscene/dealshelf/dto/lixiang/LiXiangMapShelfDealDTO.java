package com.sankuai.dzviewscene.dealshelf.dto.lixiang;

import java.io.Serializable;
import java.util.List;

/**
 * 货架上的每个团购
 */
public class LiXiangMapShelfDealDTO implements Serializable {
    /**
     * 团购id
     */
    private long itemId;

    /**
     * 标题前面(或者图片上)的活动标签
     */
    private LiXiangMapActivityTagDTO activityTag;

    /**
     * 团购标题
     */
    private String title;

    /**
     * 头图
     */
    private String picUrl;

    /**
     * 市场价（有优惠时不返回），单位分
     */
    private int marketPrice;

    /**
     * 团购售价（有优惠时为优惠价），单位分
     */
    private int salePrice;

    /**
     * 折扣
     */
    private String discountTag;

    /**
     * 优惠标签，如已减20
     */
    private String promoTag;

    /**
     * 销量信息，如半年消费30
     */
    private String sale;

    /**
     * 团购跳转链接
     */
    private String jumpUrl;

    /**
     * 副标题
     */
    private List<String> productTags;

    /**
     * 排序顺序
     */
    private int index;

    /**
     * 和平台优惠冲突的优惠信息
     */
    private List<LiXiangMapDealPromoDetailDTO> dealPromos;

    public long getItemId() {
        return itemId;
    }

    public void setItemId(long itemId) {
        this.itemId = itemId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public int getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(int marketPrice) {
        this.marketPrice = marketPrice;
    }

    public int getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(int salePrice) {
        this.salePrice = salePrice;
    }

    public String getPromoTag() {
        return promoTag;
    }

    public void setPromoTag(String promoTag) {
        this.promoTag = promoTag;
    }

    public String getSale() {
        return sale;
    }

    public void setSale(String sale) {
        this.sale = sale;
    }

    public List<String> getProductTags() {
        return productTags;
    }

    public void setProductTags(List<String> productTags) {
        this.productTags = productTags;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public List<LiXiangMapDealPromoDetailDTO> getDealPromos() {
        return dealPromos;
    }

    public void setDealPromos(List<LiXiangMapDealPromoDetailDTO> dealPromos) {
        this.dealPromos = dealPromos;
    }

    public LiXiangMapActivityTagDTO getActivityTag() {
        return activityTag;
    }

    public void setActivityTag(LiXiangMapActivityTagDTO activityTag) {
        this.activityTag = activityTag;
    }

    public String getDiscountTag() {
        return discountTag;
    }

    public void setDiscountTag(String discountTag) {
        this.discountTag = discountTag;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        LiXiangMapShelfDealDTO that = (LiXiangMapShelfDealDTO) o;

        if (getItemId() != that.getItemId()) return false;
        return getTitle() != null ? getTitle().equals(that.getTitle()) : that.getTitle() == null;
    }

    @Override
    public int hashCode() {
        int result = (int) (getItemId() ^ (getItemId() >>> 32));
        result = 31 * result + (getTitle() != null ? getTitle().hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "LiXiangMapShelfDealDTO{" +
                "itemId=" + itemId +
                ", activityTag=" + activityTag +
                ", title='" + title + '\'' +
                ", picUrl='" + picUrl + '\'' +
                ", marketPrice=" + marketPrice +
                ", salePrice=" + salePrice +
                ", discountTag='" + discountTag + '\'' +
                ", promoTag='" + promoTag + '\'' +
                ", sale='" + sale + '\'' +
                ", productTags=" + productTags +
                ", index=" + index +
                ", dealPromos=" + dealPromos +
                '}';
    }
}
