/*
 * Create Author  : liyan<PERSON>
 * Create Date    : 2024-09-07
 * Project        :
 * File Name      : ShelfItemVO.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-07
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
public class ShelfItemVO implements Serializable {

    /**
     * 售价后缀信息对象
     */
    @MobileDo.MobileField
    private ShelfSalePriceSuffixVO salePriceSuffixObject;

    /**
     * 货架预热信息
     */
    @MobileDo.MobileField
    private List<ShelfWarmUpVO> warmUp;

    /**
     * 特色标签
     */
    @MobileDo.MobileField
    private ItemSpecialTagVO specialTags;

    /**
     * 副标题
     */
    @MobileDo.MobileField
    private ItemSubTitleVO productTags;

    /**
     * 标题
     */
    @MobileDo.MobileField
    private List<StyleTextModel> title;

    /**
     * 标题标签
     */
    @MobileDo.MobileField
    private IconRichLabelModel titleTag;

    /**
     * 头图
     */
    @MobileDo.MobileField
    private PicAreaVO headPic;

    /**
     * 划线价（门市价）
     */
    @MobileDo.MobileField
    private String marketPrice;

    /**
     * 价格下方标签
     */
    @MobileDo.MobileField
    private List<ShelfTagVO> priceBottomTags;

    /**
     * 按钮上方轮播信息，如销量、最近购买
     */
    @MobileDo.MobileField
    private List<CarouselMsg> buttonCarouselMsg;

    /**
     * 活动信息
     */
    @MobileDo.MobileField
    private ShelfItemActivityVO activity;

    /**
     * json格式扩展信息，返回非标信息
     */
    @MobileDo.MobileField
    private String extra;

    /**
     * 埋点信息，json字符串
     */
    @MobileDo.MobileField
    private String labs;

    /**
     * 按钮
     */
    @MobileDo.MobileField
    private ShelfButtonVO button;

    /**
     * 是否有效
     */
    @MobileDo.MobileField
    private boolean available;

    /**
     * 商品跳转链接
     */
    @MobileDo.MobileField
    private String jumpUrl;

    /**
     * 商品id
     */
    @MobileDo.MobileField
    private long itemId;

    /**
     * 商品类型
     */
    @MobileDo.MobileField
    private int itemType;

    /**
     * 优惠力度标签
     */
    @MobileDo.MobileField
    private List<RichLabelModel> promoTags;

    /**
     * 售价后缀信息
     */
    @MobileDo.MobileField
    private String salePriceSuffix;

    /**
     * 售价前缀信息
     */
    @MobileDo.MobileField
    private String salePricePrefix;

    /**
     * 售价
     */
    @MobileDo.MobileField
    private String salePrice;

    /**
     * 原始价，不包含优惠
     */
    @MobileDo.MobileField
    private String basePrice;

    /**
     * 子项商品列表
     */
    @MobileDo.MobileField
    private List<ShelfItemVO> subItems;

    /**
     * 子项默认展示数量
     */
    @MobileDo.MobileField
    private int defaultShowNum;

    /**
     * 子项更多文案
     */
    @MobileDo.MobileField
    private String moreText;

    /**
     * 商卡样式
     */
    @MobileDo.MobileField
    private int showType;
}
