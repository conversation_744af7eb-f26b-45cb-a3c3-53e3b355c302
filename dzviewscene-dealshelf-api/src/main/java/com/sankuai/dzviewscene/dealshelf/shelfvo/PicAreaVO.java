/*
 * Create Author  : liyan<PERSON>
 * Create Date    : 2024-09-11
 * Project        :
 * File Name      : PicAreaVO.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-11
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
public class PicAreaVO implements Serializable {
    /**
     * 16:9头图，团详预加载用
     */
    @MobileDo.MobileField
    private String defaultPicUrl;

    /**
     * 头图角标
     */
    @MobileDo.MobileField
    private List<FloatTagModel> floatTags;

    /**
     * 头图
     */
    @MobileDo.MobileField
    private PictureModel pic;

}
