package com.sankuai.dzviewscene.dealshelf.service;

import com.sankuai.dzviewscene.dealshelf.dto.OpenApiResponse;
import com.sankuai.dzviewscene.dealshelf.dto.baidumap.BaiduMapShelfDTO;
import com.sankuai.dzviewscene.dealshelf.dto.lixiang.LiXiangMapShelfDTO;
import com.sankuai.dzviewscene.dealshelf.dto.tencentmap.ShelfComponentDTO;

/**
 * 团购货架开放平台接口
 * <AUTHOR>
 * @date 2022/5/20
 */
public interface DealShelfOpenApiService {
    /**
     * 获取腾讯地图展示的货架（美团侧数据源）
     * @param openShopId 外部shopId，需要内部自行转换
     * @param openAppKey
     * @return
     */
    OpenApiResponse<ShelfComponentDTO> getDealShelfForTencentMap(String openShopId, String openAppKey);

    /**
     * 获取百度地图货架（点评侧数据源）
     * @param dpPoiId 点评poiId
     * @return
     */
    OpenApiResponse<BaiduMapShelfDTO> getDealShelfForBaiduMap(long dpPoiId);

    /**
     * 获取理想地图的货架（美团侧数据）
     * @param mtPoiId 美团侧poiId
     * @return
     */
    OpenApiResponse<LiXiangMapShelfDTO> getDealShelfForLiXiangMap(long mtPoiId);
}
