/*
 * Create Author  : liya<PERSON><PERSON>
 * Create Date    : 2024-09-07
 * Project        :
 * File Name      : ShepherdGatewayParam.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.req;

import lombok.Data;

import java.io.Serializable;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-07
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
public class ShepherdGatewayParam implements Serializable {

    /**
     * 商户ID
     */
    private long shopId;

    /**
     * 点评用户ID
     */
    private long dpUserId;

    /**
     * 美团用户ID
     */
    private long mtUserId;
    /**
     * 用户ID
     */
    private long userId;
    /**
     * 请求是否来源于美团环境
     */
    private boolean mt;

    /**
     * 设备信息
     */
    private String dpid;

    /**
     * 对应请求头中的pragma-unionid字段
     */
    private String unionid;

    /**
     * 客户端类型(ios | android | 空字符串)
     */
    private String client;

    /**
     * App 版本号(如果是非标准版本号,则返回空字符串)
     */
    private String version;

    /**
     * 对应请求头中的MTSI-flag字段，只有过了反爬才会有该字段，该字段是反爬技术手动添加
     */
    private String mtsiflag;

    /**
     * 平台(dp | mt | 空字符串)
     */
    private String platform;

    /**
     * openId
     */
    private String openId;

    /**
     * appId
     */
    private String appId;
}
