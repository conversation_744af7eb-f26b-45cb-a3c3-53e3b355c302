<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-basic-parent</artifactId>
        <version>1.8.5.4</version>
        <relativePath/>
    </parent>
    <groupId>com.sankuai</groupId>
    <artifactId>dzviewscene-dealshelf-api</artifactId>
    <version>1.0.5</version>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dzviewscene-productshelf-api</artifactId>
            <version>1.1.81</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.dpsf</groupId>
                    <artifactId>dpsf-net</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.meituan</groupId>
                    <artifactId>mtconfig-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.pearl</groupId>
                    <artifactId>pearl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.pearl</groupId>
            <artifactId>pearl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-common-api</artifactId>
            <version>2.2.9</version>
        </dependency>
    </dependencies>
</project>
